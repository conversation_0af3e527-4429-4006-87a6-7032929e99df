<template>
  <div class="item-logistics-btn">
    <!-- 调试信息 -->
    <div v-if="showDebug" class="debug-info text-caption text-grey-6 q-mb-xs">
      <div>logisticsNo: "{{ item.logisticsNo }}" ({{ typeof item.logisticsNo }})</div>
      <div>logisticsName: "{{ item.logisticsName }}" ({{ typeof item.logisticsName }})</div>
      <div>hasLogistics: {{ hasLogistics }}</div>
    </div>

    <q-btn
      v-if="hasLogistics"
      flat
      dense
      color="primary"
      :label="$t('accountOrderDetail.buttons.trackShipment')"
      size="sm"
      icon="local_shipping"
      @click="viewItemLogistics"
    />
    <span v-else class="text-grey-5 text-caption">-</span>
  </div>
</template>

<script setup>
import { defineAsyncComponent, h } from 'vue';
import { useQuasar } from 'quasar';
import { useI18n } from 'vue-i18n';
import { showNotify } from '~/utils/utils';

const props = defineProps({
  item: {
    type: Object,
    required: true,
  },
});

const $q = useQuasar();
const { t } = useI18n();

// 调试开关 - 关闭调试信息
const showDebug = false;

// 检查是否有物流信息
const hasLogistics = computed(() => {
  return props.item.logisticsNo &&
         props.item.logisticsNo !== '' &&
         props.item.logisticsNo !== null &&
         props.item.logisticsNo !== undefined;
});

// 解析物流轨迹数据
const trackList = computed(() => {
  if (!props.item.logisticsTrack) return [];
  
  try {
    const tracks = JSON.parse(props.item.logisticsTrack);
    return Array.isArray(tracks) ? tracks : [];
  } catch (error) {
    console.error('解析物流轨迹数据失败:', error);
    return [];
  }
});



// 查看商品物流信息
function viewItemLogistics() {
  if (!hasLogistics.value) {
    showNotify(t('accountOrderDetail.logistics.noInfo'), 'warning');
    return;
  }

  // 准备物流轨迹数据并按时间倒序排列
  const formattedTrackList = trackList.value
    .map(track => ({
      logisticsTime: track.time,
      logisticsContent: track.content,
    }))
    .sort((a, b) => new Date(b.logisticsTime) - new Date(a.logisticsTime)); // 按时间倒序排列

  // 构建HTML内容
  let htmlContent = `<div class="text-subtitle2 q-mb-md">${props.item.logisticsName} - ${props.item.logisticsNo}</div>`;

  if (formattedTrackList.length > 0) {
    htmlContent += '<div class="logistics-timeline">';
    formattedTrackList.forEach((track, index) => {
      htmlContent += `
        <div class="timeline-item q-mb-md q-pa-sm" style="border-left: 3px solid ${index === 0 ? '#1976d2' : '#ccc'}; padding-left: 12px;">
          <div class="text-weight-medium text-body2">${formatDateTime(track.logisticsTime)}</div>
          <div class="text-grey-7 text-body2">${track.logisticsContent}</div>
        </div>
      `;
    });
    htmlContent += '</div>';
  } else {
    htmlContent += '<div class="text-grey-7">暂无物流信息</div>';
  }

  // 显示物流轨迹信息弹窗
  $q.dialog({
    title: t('accountOrderDetail.logistics.trackTitle'),
    message: htmlContent,
    html: true,
    style: 'min-width: 400px; max-width: 600px;',
    maximized: $q.screen.lt.sm,
    ok: {
      label: t('accountOrderDetail.buttons.close'),
      flat: true,
      color: 'primary',
    },
  });
}

// 格式化日期时间
function formatDateTime(timestamp) {
  if (!timestamp) return '';
  const date = new Date(timestamp);
  return date
    .toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    })
    .replace(/\//g, '-');
}


</script>

<style lang="scss" scoped>
.item-logistics-btn {
  text-align: center;
  min-height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

@media (max-width: 599px) {
  .item-logistics-btn {
    text-align: right;
    justify-content: flex-end;
  }
}
</style>
