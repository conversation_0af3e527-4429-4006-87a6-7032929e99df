const ArticleApi = {
  // 获得blog分页
  getPage: (params) => {
    return useClientGet('/promotion/article/page', {
      params,
    });
  },

  // 获得blog详情
  getDetail: (params) => {
    return useClientGet('/promotion/article/get', {
      params,
    });
  },

  // 增加浏览次数
  incrementViews: (params) => {
    return useClientPut('/promotion/article/add-browse-count', {
      params,
    });
  },
};

export default ArticleApi;
