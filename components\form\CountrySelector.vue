<template>
  <div class="row justify-start items-center q-mt-lg q-pa-md wrap-on-mobile country-section rounded-borders">
    <span class="col-12 col-sm-1 q-mb-sm-none q-mb-sm text-weight-medium">选择收货国家:</span>
    <div class="col-12 col-sm-3 q-ml-md-desktop country-selector">
      <Vue3CountryIntl 
        v-model="selectedCountry" 
        type="country" 
        :searchAble="true" 
        :useChinese="false" 
        :showSelectedText="false" 
        :disableCountry="disableCountry"
      />
    </div>
    <div class="col-12 col-sm-7 q-mt-sm q-mt-sm-none">
      <q-icon name="help_outline" size="20px" class="q-ml-xs cursor-pointer text-primary" />
      <span class="q-ml-sm text-grey-8">请填写/选择将要寄送的国家，平台会根据填写国家，判断您的商品是否可以寄送。</span>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  disableCountry: {
    type: String,
    default: 'us'
  }
});

const emit = defineEmits(['update:modelValue']);

// 使用计算属性实现双向绑定
const selectedCountry = computed({
  get: () => props.modelValue,
  set: (value) => {
    emit('update:modelValue', value);
  }
});
</script>

<style lang="scss" scoped>
// 国家选择区域样式
.country-section {
  background-color: #f5f7fa;
  border: 1px solid #e0e6ed;
}

// 响应式布局样式
@media (max-width: 600px) {
  .wrap-on-mobile {
    flex-wrap: wrap;
  }
  
  .country-selector {
    width: 100%;
    margin-left: 0 !important;
  }
  
  .q-mb-sm {
    margin-bottom: 8px;
  }
  
  .q-mt-sm {
    margin-top: 8px;
  }
}

@media (min-width: 601px) {
  .q-ml-md-desktop {
    margin-left: 16px;
  }
  
  .q-mb-sm-none {
    margin-bottom: 0;
  }
  
  .q-mt-sm-none {
    margin-top: 0;
  }
}
</style>
