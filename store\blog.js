import { defineStore } from 'pinia';
import ArticleApi from '../composables/articleApi';

export const useBlogStore = defineStore({
  id: 'blog',
  state: () => ({
    pages: {}, // 缓存每页数据，键为页码
    totalPages: 0, // 总页数
    pageSize: 2, // 每页大小
  }),
  actions: {
    // 获取分页数据（如果缓存中有则直接返回，否则调用 API）
    async fetchBlogs(page) {
      // 如果数据已缓存，则直接返回
      if (this.pages[page]) {
        return { data: this.pages[page], totalPages: this.totalPages };
      }

      // 调用 API 获取数据
      const response = await ArticleApi.getPage({ categoryId: 4, pageNo: page, pageSize: this.pageSize });
      // 缓存数据
      this.pages[page] = response.data.list;
      this.totalPages = Math.ceil(response.data.total / this.pageSize);
      console.log('totalPages-----------------', this.totalPages);
      return response.data.list;
    },
    async fetchBlogDetail(id) {
      // 遍历缓存的分页数据（对象），查找详情
      for (const pageKey in this.pages) {
        const page = this.pages[pageKey];
        const found = page.find((blog) => blog.id === parseInt(id, 10));
        if (found) {
          return found;
        }
      }

      // 如果缓存中不存在，则调用 API 获取
      const response = await ArticleApi.getDetail(id);
      return response.data;
    },
  },
  getters: {
    getPageSize: (state) => {
      return state.pageSize;
    },
  },
});
