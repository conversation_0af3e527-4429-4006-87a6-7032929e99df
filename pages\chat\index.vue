<template>
  <div class="chat-wrap">
    <!-- 头部导航栏 -->
    <div class="chat-header">
      <div class="header-back" @click="goBack" :title="$t('help.chat.backToHome')">
        <q-icon name="home" size="24px" color="white" />
      </div>
      <div class="header-title">{{ isHumanMode ? (!isReconnecting.value ? $t('help.chat.humanService') : $t('help.chat.reconnecting')) : $t('help.chat.aiService') }}</div>
    </div>

    <!-- 覆盖头部导航栏背景颜色 -->
    <div class="page-bg" :style="{ height: navBarHeight + 'px' }"></div>

    <!-- 机器人模式 - 显示FAQ -->
    <div v-if="!isHumanMode" class="robot-mode">
      <!-- 内容区域 -->
      <div class="content-area">
        <div class="robot-welcome">
          <div class="robot-avatar">
            <q-avatar size="50px">
              <img src="/images/avatar/default.png" :alt="$t('help.chat.robot.avatar')" />
            </q-avatar>
          </div>
          <div class="robot-message">
            <h3>{{ $t('help.chat.robot.welcome') }}</h3>
            <p>{{ $t('help.chat.robot.intro') }}</p>
          </div>
        </div>

        <!-- 热门FAQ列表 -->
        <div class="popular-faqs">
          <h4>{{ $t('help.chat.robot.popularQuestions') }}</h4>
          <q-list bordered separator>
            <q-item v-for="faq in popularFaqs" :key="faq.id" clickable @click="showFaqDetail(faq.id, faq.title)">
              <q-item-section avatar>
                <q-icon name="help_outline" color="primary" />
              </q-item-section>
              <q-item-section>{{ faq.title }}</q-item-section>
              <q-item-section side>
                <q-icon name="chevron_right" color="grey" />
              </q-item-section>
            </q-item>
          </q-list>

          <!-- 分页控件 -->
          <div class="pagination-container q-mt-md">
            <q-pagination v-model="currentPage" :max="totalPages" :max-pages="5" boundary-numbers direction-links @update:model-value="handlePageChange" />
          </div>
        </div>

        <!-- FAQ详情对话框 -->
        <q-dialog v-model="showFaqDialog" persistent>
          <q-card style="width: 90%; max-width: 600px" class="faq-dialog">
            <q-card-section class="row items-center q-py-sm faq-dialog-header">
              <div class="faq-dialog-title">{{ currentFaqTitle }}</div>
              <q-space />
              <q-btn icon="close" flat round dense v-close-popup />
            </q-card-section>

            <q-card-section v-if="faqLoading" class="text-center q-py-md">
              <q-spinner color="primary" size="2em" />
              <div class="q-mt-sm text-body2">{{ $t('help.chat.robot.loading') }}</div>
            </q-card-section>

            <q-card-section v-else class="faq-content-wrapper">
              <div v-html="currentFaqContent" class="faq-html-content"></div>
            </q-card-section>

            <q-card-actions align="center" class="q-py-sm">
              <q-btn :label="$t('help.buttons.close')" color="primary" dense @click="closeFaqDialog()" />
            </q-card-actions>
          </q-card>
        </q-dialog>
      </div>

      <!-- 底部区域 - 窗体底部而非浏览器视图底部 -->
      <div class="footer-container">
        <!-- 转人工客服按钮 -->
        <div class="human-support-button">
          <q-btn color="primary" icon="headset_mic" :label="$t('help.chat.robot.contactHuman')" @click="switchToHumanMode" class="full-width" />
        </div>
      </div>
    </div>

    <!-- 人工客服模式 - 显示聊天界面 -->
    <div v-else class="human-chat-container">
      <!-- 聊天区域 -->
      <div class="chat-content">
        <MessageList ref="messageListRef" />
      </div>

      <!-- 底部输入区域 -->
      <div class="chat-input-area">
        <message-input v-model="chat.msg" @on-tools="onTools" @send-message="onSendMessage"></message-input>
      </div>

      <!-- 表情弹窗 -->
      <tools-popup v-if="chat.toolsMode === 'emoji'" :show-tools="chat.showTools" :tools-mode="chat.toolsMode" @close="handleToolsClose" @on-emoji="onEmoji">
        <!-- 只保留表情功能，移除其他工具 -->
      </tools-popup>

      <!-- 商品订单选择功能已移除 -->
    </div>
  </div>
</template>

<script setup>
import MessageList from './components/messageList.vue';
import { reactive, ref, toRefs, onMounted, onUnmounted, watch, nextTick } from 'vue';
import { useRouter } from 'vue-router';
import ToolsPopup from './components/toolsPopup.vue';
import MessageInput from './components/messageInput.vue';
import { KeFuMessageContentTypeEnum, WebSocketMessageTypeConstants } from './util/constants';
import { useWebSocket } from '~/composables/useWebSocket';
import { useQuasar } from 'quasar';
import KeFuApi from '~/composables/kefuApi';
import { useCookie } from '#app';
import { useHelpStore } from '~/store/help';
import HelpApi from '~/composables/helpApi';
import { useI18n } from 'vue-i18n';

const $q = useQuasar();
const router = useRouter();
const { t } = useI18n();
const navBarHeight = 50; // 导航栏高度
const isUserLoggedIn = ref(false);
const loginRedirectUrl = '/login?redirect=/chat';
const helpStore = useHelpStore();

// 模式控制
const isHumanMode = ref(false);
const helpCategories = ref([]);
const popularFaqs = ref([]);

// 分页控制
const currentPage = ref(1);
const pageSize = ref(2);
const totalItems = ref(0);
const totalPages = computed(() => Math.ceil(totalItems.value / pageSize.value));

// FAQ详情对话框
const showFaqDialog = ref(false);
const currentFaqTitle = ref('');
const currentFaqContent = ref('');
const faqLoading = ref(false);

// 获取帮助中心数据
const fetchHelpData = async (page = 1, size = 6) => {
  try {
    // 获取分类数据
    await helpStore.fetchCategories();
    helpCategories.value = helpStore.getCategories;

    // 获取热门FAQ（带分页）
    const result = await helpStore.fetchFaqs(page, size);
    popularFaqs.value = result.list.map((faq) => ({
      id: faq.id,
      title: faq.title,
      categoryCode: faq.categoryCode || 'uncategorized',
      itemCode: faq.code,
    }));

    // 更新分页信息
    totalItems.value = result.total;
    currentPage.value = page;
    pageSize.value = size;
  } catch (error) {
    console.error(t('help.chat.robot.fetchDataError'), error);
    $q.notify({
      message: t('help.chat.robot.fetchError'),
      color: 'negative',
      position: 'top',
    });
  }
};

// 处理页面变化
const handlePageChange = async (page) => {
  await fetchHelpData(page, pageSize.value);
};

// 显示FAQ详情
const showFaqDetail = async (id, title) => {
  currentFaqTitle.value = title;
  faqLoading.value = true;
  showFaqDialog.value = true;

  try {
    const { code, data } = await HelpApi.get(id);
    if (code === 0 && data) {
      // 处理HTML内容，添加内联样式
      const processedContent = processHtmlContent(data.content);
      currentFaqContent.value = processedContent;

      // 在内容加载后，等待DOM更新，然后手动应用样式
      await nextTick();
      applyStylesToFaqContent();

      // 再次延迟执行，确保样式被应用
      setTimeout(() => {
        applyStylesToFaqContent();
      }, 100);
    } else {
      throw new Error(t('help.chat.robot.fetchDetailError'));
    }
  } finally {
    faqLoading.value = false;
  }
};

// 处理HTML内容，添加内联样式
const processHtmlContent = (htmlContent) => {
  try {
    // 创建一个临时的DOM元素来解析HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = htmlContent;

    // 处理所有h1-h6标题
    const headings = tempDiv.querySelectorAll('h1, h2, h3, h4, h5, h6');
    headings.forEach((heading) => {
      const tag = heading.tagName.toLowerCase();
      const fontSize = tag === 'h1' ? '18px' : tag === 'h2' ? '16px' : '15px';
      const fontWeight = tag === 'h1' || tag === 'h2' ? '600' : '500';

      heading.style.fontSize = fontSize;
      heading.style.fontWeight = fontWeight;
      heading.style.margin = '10px 0 8px';
      heading.style.color = '#222';
      heading.style.lineHeight = '1.3';
      heading.style.fontFamily = 'inherit';

      // 添加自定义类名
      heading.classList.add('faq-heading-override');
    });

    // 处理段落
    const paragraphs = tempDiv.querySelectorAll('p');
    paragraphs.forEach((p) => {
      p.style.margin = '0 0 8px';
      p.style.fontSize = '14px';
      p.style.lineHeight = '1.5';
      p.style.color = '#333';
      p.classList.add('faq-paragraph-override');
    });

    // 返回处理后的HTML内容
    return tempDiv.innerHTML;
  } catch (error) {
    console.error(t('help.chat.robot.processHtmlError'), error);
    return htmlContent; // 出错时返回原始内容
  }
};

// 手动应用样式到FAQ内容
const applyStylesToFaqContent = () => {
  const contentContainer = document.querySelector('.faq-html-content');
  if (!contentContainer) return;

  // 创建一个样式表并添加到文档头部
  const styleId = 'faq-content-styles';
  let styleElement = document.getElementById(styleId);

  // 如果样式表已存在，先移除它
  if (styleElement) {
    styleElement.parentNode.removeChild(styleElement);
  }

  // 创建新的样式表
  styleElement = document.createElement('style');
  styleElement.id = styleId;
  styleElement.innerHTML = `
      /* 强制覆盖Quasar样式 */
      .faq-html-content h1,
      .faq-html-content h2,
      .faq-html-content h3,
      .faq-html-content h4,
      .faq-html-content h5,
      .faq-html-content h6,
      .faq-dialog .faq-html-content h1,
      .faq-dialog .faq-html-content h2,
      .faq-dialog .faq-html-content h3,
      .faq-dialog .faq-html-content h4,
      .faq-dialog .faq-html-content h5,
      .faq-dialog .faq-html-content h6 {
        font-family: inherit !important;
        line-height: 1.3 !important;
        color: #222 !important;
        margin-top: 10px !important;
        margin-bottom: 8px !important;
      }

      .faq-html-content h1,
      .faq-dialog .faq-html-content h1 {
        font-size: 18px !important;
        font-weight: 600 !important;
      }

      .faq-html-content h2,
      .faq-dialog .faq-html-content h2 {
        font-size: 16px !important;
        font-weight: 600 !important;
      }

      .faq-html-content h3,
      .faq-html-content h4,
      .faq-html-content h5,
      .faq-html-content h6,
      .faq-dialog .faq-html-content h3,
      .faq-dialog .faq-html-content h4,
      .faq-dialog .faq-html-content h5,
      .faq-dialog .faq-html-content h6 {
        font-size: 15px !important;
        font-weight: 500 !important;
      }

      .faq-html-content p,
      .faq-dialog .faq-html-content p {
        margin: 0 0 8px !important;
        font-size: 14px !important;
        line-height: 1.5 !important;
        color: #333 !important;
      }

      .faq-html-content ul,
      .faq-html-content ol,
      .faq-dialog .faq-html-content ul,
      .faq-dialog .faq-html-content ol {
        margin: 8px 0 !important;
        padding-left: 20px !important;
      }

      .faq-html-content li,
      .faq-dialog .faq-html-content li {
        margin-bottom: 4px !important;
        font-size: 14px !important;
        line-height: 1.4 !important;
        color: #333 !important;
      }

      .faq-html-content table,
      .faq-dialog .faq-html-content table {
        width: 100% !important;
        border-collapse: collapse !important;
        margin: 10px 0 !important;
        font-size: 13px !important;
      }

      .faq-html-content th,
      .faq-html-content td,
      .faq-dialog .faq-html-content th,
      .faq-dialog .faq-html-content td {
        border: 1px solid #ddd !important;
        padding: 6px 8px !important;
        text-align: left !important;
        font-size: 13px !important;
      }

      .faq-html-content th,
      .faq-dialog .faq-html-content th {
        background-color: #f5f5f5 !important;
        font-weight: 500 !important;
      }

      .faq-html-content tr:nth-child(even),
      .faq-dialog .faq-html-content tr:nth-child(even) {
        background-color: #f9f9f9 !important;
      }

      .faq-html-content a,
      .faq-dialog .faq-html-content a {
        color: var(--q-primary) !important;
        text-decoration: none !important;
      }

      .faq-html-content blockquote,
      .faq-dialog .faq-html-content blockquote {
        margin: 10px 0 !important;
        padding: 8px 12px !important;
        border-left: 3px solid #ddd !important;
        background-color: #f9f9f9 !important;
        color: #555 !important;
        font-size: 14px !important;
      }

      .faq-html-content code,
      .faq-dialog .faq-html-content code {
        font-family: monospace !important;
        background-color: #f5f5f5 !important;
        padding: 2px 4px !important;
        border-radius: 3px !important;
        font-size: 13px !important;
        color: #333 !important;
      }

      /* 覆盖Quasar的文本类 */
      .faq-html-content .text-h1,
      .faq-html-content .text-h2,
      .faq-html-content .text-h3,
      .faq-html-content .text-h4,
      .faq-html-content .text-h5,
      .faq-html-content .text-h6,
      .faq-html-content .text-subtitle1,
      .faq-html-content .text-subtitle2,
      .faq-html-content .text-body1,
      .faq-html-content .text-body2,
      .faq-dialog .faq-html-content .text-h1,
      .faq-dialog .faq-html-content .text-h2,
      .faq-dialog .faq-html-content .text-h3,
      .faq-dialog .faq-html-content .text-h4,
      .faq-dialog .faq-html-content .text-h5,
      .faq-dialog .faq-html-content .text-h6,
      .faq-dialog .faq-html-content .text-subtitle1,
      .faq-dialog .faq-html-content .text-subtitle2,
      .faq-dialog .faq-html-content .text-body1,
      .faq-dialog .faq-html-content .text-body2 {
        font-size: inherit !important;
        line-height: inherit !important;
        font-weight: inherit !important;
        color: inherit !important;
        margin: inherit !important;
      }
    `;

  // 添加样式表到文档头部
  document.head.appendChild(styleElement);

  // 为内容容器添加特定的类名，以便更好地定位样式
  contentContainer.classList.add('faq-content-override');

  // 直接设置内联样式到h2元素
  const h2Elements = contentContainer.querySelectorAll('h2');
  h2Elements.forEach((h2) => {
    // 使用setAttribute方法设置内联样式
    h2.setAttribute(
      'style',
      'font-size: 16px !important; font-weight: 600 !important; margin: 10px 0 8px !important; color: #222 !important; line-height: 1.3 !important; font-family: inherit !important;'
    );
  });

  // 设置MutationObserver监听内容变化
  setupMutationObserver(contentContainer);
};

// 设置MutationObserver监听内容变化
let observer = null;
const setupMutationObserver = (container) => {
  // 如果已经有观察者，先断开连接
  if (observer) {
    observer.disconnect();
  }

  // 创建新的观察者
  observer = new MutationObserver((mutations) => {
    let needsStyleUpdate = false;

    // 检查是否有新元素添加
    mutations.forEach((mutation) => {
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        needsStyleUpdate = true;
      }
    });

    // 如果有新元素，重新应用样式
    if (needsStyleUpdate) {
      applyStylesToFaqContent();
    }
  });

  // 开始观察
  observer.observe(container, {
    childList: true,
    subtree: true,
    attributes: false,
    characterData: false,
  });

  // MutationObserver已设置完成
};

// 关闭FAQ对话框
const closeFaqDialog = () => {
  showFaqDialog.value = false;
};

// 返回主页
const goBack = () => {
  router.push('/'); // 导航到网站主页
};

// 切换到人工客服模式
const switchToHumanMode = async () => {
  showFaqDialog.value = false;
  isHumanMode.value = true;

  // 更新连接状态为连接中
  if (messageListRef.value && typeof messageListRef.value.updateConnectionStatus === 'function') {
    messageListRef.value.updateConnectionStatus('connecting', 2);
  }

  // 确保在切换到人工客服模式后加载历史消息
  await nextTick();
  if (messageListRef.value && typeof messageListRef.value.getMessageList === 'function') {
    await messageListRef.value.getMessageList();
  }
};

// 检查用户是否已登录
onMounted(() => {
  const tokenCookie = useCookie('token');
  isUserLoggedIn.value = !!tokenCookie.value;

  // 如果未登录，显示提示并跳转到登录页面
  if (!isUserLoggedIn.value) {
    $q.notify({
      message: t('help.chat.loginRequired'),
      color: 'warning',
      position: 'top',
      timeout: 3000,
      actions: [
        {
          label: t('help.chat.goToLogin'),
          color: 'white',
          handler: () => {
            router.push(loginRedirectUrl);
          },
        },
      ],
    });

    // 延迟跳转，给用户时间看到提示
    setTimeout(() => {
      router.push(loginRedirectUrl);
    }, 2000);
    return;
  }

  // 获取帮助中心数据（带分页）
  fetchHelpData(currentPage.value, pageSize.value);
});

// 组件卸载时清理MutationObserver
onUnmounted(() => {
  if (observer) {
    observer.disconnect();
    observer = null;
  }
});

// 监听人工客服模式变化，只有在人工模式下才连接WebSocket
watch(
  () => isHumanMode.value,
  (newValue) => {
    if (newValue) {
      // 如果切换到人工客服模式，手动连接WebSocket
      if (typeof reconnect === 'function') {
        reconnect();
      } else {
        console.error(t('help.chat.websocketReconnectError'));
      }
    }
  }
);

// 监听FAQ对话框状态，确保在对话框打开时应用样式
watch(
  () => showFaqDialog.value,
  (newValue) => {
    if (newValue && !faqLoading.value) {
      // 对话框打开且内容已加载完成，应用样式
      nextTick(() => {
        applyStylesToFaqContent();
      });
    }
  }
);

const chat = reactive({
  msg: '',
  scrollInto: '',
  showTools: false,
  toolsMode: '',
  showSelect: false,
  selectMode: '',
});

// 发送消息
async function onSendMessage() {
  if (!chat.msg || !chat.msg.trim()) return;

  // 检查用户是否已登录
  const tokenCookie = useCookie('token');
  if (!tokenCookie.value) {
    $q.notify({
      message: t('help.chat.loginRequired'),
      color: 'warning',
      position: 'top',
      actions: [
        {
          label: t('help.chat.goToLogin'),
          color: 'white',
          handler: () => {
            router.push(loginRedirectUrl);
          },
        },
      ],
    });
    return;
  }

  try {
    // 构建消息数据
    const data = {
      contentType: KeFuMessageContentTypeEnum.TEXT,
      content: chat.msg.trim(),
    };

    // 使用KeFuApi发送消息
    const res = await KeFuApi.sendKefuMessage(data);

    if (res && res.code === 0) {
      // 添加到本地消息列表
      const localMessage = {
        id: Date.now(),
        content: chat.msg.trim(),
        contentType: KeFuMessageContentTypeEnum.TEXT,
        senderType: 1, // 用户发送的消息 (1=用户, 2=客服)
        createTime: new Date().toISOString(),
      };

      await messageListRef.value.refreshMessageList(localMessage);

      // 发送成功后清空消息
      chat.msg = '';

      // 添加成功提示音效
      try {
        const audio = new Audio('/sounds/message-sent.mp3');
        audio.volume = 0.5;
        audio.play().catch((e) => console.log(t('help.chat.audioPlayError'), e));
      } catch (audioError) {
        console.log(t('help.chat.audioPlayError'), audioError);
      }
    } else if (res && res.code === 401) {
      // 未登录或登录已过期
      $q.notify({
        message: t('help.chat.loginRequired'),
        color: 'warning',
        position: 'top',
        actions: [
          {
            label: t('help.chat.goToLogin'),
            color: 'white',
            handler: () => {
              router.push(loginRedirectUrl);
            },
          },
        ],
      });
    } else {
      throw new Error(res?.msg || t('help.chat.sendFailed'));
    }
  } catch (error) {
    console.error(t('help.chat.sendMessageError'), error);

    // 检查是否是未授权错误
    if (error.message && (error.message.includes('未登录') || error.message.includes('未授权') || error.message.includes('token'))) {
      $q.notify({
        message: t('help.chat.loginRequired'),
        color: 'warning',
        position: 'top',
        actions: [
          {
            label: t('help.chat.goToLogin'),
            color: 'white',
            handler: () => {
              router.push(loginRedirectUrl);
            },
          },
        ],
      });
    } else {
      $q.notify({
        message: t('help.chat.robot.loadError'),
        color: 'negative',
        position: 'bottom',
      });
    }
  } finally {
    chat.showTools = false;
  }
}

const messageListRef = ref();

//======================= 聊天工具相关 start =======================

function handleToolsClose() {
  chat.showTools = false;
  chat.toolsMode = '';
}

function onEmoji(item) {
  chat.msg += item.name;
}

// 点击工具栏开关
function onTools(mode) {
  // 检查用户是否已登录
  const tokenCookie = useCookie('token');
  if (!tokenCookie.value) {
    $q.notify({
      message: t('help.chat.loginRequired'),
      color: 'warning',
      position: 'top',
      actions: [
        {
          label: t('help.chat.goToLogin'),
          color: 'white',
          handler: () => {
            router.push(loginRedirectUrl);
          },
        },
      ],
    });
    return;
  }

  // 检查连接状态
  if (isReconnecting.value) {
    $q.notify({
      message: t('help.chat.connectionError'),
      color: 'warning',
      position: 'center',
    });
    return;
  }

  if (!chat.toolsMode || chat.toolsMode === mode) {
    chat.showTools = !chat.showTools;
  }
  chat.toolsMode = mode;
  if (!chat.showTools) {
    chat.toolsMode = '';
  }
}

// 商品和订单选择功能已完全移除

//======================= 聊天工具相关 end =======================
// 监听人工客服模式变化，只有在人工模式下才连接WebSocket
const { options, reconnect } = useWebSocket({
  // 是否需要登录才能连接
  loginRequired: true,

  // 是否自动连接
  autoConnect: false,

  // 未登录处理
  onNotLoggedIn: () => {
    // 用户未登录，无法建立WebSocket连接
    $q.notify({
      message: t('help.chat.loginRequired'),
      color: 'warning',
      position: 'top',
      timeout: 3000,
      actions: [
        {
          label: t('help.chat.goToLogin'),
          color: 'white',
          handler: () => {
            router.push(loginRedirectUrl);
          },
        },
      ],
    });
  },

  // 连接成功
  onConnected: async () => {
    // 更新连接状态
    if (messageListRef.value && typeof messageListRef.value.updateConnectionStatus === 'function') {
      messageListRef.value.updateConnectionStatus('connected', 2);
    }
  },

  // 收到消息
  onMessage: async (data) => {
    // 播放收到消息提示音
    try {
      const audio = new Audio('/sounds/message-received.mp3');
      audio.volume = 0.3;
      audio.play().catch(() => {
        /* 忽略错误 */
      });
    } catch {
      // 忽略音频播放错误
    }

    try {
      // 确保数据是对象
      let parsedData = data;
      if (typeof data === 'string') {
        try {
          parsedData = JSON.parse(data);
        } catch {
          // 如果是纯文本消息，创建一个简单的消息对象
          parsedData = {
            type: WebSocketMessageTypeConstants.KEFU_MESSAGE_TYPE,
            content: {
              content: data,
              contentType: KeFuMessageContentTypeEnum.TEXT,
              senderType: 2, // 客服发送的消息 (1=用户, 2=客服)
              createTime: new Date().toISOString(),
            },
          };
        }
      }

      // 检查消息类型
      const type = parsedData.type;

      // 如果没有类型但有内容，尝试作为消息处理
      if (!type && parsedData.content) {
        const message = {
          id: Date.now(),
          content: parsedData.content,
          contentType: parsedData.contentType || KeFuMessageContentTypeEnum.TEXT,
          senderType: 2, // 客服发送的消息 (1=用户, 2=客服)
          createTime: new Date().toISOString(),
        };
        await messageListRef.value.refreshMessageList(message);
        return;
      }

      // 如果没有类型也没有内容，直接返回
      if (!type) return;

      // 消息类型：KEFU_MESSAGE_TYPE
      if (type === WebSocketMessageTypeConstants.KEFU_MESSAGE_TYPE) {
        let messageContent;

        // 尝试解析content
        try {
          messageContent = typeof parsedData.content === 'string' ? JSON.parse(parsedData.content) : parsedData.content;
        } catch {
          messageContent = {
            id: Date.now(),
            content: parsedData.content,
            contentType: KeFuMessageContentTypeEnum.TEXT,
            senderType: 2, // 客服发送的消息 (1=用户, 2=客服)
            createTime: new Date().toISOString(),
          };
        }

        await messageListRef.value.refreshMessageList(messageContent);

        // 添加通知
        $q.notify({
          message: t('help.chat.newMessage'),
          color: 'info',
          position: 'bottom',
          timeout: 2000,
        });
        return;
      }

      // 消息类型：KEFU_MESSAGE_ADMIN_READ
      if (type === WebSocketMessageTypeConstants.KEFU_MESSAGE_ADMIN_READ) {
        $q.notify({
          message: t('help.chat.messageRead'),
          color: 'info',
          position: 'bottom',
          timeout: 2000,
        });
        return;
      }

      // 其他类型的消息，尝试作为普通消息处理
      const genericMessage = {
        id: Date.now(),
        content: typeof parsedData === 'object' ? JSON.stringify(parsedData) : String(parsedData),
        contentType: KeFuMessageContentTypeEnum.TEXT,
        senderType: 2, // 客服发送的消息 (1=用户, 2=客服)
        createTime: new Date().toISOString(),
      };

      await messageListRef.value.refreshMessageList(genericMessage);
    } catch {
      // 处理失败时，尝试将原始消息作为文本消息处理
      try {
        const fallbackMessage = {
          id: Date.now(),
          content: typeof data === 'string' ? data : JSON.stringify(data),
          contentType: KeFuMessageContentTypeEnum.TEXT,
          senderType: 2, // 客服发送的消息 (1=用户, 2=客服)
          createTime: new Date().toISOString(),
        };

        await messageListRef.value.refreshMessageList(fallbackMessage);
      } catch {
        // 忽略最终错误
      }
    }
  },

  // 连接关闭
  onClosed: () => {
    // 更新连接状态为断开连接
    if (messageListRef.value && typeof messageListRef.value.updateConnectionStatus === 'function') {
      messageListRef.value.updateConnectionStatus('disconnected', 2);
    }
  },

  // 连接错误
  onError: () => {
    // 检查是否是因为未登录导致的错误
    const tokenCookie = useCookie('token');
    if (!tokenCookie.value) {
      $q.notify({
        message: t('help.chat.loginRequired'),
        color: 'warning',
        position: 'top',
        actions: [
          {
            label: t('help.chat.goToLogin'),
            color: 'white',
            handler: () => {
              router.push(loginRedirectUrl);
            },
          },
        ],
      });
    } else {
      $q.notify({
        message: t('help.chat.robot.loadError'),
        color: 'negative',
        position: 'top',
      });
    }
  },
});
const isReconnecting = toRefs(options).isReconnecting; // 重连状态

// 监听重连状态变化
watch(
  () => isReconnecting.value,
  (newValue) => {
    if (newValue && messageListRef.value && typeof messageListRef.value.updateConnectionStatus === 'function') {
      // 更新连接状态为重连中
      messageListRef.value.updateConnectionStatus('reconnecting', 2);
    }
  }
);
</script>

<style scoped lang="scss">
.chat-wrap {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
  background-color: #f5f7fa;

  // PC视图下的样式
  @media (min-width: 768px) {
    max-width: 900px;
    max-height: 700px;
    height: 80vh;
    margin: 50px auto;
    border-radius: 12px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    overflow: hidden;
  }

  // 机器人模式样式
  .robot-mode {
    padding: 15px;
    margin-top: 50px;
    height: calc(100vh - 50px);
    overflow-y: auto;
    background-color: #f5f7fa;
    display: flex;
    flex-direction: column;

    @media (min-width: 768px) {
      height: calc(80vh - 50px);
      max-height: 650px;
    }

    // 内容区域样式
    .content-area {
      flex: 1;
      display: flex;
      flex-direction: column;
      margin-bottom: 20px;
    }

    .robot-welcome {
      display: flex;
      align-items: flex-start;
      margin-bottom: 20px;
      background-color: #fff;
      border-radius: 12px;
      padding: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      border-left: 4px solid var(--q-primary);

      .robot-avatar {
        margin-right: 15px;

        .q-avatar {
          background-color: rgba(25, 118, 210, 0.1);
          border: 2px solid var(--q-primary);
        }
      }

      .robot-message {
        h3 {
          margin: 0 0 8px 0;
          font-size: 16px;
          font-weight: 600;
          color: var(--q-primary);
        }

        p {
          margin: 0;
          color: #666;
          font-size: 14px;
          line-height: 1.5;
        }
      }
    }

    .faq-categories,
    .popular-faqs {
      margin-bottom: 20px;
      background-color: #fff;
      border-radius: 12px;
      padding: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      flex: 1; // 让内容区域自动填充可用空间

      h4 {
        margin-top: 0;
        margin-bottom: 12px;
        color: #333;
        font-size: 15px;
        font-weight: 600;
        display: flex;
        align-items: center;

        &:before {
          content: '';
          display: inline-block;
          width: 4px;
          height: 16px;
          background-color: var(--q-primary);
          margin-right: 8px;
          border-radius: 2px;
        }
      }

      .q-list {
        border-radius: 8px;
        overflow: hidden;
      }

      .faq-category-header {
        font-weight: 500;
        font-size: 14px;
      }

      .faq-items {
        padding-left: 12px;
        background-color: #f9f9f9;
      }

      .q-item {
        min-height: 48px;
        padding: 8px 16px;

        &:hover {
          background-color: rgba(25, 118, 210, 0.05);
        }
      }

      .q-item__section--side {
        padding-right: 0;
      }
    }

    // 分页容器样式
    .pagination-container {
      display: flex;
      justify-content: center;
      margin: 20px 0;

      .q-pagination {
        .q-btn {
          font-weight: 500;

          &.q-btn--active {
            background-color: var(--q-primary);
            color: white;
          }
        }
      }
    }

    // 底部区域 - 相对于窗体而非浏览器视图
    .footer-container {
      position: relative;
      margin-top: auto;
      padding: 16px 0;
      background-color: transparent;
      z-index: 5;

      @media (min-width: 768px) {
        padding: 16px 0;
      }
    }

    .human-support-button {
      padding: 0;

      .q-btn {
        height: 48px;
        font-size: 15px;
        font-weight: 500;
        border-radius: 24px;
        box-shadow: 0 4px 12px rgba(25, 118, 210, 0.25);
        transition: transform 0.2s, box-shadow 0.2s;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 16px rgba(25, 118, 210, 0.3);
        }
      }
    }
  }

  .chat-header {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 50px;
    background: linear-gradient(135deg, var(--q-primary) 0%, #1565c0 100%);
    color: white;
    display: flex;
    align-items: center;
    padding: 0 16px;
    z-index: 10;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

    @media (min-width: 768px) {
      border-radius: 12px 12px 0 0;
    }

    .header-back {
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border-radius: 50%;
      transition: background-color 0.3s;

      &:hover {
        background-color: rgba(255, 255, 255, 0.2);
      }
    }

    .header-title {
      flex: 1;
      font-size: 16px;
      font-weight: 500;
      text-align: center;
      margin-right: 32px; /* 平衡左侧返回按钮 */
      letter-spacing: 0.3px;
    }
  }

  .page-bg {
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    height: 50px;
    background: linear-gradient(135deg, var(--q-primary) 0%, #1565c0 100%);
    z-index: 1;

    @media (min-width: 768px) {
      border-radius: 12px 12px 0 0;
    }
  }

  // 人工客服模式样式
  .human-chat-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    position: relative;

    .chat-content {
      flex: 1;
      overflow: hidden;
      margin-top: 50px; /* 为顶部导航栏留出空间 */
    }

    .chat-input-area {
      position: sticky;
      bottom: 0;
      left: 0;
      right: 0;
      z-index: 10;
      background-color: #fff;
      border-top: 1px solid rgba(0, 0, 0, 0.08);
      box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);

      @media (min-width: 768px) {
        border-radius: 0 0 12px 12px;
      }
    }
  }

  .status {
    position: relative;
    box-sizing: border-box;
    z-index: 3;
    height: 35px;
    padding: 0 15px;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: 500;
    color: var(--q-primary);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  }

  // FAQ弹窗整体样式
  .faq-dialog {
    border-radius: 8px;
    overflow: hidden;

    .faq-dialog-header {
      border-bottom: 1px solid #eee;
    }

    .faq-dialog-title {
      font-size: 16px;
      font-weight: 500;
      line-height: 1.3;
      color: #333;
    }
  }

  // FAQ内容样式
  .faq-content-wrapper {
    padding: 16px 20px;
    flex: 1; // 使用flex布局，让内容区域自动填充剩余空间
    overflow-y: auto;
    overflow-x: hidden; // 防止水平滚动
    scrollbar-width: thin; // Firefox滚动条样式
    scrollbar-color: rgba(0, 0, 0, 0.2) transparent; // Firefox滚动条颜色

    // 确保在移动设备上有合适的高度
    @media (max-width: 599px) {
      max-height: calc(85vh - 120px); // 减去头部和底部的高度
    }

    @media (min-width: 600px) {
      max-height: calc(90vh - 120px); // 减去头部和底部的高度
    }

    // 自定义滚动条样式 (Webkit浏览器)
    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background-color: rgba(0, 0, 0, 0.2);
      border-radius: 3px;

      &:hover {
        background-color: rgba(0, 0, 0, 0.3);
      }
    }

    .faq-html-content {
      font-size: 14px !important;
      line-height: 1.5 !important;
      color: #333 !important;
    }
  }
}
</style>

<style>
/* 全局样式，不使用scoped，确保能覆盖Quasar样式 */
.q-dialog .faq-dialog .faq-html-content h1,
.q-dialog .faq-dialog .faq-html-content h2,
.q-dialog .faq-dialog .faq-html-content h3,
.q-dialog .faq-dialog .faq-html-content h4,
.q-dialog .faq-dialog .faq-html-content h5,
.q-dialog .faq-dialog .faq-html-content h6 {
  font-family: inherit !important;
  line-height: 1.3 !important;
  color: #222 !important;
  margin: 10px 0 8px !important;
}

.q-dialog .faq-dialog .faq-html-content h1 {
  font-size: 18px !important;
  font-weight: 600 !important;
}

.q-dialog .faq-dialog .faq-html-content h2 {
  font-size: 16px !important;
  font-weight: 600 !important;
}

.q-dialog .faq-dialog .faq-html-content h3,
.q-dialog .faq-dialog .faq-html-content h4,
.q-dialog .faq-dialog .faq-html-content h5,
.q-dialog .faq-dialog .faq-html-content h6 {
  font-size: 15px !important;
  font-weight: 500 !important;
}

.q-dialog .faq-dialog .faq-html-content p {
  margin: 0 0 8px !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
  color: #333 !important;
}

/* 覆盖Quasar的文本类 */
.q-dialog .faq-dialog .faq-html-content .text-h1,
.q-dialog .faq-dialog .faq-html-content .text-h2,
.q-dialog .faq-dialog .faq-html-content .text-h3,
.q-dialog .faq-dialog .faq-html-content .text-h4,
.q-dialog .faq-dialog .faq-html-content .text-h5,
.q-dialog .faq-dialog .faq-html-content .text-h6,
.q-dialog .faq-dialog .faq-html-content .text-subtitle1,
.q-dialog .faq-dialog .faq-html-content .text-subtitle2,
.q-dialog .faq-dialog .faq-html-content .text-body1,
.q-dialog .faq-dialog .faq-html-content .text-body2 {
  font-size: inherit !important;
  line-height: inherit !important;
  font-weight: inherit !important;
  color: inherit !important;
  margin: inherit !important;
}

/* 自定义类名的样式 */
.q-dialog .faq-dialog .faq-html-content .faq-heading-override {
  font-family: inherit !important;
  line-height: 1.3 !important;
  color: #222 !important;
}

.q-dialog .faq-dialog .faq-html-content .faq-paragraph-override {
  margin: 0 0 8px !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
  color: #333 !important;
}
</style>
