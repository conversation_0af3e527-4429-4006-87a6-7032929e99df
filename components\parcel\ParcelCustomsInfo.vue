<template>
  <div class="customs-info-section">
    <div class="row items-center q-mb-sm">
      <q-icon name="assignment" color="primary" size="sm" class="q-mr-sm" />
      <div class="text-h6 text-weight-bold">报关信息</div>
    </div>
    <div class="text-caption text-grey-8 q-mb-md">请填写包裹的报关信息，以便海关处理</div>

    <q-card flat bordered class="q-pa-md">
      <div class="row q-col-gutter-md">
        <!-- 申报价值 -->
        <div class="col-12 col-sm-4">
          <q-input
            v-model.number="localValue.declaredValue"
            type="number"
            label="申报价值 (USD)"
            outlined
            dense
            :rules="declareValueRules"
            :min="minDeclareValueUSD"
            :max="maxDeclareValueUSD"
            step="0.01">
            <template #prepend>
              <q-icon name="attach_money" />
            </template>
          </q-input>
          <!-- 申报价值范围提示 -->
          <div v-if="selectedTransportPlan && (selectedTransportPlan.minDeclareValue || selectedTransportPlan.maxDeclareValue)" class="text-caption text-grey-7 q-mt-xs">
            <q-icon name="info" size="xs" class="q-mr-xs" />
            申报价值范围: ${{ minDeclareValueUSD }} - ${{ maxDeclareValueUSD }}
          </div>
        </div>

        <!-- 收件人清关代码 -->
        <div class="col-12 col-sm-8">
          <q-input v-model="localValue.clearanceCode" label="收件人清关代码" outlined dense hint="如有清关代码请填写，没有可留空" />
        </div>

        <!-- 申报内容 -->
        <div class="col-12">
          <q-input v-model="localValue.declareContent" type="textarea" label="申报内容" outlined dense hint="请简要描述包裹内物品，例如：服装、电子产品等" counter maxlength="200" autogrow />
        </div>
      </div>

      <!-- 申报提示 -->
      <div class="text-caption text-grey-8 q-mt-md">
        <q-icon name="info" size="xs" class="q-mr-xs" />
        提示：准确的申报信息有助于包裹顺利通过海关检查，避免延误或退运。
      </div>
    </q-card>
  </div>
</template>

<script setup>
import { ref, watch, computed } from 'vue';

// 定义组件接收的属性
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({
      declaredValue: 0,
      clearanceCode: '',
      declareContent: '',
    }),
  },
  selectedTransportPlan: {
    type: Object,
    default: null,
  },
});

// 定义组件向外发出的事件
const emit = defineEmits(['update:modelValue', 'validation-change']);

// 计算最小和最大申报价值（美分转美元）
const minDeclareValueUSD = computed(() => {
  if (!props.selectedTransportPlan || !props.selectedTransportPlan.minDeclareValue) {
    return 0;
  }
  return (props.selectedTransportPlan.minDeclareValue / 100).toFixed(2);
});

const maxDeclareValueUSD = computed(() => {
  if (!props.selectedTransportPlan || !props.selectedTransportPlan.maxDeclareValue) {
    return 999999;
  }
  return (props.selectedTransportPlan.maxDeclareValue / 100).toFixed(2);
});

// 申报价值验证规则
const declareValueRules = computed(() => [
  (val) => (val !== null && val !== undefined) || '请输入申报价值',
  (val) => val >= 0 || '申报价值不能为负数',
  (val) => {
    if (props.selectedTransportPlan && props.selectedTransportPlan.minDeclareValue) {
      const minValue = parseFloat(minDeclareValueUSD.value);
      return val >= minValue || `申报价值不能低于 $${minValue}`;
    }
    return true;
  },
  (val) => {
    if (props.selectedTransportPlan && props.selectedTransportPlan.maxDeclareValue) {
      const maxValue = parseFloat(maxDeclareValueUSD.value);
      return val <= maxValue || `申报价值不能超过 $${maxValue}`;
    }
    return true;
  },
]);

// 验证状态
const isValid = computed(() => {
  const value = localValue.value.declaredValue;

  // 检查基本验证
  if (value === null || value === undefined || value < 0) {
    return false;
  }

  // 检查范围验证
  if (props.selectedTransportPlan) {
    const minValue = parseFloat(minDeclareValueUSD.value);
    const maxValue = parseFloat(maxDeclareValueUSD.value);

    if (value < minValue || value > maxValue) {
      return false;
    }
  }

  return true;
});

// 本地值，用于双向绑定
const localValue = ref({
  declaredValue: props.modelValue?.declaredValue || 0,
  clearanceCode: props.modelValue?.clearanceCode || '',
  declareContent: props.modelValue?.declareContent || '',
});

// 监听本地值变化，向父组件发送更新事件
watch(
  localValue,
  (newValue) => {
    emit('update:modelValue', {
      declaredValue: newValue.declaredValue || 0,
      clearanceCode: newValue.clearanceCode || '',
      declareContent: newValue.declareContent || '',
    });
  },
  { deep: true }
);

// 监听验证状态变化，通知父组件
watch(
  isValid,
  (newValid) => {
    emit('validation-change', newValid);
  },
  { immediate: true }
);

// 监听父组件传入的值变化，更新本地值
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue) {
      localValue.value = {
        declaredValue: newValue.declaredValue || 0,
        clearanceCode: newValue.clearanceCode || '',
        declareContent: newValue.declareContent || '',
      };
    }
  },
  { deep: true, immediate: true }
);

// 监听物流方案变化，检查并调整申报价值
watch(
  () => props.selectedTransportPlan,
  (newPlan) => {
    if (newPlan && (newPlan.minDeclareValue || newPlan.maxDeclareValue)) {
      const currentValue = localValue.value.declaredValue;
      const minValue = parseFloat(minDeclareValueUSD.value);
      const maxValue = parseFloat(maxDeclareValueUSD.value);

      // 如果当前申报价值超出范围，调整为最小值
      if (currentValue < minValue || currentValue > maxValue) {
        console.log(`申报价值 ${currentValue} 超出范围 [${minValue}, ${maxValue}]，调整为最小值 ${minValue}`);
        localValue.value.declaredValue = minValue;
      }
    }
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
.customs-info-section {
  .q-card {
    border-radius: 8px;
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    }
  }
}
</style>
