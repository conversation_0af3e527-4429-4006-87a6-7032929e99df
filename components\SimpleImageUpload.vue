<template>
  <div class="simple-image-upload">
    <!-- 上传按钮 -->
    <q-btn 
      v-if="imageList.length < maxFiles"
      outline 
      color="primary" 
      icon="cloud_upload" 
      :label="`上传图片 (${imageList.length}/${maxFiles})`"
      @click="triggerFileUpload" 
      class="upload-btn"
      :loading="uploading"
    />

    <!-- 已上传的图片缩略图 -->
    <div v-if="imageList.length > 0" class="uploaded-images">
      <div 
        v-for="(image, index) in imageList" 
        :key="index" 
        class="image-item"
      >
        <q-img
          :src="image.url"
          class="thumbnail"
          fit="cover"
          @click="previewImage(image.url)"
        >
          <template v-slot:loading>
            <div class="absolute-full flex flex-center">
              <q-spinner color="primary" size="1em" />
            </div>
          </template>
          <template v-slot:error>
            <div class="absolute-full bg-grey-3 flex flex-center">
              <q-icon name="broken_image" color="grey-6" size="1.5em" />
            </div>
          </template>
        </q-img>
        
        <!-- 删除按钮 -->
        <q-btn
          round
          dense
          flat
          icon="close"
          color="negative"
          class="remove-btn"
          @click="removeImage(index)"
          size="xs"
        />
      </div>
    </div>

    <!-- 隐藏的文件输入 -->
    <input
      ref="fileInputRef"
      type="file"
      :accept="accept"
      :multiple="multiple"
      style="display: none"
      @change="onFileInputChange"
    />

    <!-- 上传提示 -->
    <div class="upload-hint">
      <q-icon name="info" size="14px" color="grey-6" class="q-mr-xs" />
      <span class="text-caption text-grey-7">
        支持JPG、PNG格式，单张图片不超过{{ maxFileSizeText }}
      </span>
    </div>

    <!-- 图片预览对话框 -->
    <q-dialog v-model="previewDialog" maximized>
      <q-card class="preview-card">
        <q-card-section class="q-pa-none flex flex-center" style="height: 100vh; background: rgba(0,0,0,0.8);">
          <q-img
            :src="previewImageUrl"
            style="max-width: 95vw; max-height: 95vh; object-fit: contain;"
            fit="contain"
            @click="previewDialog = false"
          />
        </q-card-section>
        <q-card-actions class="absolute-top-right q-pa-md">
          <q-btn
            flat
            round
            icon="close"
            color="white"
            size="lg"
            @click="previewDialog = false"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { useQuasar } from 'quasar';
import { uploadFile as uploadFileApi } from '~/composables/fileApi';

// Props
const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  maxFiles: {
    type: Number,
    default: 5
  },
  maxFileSize: {
    type: Number,
    default: 5 * 1024 * 1024 // 5MB
  },
  accept: {
    type: String,
    default: 'image/*'
  },
  multiple: {
    type: Boolean,
    default: true
  }
});

// Emits
const emit = defineEmits(['update:modelValue']);

// Quasar
const $q = useQuasar();

// Refs
const fileInputRef = ref(null);
const imageList = ref([]);
const uploading = ref(false);
const previewDialog = ref(false);
const previewImageUrl = ref('');

// Computed
const maxFileSizeText = computed(() => {
  const size = props.maxFileSize / (1024 * 1024);
  return `${size}MB`;
});

// Watch
watch(() => props.modelValue, (newVal) => {
  if (newVal && Array.isArray(newVal)) {
    const currentUrls = imageList.value.map(item => item.url);
    const newUrls = newVal.filter(url => url && url.trim() !== '');

    if (JSON.stringify(currentUrls) !== JSON.stringify(newUrls)) {
      imageList.value = newUrls.map(url => ({ url }));
    }
  }
}, { immediate: true });

watch(imageList, (newVal) => {
  const urls = newVal.map(item => item.url).filter(url => url && url.trim() !== '');
  const currentModelValue = props.modelValue || [];
  
  if (JSON.stringify(urls) !== JSON.stringify(currentModelValue)) {
    emit('update:modelValue', urls);
  }
}, { deep: true });

// Methods
const validateFile = (file) => {
  if (!file.type.startsWith('image/')) {
    $q.notify({
      color: 'negative',
      message: '只能上传图片文件',
      icon: 'error'
    });
    return false;
  }

  if (file.size > props.maxFileSize) {
    $q.notify({
      color: 'negative',
      message: `文件大小不能超过 ${maxFileSizeText.value}`,
      icon: 'error'
    });
    return false;
  }

  return true;
};

const uploadFile = async (file) => {
  uploading.value = true;
  
  try {
    const response = await uploadFileApi(file);
    
    if (response.code === 0 && response.data) {
      const imageUrl = response.data.trim();
      
      if (imageUrl && (imageUrl.startsWith('http://') || imageUrl.startsWith('https://') || imageUrl.startsWith('/'))) {
        imageList.value.push({ url: imageUrl });
        
        $q.notify({
          color: 'positive',
          message: '图片上传成功',
          icon: 'check'
        });
      } else {
        throw new Error('服务器返回的图片URL无效');
      }
    } else {
      throw new Error(response.msg || '上传失败');
    }
  } catch (error) {
    console.error('上传失败:', error);
    $q.notify({
      color: 'negative',
      message: error.message || '图片上传失败',
      icon: 'error'
    });
  } finally {
    uploading.value = false;
  }
};

const removeImage = (index) => {
  imageList.value.splice(index, 1);
};

const previewImage = (url) => {
  previewImageUrl.value = url;
  previewDialog.value = true;
};

const triggerFileUpload = () => {
  fileInputRef.value?.click();
};

const onFileInputChange = async (event) => {
  const files = Array.from(event.target.files || []);
  
  for (const file of files) {
    if (imageList.value.length >= props.maxFiles) {
      $q.notify({
        color: 'warning',
        message: `最多只能上传 ${props.maxFiles} 张图片`,
        icon: 'warning'
      });
      break;
    }
    
    if (validateFile(file)) {
      await uploadFile(file);
    }
  }
  
  event.target.value = '';
};
</script>

<style scoped>
.simple-image-upload {
  width: 100%;
}

.upload-btn {
  margin-bottom: 15px;
}

.uploaded-images {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 10px;
}

.image-item {
  position: relative;
  width: 80px;
  height: 80px;
}

.thumbnail {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  border: 2px solid #e0e0e0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.thumbnail:hover {
  border-color: #1976d2;
  transform: scale(1.05);
}

.remove-btn {
  position: absolute;
  top: -5px;
  right: -5px;
  background: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.upload-hint {
  display: flex;
  align-items: center;
}

.preview-card {
  background: transparent;
}

.preview-card .q-card__section {
  cursor: pointer;
}
</style>
