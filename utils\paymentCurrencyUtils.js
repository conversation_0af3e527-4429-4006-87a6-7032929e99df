/**
 * 支付相关的货币工具函数
 * 处理支付时的货币转换逻辑
 */

import { exchangeRateConvert } from './moneyUtil';

/**
 * 获取支付时使用的货币和金额
 * 如果当前币种是人民币(CNY)，则转换为美元(USD)进行支付
 * 其他币种则直接使用
 * 
 * @param {number} amount - 金额，单位：分
 * @param {Object} selectedCurrency - 用户选择的货币对象
 * @param {Array} currencyList - 所有可用的货币列表
 * @returns {Object} - 返回用于支付的货币和金额信息
 */
export function getPaymentCurrency(amount, selectedCurrency, currencyList) {
  // 如果选择的是人民币，则转换为美元支付
  if (selectedCurrency.currency === 'CNY') {
    // 查找美元的汇率信息
    const usdCurrency = currencyList.find(c => c.currency === 'USD');
    
    if (!usdCurrency) {
      console.error('未找到美元汇率信息，将使用原始币种支付');
      return {
        currency: selectedCurrency,
        amount: amount,
        isConverted: false
      };
    }
    
    // 计算CNY到USD的汇率
    // 注意：store中存储的汇率是所选语言针对默认语言的汇率
    // 所以需要用 1/usdCurrency.rate 来计算人民币到美元的汇率
    const cnyToUsdRate = 1 / usdCurrency.rate;
    
    // 转换金额：人民币转美元
    const convertedAmount = exchangeRateConvert(amount, cnyToUsdRate);
    
    return {
      currency: usdCurrency,
      amount: convertedAmount,
      isConverted: true,
      originalCurrency: selectedCurrency,
      originalAmount: amount
    };
  }
  
  // 其他币种直接使用
  return {
    currency: selectedCurrency,
    amount: amount,
    isConverted: false
  };
}
