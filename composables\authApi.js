const AuthApi = {
  // 使用邮箱 + 密码登录
  loginEmail: (data, t) => {
    console.log('loginEmail');
    return useClientPost('/member/auth/login-email', {
      body: data,
      custom: {
        showError: true,
        showSuccess: true,
        successMsg: 'login success',
      },
    });
  },
  // 使用邮箱 + 密码注册（支持验证码）
  registerEmail: (data) => {
    console.log('registerEmail', data);
    return useClientPost('/member/auth/register-email', {
      body: data,
      custom: {
        showError: true,
      },
    });
  },

  // 验证是否真人
  validateHuman: (data) => {
    return useClientPost('/member/auth/verify-human', {
      body: data,
      custom: {
        showSuccess: false,
      },
    });
  },

  //验证邮件
  validateEmail: (data) => {
    return useClientPost('/member/email/verify', {
      body: data,
    });
  },

  //重发验证邮件
  resend: (data) => {
    return useClientPost('/member/email/resend', {
      body: data,
    });
  },

  // 登出系统
  logout: () => {
    return useClientPost('/member/auth/logout');
  },

  // 刷新令牌
  refreshToken: (refreshToken) => {
    return useClientPost('/member/auth/refresh-token', {
      params: { refreshToken },
      custom: {
        showLoading: false, // 不用加载中
        showError: false, // 不展示错误提示
      },
    });
  },

  // 发送手机验证码
  sendMailCode: (data) => {
    return useClientPost('/member/auth/send-mail-code', {
      body: data,
    });
  },

  // 社交授权的跳转
  socialAuthRedirect: (type, redirectUri) => {
    return useApiFetch('/member/auth/social-auth-redirect', {
      method: 'GET',
      params: { type, redirectUri },
      custom: {
        showSuccess: true,
        loadingMsg: '登陆中',
      },
    });
  },

  // 社交快捷登录
  socialLogin: (type, code, state) => {
    return useApiFetch('/member/auth/social-login', {
      method: 'POST',
      body: { type, code, state },
      custom: {
        showSuccess: true,
        loadingMsg: '登陆中',
      },
    });
  },

  // 微信小程序的一键登录
  weixinMiniAppLogin: (phoneCode, loginCode, state) => {
    return useApiFetch('/member/auth/weixin-mini-app-login', {
      method: 'POST',
      body: { phoneCode, loginCode, state },
      custom: {
        showSuccess: true,
        loadingMsg: '登陆中',
        successMsg: '登录成功',
      },
    });
  },

  // 创建微信 JS SDK 初始化所需的签名
  createWeixinMpJsapiSignature: (url) => {
    return useApiFetch('/member/auth/create-weixin-jsapi-signature', {
      method: 'POST',
      params: { url },
      custom: {
        showError: false,
        showLoading: false,
      },
    });
  },
};

export default AuthApi;
