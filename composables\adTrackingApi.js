/**
 * 广告追踪API服务
 * 封装所有广告追踪相关的API调用
 */

const AdTrackingApi = {
  /**
   * 创建广告跟踪记录（已注册用户）
   * @param {Object} params - 跟踪参数
   * @returns {Promise} API响应
   */
  createAdTracking: (params) => {
    return useClientPost('/member/tracking/create', {
      body: params,
      custom: {
        showError: false, // 静默处理错误，避免影响用户体验
        showSuccess: false,
        showLoading: false,
      },
    });
  },

  /**
   * 创建访客广告跟踪记录（未注册用户）
   * @param {Object} params - 跟踪参数
   * @returns {Promise} API响应
   */
  createVisitorAdTracking: (params) => {
    return useClientPost('/member/tracking/create-visitor', {
      body: params,
      custom: {
        showError: false, // 静默处理错误
        showSuccess: false,
        showLoading: false,
      },
    });
  },

  /**
   * 关联访客记录到用户
   * @param {string} sessionId - 会话ID
   * @returns {Promise} API响应
   */
  linkVisitorToUser: (sessionId) => {
    return useClientPost('/member/tracking/link-visitor-to-user', {
      params: { sessionId },
      custom: {
        showError: false,
        showSuccess: false,
        showLoading: false,
      },
    });
  },

  /**
   * 记录注册转化
   * @param {string} sessionId - 会话ID
   * @returns {Promise} API响应
   */
  recordRegisterConversion: (sessionId) => {
    const params = sessionId ? { sessionId } : {};
    return useClientPost('/member/tracking/record-register-conversion', {
      params,
      custom: {
        showError: false,
        showSuccess: false,
        showLoading: false,
      },
    });
  },
};

export default AdTrackingApi;
