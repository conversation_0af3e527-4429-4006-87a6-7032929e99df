<template>
  <div class="send-wrap">
    <div class="input-container">
      <q-input v-model="message" dense outlined :placeholder="$t('help.chat.inputPlaceholder')" class="message-input" bg-color="white" :disable="isThrottled" @keyup.enter="sendMessage">
        <!-- 移除加号按钮，保留表情按钮 -->
        <template v-slot:append>
          <q-icon name="sentiment_satisfied_alt" class="cursor-pointer emoji-icon" @click.stop="onTools('emoji')" />
        </template>
      </q-input>
    </div>

    <q-btn v-if="message" class="send-btn" :color="isThrottled ? 'grey-5' : 'primary'" rounded :loading="isThrottled" :disable="isThrottled" no-caps @click="sendMessage">
      <span v-if="!isThrottled">{{ $t('help.chat.send') }}</span>
      <span v-else>{{ throttleCountdown }}s</span>

      <template v-slot:loading>
        <q-spinner-dots size="20px" />
      </template>
    </q-btn>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue';

/**
 * 消息发送组件
 */
const props = defineProps({
  // 消息
  modelValue: {
    type: String,
    default: '',
  },
  // 工具模式
  toolsMode: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['update:modelValue', 'onTools', 'sendMessage']);

const message = computed({
  get() {
    return props.modelValue;
  },
  set(newValue) {
    emits(`update:modelValue`, newValue);
  },
});

// 节流控制
const isThrottled = ref(false);
const throttleCountdown = ref(0);
const THROTTLE_TIME = 3; // 发送消息后的冷却时间（秒）

// 打开工具菜单
function onTools(mode) {
  emits('onTools', mode);
}

// 发送消息
function sendMessage() {
  if (isThrottled.value || !message.value.trim()) return;

  emits('sendMessage');

  // 启动节流
  isThrottled.value = true;
  throttleCountdown.value = THROTTLE_TIME;

  // 倒计时
  const countdownInterval = setInterval(() => {
    throttleCountdown.value--;

    if (throttleCountdown.value <= 0) {
      clearInterval(countdownInterval);
      isThrottled.value = false;
    }
  }, 1000);
}
</script>

<style scoped lang="scss">
.send-wrap {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: #fff;
  border-top: 1px solid rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);

  .input-container {
    flex: 1;
    margin-right: 12px;
    position: relative;

    .message-input {
      border-radius: 24px;
      box-shadow: 0 1px 5px rgba(0, 0, 0, 0.08);
      transition: all 0.3s ease;

      &:focus-within {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
      }

      :deep(.q-field__control) {
        height: 44px;
        padding: 0 5px;
      }

      :deep(.q-field__marginal) {
        height: 44px;
      }
    }

    .emoji-icon {
      color: #666;
      transition: all 0.2s ease;
      padding: 5px;
      border-radius: 50%;

      &:hover {
        color: var(--q-primary);
        background-color: rgba(0, 0, 0, 0.05);
      }
    }
  }

  .tools-icon {
    color: #666;
    margin: 0 5px;
    transform: rotate(0deg);
    transition: all 0.3s ease;
    padding: 5px;
    border-radius: 50%;

    &:hover {
      color: var(--q-primary);
      background-color: rgba(0, 0, 0, 0.05);
    }

    &.is-active {
      transform: rotate(45deg);
      color: var(--q-primary);
    }
  }

  .send-btn {
    min-width: 70px;
    height: 44px;
    margin-left: 8px;
    font-weight: 500;
    border-radius: 22px;
    box-shadow: 0 2px 5px rgba(25, 118, 210, 0.2);
    transition: all 0.3s ease;

    &:not(:disabled):hover {
      box-shadow: 0 4px 8px rgba(25, 118, 210, 0.3);
      transform: translateY(-1px);
    }
  }
}
</style>
