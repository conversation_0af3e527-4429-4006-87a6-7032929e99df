<template>
  <div class="warehouse-btn-wrapper">
    <!-- 仓库地址按钮 -->
    <q-btn color="primary" outline icon="location_on" :label="t('transfer.warehouse.myWarehouseAddress')" dense @click="showDialog = true" class="warehouse-address-btn" />

    <!-- 仓库地址对话框 -->
    <q-dialog v-model="showDialog" persistent>
      <q-card style="width: 650px; max-width: 90vw">
        <q-card-section class="row items-center bg-primary text-white dialog-header">
          <div class="text-h6">{{ t('transfer.warehouse.warehouseAddress') }}</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-card-section class="q-pt-md">
          <div class="text-subtitle2 q-mb-sm">{{ t('transfer.warehouse.selectAddress') }}</div>
          <div class="warehouse-list q-gutter-y-md">
            <div v-for="(warehouse, index) in warehouseList" :key="index" class="warehouse-item">
              <div class="warehouse-header bg-blue-1 q-pa-sm">
                <div class="text-subtitle2 text-primary">{{ warehouse.name }}</div>
              </div>
              <div class="warehouse-content q-pa-md">
                <div class="row q-col-gutter-md">
                  <div class="col-12 col-sm-6">
                    <div class="text-grey-7">{{ t('transfer.warehouse.receiver') }}</div>
                    <div class="q-mb-md">{{ warehouse.receiver }}</div>
                  </div>
                  <div class="col-12 col-sm-6">
                    <div class="text-grey-7">{{ t('transfer.warehouse.phone') }}</div>
                    <div class="q-mb-md">{{ warehouse.phone }}</div>
                  </div>
                </div>
                <div>
                  <div class="text-grey-7">{{ t('transfer.warehouse.address') }}</div>
                  <div>{{ warehouse.address }}</div>
                </div>
                <div class="row justify-end q-mt-md">
                  <q-btn flat color="primary" icon="content_copy" :label="t('transfer.warehouse.copyAddress')" @click="copyAddress(warehouse)" class="copy-btn" />
                </div>
              </div>
            </div>
          </div>
        </q-card-section>

        <q-card-section class="bg-grey-2 text-grey-8 text-caption note-section">
          <q-icon name="info" size="16px" class="q-mr-xs" />
          {{ t('transfer.warehouse.addressNote') }}
        </q-card-section>

        <q-card-actions align="right" class="q-py-sm q-px-md">
          <q-btn unelevated color="primary" :label="t('common.close')" v-close-popup class="close-btn" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { useQuasar } from 'quasar';
import { useI18n } from 'vue-i18n';

const $q = useQuasar();
const { t } = useI18n();
const showDialog = ref(false);

// 仓库地址列表
const warehouseList = ref([
  {
    id: 1,
    name: '华南仓',
    receiver: '转运公司收发部',
    phone: '020-12345678',
    address: '广东省广州市白云区机场路123号转运中心A区3号仓',
  },
  {
    id: 2,
    name: '华东仓',
    receiver: '转运公司收发部',
    phone: '021-87654321',
    address: '上海市浦东新区航头镇航鹤路568号转运中心B区2号仓',
  },
  {
    id: 3,
    name: '华北仓',
    receiver: '转运公司收发部',
    phone: '010-56781234',
    address: '北京市顺义区空港物流园区顺安路88号转运中心C区1号仓',
  },
]);

// 复制仓库地址
function copyAddress(warehouse) {
  const addressText = `${t('transfer.warehouse.receiver')}: ${warehouse.receiver}\n${t('transfer.warehouse.phone')}: ${warehouse.phone}\n${t('transfer.warehouse.address')}: ${warehouse.address}`;

  navigator.clipboard.writeText(addressText).then(
    () => {
      $q.notify({
        type: 'positive',
        message: t('transfer.common.copySuccess'),
        timeout: 1500,
      });
    },
    () => {
      $q.notify({
        type: 'negative',
        message: t('transfer.common.copyFailed'),
      });
    }
  );
}
</script>

<style lang="scss" scoped>
.dialog-header {
  min-height: 50px;
}

.warehouse-list {
  max-height: 60vh;
  overflow-y: auto;
  padding: 0 1px; // 防止边框被裁剪
}

.warehouse-item {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  margin-bottom: 16px;
  overflow: hidden;
  transition: all 0.2s ease;

  &:hover {
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  }
}

.warehouse-header {
  padding: 8px 12px;
}

.warehouse-content {
  padding: 16px;
}

.text-grey-7 {
  font-size: 0.9rem;
  margin-bottom: 4px;
}

.copy-btn {
  text-transform: none;
  font-weight: normal;

  .q-icon {
    font-size: 1.2rem;
  }
}

.note-section {
  display: flex;
  align-items: flex-start;
  padding: 12px 16px;
  font-size: 0.85rem;
  line-height: 1.4;
}

.close-btn {
  min-width: 100px;
  text-transform: none;
}
</style>
