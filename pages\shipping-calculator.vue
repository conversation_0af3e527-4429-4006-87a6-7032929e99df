<template>
  <HeaderSimple />
  <Breadcrumbs :breadcrumbs="breadcrumbs" />

  <div class="shipping-calculator-page">
    <div class="container q-pa-md">
      <!-- 页面标题 -->
      <div class="page-header q-mb-lg">
        <h1 class="text-h4 text-weight-medium q-mb-sm">{{ $t('shippingCalculator.title', '运费计算') }}</h1>
        <p class="text-subtitle1 text-grey-7">{{ $t('shippingCalculator.subtitle', '我们与多家国际物流公司合作，提供最优惠的折扣！') }}</p>
      </div>

      <!-- 计算表单 -->
      <div class="calculator-form-card">
        <q-card class="q-pa-lg">
          <q-form ref="calculatorForm" @submit="calculateShipping" class="q-gutter-md">
            <!-- 目的地和产品类别选择 -->
            <div class="form-section">
              <h3 class="text-h6 q-mb-md">{{ $t('shippingCalculator.basicInfo', '包裹信息') }}</h3>
              <div class="row q-col-gutter-md">
                <div class="col-12 col-md-6">
                  <CountrySelector
                    v-model="formData.destinationCountry"
                    :label="$t('shippingCalculator.selectCountry', '目的地') + ' *'"
                    :error="!!$v.formData.destinationCountry.$error"
                    :error-message="$v.formData.destinationCountry.$errors[0]?.$message" />
                </div>
                <div class="col-12 col-md-6">
                  <CategorySelector
                    v-model="formData.category"
                    :label="$t('shippingCalculator.selectCategory', '产品类别')"
                    :error="!!$v.formData.category.$error"
                    :error-message="$v.formData.category.$errors[0]?.$message" />
                </div>
              </div>
            </div>

            <!-- 包裹信息 -->
            <div class="form-section">
              <!-- <h3 class="text-h6 q-mb-md">{{ $t('shippingCalculator.packageInfo', '包裹信息') }}</h3> -->
              <div class="row q-col-gutter-md">
                <div class="col-12 col-sm-6 col-md-3">
                  <q-input
                    v-model.number="formData.weight"
                    type="number"
                    outlined
                    :label="$t('shippingCalculator.weight', '重量(g)') + ' *'"
                    :placeholder="$t('shippingCalculator.weightPlaceholder', '300')"
                    suffix="g"
                    :error="!!$v.formData.weight.$error"
                    :error-message="$v.formData.weight.$errors[0]?.$message" />
                </div>
                <div class="col-12 col-sm-6 col-md-3">
                  <q-input
                    v-model.number="formData.length"
                    type="number"
                    outlined
                    :label="$t('shippingCalculator.length', '长度(cm)')"
                    :placeholder="$t('shippingCalculator.lengthPlaceholder', '30')"
                    suffix="cm"
                    :error="!!$v.formData.length.$error"
                    :error-message="$v.formData.length.$errors[0]?.$message" />
                </div>
                <div class="col-12 col-sm-6 col-md-3">
                  <q-input
                    v-model.number="formData.width"
                    type="number"
                    outlined
                    :label="$t('shippingCalculator.width', '宽度(cm)')"
                    :placeholder="$t('shippingCalculator.widthPlaceholder', '20')"
                    suffix="cm"
                    :error="!!$v.formData.width.$error"
                    :error-message="$v.formData.width.$errors[0]?.$message" />
                </div>
                <div class="col-12 col-sm-6 col-md-3">
                  <q-input
                    v-model.number="formData.height"
                    type="number"
                    outlined
                    :label="$t('shippingCalculator.height', '高度(cm)')"
                    :placeholder="$t('shippingCalculator.heightPlaceholder', '3')"
                    suffix="cm"
                    :error="!!$v.formData.height.$error"
                    :error-message="$v.formData.height.$errors[0]?.$message" />
                </div>
              </div>
            </div>

            <!-- 计算按钮 -->
            <div class="text-center q-mt-lg">
              <q-btn type="submit" color="primary" size="lg" :loading="calculating" :label="$t('shippingCalculator.calculate', '计算')" class="q-px-xl" />
            </div>
          </q-form>
        </q-card>
      </div>

      <!-- 计算结果 -->
      <ShippingResults v-if="showResults" :results="calculationResults" :loading="calculating" @select-option="handleShippingOptionSelect" class="q-mt-lg" />
    </div>
  </div>

  <Footer />
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import useVuelidate from '@vuelidate/core';
import { required, minValue } from '@vuelidate/validators';
import CountrySelector from '~/components/shipping/CountrySelector.vue';
import CategorySelector from '~/components/shipping/CategorySelector.vue';
import ShippingResults from '~/components/shipping/ShippingResults.vue';
import ShippingApi from '~/composables/shippingApi';
import CategoryApi from '~/composables/categoryApi';

// 设置页面元数据
definePageMeta({
  middleware: 'auth', // 需要登录才能访问
  layout: 'default',
});

const { t } = useI18n();

// 面包屑导航
const breadcrumbs = [{ label: t('shippingCalculator.title', '运费计算'), to: '/shipping-calculator' }];

// 表单数据
const formData = reactive({
  destinationCountry: null,
  category: [],
  weight: null,
  length: null,
  width: null,
  height: null,
});

// 自定义验证器
const categoryValidator = (value) => {
  return value && Array.isArray(value) && value.length > 0;
};

// 表单验证规则
const rules = {
  formData: {
    destinationCountry: { required },
    category: {
      // 分类改为非必填
      categoryValidator: (value) => !value || !Array.isArray(value) || value.length === 0 || categoryValidator(value) || t('validation.categoryRequired', '请至少选择一个商品分类'),
    },
    weight: {
      required,
      minValue: minValue(1),
      maxValue: (value) => !value || value <= 50000 || t('validation.weightMax', '重量不能超过50kg'),
    },
    length: {
      // 长度改为非必填
      minValue: (value) => !value || value >= 1 || t('validation.dimensionMin', '尺寸必须大于0'),
      maxValue: (value) => !value || value <= 200 || t('validation.dimensionMax', '尺寸不能超过200cm'),
    },
    width: {
      // 宽度改为非必填
      minValue: (value) => !value || value >= 1 || t('validation.dimensionMin', '尺寸必须大于0'),
      maxValue: (value) => !value || value <= 200 || t('validation.dimensionMax', '尺寸不能超过200cm'),
    },
    height: {
      // 高度改为非必填
      minValue: (value) => !value || value >= 1 || t('validation.dimensionMin', '尺寸必须大于0'),
      maxValue: (value) => !value || value <= 200 || t('validation.dimensionMax', '尺寸不能超过200cm'),
    },
  },
};

const $v = useVuelidate(rules, { formData });

// 状态管理
const calculating = ref(false);
const showResults = ref(false);
const calculationResults = ref([]);
const calculatorForm = ref(null);
const categoryMap = ref(new Map()); // 分类ID到名称的映射

// 初始化分类映射
const initializeCategoryMap = async () => {
  try {
    const response = await CategoryApi.getList();
    if (response.code === 0 && response.data) {
      const map = new Map();
      response.data.forEach((category) => {
        // 添加主分类
        map.set(category.id.toString(), category.name);
        // 添加子分类
        if (category.children && Array.isArray(category.children)) {
          category.children.forEach((subCategory) => {
            map.set(subCategory.id.toString(), subCategory.name);
          });
        }
      });
      categoryMap.value = map;
    }
  } catch (error) {
    console.error('初始化分类映射失败:', error);
  }
};

// 根据ID获取分类名称
const getCategoryNameById = (id) => {
  return categoryMap.value.get(id.toString()) || `分类${id}`;
};

// 计算运费
const calculateShipping = async () => {
  // 验证表单
  const isValid = await $v.value.$validate();
  if (!isValid) {
    useNuxtApp().$showNotify({
      msg: t('validation.formInvalid', '请检查表单填写是否正确'),
      type: 'negative',
    });
    return;
  }

  calculating.value = true;
  showResults.value = false;

  try {
    // 转换数据格式
    const countryCode = ShippingApi.convertCountryToCode(formData.destinationCountry);
    const categoryIds = ShippingApi.convertCategoriesToIds(formData.category);

    // 构建API请求参数
    const requestParams = {
      countryCode: countryCode,
      weight: formData.weight,
      categoryIds: categoryIds,
    };

    // 添加可选的尺寸参数
    if (formData.length) {
      requestParams.length = formData.length;
    }
    if (formData.width) {
      requestParams.width = formData.width;
    }
    if (formData.height) {
      requestParams.height = formData.height;
    }

    console.log('运费计算请求参数:', requestParams);

    // 调用真实的运费计算API
    const response = await ShippingApi.getQuote(requestParams);

    console.log('运费计算响应:', response);

    // 检查响应格式并提取数据
    let results = [];
    if (response && response.code === 0 && Array.isArray(response.data)) {
      results = response.data;
    } else if (response && Array.isArray(response)) {
      // 如果直接返回数组
      results = response;
    } else {
      console.warn('运费查询响应格式异常:', response);
      results = [];
    }

    console.log('运费计算结果:', results);

    // 转换API响应数据为前端需要的格式
    const transformedResults = transformApiResults(results);
    console.log('转换后的结果:', transformedResults);

    calculationResults.value = transformedResults;
    showResults.value = true;

    if (transformedResults.length > 0) {
      useNuxtApp().$showNotify({
        msg: t('shippingCalculator.calculateSuccess', `运费计算完成，找到 ${transformedResults.length} 个方案`),
        type: 'positive',
      });
    } else {
      useNuxtApp().$showNotify({
        msg: t('shippingCalculator.noResults', '暂无可用的运输方案'),
        type: 'warning',
      });
    }
  } catch (error) {
    console.error('计算运费失败:', error);

    // 项目的错误处理已经在useClientPost中统一处理了
    // 这里只需要处理特殊情况
    if (error && !error.handled) {
      useNuxtApp().$showNotify({
        msg: t('shippingCalculator.calculateError', '运费计算失败，请稍后重试'),
        type: 'negative',
      });
    }
  } finally {
    calculating.value = false;
  }
};

// 转换API响应数据为前端需要的格式
const transformApiResults = (apiResults) => {
  if (!Array.isArray(apiResults) || apiResults.length === 0) {
    return [];
  }

  return (
    apiResults
      // 不再过滤不可用的方案，而是显示它们
      .map((item) => {
        // 基础信息
        const result = {
          id: item.id,
          name: item.name,
          nameEn: item.name, // API返回的name已经是英文，如果需要中文名称需要额外处理
          price: item.feeDetail ? parseFloat(item.feeDetail.total || '0') : 0,
          currency: item.feeDetail?.currency || 'USD',
          estimatedDays: item.transitTime || '未知',
          reliability: item.logisticsTimeliness?.deliveryRate || 0,
          icon: getShippingIcon(item.name), // 根据名称获取图标
          available: item.available !== false,
          unavailableReason: item.unavailableReason,
        };

        // 费用详情
        if (item.feeDetail) {
          result.feeDetail = {
            weight: item.feeDetail.weight,
            volumeWeight: item.feeDetail.volumeWeight,
            chargeableWeight: item.feeDetail.chargeableWeight,
            freight: parseFloat(item.feeDetail.freight || '0'),
            operationFee: parseFloat(item.feeDetail.operationFee || '0'),
            serviceFee: parseFloat(item.feeDetail.serviceFee || '0'),
            customsFee: parseFloat(item.feeDetail.customsFee || '0'),
            fuelFee: parseFloat(item.feeDetail.fuelFee || '0'),
            total: parseFloat(item.feeDetail.total || '0'),
          };
        }

        // 产品特色
        if (item.features) {
          result.features = item.features;
        }

        // 是否包税
        result.taxInclude = item.taxInclude || false;

        // 限制信息
        if (item.restrictions) {
          result.restrictions = item.restrictions;
        }

        // 时效信息
        if (item.logisticsTimeliness) {
          result.timelinessInfo = item.logisticsTimeliness;
        }

        // 品类限制信息 - 从 restrictions.categoryRestrictions 获取
        if (item.restrictions && item.restrictions.categoryRestrictions) {
          try {
            // 解析JSON字符串
            const categoryRestrictions = typeof item.restrictions.categoryRestrictions === 'string' ? JSON.parse(item.restrictions.categoryRestrictions) : item.restrictions.categoryRestrictions;

            // 处理品类限制，将ID转换为可读名称
            result.categoryRestrictions = categoryRestrictions.map((restriction) => ({
              name: getCategoryNameById(restriction.id) || `分类${restriction.id}`,
              allowList: restriction.allowList ? restriction.allowList.map((id) => getCategoryNameById(id)) : [],
              blockList: restriction.blockList ? restriction.blockList.map((id) => getCategoryNameById(id)) : [],
            }));
          } catch (error) {
            console.error('解析品类限制数据失败:', error);
            result.categoryRestrictions = [];
          }
        }

        // 收费标准
        if (item.pricingStandard) {
          result.pricingStandard = item.pricingStandard;
        } else {
          // 添加一些测试数据
          result.pricingStandard = {
            weightRange: '0-30kg',
            firstWeight: '16.57',
            additionalWeight: '4.81',
            maxWeight: '0.00',
            fuelSurcharge: '0.00',
            serviceFee: '2.37',
            remoteAreaFee: '1.58',
          };
        }

        return result;
      })
  );
};

// 根据物流名称获取对应的图标
const getShippingIcon = (name) => {
  if (!name) return 'local_shipping';

  const lowerName = name.toLowerCase();
  if (lowerName.includes('经济') || lowerName.includes('economy')) {
    return 'local_shipping';
  } else if (lowerName.includes('标准') || lowerName.includes('standard')) {
    return 'flight';
  } else if (lowerName.includes('快速') || lowerName.includes('fast') || lowerName.includes('express')) {
    return 'rocket_launch';
  } else if (lowerName.includes('专线') || lowerName.includes('special')) {
    return 'flight_takeoff';
  } else {
    return 'local_shipping';
  }
};

// 处理运输方案选择
const handleShippingOptionSelect = (option) => {
  console.log('选择的运输方案:', option);
  useNuxtApp().$showNotify({
    msg: t('shippingCalculator.optionSelected', `已选择 ${option.name}`),
    type: 'info',
  });
  // 这里可以添加选择后的逻辑，比如保存到购物车或跳转到下单页面
};

// 页面初始化
onMounted(() => {
  initializeCategoryMap();
});
</script>

<style lang="scss" scoped>
.shipping-calculator-page {
  min-height: calc(100vh - 200px);
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 300px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    opacity: 0.1;
    z-index: 0;
  }
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.page-header {
  text-align: center;
  padding: 2rem 0 1.5rem;

  h1 {
    color: #1976d2;
    margin-bottom: 0.5rem;
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    font-size: 2rem;
  }

  p {
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
    font-size: 0.95rem;
  }
}

.calculator-form-card {
  .q-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    background: white;
    border: 1px solid #e0e0e0;
  }
}

.form-section {
  margin-bottom: 1.5rem;

  h3 {
    color: #2c3e50;
    border-bottom: 2px solid #3498db;
    padding-bottom: 0.5rem;
    margin-bottom: 1rem;
    font-weight: 600;
    font-size: 1.1rem;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: -2px;
      left: 0;
      width: 50px;
      height: 2px;
      background: linear-gradient(90deg, #3498db, #2980b9);
      border-radius: 2px;
    }
  }
}

// 输入框样式
:deep(.q-field) {
  .q-field__control {
    border-radius: 4px;
  }
}

// 按钮样式
:deep(.q-btn) {
  border-radius: 4px;
  font-weight: 500;
  text-transform: none;
}

// 响应式设计
@media (max-width: 1024px) {
  .container {
    padding: 1rem;
  }
}

@media (max-width: 768px) {
  .shipping-calculator-page {
    &::before {
      height: 150px;
    }
  }

  .page-header {
    padding: 1.5rem 0 1rem;

    h1 {
      font-size: 1.6rem;
    }

    p {
      font-size: 0.85rem;
    }
  }

  .calculator-form-card {
    .q-card {
      border-radius: 12px;
      margin: 0 0.5rem;
      padding: 1rem !important;
    }
  }

  .form-section {
    margin-bottom: 1.2rem;

    h3 {
      font-size: 1rem;
      margin-bottom: 0.8rem;
    }
  }

  // 移动端表单布局优化
  .row.q-col-gutter-md {
    .col-md-6 {
      margin-bottom: 1rem;
    }
  }
}

@media (max-width: 480px) {
  .page-header {
    padding: 1.5rem 0 1rem;

    h1 {
      font-size: 1.5rem;
    }
  }

  .calculator-form-card {
    .q-card {
      margin: 0;
      border-radius: 8px;
    }
  }

  // 移动端表单布局优化
  :deep(.row.q-col-gutter-md) {
    .col-md-3 {
      margin-bottom: 1rem;
    }
  }
}

// 移除动画效果

// 加载状态样式
.q-btn--loading {
  .q-btn__content {
    opacity: 0.6;
  }
}

// 错误状态样式
:deep(.q-field--error) {
  .q-field__control {
    border-color: #e74c3c !important;
    box-shadow: 0 0 0 2px rgba(231, 76, 60, 0.2) !important;
  }
}

// 成功状态样式
:deep(.q-field--success) {
  .q-field__control {
    border-color: #27ae60 !important;
    box-shadow: 0 0 0 2px rgba(39, 174, 96, 0.2) !important;
  }
}
</style>
