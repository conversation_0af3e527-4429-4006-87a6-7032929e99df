import { defineStore } from 'pinia';
import { useAuthStore } from './auth';
import Wishlist<PERSON>pi from '../composables/wishlistApi';
export const useWishlistStore = defineStore({
  id: 'wishlist',
  state: () => {
    return {
      list: [],
    };
  },
  actions: {
    // 获取心愿单列表
    async getList() {
      if (useAuthStore().isLogin) {
        const response = await WishlistApi.getList();
        if (response.code === 0) {
          //合并心愿单
          const mergedData = await this.mergeAndSync(this.list, response.data.validList);
          this.list = mergedData; // 确保 this.list 是一个数组
        }
      }
    },
    addToWishlist(payload) {
      const wishlistItems = this.list.find((item) => item.spu.id === payload.id);
      if (wishlistItems) {
      } else {
        this.list.push(payload);
        // this.list.push({
        //   id:0,
        //   spu:{
        //     id:payload.id,
        //     name:payload.title,
        //     picUrl:payload.images[0].src,
        //     categoryId:payload.category,
        //     price:payload.price
        //   }
        // })
      }
      if (useAuthStore().isLogin) {
        WishlistApi.addWishlist({ spuId: payload.spu.id });
      }
    },
    removeFromWishlist(productId) {
      console.log('移除商品', productId);
      const item = this.list.find((item) => item.spu.id === productId);
      this.list = this.list.filter((item) => item.spu.id !== productId);
      if (useAuthStore().isLogin) {
        WishlistApi.deleteWishlist(item.spu.id);
      }
    },

    //合并心愿单
    async mergeAndSync(localList, serverList) {
      // 确保 `localList` 是一个数组
      localList = Array.isArray(localList) ? localList : [];
      const mergedList = [];
      const localOnlyList = [];

      // 创建 Map，快速查找服务端商品
      const serverMap = new Map(
        serverList.map((item) => [item.spu.id, item]) // 以 `spu.id` 为键
      );

      // 遍历本地购物车，合并与服务器的数据
      for (const localItem of localList) {
        const serverItem = serverMap.get(localItem.spu.id);

        if (serverItem) {
          mergedList.push(serverItem);
          // 从服务端 Map 中移除已经处理的商品
          serverMap.delete(localItem.spu.id);
        } else {
          // 如果仅本地存在，加入 `localOnlyList`
          localOnlyList.push(localItem);
        }
      }

      // 将服务端存在但本地不存在的商品加入合并列表
      for (const serverItem of serverMap.values()) {
        mergedList.push(serverItem);
      }

      // 循环调用 `addToServer` 方法，将本地存在但服务端不存在的商品同步到服务器
      for (const localOnlyItem of localOnlyList) {
        await WishlistApi.addWishlist({
          spuId: localOnlyItem.spu.id,
        });
      }
      // 返回合并后的列表
      return mergedList;
    },
    clearWishlist() {
      this.list = [];
    },

    setInitialWhishlist(payload) {
      this.list = payload;
    },
    removeWishlistItem(payload) {
      const index = this.list.indexOf(payload);
      this.list.splice(index, 1);
    },
  },
  getters: {
    wishlistItems: (state) => {
      return state.list;
    },
  },
  persist: process.client && {
    storage: localStorage,
    paths: ['wishlist'],
  },
});
