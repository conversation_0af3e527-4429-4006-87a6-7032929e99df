<template>
  <Header />
  <Breadcrumbs :breadcrumbs="breadcrumbs" />
  <div class="pay-page">
    <!-- 购物流程组件 - 更紧凑的设计 -->
    <div class="shopping-progress bg-white rounded-borders q-pa-sm">
      <div class="row justify-between items-center no-wrap">
        <div class="page-title text-primary q-ml-xs">{{ $t('payment.title') }}</div>
        <!-- PC端直接显示流程 -->
        <div class="col-auto desktop-progress" v-if="!$q.screen.lt.sm">
          <ModernStepProgress currentStep="1" class="compact-progress" v-if="paySource == PayOrderSources.ORDER" />
          <ParcelStepProgress currentStep="1" class="compact-progress" v-if="paySource == PayOrderSources.PARCEL" />
        </div>
        <!-- 移动端使用折叠按钮 -->
        <q-btn v-else flat dense round size="sm" :icon="showFullProgress ? 'expand_less' : 'expand_more'" @click="showFullProgress = !showFullProgress" color="primary" />
      </div>

      <!-- 移动端可折叠的流程指示器 -->
      <q-slide-transition>
        <div v-if="$q.screen.lt.sm && showFullProgress" class="q-mt-xs mobile-progress-container">
          <ModernStepProgress currentStep="1" class="compact-progress" />
        </div>
      </q-slide-transition>
    </div>
    <!-- 结果展示 - 更紧凑的设计 -->
    <div class="text-center order-result-container">
      <div v-if="state.payStatus === 1" class="order-result-content">
        <div class="row justify-center items-center q-mb-sm">
          <q-icon name="check_circle" size="40px" color="green" class="q-mr-sm" />
          <div class="order-status">{{ $t('payment.orderStatus.submitted') }}</div>
        </div>
        <div class="order-amount-container">
          <div class="order-amount-box">
            <span class="order-amount-label">{{ $t('payment.amount') }}</span>
            <span class="currency-text" v-html="formatAmount(state.orderInfo.orderPrice, { allowWrap: false })"></span>
          </div>
        </div>
        <div class="order-time-text text-grey-6 q-mt-sm">{{ payDescText }}</div>
      </div>
      <div v-else-if="state.payStatus === 2" class="order-result-content">
        <div class="row justify-center items-center">
          <q-icon name="check_circle" size="40px" color="green" class="q-mr-sm" />
          <div class="order-status">{{ payDescText }}</div>
        </div>
      </div>
      <div v-else class="order-result-content">
        <div class="row justify-center items-center">
          <q-icon name="cancel" size="40px" color="red" class="q-mr-sm" />
          <div class="order-status">{{ payDescText }}</div>
        </div>
      </div>
    </div>

    <!-- 支付方式选择 -->
    <div v-if="state.payStatus === 1" class="row justify-center">
      <div class="payment-container">
        <PaymentChannelSelector :amount="state.orderInfo.price" :orderPrice="state.orderInfo.orderPrice" :enableChannels="enablePayMethods" v-model="state.payment" />
        <q-separator class="q-mt-xl" />
      </div>
    </div>

    <!-- 支付按钮 -->
    <div v-if="state.payStatus === 1" class="row justify-center">
      <div class="payment-container text-right q-mb-xl">
        <q-btn :label="$t('payment.payButton')" color="primary" size="lg" @click="onPay" icon="bolt" unelevated :disable="isPayButtonDisabled" class="pay-button" />
        <div v-if="insufficientBalance" class="text-negative q-mt-sm">{{ $t('payment.insufficientBalance') }}</div>
      </div>
    </div>
    <!-- 操作按钮 -->
    <div v-if="state.payStatus === 2" class="row justify-center items-center q-my-xl q-col-gutt">
      <q-btn class="q-mx-lg q-px-lg" dense color="primary" size="lg" @click="navigateTo('/')">{{ $t('payment.navigation.backToHome') }}</q-btn>
      <q-btn class="q-mx-lg q-px-lg" dense color="primary" size="lg" @click="navigateTo('/account/orders')">{{ $t('payment.navigation.viewOrder') }}</q-btn>
    </div>
    <!-- 模态窗口 -->
    <q-dialog v-model="showPaymentDialog" persistent transition-show="scale" transition-hide="scale">
      <q-card class="payment-dialog-card">
        <q-card-section class="payment-dialog-header">
          <div class="payment-processing-title">
            <q-spinner color="primary" size="32px" class="q-mr-md" />
            <span>{{ $t('payment.paymentDialog.processing') }}</span>
          </div>
        </q-card-section>
        <q-separator />
        <q-card-section class="payment-dialog-content">
          <p class="payment-dialog-message">
            {{ $t('payment.paymentDialog.message') || '请在新打开的窗口中完成支付，支付完成后请点击下方按钮' }}
          </p>
        </q-card-section>
        <q-card-actions align="center" class="payment-dialog-actions">
          <q-btn :label="$t('payment.paymentDialog.completed')" color="primary" @click="checkPaymentStatus" class="payment-dialog-btn" unelevated rounded icon="check_circle" />
          <q-btn :label="$t('payment.paymentDialog.reselect')" color="negative" @click="handlePaymentIssue" class="payment-dialog-btn" unelevated rounded icon="refresh" />
        </q-card-actions>
        <q-separator />
        <q-card-section class="payment-dialog-footer">
          <q-btn :label="$t('payment.paymentDialog.abandon')" flat color="grey-7" class="payment-dialog-link-btn" icon="close" dense />
          <q-btn :label="$t('payment.paymentDialog.problem')" flat color="grey-7" class="payment-dialog-link-btn" icon="help_outline" dense />
        </q-card-section>
      </q-card>
    </q-dialog>
  </div>
  <Footer />
</template>

<script setup>
import ModernStepProgress from '~/components/widgets/ModernStepProgress';
import ParcelStepProgress from '~/components/widgets/ParcelStepProgress';
import PayApi from '~/composables/payApi';
import { useUserStore } from '~/store/user';
import { useCurrency } from '~/composables/useCurrency';
import { useCurrencyStore } from '~/store/currency';
import { useI18n } from 'vue-i18n';
import { useQuasar } from 'quasar';
import WalletPaymentConfirmDialog from '~/components/payment/WalletPaymentConfirmDialog';
import { useRoute } from 'vue-router';

const route = useRoute();
const userStore = useUserStore();
const currencyStore = useCurrencyStore();
const { formatAmount } = useCurrency();
const { t } = useI18n();
const $q = useQuasar();

const breadcrumbs = [{ label: t('payment.title'), to: '/pay' }];

// const payOrderId = sessionStorage.getItem('payOrderId');
const payOrderId = route.query.pId;
if (!payOrderId) {
  navigateTo('/error');
}
const paySource = route.query.ps;

const userWallet = computed(() => userStore.userWallet);

// 检查余额是否不足
const insufficientBalance = computed(() => {
  // 如果选择了余额支付，并且余额小于订单金额
  return state.payment === 'wallet' && userWallet.value.balance < state.orderInfo.price;
});

// 支付按钮是否禁用
const isPayButtonDisabled = computed(() => {
  // 如果没有选择支付渠道，或者选择了余额支付但余额不足，则禁用按钮
  return !state.payment || insufficientBalance.value;
});

const showPaymentDialog = ref(false); // 控制模态弹窗
const showFullProgress = ref(false); // 控制移动端流程显示

const returnUrl = globalConfig.payReturnUrl;

let paymentWindow = null; // 支付新窗口

let pollingInterval = null; // 保存轮询定时器
const maxAttempts = 10; // 最大查询次数
const pollingCount = ref(0); // 当前查询次数
const isPolling = ref(false); // 是否正在轮询
const isSuccess = ref(false); // 查询是否成功

// 检测支付环境
const state = reactive({
  orderType: 'goods', // 订单类型; goods - 商品订单, recharge - 充值订单
  orderInfo: {}, // 支付单信息
  payStatus: 0, // 0=检测支付环境, -2=未查询到支付单信息， -1=支付已过期， 1=待支付，2=订单已支付
  payMethods: [], // 可选的支付方式
  payment: '', // 选中的支付方式
});

const enablePayMethods = ref([]);

onMounted(async () => {
  // 获取订单详情
  if (payOrderId) {
    await loadOrder();

    //待支付
    if (state.payStatus === 1) {
      // 获取可用的支付方式
      enablePayMethods.value = await getEnablePayChannels();
      //获取用户钱包余额
      await userStore.getWallet();

      // 默认选中第一个支付渠道
      if (enablePayMethods.value && enablePayMethods.value.length > 0) {
        state.payment = enablePayMethods.value[0].code;
      }
    }
  }
});

const loadOrder = async () => {
  const { code, data } = await PayApi.getOrder(payOrderId, true);
  if (code !== 0 || !data) {
    state.payStatus = -2;
    return;
  }
  state.orderInfo = data;
  // 设置支付状态
  setPayStatus();
};

// 支付文案提示
const payDescText = computed(() => {
  if (state.payStatus === 2) {
    return t('payment.orderStatus.paid');
  }
  if (state.payStatus === 1) {
    const time = useDurationTime(state.orderInfo.expireTime);
    return t('payment.orderStatus.remainingTime', { h: time.h, m: time.m, s: time.s });
  }
  if (state.payStatus === -1) {
    return t('payment.orderStatus.expired');
  }
  if (state.payStatus === -2) {
    return t('payment.orderStatus.notFound');
  }
  return '';
});

// 监听订单过期时间并处理状态更新
watch(
  () => {
    // 检查 expireTime 是否有效
    if (!state.orderInfo || !state.orderInfo.expireTime) {
      console.warn('订单过期时间未定义或无效:', state.orderInfo?.expireTime);
      return 0; // 返回一个默认值，避免 useDurationTime 抛出错误
    }
    return useDurationTime(state.orderInfo.expireTime).ms;
  },
  (ms) => {
    if (ms <= 0 && state.payStatus === 1) {
      state.payStatus = -1; // 更新为订单已过期状态
    }
  },
  { immediate: true } // 添加 immediate 以确保初始状态被检查
);

const onPay = async () => {
  if (state.payment === '') {
    useNuxtApp().$showNotify({ msg: t('payment.notification.selectPayment'), type: 'negative' });
    return;
  }
  //分情况支付
  if (state.payment === 'wallet') {
    payByWallet();
  } else {
    payByChannel(state.payment);
  }
};

//渠道支付
async function payByChannel(channelCode) {
  // 提前打开一个空白页面，避免浏览器拦截
  paymentWindow = window.open('/loading', '_blank');
  if (!paymentWindow) {
    useNuxtApp().$showNotify({
      msg: t('payment.notification.popupBlocked'),
      type: 'negative',
    });
    return;
  }
  showPaymentDialog.value = true; // 显示模态框

  try {
    const { code, data } = await PayApi.submitOrder({
      id: state.orderInfo.id,
      channelCode: channelCode,
      returnUrl: returnUrl + '?t=1',
      cancelUrl: returnUrl + '?t=2',
      currency: currencyStore.selectedCurrency.currency,
    });

    if (code !== 0) {
      paymentWindow.close(); // 请求失败，关闭窗口
      useNuxtApp().$showNotify({ msg: t('payment.notification.paymentFailed'), type: 'negative' });
      return;
    }

    // 添加事件监听器
    window.addEventListener('message', handleMessage);
    // 在新窗口跳转到支付页面
    paymentWindow.location.href = data.displayContent;
  } catch (_error) {
    useNuxtApp().$showNotify({ msg: t('payment.notification.paymentError'), type: 'negative' });
  } finally {
    // showPaymentDialog.value = false;
  }
}

//钱包支付
function payByWallet() {
  // 检查余额是否足够
  if (insufficientBalance.value) {
    useNuxtApp().$showNotify({
      msg: t('payment.insufficientBalance'),
      type: 'negative',
    });
    return;
  }

  // 使用自定义对话框组件
  const walletDialog = $q.dialog({
    component: WalletPaymentConfirmDialog,
    componentProps: {
      amount: formatAmount(state.orderInfo.orderPrice, { useHtml: true }),
      balance: formatAmount(userWallet.value.balance, { useHtml: true }),
      onConfirm: async (data) => {
        console.log('开始钱包支付');
        try {
          const { code, msg } = await PayApi.submitOrder({
            id: state.orderInfo.id,
            channelCode: 'wallet',
            payPassword: data.payPassword,
          });
          console.log('支付结果', code, msg);
          if (code === 0) {
            // 支付成功，关闭弹窗并跳转
            data.closeDialog();
            goPayResult(state.orderInfo.id, state.orderType);
          } else {
            // 支付失败，清空密码但不关闭弹窗
            data.clearPassword();
            useNuxtApp().$showNotify({
              msg: msg || '支付失败，请重试',
              type: 'negative',
            });
          }
        } catch (error) {
          // 网络错误，清空密码但不关闭弹窗
          data.clearPassword();
          console.error('钱包支付失败', error);
          useNuxtApp().$showNotify({
            msg: '支付失败，请重试',
            type: 'negative',
          });
        }
      }
    },
  })
    .onCancel(() => {
      // 用户取消支付，不做任何操作
      console.log('用户取消了余额支付');
    });
}

// 定义监听器回调函数
const handleMessage = async (event) => {
  // 判断事件类型
  if (event.data === 'payment-completed') {
    await loadOrder(); // 重新获取支付状态的
    if (paymentWindow) {
      paymentWindow.close(); // 关闭支付窗口，仅在支付完成时关闭
    }
    showPaymentDialog.value = false;
    // 移除监听器（防止重复监听）
    window.removeEventListener('message', handleMessage);
    //轮询订单支付状态
    startPolling();
  } else if (event.data === 'payment-cancelled') {
    if (paymentWindow) {
      paymentWindow.close(); // 关闭支付窗口，仅在支付取消时关闭
    }
    showPaymentDialog.value = false;
    // 移除监听器（防止重复监听）
    window.removeEventListener('message', handleMessage);
  } else {
    // console.log('未知事件类型：', event.data);
    // 如果是未知事件，选择忽略或添加日志，而不是直接关闭窗口
    return;
  }

  // 移除监听器（防止重复监听）
  window.removeEventListener('message', handleMessage);
};

function checkPaymentStatus() {
  showPaymentDialog.value = false;
  //轮询订单状态
  startPolling();
}
function handlePaymentIssue() {
  showPaymentDialog.value = false;
}

// 获得支付方式
async function getEnablePayChannels() {
  const { code, data } = await PayApi.getEnablePayChannelList(state.orderInfo.appId);
  if (code === 0) {
    return data;
  }
}

// 状态转换：payOrder.status => payStatus
function setPayStatus() {
  if (state.orderInfo.status === 10 || state.orderInfo.status === 20) {
    // 支付成功
    state.payStatus = 2;
    // 跳转回支付成功页
    goPayResult(state.orderInfo.id, state.orderType);
    return;
  }
  if (state.orderInfo.status === 30) {
    // 支付关闭
    state.payStatus = -1;
    return;
  }
  state.payStatus = 1; // 待支付
}

function goPayResult(id, orderType, resultType) {
  sessionStorage.setItem('resultParams', JSON.stringify({ id, orderType, resultType }));
  navigateTo('/pay/result');
}

// 开始轮询
const startPolling = async () => {
  if (isPolling.value) return; // 避免重复启动轮询

  console.log('开始轮询...');
  isPolling.value = true;
  pollingCount.value = 0;

  pollingInterval = setInterval(async () => {
    console.log('轮询中');
    if (pollingCount.value >= maxAttempts || isSuccess.value) {
      stopPolling(); // 停止轮询
      return;
    }
    pollingCount.value += 1;

    await loadOrder();
    if (state.payStatus !== 1) {
      isSuccess.value = true;
      console.log('支付完成！');
      stopPolling(); // 成功后停止轮询
    }
  }, 3000); // 每3秒执行一次
};

// 停止轮询
const stopPolling = () => {
  console.log('停止轮询...');
  if (pollingInterval) {
    clearInterval(pollingInterval);
    pollingInterval = null;
  }
  isPolling.value = false;
};

// 在组件销毁时清理资源
onUnmounted(() => {
  stopPolling(); // 停止轮询
  window.removeEventListener('message', handleMessage);
  if (paymentWindow) {
    paymentWindow.close();
  }
});

// 在组件销毁时清理资源
onUnmounted(() => {
  window.removeEventListener('message', handleMessage);
  if (paymentWindow) {
    paymentWindow.close();
  }
});
</script>

<style lang="scss" scoped>
.pay-page {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;

  @media (max-width: 599px) {
    padding: 0 8px;
  }
}

.payment-container {
  width: 100%;
  max-width: 800px;
  padding: 20px;
  background-color: #f9f9f9;

  @media (max-width: 599px) {
    padding: 12px;
  }
}

.border-color {
  border: 1px solid #1976d2;
}

// 金额列样式 - 使用全局样式，只添加特定的覆盖
.amount-column {
  display: inline-block;
  min-width: 140px;
  text-align: center;
  font-size: 1.3rem; // 增大字体

  @media (max-width: 599px) {
    min-width: 100px;
    font-size: 1.2rem;
  }

  .primary-currency {
    white-space: nowrap;
    display: inline-block;

    :deep(.default-currency) {
      font-size: 0.85em;
      color: #666;
      opacity: 0.85;
      font-weight: normal;
      display: inline-block;
      margin-left: 4px;
      white-space: normal;
    }
  }
}

// 订单提交成功页面的金额文字样式
.order-amount-container {
  .currency-text {
    font-size: 1.5rem; // 增大字体
    font-weight: 600; // 加粗

    @media (max-width: 599px) {
      font-size: 1.3rem;
    }

    .primary-currency {
      font-size: 1.1em;

      :deep(.default-currency) {
        font-size: 0.7em;
        margin-left: 6px;
      }
    }
  }
}

// 支付按钮响应式样式
.pay-button {
  @media (max-width: 599px) {
    width: 100%;
  }
}

// 响应式辅助类
@media (max-width: 599px) {
  .q-my-xl {
    margin-top: 16px !important;
    margin-bottom: 16px !important;
  }
}

// 页面标题样式
.page-title {
  font-size: 22px;
  font-weight: 600;

  @media (max-width: 599px) {
    font-size: 18px;
  }
}

// 订单状态文字样式
.order-status {
  font-size: 20px;
  font-weight: 500;
  color: #333;

  @media (max-width: 599px) {
    font-size: 16px;
  }
}

.order-amount-container {
  text-align: center;
  margin: 10px 0;
}

.order-amount-box {
  display: inline-block;
  padding: 10px 20px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  background-color: #fff;
  font-size: 1.4rem; /* 增大字体大小 */

  @media (max-width: 599px) {
    padding: 8px 16px;
    font-size: 1.2rem; /* 移动端稍小一些 */
  }
}

.order-amount-label {
  font-size: 20px;
  font-weight: 500;
  color: #333;
  margin-right: 10px;

  @media (max-width: 599px) {
    font-size: 16px;
    margin-right: 8px;
  }
}

.order-time-text {
  font-size: 14px;

  @media (max-width: 599px) {
    font-size: 13px;
  }
}

// 购物流程组件样式
.shopping-progress {
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
  margin-bottom: 12px;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }

  .desktop-progress {
    max-width: 70%;
    overflow: hidden;
  }

  .mobile-progress-container {
    display: flex;
    justify-content: center;
    width: 100%;
  }

  .compact-progress {
    transform: scale(0.9);
    transform-origin: center center;
    margin: 0 auto;

    @media (max-width: 599px) {
      transform: scale(0.85);
      width: 100%;
      max-width: 360px;
    }
  }
}

// 订单结果容器样式
.order-result-container {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 16px 12px;
  margin: 12px 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

  @media (max-width: 599px) {
    padding: 12px 8px;
    margin: 8px 0;
  }
}

.order-result-content {
  max-width: 600px;
  margin: 0 auto;
}

// 支付对话框样式
.payment-dialog-card {
  width: 400px;
  max-width: 90vw;
  border-radius: 12px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
  overflow: hidden;

  @media (max-width: 599px) {
    width: 90vw;
  }
}

.payment-dialog-header {
  padding: 20px;
  background-color: #f5f7fa;

  @media (max-width: 599px) {
    padding: 16px;
  }
}

.payment-processing-title {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: 500;
  color: #1976d2;

  @media (max-width: 599px) {
    font-size: 18px;
  }
}

.payment-dialog-content {
  padding: 24px 20px;

  @media (max-width: 599px) {
    padding: 16px;
  }
}

.payment-dialog-message {
  text-align: center;
  margin: 0;
  color: #666;
  font-size: 15px;
  line-height: 1.5;
}

.payment-dialog-actions {
  padding: 0 20px 20px;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 16px;

  @media (max-width: 599px) {
    padding: 0 16px 16px;
    gap: 12px;
  }
}

.payment-dialog-btn {
  min-width: 160px;
  height: 44px;
  font-size: 15px;
  font-weight: 500;

  @media (max-width: 599px) {
    min-width: 140px;
    height: 40px;
    font-size: 14px;
  }
}

.payment-dialog-footer {
  padding: 12px 20px;
  display: flex;
  justify-content: center;
  gap: 24px;
  background-color: #f5f7fa;

  @media (max-width: 599px) {
    padding: 10px 16px;
    gap: 16px;
  }
}

.payment-dialog-link-btn {
  font-size: 13px;
  min-height: 32px;

  @media (max-width: 599px) {
    font-size: 12px;
  }
}
</style>
