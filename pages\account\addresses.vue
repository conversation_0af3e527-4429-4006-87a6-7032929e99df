<template>
  <div class="address-page q-pa-md">
    <!-- 标题区域 -->
    <div class="bg-grey-2 q-px-md q-py-sm q-mb-md">
      <div class="row items-center justify-between">
        <div class="row items-center">
          <q-icon name="location_on" size="xs" color="primary" class="q-mr-xs" />
          <span class="text-subtitle1 text-weight-medium">地址管理</span>
        </div>
        <q-btn color="primary" flat dense icon="add" label="新增地址" @click="openAddressDialog()" />
      </div>
    </div>

    <div class="row q-col-gutter-md">
      <div v-for="address in addresses" :key="address.id" class="col-12 col-md-6 col-lg-4">
        <q-card class="address-card">
          <q-card-section>
            <div class="row items-center justify-between q-mb-sm">
              <div class="text-subtitle1 text-weight-medium">
                <q-icon name="home" size="xs" color="primary" class="q-mr-xs" v-if="address.title === '家'" />
                <q-icon name="business" size="xs" color="primary" class="q-mr-xs" v-else-if="address.title === '公司'" />
                <q-icon name="place" size="xs" color="primary" class="q-mr-xs" v-else />
                {{ address.title || address.name }}
              </div>
              <div>
                <q-badge v-if="address.defaultStatus" color="primary" class="q-mr-sm">默认</q-badge>
              </div>
            </div>

            <div class="address-info q-mb-sm">
              <div class="row items-center q-mb-xs">
                <q-icon name="person" size="xs" color="grey-7" class="q-mr-xs" />
                <span>{{ address.name }}</span>
              </div>
              <div class="row items-center q-mb-xs">
                <q-icon name="phone" size="xs" color="grey-7" class="q-mr-xs" />
                <span>{{ address.phoneCode }} {{ address.mobile }}</span>
              </div>
              <div class="row items-start q-mb-xs">
                <q-icon name="home" size="xs" color="grey-7" class="q-mr-xs q-mt-xs" />
                <div class="address-detail">
                  <div>{{ address.detailAddress }}</div>
                  <div>{{ address.cityName || getCityName(address.city) }}, {{ address.stateName || getStateName(address.state) }}</div>
                  <div>{{ address.postCode }}, {{ getCountryName(address.countryCode) }}</div>
                </div>
              </div>
            </div>
          </q-card-section>

          <q-separator />

          <q-card-actions align="right">
            <q-btn flat color="primary" dense icon="edit" label="编辑" @click="openAddressDialog(address.id)" />
            <q-btn flat color="negative" dense icon="delete" label="删除" @click="confirmDelete(address)" />
          </q-card-actions>
        </q-card>
      </div>
    </div>

    <!-- 地址表单对话框 -->
    <AddressFormDialog ref="addressFormDialog" @confirm="handleAddressConfirm" />

    <!-- 删除确认对话框 -->
    <q-dialog v-model="deleteDialog" persistent>
      <q-card>
        <q-card-section class="row items-center">
          <q-avatar icon="warning" color="negative" text-color="white" />
          <span class="q-ml-sm">确认删除此地址？</span>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="取消" color="grey-7" v-close-popup />
          <q-btn flat label="确认" color="negative" @click="deleteAddress" :loading="deleting" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useQuasar } from 'quasar';
import AddressApi from '~/composables/addressApi';
import AddressFormDialog from '~/components/account/AddressFormDialog.vue';

import { useI18n } from 'vue-i18n';
const { t } = useI18n();
const $q = useQuasar();

// 地址列表
const addresses = ref([]);
const loading = ref(false);

// 地址表单对话框
const addressFormDialog = ref(null);

// 删除确认对话框
const deleteDialog = ref(false);
const addressToDelete = ref(null);
const deleting = ref(false);

// 国家、州/省、城市数据缓存
const countriesCache = ref([]);
const statesCache = ref({});
const citiesCache = ref({});

// 初始化
onMounted(async () => {
  await loadAddresses();
  await loadCountries();
});

// 加载地址列表
async function loadAddresses() {
  loading.value = true;
  try {
    const { code, data } = await AddressApi.getAddressList();
    if (code === 0 && data) {
      addresses.value = data;
    }
  } catch (error) {
    console.error('获取地址列表失败:', error);
    $q.notify({
      color: 'negative',
      message: '获取地址列表失败',
      icon: 'error',
    });
  } finally {
    loading.value = false;
  }
}

// 加载国家列表
async function loadCountries() {
  try {
    const { code, data } = await AddressApi.getCountry();
    if (code === 0 && data) {
      countriesCache.value = data;
    }
  } catch (error) {
    console.error('获取国家列表失败:', error);
  }
}

// 打开地址表单对话框
function openAddressDialog(id = null) {
  addressFormDialog.value.openModal(id);
}

// 处理地址确认
function handleAddressConfirm(address) {
  // 如果是编辑现有地址
  const index = addresses.value.findIndex((a) => a.id === address.id);
  if (index !== -1) {
    addresses.value[index] = address;
  } else {
    // 如果是新增地址
    addresses.value.push(address);
  }

  // 如果设为默认地址，更新其他地址的默认状态
  if (address.defaultStatus) {
    addresses.value.forEach((a) => {
      if (a.id !== address.id) {
        a.defaultStatus = false;
      }
    });
  }
}

// 确认删除
function confirmDelete(address) {
  addressToDelete.value = address;
  deleteDialog.value = true;
}

// 删除地址
async function deleteAddress() {
  if (!addressToDelete.value) return;

  deleting.value = true;
  try {
    const { code } = await AddressApi.deleteAddress(addressToDelete.value.id);
    if (code === 0) {
      // 从列表中移除
      addresses.value = addresses.value.filter((a) => a.id !== addressToDelete.value.id);

      $q.notify({
        color: 'positive',
        message: '地址删除成功',
        icon: 'check_circle',
      });
    }
  } catch (error) {
    console.error('删除地址失败:', error);
    $q.notify({
      color: 'negative',
      message: '删除地址失败',
      icon: 'error',
    });
  } finally {
    deleting.value = false;
    addressToDelete.value = null;
  }
}

// 获取国家名称
function getCountryName(countryCode) {
  const country = countriesCache.value.find((c) => c.iso2 === countryCode);
  return country ? country.name : countryCode;
}

// 获取州/省名称
function getStateName(stateId) {
  if (!stateId) return '';

  // 遍历所有州/省缓存
  for (const countryId in statesCache.value) {
    const state = statesCache.value[countryId].find((s) => s.id === stateId);
    if (state) return state.name;
  }

  return stateId;
}

// 获取城市名称
function getCityName(cityId) {
  if (!cityId) return '';

  // 遍历所有城市缓存
  for (const stateId in citiesCache.value) {
    const city = citiesCache.value[stateId].find((c) => c.id === cityId);
    if (city) return city.name;
  }

  return cityId;
}
</script>

<style lang="scss" scoped>
.address-page {
  .address-card {
    height: 100%;
    transition: all 0.2s ease;
    border-radius: 8px;

    &:hover {
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .q-card-section {
      padding: 16px;
    }

    .address-info {
      font-size: 0.9rem;
      line-height: 1.4;

      .address-detail {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .q-card-actions {
      padding: 8px;
    }
  }

  // 移动端适配
  @media (max-width: 599px) {
    .address-card {
      .q-card-section {
        padding: 12px;
      }

      .q-card-actions {
        padding: 4px 8px;
      }
    }
  }
}
</style>
