# 广告跟踪功能前端接口文档

## 概述

广告跟踪功能用于跟踪用户从广告渠道访问网站的行为，支持 UTM 参数、Google Ads、Facebook Ads 等多种广告平台的跟踪，并记录用户的转化行为（注册、订单等）。

## 功能特点

- **完整访客跟踪**：从访客阶段就开始记录广告数据，无需等待用户注册
- **两阶段跟踪机制**：
  - 阶段 1：访客阶段 - 立即创建数据库记录（无用户 ID）
  - 阶段 2：用户关联阶段 - 注册/登录后关联用户 ID
- **多平台支持**：UTM 参数、Google Ads (gclid)、Facebook Ads (fbclid)
- **转化跟踪**：注册转化、订单转化、自定义转化
- **会话关联**：支持通过会话 ID 关联访客和用户行为
- **完整数据分析**：可分析所有点击广告的访客数据，包括未注册用户

## API 接口

### 1. 创建广告跟踪记录

**接口地址：** `POST /member/ad-tracking/create`

**请求参数：**

```json
{
  "userId": 1024,
  "sessionId": "abc123",
  "utmSource": "google",
  "utmMedium": "cpc",
  "utmCampaign": "summer_sale",
  "utmTerm": "代购",
  "utmContent": "banner_top",
  "gclid": "Cj0KCQjw...",
  "fbclid": "IwAR0...",
  "landingPage": "https://example.com/products",
  "referrer": "https://google.com",
  "userAgent": "Mozilla/5.0...",
  "ipAddress": "***********"
}
```

**响应结果：**

```json
{
  "code": 0,
  "data": 1024,
  "msg": "操作成功"
}
```

### 3. 获取用户广告跟踪记录列表

**接口地址：** `GET /member/ad-tracking/list-by-user`

**响应结果：**

```json
{
  "code": 0,
  "data": [
    {
      "id": 1024,
      "userId": 1024,
      "sessionId": "abc123",
      "utmSource": "google",
      "utmMedium": "cpc",
      "utmCampaign": "summer_sale",
      "utmTerm": "代购",
      "utmContent": "banner_top",
      "gclid": "Cj0KCQjw...",
      "fbclid": null,
      "landingPage": "https://example.com/products",
      "referrer": "https://google.com",
      "userAgent": "Mozilla/5.0...",
      "ipAddress": "***********",
      "isConverted": true,
      "conversionType": "REGISTER",
      "conversionValue": null,
      "conversionTime": "2024-01-01T10:00:00",
      "createTime": "2024-01-01T09:00:00"
    }
  ],
  "msg": "操作成功"
}
```

### 4. 根据会话 ID 获取广告跟踪记录

**接口地址：** `GET /member/ad-tracking/get-by-session?sessionId=abc123`

### 5. 记录注册转化

**接口地址：** `POST /member/ad-tracking/record-register-conversion?sessionId=abc123`

### 6. 分页查询广告跟踪记录

**接口地址：** `GET /member/ad-tracking/page`

**请求参数：**

```json
{
  "pageNo": 1,
  "pageSize": 10,
  "utmSource": "google",
  "utmCampaign": "summer_sale",
  "isConverted": true,
  "createTime": ["2024-01-01 00:00:00", "2024-01-31 23:59:59"]
}
```

### 7. 创建访客广告跟踪记录

**接口地址：** `POST /member/ad-tracking/create-visitor`

**说明：** 用于访客阶段（用户未注册/登录）创建广告跟踪记录

**请求参数：**

```json
{
  "sessionId": "abc123",
  "utmSource": "google",
  "utmMedium": "cpc",
  "utmCampaign": "summer_sale",
  "utmTerm": "代购",
  "utmContent": "banner_top",
  "gclid": "Cj0KCQjw...",
  "landingPage": "https://example.com/products",
  "referrer": "https://google.com",
  "userAgent": "Mozilla/5.0..."
}
```

### 8. 关联访客记录到用户

**接口地址：** `POST /member/ad-tracking/link-visitor-to-user?sessionId=abc123`

**说明：** 用户注册/登录后，将访客记录关联到用户账户

## Nuxt3/Vue3 集成示例

### 1. 创建广告跟踪工具类

```typescript
// utils/adTrackingUtils.ts
export interface AdTrackingParams {
  utm_source?: string;
  utm_medium?: string;
  utm_campaign?: string;
  utm_term?: string;
  utm_content?: string;
  gclid?: string;
  fbclid?: string;
}

export class AdTrackingUtil {
  private static readonly STORAGE_KEY = 'ad_tracking_params';
  private static readonly SESSION_KEY = 'ad_tracking_session';

  /**
   * 从URL参数中提取广告跟踪参数
   */
  static extractParamsFromUrl(): AdTrackingParams {
    if (process.server) return {};

    const urlParams = new URLSearchParams(window.location.search);
    const params: AdTrackingParams = {};

    // UTM参数
    if (urlParams.get('utm_source')) params.utm_source = urlParams.get('utm_source')!;
    if (urlParams.get('utm_medium')) params.utm_medium = urlParams.get('utm_medium')!;
    if (urlParams.get('utm_campaign')) params.utm_campaign = urlParams.get('utm_campaign')!;
    if (urlParams.get('utm_term')) params.utm_term = urlParams.get('utm_term')!;
    if (urlParams.get('utm_content')) params.utm_content = urlParams.get('utm_content')!;

    // 平台特定参数
    if (urlParams.get('gclid')) params.gclid = urlParams.get('gclid')!;
    if (urlParams.get('fbclid')) params.fbclid = urlParams.get('fbclid')!;

    return params;
  }

  /**
   * 保存广告跟踪参数到Cookie
   */
  static saveParams(params: AdTrackingParams) {
    if (process.server || Object.keys(params).length === 0) return;

    const cookie = useCookie(this.STORAGE_KEY, {
      maxAge: 30 * 24 * 60 * 60, // 30天
      sameSite: 'lax',
    });
    cookie.value = JSON.stringify(params);
  }

  /**
   * 从Cookie获取广告跟踪参数
   */
  static getParams(): AdTrackingParams {
    if (process.server) return {};

    const cookie = useCookie(this.STORAGE_KEY);
    try {
      return cookie.value ? JSON.parse(cookie.value as string) : {};
    } catch {
      return {};
    }
  }

  /**
   * 生成会话ID
   */
  static generateSessionId(): string {
    return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * 获取或创建会话ID
   */
  static getSessionId(): string {
    if (process.server) return '';

    const cookie = useCookie(this.SESSION_KEY, {
      maxAge: 24 * 60 * 60, // 24小时
      sameSite: 'lax',
    });

    if (!cookie.value) {
      cookie.value = this.generateSessionId();
    }

    return cookie.value as string;
  }

  /**
   * 清除广告跟踪数据
   */
  static clearParams() {
    if (process.server) return;

    const paramsCookie = useCookie(this.STORAGE_KEY);
    const sessionCookie = useCookie(this.SESSION_KEY);

    paramsCookie.value = null;
    sessionCookie.value = null;
  }
}
```

### 2. 创建 API 服务

```typescript
// composables/useAdTracking.ts
export const useAdTracking = () => {
  const { $api } = useNuxtApp();

  /**
   * 创建广告跟踪记录（已注册用户）
   */
  const createAdTracking = async (params: {
    userId: number;
    sessionId?: string;
    utmSource?: string;
    utmMedium?: string;
    utmCampaign?: string;
    utmTerm?: string;
    utmContent?: string;
    gclid?: string;
    fbclid?: string;
    landingPage?: string;
    referrer?: string;
    userAgent?: string;
  }) => {
    return await $api.post('/member/ad-tracking/create', params);
  };

  /**
   * 创建访客广告跟踪记录（未注册用户）
   */
  const createVisitorAdTracking = async (params: {
    sessionId: string;
    utmSource?: string;
    utmMedium?: string;
    utmCampaign?: string;
    utmTerm?: string;
    utmContent?: string;
    gclid?: string;
    fbclid?: string;
    landingPage?: string;
    referrer?: string;
    userAgent?: string;
  }) => {
    return await $api.post('/member/ad-tracking/create-visitor', params);
  };

  /**
   * 关联访客记录到用户
   */
  const linkVisitorToUser = async (sessionId: string) => {
    return await $api.post('/member/ad-tracking/link-visitor-to-user', null, { params: { sessionId } });
  };

  /**
   * 记录注册转化
   */
  const recordRegisterConversion = async (sessionId?: string) => {
    const params = sessionId ? { sessionId } : {};
    return await $api.post('/member/ad-tracking/record-register-conversion', null, { params });
  };
};
```

### 3. 页面集成示例

```vue
<!-- pages/index.vue -->
<template>
  <div>
    <!-- 页面内容 -->
  </div>
</template>

<script setup>
import { AdTrackingUtil } from '~/utils/adTrackingUtils';

const { createVisitorAdTracking } = useAdTracking();

// 页面加载时处理广告跟踪
onMounted(async () => {
  // 提取URL中的广告参数
  const adParams = AdTrackingUtil.extractParamsFromUrl();

  // 如果有广告参数，立即创建访客跟踪记录
  if (Object.keys(adParams).length > 0) {
    try {
      const sessionId = AdTrackingUtil.getSessionId();

      // 创建访客跟踪记录
      await createVisitorAdTracking({
        sessionId,
        ...adParams,
        landingPage: window.location.href,
        referrer: document.referrer,
        userAgent: navigator.userAgent,
      });

      console.log('访客广告跟踪记录已创建:', adParams);
    } catch (error) {
      console.error('创建访客跟踪记录失败:', error);
    }
  }
});
</script>
```

### 4. 用户注册/登录后的处理

```vue
<!-- pages/auth/register.vue -->
<script setup>
import { AdTrackingUtil } from '~/utils/adTrackingUtils';

const { linkVisitorToUser, recordRegisterConversion } = useAdTracking();

// 注册成功后的处理
const handleRegisterSuccess = async (userId: number) => {
  try {
    const sessionId = AdTrackingUtil.getSessionId();

    // 关联访客记录到用户（如果存在访客记录）
    if (sessionId) {
      const linkSuccess = await linkVisitorToUser(sessionId);

      if (linkSuccess) {
        // 记录注册转化
        await recordRegisterConversion(sessionId);
        console.log('访客记录已关联到用户，注册转化已记录');
      }
    }
  } catch (error) {
    console.error('广告跟踪处理失败:', error);
  }
};

// 登录成功后的处理
const handleLoginSuccess = async (userId: number) => {
  try {
    const sessionId = AdTrackingUtil.getSessionId();

    // 关联访客记录到用户（如果存在访客记录）
    if (sessionId) {
      await linkVisitorToUser(sessionId);
      console.log('访客记录已关联到用户');
    }
  } catch (error) {
    console.error('广告跟踪处理失败:', error);
  }
};
</script>
```

### 5. 订单转化跟踪

```typescript
// 在订单支付成功后调用
const handleOrderSuccess = async (orderId: number, orderValue: number) => {
  try {
    // 后端会自动处理订单转化记录
    // 前端只需要确保用户已登录即可
    console.log('订单转化将由后端自动处理');
  } catch (error) {
    console.error('订单转化处理失败:', error);
  }
};
```

## 注意事项

1. **隐私合规**：确保符合 GDPR、CCPA 等隐私法规要求
2. **Cookie 策略**：设置合适的 Cookie 过期时间和 SameSite 属性
3. **错误处理**：妥善处理 API 调用失败的情况
4. **性能优化**：避免在每个页面都执行广告跟踪逻辑
5. **数据清理**：定期清理过期的广告跟踪数据

## 高级集成示例

### 1. 全局插件集成

```typescript
// plugins/adTracking.client.ts
export default defineNuxtPlugin(() => {
  // 页面路由变化时检查广告参数
  const router = useRouter();

  router.afterEach((to) => {
    // 只在首次访问时处理广告参数
    if (to.query.utm_source || to.query.gclid || to.query.fbclid) {
      const adParams = AdTrackingUtil.extractParamsFromUrl();
      AdTrackingUtil.saveParams(adParams);
    }
  });
});
```

### 2. 中间件集成

```typescript
// middleware/adTracking.global.ts
export default defineNuxtRouteMiddleware((to) => {
  // 服务端渲染时跳过
  if (process.server) return;

  // 检查是否有广告参数
  const hasAdParams = to.query.utm_source || to.query.gclid || to.query.fbclid;

  if (hasAdParams) {
    // 提取并保存广告参数
    const adParams = AdTrackingUtil.extractParamsFromUrl();
    AdTrackingUtil.saveParams(adParams);

    // 清理URL中的广告参数（可选）
    const cleanQuery = { ...to.query };
    delete cleanQuery.utm_source;
    delete cleanQuery.utm_medium;
    delete cleanQuery.utm_campaign;
    delete cleanQuery.utm_term;
    delete cleanQuery.utm_content;
    delete cleanQuery.gclid;
    delete cleanQuery.fbclid;

    // 重定向到清理后的URL
    if (Object.keys(cleanQuery).length !== Object.keys(to.query).length) {
      return navigateTo(
        {
          path: to.path,
          query: cleanQuery,
        },
        { replace: true }
      );
    }
  }
});
```

### 3. Pinia 状态管理集成

```typescript
// stores/adTracking.ts
export const useAdTrackingStore = defineStore('adTracking', () => {
  const trackingData = ref<AdTrackingParams>({});
  const sessionId = ref<string>('');
  const isTracked = ref(false);

  // 初始化广告跟踪数据
  const initTracking = () => {
    trackingData.value = AdTrackingUtil.getParams();
    sessionId.value = AdTrackingUtil.getSessionId();
    isTracked.value = Object.keys(trackingData.value).length > 0;
  };

  // 保存广告跟踪数据
  const saveTracking = (params: AdTrackingParams) => {
    trackingData.value = params;
    AdTrackingUtil.saveParams(params);
    isTracked.value = true;
  };

  // 清除广告跟踪数据
  const clearTracking = () => {
    trackingData.value = {};
    sessionId.value = '';
    isTracked.value = false;
    AdTrackingUtil.clearParams();
  };

  return {
    trackingData: readonly(trackingData),
    sessionId: readonly(sessionId),
    isTracked: readonly(isTracked),
    initTracking,
    saveTracking,
    clearTracking,
  };
});
```

### 4. 组合式函数完整版

```typescript
// composables/useAdTrackingComplete.ts
export const useAdTrackingComplete = () => {
  const { $api } = useNuxtApp();
  const user = useAuthUser(); // 假设有用户认证composable

  /**
   * 处理页面访问的广告跟踪
   */
  const handlePageVisit = async () => {
    // 提取URL参数
    const adParams = AdTrackingUtil.extractParamsFromUrl();

    if (Object.keys(adParams).length > 0) {
      // 保存到Cookie
      AdTrackingUtil.saveParams(adParams);

      // 如果用户已登录，立即创建跟踪记录
      if (user.value?.id) {
        await createTrackingRecord(user.value.id, adParams);
      }
    }
  };

  /**
   * 创建跟踪记录
   */
  const createTrackingRecord = async (userId: number, adParams?: AdTrackingParams) => {
    const params = adParams || AdTrackingUtil.getParams();

    if (Object.keys(params).length === 0) return;

    try {
      await $api.post('/member/ad-tracking/create', {
        userId,
        sessionId: AdTrackingUtil.getSessionId(),
        ...params,
        landingPage: window.location.href,
        referrer: document.referrer,
        userAgent: navigator.userAgent,
      });
    } catch (error) {
      console.error('创建广告跟踪记录失败:', error);
    }
  };

  /**
   * 处理用户登录后的广告跟踪
   */
  const handleUserLogin = async (userId: number) => {
    const adParams = AdTrackingUtil.getParams();

    if (Object.keys(adParams).length > 0) {
      await createTrackingRecord(userId, adParams);
    }
  };

  /**
   * 处理用户注册转化
   */
  const handleRegisterConversion = async () => {
    if (!user.value?.id) return;

    try {
      const sessionId = AdTrackingUtil.getSessionId();
      await $api.post('/member/ad-tracking/record-register-conversion', null, {
        params: { sessionId },
      });
    } catch (error) {
      console.error('记录注册转化失败:', error);
    }
  };

  return {
    handlePageVisit,
    createTrackingRecord,
    handleUserLogin,
    handleRegisterConversion,
  };
};
```

## 测试示例

### 测试 URL 示例

```
https://yoursite.com/?utm_source=google&utm_medium=cpc&utm_campaign=summer_sale&utm_term=代购&utm_content=banner_top&gclid=Cj0KCQjw...
```

### 测试流程

1. 访问带有 UTM 参数的 URL
2. 检查 Cookie 中是否保存了广告参数
3. 用户注册/登录
4. 检查数据库中是否创建了广告跟踪记录
5. 验证转化记录是否正确

### 调试工具

```typescript
// utils/adTrackingUtilsDebug.ts
export const AdTrackingDebug = {
  /**
   * 打印当前广告跟踪状态
   */
  logCurrentState() {
    console.group('广告跟踪调试信息');
    console.log('URL参数:', AdTrackingUtil.extractParamsFromUrl());
    console.log('Cookie参数:', AdTrackingUtil.getParams());
    console.log('会话ID:', AdTrackingUtil.getSessionId());
    console.groupEnd();
  },

  /**
   * 模拟广告参数
   */
  simulateAdParams(params: AdTrackingParams) {
    AdTrackingUtil.saveParams(params);
    console.log('已模拟广告参数:', params);
  },

  /**
   * 清除所有跟踪数据
   */
  clearAll() {
    AdTrackingUtil.clearParams();
    console.log('已清除所有广告跟踪数据');
  },
};

// 开发环境下暴露到全局
if (process.dev) {
  (window as any).AdTrackingDebug = AdTrackingDebug;
}
```
