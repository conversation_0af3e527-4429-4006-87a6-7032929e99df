<template>
  <Header />
  <div class="help-category-page">
    <div class="help-category-container">
      <!-- 添加标题和搜索框 -->
      <HelpHeader :title="currentCategory ? currentCategory.title : ''" />

      <div class="help-content-wrapper">
        <!-- 左侧导航菜单 -->
        <HelpSidebar :categories="helpCategories" :current-category-code="currentCategoryCode" />

        <!-- 右侧内容区域 -->
        <div class="help-content">
          <div v-if="currentCategory">
            <!-- 面包屑导航 -->
            <div class="breadcrumbs q-mb-md">
              <q-breadcrumbs separator=">" class="text-grey">
                <q-breadcrumbs-el :label="$t('help.title')" to="/help" />
                <q-breadcrumbs-el :label="currentCategory.title" />
              </q-breadcrumbs>
            </div>

            <!-- 分类标题 -->
            <h1 class="category-title">{{ currentCategory.title }}</h1>
            <p class="category-description" v-if="currentCategory.description">{{ currentCategory.description }}</p>

            <!-- 文章列表 -->
            <q-list bordered separator class="category-articles">
              <q-item v-for="(article, index) in currentCategory.items" :key="index" :to="`/help/${currentCategoryCode}/${article.code}`" clickable v-ripple class="article-item">
                <q-item-section avatar>
                  <q-icon name="article" color="primary" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>{{ article.title }}</q-item-label>
                  <q-item-label caption v-if="article.description">{{ article.description }}</q-item-label>
                </q-item-section>
                <q-item-section side>
                  <q-icon name="chevron_right" color="grey" />
                </q-item-section>
              </q-item>
            </q-list>
          </div>

          <!-- 加载中状态 -->
          <div v-else-if="loading" class="loading-state">
            <q-spinner color="primary" size="3em" />
            <div class="q-mt-md">{{ $t('help.loading') }}</div>
          </div>

          <!-- 错误状态 -->
          <div v-else class="error-state">
            <q-icon name="error_outline" color="negative" size="3em" />
            <div class="q-mt-md">{{ $t('help.categoryNotFound') }}</div>
            <q-btn color="primary" to="/help" class="q-mt-md">{{ $t('help.backToHelp') }}</q-btn>
          </div>
        </div>
      </div>
    </div>
  </div>
  <Footer />
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import HelpSidebar from '~/components/help/HelpSidebar.vue';
import HelpHeader from '~/components/help/HelpHeader.vue';
import { useHelpStore } from '~/store/help';

const route = useRoute();
const loading = ref(true);

// 当前分类code
const currentCategoryCode = computed(() => route.params.category);

// 使用Pinia Store获取帮助分类数据
const helpStore = useHelpStore();
const helpCategories = computed(() => helpStore.getCategories);

// 当前分类
const currentCategory = computed(() => {
  return helpCategories.value.find((category) => category.code === currentCategoryCode.value) || null;
});

// 获取帮助分类数据
onMounted(async () => {
  try {
    loading.value = true;

    // 如果store中没有数据，则获取数据
    if (!helpStore.isInitialized) {
      await helpStore.fetchCategories();
    }

    loading.value = false;
  } catch (error) {
    console.error('Failed to fetch help categories:', error);
    loading.value = false;
  }
});
</script>

<style lang="scss" scoped>
.help-category-page {
  background-color: #f5f7fa;
  padding: 20px 0 40px;
}

.help-category-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

.help-content-wrapper {
  display: flex;
  gap: 30px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.help-content {
  flex-grow: 1;
  padding: 30px;
  overflow-y: auto;
}

.breadcrumbs {
  font-size: 14px;
}

.category-title {
  font-size: 28px;
  font-weight: bold;
  color: #333;
  margin: 16px 0;
}

.category-description {
  font-size: 16px;
  color: #666;
  margin-bottom: 24px;
}

.category-articles {
  margin-top: 20px;
}

.article-item {
  transition: background-color 0.3s;

  &:hover {
    background-color: #f5f5f5;
  }
}

.loading-state,
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
  color: #666;
}

@media (max-width: 1023px) {
  .help-content-wrapper {
    flex-direction: column;
  }
}

@media (max-width: 767px) {
  .category-title {
    font-size: 22px;
  }

  .help-content {
    padding: 20px 16px;
  }
}
</style>
