<template>
  <div class="row justify-end q-mt-md price-summary">
    <!-- PC端视图 - 重新组织结构，每行标题和金额放在同一个容器中 -->
    <div class="hide-on-mobile row justify-end q-py-md text-subtitle1 w-100">
      <div class="col-sm-6 col-md-4">
        <!-- 商品费用行 -->
        <div class="row no-wrap q-py-xs align-center">
          <div class="col-6 text-right q-pr-md text-grey-8">商品费用：</div>
          <div class="col-6 text-right">
            <div class="text-bold text-red price-amount primary-currency" v-html="formatAmount(orderInfo.price.totalPrice, { allowWrap: false })"></div>
          </div>
        </div>

        <!-- 运费行 -->
        <div class="row no-wrap q-py-xs align-center" v-if="orderInfo.price.deliveryPrice > 0">
          <div class="col-6 text-right q-pr-md text-grey-8">运费：</div>
          <div class="col-6 text-right">
            <div class="text-bold text-red price-amount primary-currency" v-html="formatAmount(orderInfo.price.deliveryPrice, { allowWrap: false })"></div>
          </div>
        </div> 

        <!-- 增值服务行 -->
        <div class="row no-wrap q-py-xs align-center" v-if="orderInfo.price.servicePrice > 0">
          <div class="col-6 text-right q-pr-md text-grey-8">增值服务：</div>
          <div class="col-6 text-right">
            <div class="text-bold text-red price-amount primary-currency" v-html="formatAmount(orderInfo.price.servicePrice, { allowWrap: false })"></div>
          </div>
        </div>

        <!-- 平台佣金行 -->
        <div class="row no-wrap q-py-xs align-center" v-if="orderInfo.price.platformPrice > 0">
          <div class="col-6 text-right q-pr-md text-grey-8">平台佣金：</div>
          <div class="col-6 text-right">
            <div class="text-bold text-red price-amount primary-currency" v-html="formatAmount(orderInfo.price.platformPrice, { allowWrap: false })"></div>
          </div>
        </div>

        <!-- 优惠券行 -->
        <div class="row no-wrap q-py-xs align-center" v-if="orderInfo.price.couponPrice > 0">
          <div class="col-6 text-right q-pr-md text-grey-8">优惠券：</div>
          <div class="col-6 text-right">
            <div class="text-bold text-red price-amount primary-currency" v-html="'- ' + formatAmount(orderInfo.price.couponPrice, { allowWrap: false })"></div>
          </div>
        </div>

        <q-separator class="q-mt-sm" />

        <!-- 应付金额行 -->
        <div class="row no-wrap q-py-xs align-center q-mt-sm">
          <div class="col-6 text-right q-pr-md text-subtitle1 text-weight-medium">应付金额：</div>
          <div class="col-6 text-right">
            <div class="text-h6 text-bold text-red price-amount primary-currency" v-html="formatAmount(orderInfo.price.payPrice, { allowWrap: false })"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 移动端视图 - 更紧凑的布局，同样重新组织结构 -->
    <div class="mobile-only-block w-100">
      <div class="q-py-xs mobile-price-summary">
        <!-- 商品费用行 -->
        <div class="row no-wrap q-py-xs align-center">
          <div class="col-6 text-right q-pr-xs text-grey-8 text-caption">商品费用：</div>
          <div class="col-6 text-right">
            <div class="text-bold text-red text-caption mobile-price-amount primary-currency" v-html="formatAmount(orderInfo.price.totalPrice, { allowWrap: false })"></div>
          </div>
        </div>

        <!-- 运费行 -->
        <div class="row no-wrap q-py-xs align-center" v-if="orderInfo.price.deliveryPrice > 0">
          <div class="col-6 text-right q-pr-xs text-grey-8 text-caption">运费：</div>
          <div class="col-6 text-right">
            <div class="text-bold text-red text-caption mobile-price-amount primary-currency" v-html="formatAmount(orderInfo.price.deliveryPrice, { allowWrap: false })"></div>
          </div>
        </div>

        <!-- 增值服务行 -->
        <div class="row no-wrap q-py-xs align-center" v-if="orderInfo.price.servicePrice > 0">
          <div class="col-6 text-right q-pr-xs text-grey-8 text-caption">增值服务：</div>
          <div class="col-6 text-right">
            <div class="text-bold text-red text-caption mobile-price-amount primary-currency" v-html="formatAmount(orderInfo.price.servicePrice, { allowWrap: false })"></div>
          </div>
        </div>

        <!-- 平台佣金行 -->
        <div class="row no-wrap q-py-xs align-center" v-if="orderInfo.price.platformPrice > 0">
          <div class="col-6 text-right q-pr-xs text-grey-8 text-caption">平台佣金：</div>
          <div class="col-6 text-right">
            <div class="text-bold text-red text-caption mobile-price-amount primary-currency" v-html="formatAmount(orderInfo.price.platformPrice, { allowWrap: false })"></div>
          </div>
        </div>

        <!-- 优惠券行 -->
        <div class="row no-wrap q-py-xs align-center" v-if="orderInfo.price.couponPrice > 0">
          <div class="col-6 text-right q-pr-xs text-grey-8 text-caption">优惠券：</div>
          <div class="col-6 text-right">
            <div class="text-bold text-red text-caption mobile-price-amount primary-currency" v-html="'- ' + formatAmount(orderInfo.price.couponPrice, { allowWrap: false })"></div>
          </div>
        </div>

        <q-separator class="q-mt-xs" />

        <!-- 应付金额行 -->
        <div class="row no-wrap q-py-xs align-center q-mt-xs">
          <div class="col-6 text-right q-pr-xs text-weight-medium">应付金额：</div>
          <div class="col-6 text-right">
            <div class="text-subtitle1 text-bold text-red mobile-price-amount primary-currency" v-html="formatAmount(orderInfo.price.payPrice, { allowWrap: false })"></div>
          </div>
        </div>
      </div>
    </div>
  </div>

</template>
<script setup>
import { useCurrency } from '~/composables/useCurrency';

const props = defineProps({
  orderInfo: {
    type: Object,
    required: true,
  },
});

const { formatAmount } = useCurrency();

</script>
<style lang="scss" scoped>
// 100%宽度
.w-100 {
  width: 100%;
}
// PC端专用样式
.hide-on-mobile {
  @media (max-width: 599px) {
    display: none !important;
  }
}

.mobile-only-block {
  display: none !important;

  @media (max-width: 599px) {
    display: block !important;
  }
}

// 价格金额样式
.price-amount {
  min-width: 140px;
  display: inline-block;
  text-align: right;

  &.primary-currency {
    white-space: nowrap;

    :deep(.default-currency) {
      font-size: 0.8em;
      color: #666;
      opacity: 0.85;
      font-weight: normal;
      display: inline-block;
      margin-left: 4px;
      white-space: normal;
    }
  }
}

// 移动端价格摘要样式
.mobile-price-summary {
  .text-caption {
    font-size: 12px;
    line-height: 1.2;
  }

  .q-py-xs {
    padding-top: 2px;
    padding-bottom: 2px;
  }

  .q-mt-xs {
    margin-top: 4px;
  }

  .mobile-price-amount {
    min-width: 100px;
    display: inline-block;
    text-align: right;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    &.primary-currency {
      white-space: nowrap;

      :deep(.default-currency) {
        font-size: 0.75em;
        color: #666;
        opacity: 0.85;
        font-weight: normal;
        display: inline-block;
        margin-left: 2px;
        white-space: normal;
      }
    }
  }
}

</style>
