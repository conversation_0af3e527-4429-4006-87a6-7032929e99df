# 图片上传组件使用文档

## 组件介绍

本项目提供了两个图片上传组件：
- `ImageUpload.vue` - 多图片上传组件，支持拖拽上传
- `SingleImageUpload.vue` - 单图片上传组件，简洁版本

## ImageUpload 多图片上传组件

### 基本用法

```vue
<template>
  <div>
    <ImageUpload v-model="imageUrls" />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import ImageUpload from '~/components/ImageUpload.vue'

const imageUrls = ref([])
</script>
```

### Props 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| modelValue | Array | [] | 图片URL数组，支持v-model |
| maxFiles | Number | 5 | 最大上传文件数量 |
| maxFileSize | Number | 10485760 | 最大文件大小（字节），默认10MB |
| accept | String | 'image/*' | 接受的文件类型 |
| multiple | Boolean | true | 是否支持多选 |
| disabled | Boolean | false | 是否禁用 |
| showDropZone | Boolean | true | 是否显示拖拽区域 |

### 高级用法

```vue
<template>
  <div>
    <!-- 限制最多3张图片，每张不超过5MB -->
    <ImageUpload 
      v-model="imageUrls" 
      :max-files="3"
      :max-file-size="5 * 1024 * 1024"
      :disabled="uploading"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import ImageUpload from '~/components/ImageUpload.vue'

const imageUrls = ref([])
const uploading = ref(false)
</script>
```

### 功能特性

- ✅ 支持拖拽上传
- ✅ 支持点击上传
- ✅ 图片预览功能
- ✅ 删除图片功能
- ✅ 上传进度显示
- ✅ 文件类型验证
- ✅ 文件大小验证
- ✅ 响应式设计

## SingleImageUpload 单图片上传组件

### 基本用法

```vue
<template>
  <div>
    <SingleImageUpload v-model="imageUrl" />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import SingleImageUpload from '~/components/SingleImageUpload.vue'

const imageUrl = ref('')
</script>
```

### Props 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| modelValue | String | '' | 图片URL，支持v-model |
| maxFileSize | Number | 10485760 | 最大文件大小（字节），默认10MB |
| accept | String | 'image/*' | 接受的文件类型 |
| disabled | Boolean | false | 是否禁用 |
| ratio | Number | 1 | 图片显示比例 |
| width | String | '120px' | 组件宽度 |
| height | String | '120px' | 组件高度 |

### 高级用法

```vue
<template>
  <div>
    <!-- 自定义尺寸的头像上传 -->
    <SingleImageUpload 
      v-model="avatarUrl" 
      width="100px"
      height="100px"
      :ratio="1"
      :max-file-size="2 * 1024 * 1024"
    />

    <!-- 横向比例的banner上传 -->
    <SingleImageUpload 
      v-model="bannerUrl" 
      width="300px"
      height="150px"
      :ratio="2"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import SingleImageUpload from '~/components/SingleImageUpload.vue'

const avatarUrl = ref('')
const bannerUrl = ref('')
</script>
```

### 功能特性

- ✅ 单图片上传
- ✅ 图片预览功能
- ✅ 删除图片功能
- ✅ 上传进度显示
- ✅ 文件类型验证
- ✅ 文件大小验证
- ✅ 自定义尺寸
- ✅ 简洁界面

## 样式自定义

### 自定义上传区域样式

```vue
<template>
  <div>
    <ImageUpload v-model="imageUrls" class="custom-upload" />
  </div>
</template>

<style scoped>
.custom-upload :deep(.drop-zone) {
  border-color: #ff6b6b;
  background-color: #fff5f5;
}

.custom-upload :deep(.drop-zone:hover) {
  border-color: #ff5252;
  background-color: #ffebee;
}
</style>
```

### 自定义图片显示样式

```vue
<style scoped>
.custom-upload :deep(.uploaded-image) {
  border-radius: 12px;
  border-width: 3px;
}

.custom-upload :deep(.delete-btn) {
  background-color: #ff5252 !important;
  color: white !important;
}
</style>
```

## 事件处理

组件使用 v-model 进行数据绑定，当图片上传完成或删除时会自动更新绑定的数据。

```vue
<template>
  <div>
    <ImageUpload v-model="imageUrls" @update:modelValue="onImagesChange" />
    <p>已上传 {{ imageUrls.length }} 张图片</p>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import ImageUpload from '~/components/ImageUpload.vue'

const imageUrls = ref([])

const onImagesChange = (urls) => {
  console.log('图片列表更新:', urls)
  // 可以在这里处理图片变化的逻辑
}
</script>
```

## 注意事项

1. **文件大小限制**：默认限制为10MB，可通过 `maxFileSize` 参数调整
2. **文件类型限制**：默认只接受图片文件，可通过 `accept` 参数调整
3. **网络请求**：组件依赖 `~/composables/fileApi` 中的 `uploadFile` 方法
4. **响应式**：组件已适配移动端，在小屏幕设备上会自动调整布局
5. **性能**：大量图片上传时建议分批处理，避免同时上传过多文件

## 常见问题

### Q: 如何限制图片的尺寸？
A: 目前组件只验证文件大小，如需验证图片尺寸，可以在 `validateFile` 方法中添加相关逻辑。

### Q: 如何自定义上传接口？
A: 修改 `~/composables/fileApi` 中的 `uploadFile` 方法，或者在组件中传入自定义的上传函数。

### Q: 如何处理上传失败的情况？
A: 组件会自动显示错误提示，失败的图片会从列表中移除。可以监听控制台日志查看详细错误信息。

### Q: 如何预设图片列表？
A: 直接设置 v-model 绑定的数组即可：
```javascript
const imageUrls = ref([
  'https://example.com/image1.jpg',
  'https://example.com/image2.jpg'
])
```

## 更新日志

### 2024-01-01 - 工单详情页面集成
- ✅ 工单详情页面已成功集成新的ImageUpload组件
- ✅ 替换了原有的文件上传控件
- ✅ 修复了回复内容提交bug
- ✅ 增加了回复内容框高度
- ✅ 清理了不需要的代码和样式

**修改的文件：**
- `pages/account/ticket-detail.vue` - 集成ImageUpload组件，修复回复提交逻辑

**主要改进：**
1. 使用统一的图片上传组件，提升用户体验
2. 修复了回复内容和附件无法正确提交到后端的问题
3. 增加了回复内容框的高度，提升输入体验
4. 代码更加简洁，维护性更好
