<template>
  <div class="coupon-selector-section">
    <div class="row items-center q-mb-sm">
      <q-icon name="local_offer" color="primary" size="sm" class="q-mr-sm" />
      <div class="text-h6">优惠券</div>
    </div>
    <div class="text-caption text-grey-8 q-mb-md">选择适用的优惠券，享受更多优惠</div>

    <q-card flat bordered class="q-pa-md">
      <div class="row items-center">
        <div v-if="modelValue" class="col-grow">
          <div class="row items-center justify-between">
            <div>
              <div class="text-body1 text-weight-medium">{{ modelValue.name }}</div>
              <div class="text-caption text-grey-8">{{ modelValue.description }}</div>
            </div>
            <div class="text-right">
              <div class="text-body1 text-negative text-weight-medium" v-if="modelValue.discountType === 1">-{{ fen2yuan(modelValue.discountPrice) }}</div>
              <div class="text-body1 text-negative text-weight-medium" v-if="modelValue.discountType === 2">{{ modelValue.discountPercent }}折</div>
              <q-btn flat round color="primary" icon="edit" @click="openDialog" />
            </div>
          </div>
        </div>
        <div v-else class="col-grow text-center">
          <q-btn color="primary" outline label="选择优惠券" @click="openDialog" />
        </div>
      </div>
    </q-card>

    <!-- 优惠券选择弹窗 -->
    <q-dialog v-model="couponDialog" persistent>
      <q-card style="width: 700px; max-width: 90vw">
        <q-card-section class="row items-center">
          <div class="text-h6">选择优惠券</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-card-section class="q-pt-none">
          <div class="text-body1 q-mb-md">
            运费金额: <span class="text-weight-medium">{{ fen2yuan(baseShippingFee) }}</span>
          </div>

          <div v-if="availableCoupons.length > 0">
            <q-list separator>
              <q-item
                v-for="coupon in availableCoupons"
                :key="coupon.id"
                clickable
                :active="tempSelectedCoupon && tempSelectedCoupon.id === coupon.id"
                active-class="bg-blue-1"
                @click="selectCoupon(coupon)">
                <q-item-section>
                  <q-item-label class="text-weight-medium">{{ coupon.name }}</q-item-label>
                  <q-item-label caption>{{ coupon.description }}</q-item-label>
                  <q-item-label caption>
                    <span v-if="coupon.usePrice > 0">满 {{ fen2yuan(coupon.usePrice) }} 可用</span>
                    <span v-else>无门槛</span>
                    <span class="q-ml-md">有效期至: {{ formatDate(coupon.validEndTime) }}</span>
                  </q-item-label>
                </q-item-section>
                <q-item-section side>
                  <div class="text-negative text-weight-medium" v-if="coupon.discountType === 1">-{{ fen2yuan(coupon.discountPrice) }}</div>
                  <div class="text-negative text-weight-medium" v-if="coupon.discountType === 2">{{ coupon.discountPercent }}折</div>
                </q-item-section>
              </q-item>
            </q-list>
          </div>

          <div v-else class="text-center q-py-lg">
            <q-icon name="card_giftcard" size="2rem" color="grey-7" />
            <div class="text-grey-7 q-mt-sm">暂无可用优惠券</div>
          </div>

          <!-- 不使用优惠券选项 -->
          <q-item clickable :active="tempSelectedCoupon === null" active-class="bg-blue-1" @click="selectCoupon(null)" class="q-mt-md">
            <q-item-section>
              <q-item-label class="text-weight-medium">不使用优惠券</q-item-label>
            </q-item-section>
          </q-item>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="取消" color="grey" v-close-popup />
          <q-btn flat label="确认" color="primary" @click="confirmCoupon" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
import { ref, watch, computed } from 'vue';
import { fen2yuan } from '../../utils/utils';

// 定义组件接收的属性
const props = defineProps({
  modelValue: {
    type: Object,
    default: null,
  },
  coupons: {
    type: Array,
    required: true,
    default: () => [],
  },
  baseShippingFee: {
    type: Number,
    required: true,
    default: 0,
  },
});

// 定义组件向外发出的事件
const emit = defineEmits(['update:modelValue', 'calculate-discount']);

// 弹窗状态
const couponDialog = ref(false);
const tempSelectedCoupon = ref(props.modelValue);

// 可用优惠券列表（根据订单金额过滤）
const availableCoupons = computed(() => {
  const now = Date.now();
  return props.coupons.filter((coupon) => {
    // 检查优惠券是否过期
    if (coupon.validEndTime && coupon.validEndTime < now) {
      return false;
    }

    // 检查订单金额是否满足优惠券使用条件
    return coupon.usePrice <= props.baseShippingFee;
  });
});

// 格式化日期
function formatDate(timestamp) {
  if (!timestamp) return '';
  const date = new Date(timestamp);
  return date.toLocaleDateString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit' });
}

// 打开优惠券选择弹窗
function openDialog() {
  tempSelectedCoupon.value = props.modelValue;
  couponDialog.value = true;
}

// 选择优惠券
function selectCoupon(coupon) {
  tempSelectedCoupon.value = coupon;
}

// 确认优惠券选择
function confirmCoupon() {
  emit('update:modelValue', tempSelectedCoupon.value);
  emit('calculate-discount');
  couponDialog.value = false;
}

// 监听父组件传入的值变化
watch(
  () => props.modelValue,
  (newValue) => {
    tempSelectedCoupon.value = newValue;
  }
);
</script>

<style lang="scss" scoped>
.coupon-selector-section {
  .q-card {
    border-radius: 8px;
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    }
  }
}
</style>
