<template>
  <div class="order-detail q-pa-md">
    <!-- 标题区域 -->
    <div class="bg-grey-2 q-px-md q-py-sm rounded-borders">
      <div class="row justify-between items-center">
        <div class="row items-center">
          <q-btn flat dense color="primary" icon="arrow_back" @click="goBack" class="q-mr-sm" />
          <span class="text-subtitle1 text-weight-medium">{{ $t('accountOrderDetail.title') }}</span>
        </div>
        <div class="row items-center">
          <q-btn flat dense color="primary" icon="print" @click="printOrder" class="q-mr-sm" :label="$t('accountOrderDetail.buttons.print')" />
          <q-btn flat dense color="primary" to="/account/orders" :label="$t('accountOrderDetail.backToList')" class="q-px-sm" />
        </div>
      </div>
    </div>

    <!-- 加载中状态 -->
    <div v-if="loading" class="column items-center q-py-xl">
      <q-spinner color="primary" size="3em" />
      <div class="q-mt-sm text-grey-7">{{ $t('accountOrderDetail.loading') }}</div>
    </div>

    <!-- 订单不存在 -->
    <div v-else-if="!state.orderInfo.id" class="column items-center q-py-xl">
      <q-icon name="error_outline" size="3em" color="grey-5" />
      <div class="q-mt-sm text-grey-7">{{ $t('accountOrderDetail.notFound') }}</div>
      <q-btn color="primary" class="q-mt-md" :label="$t('accountOrderDetail.backToList')" @click="goBack" />
    </div>

    <!-- 订单详情内容 -->
    <div v-else>
      <!-- 订单状态进度条 -->
      <div class="order-progress-section q-my-md">
        <OrderStatusProgress :current-step="displayStatus" />
      </div>

      <!-- 订单基本信息 -->
      <q-card class="q-mt-md">
        <q-card-section class="bg-grey-2">
          <div class="text-subtitle2 text-weight-medium">{{ $t('accountOrderDetail.sections.basicInfo') }}</div>
        </q-card-section>
        <q-card-section>
          <div class="row q-col-gutter-md">
            <div class="col-12 col-sm-6 col-md-4">
              <div class="row">
                <div class="col-5 text-grey-7">{{ $t('accountOrderDetail.fields.orderNumber') }}</div>
                <div class="col-7 text-weight-medium">{{ state.orderInfo.no }}</div>
              </div>
            </div>
            <div class="col-12 col-sm-6 col-md-4">
              <div class="row">
                <div class="col-5 text-grey-7">{{ $t('accountOrderDetail.fields.createTime') }}</div>
                <div class="col-7">{{ formatDateTime(state.orderInfo.createTime) }}</div>
              </div>
            </div>
            <div class="col-12 col-sm-6 col-md-4">
              <div class="row">
                <div class="col-5 text-grey-7">{{ $t('accountOrderDetail.fields.orderStatus') }}</div>
                <div class="col-7">
                  <q-badge :color="getStatusColor(state.orderInfo.status)">
                    {{ getOrderStatus(state.orderInfo) }}
                  </q-badge>
                </div>
              </div>
            </div>
            <div class="col-12 col-sm-6 col-md-4">
              <div class="row">
                <div class="col-5 text-grey-7">{{ $t('accountOrderDetail.fields.paymentMethod') }}</div>
                <div class="col-7">{{ state.orderInfo.payChannelName }}</div>
              </div>
            </div>
            <div class="col-12 col-sm-6 col-md-4">
              <div class="row">
                <div class="col-5 text-grey-7">{{ $t('accountOrderDetail.fields.paymentTime') }}</div>
                <div class="col-7">{{ formatDateTime(state.orderInfo.payTime) || $t('accountOrderDetail.placeholders.unpaid') }}</div>
              </div>
            </div>

            <div class="col-12 col-sm-6 col-md-4">
              <div class="row">
                <div class="col-5 text-grey-7">{{ $t('accountOrderDetail.fields.remarks') }}</div>
                <div class="col-7">{{ state.orderInfo.remark || $t('accountOrderDetail.placeholders.none') }}</div>
              </div>
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- 商品信息 -->
      <q-card class="q-mt-md product-card">
        <q-card-section class="bg-grey-2">
          <div class="text-subtitle2 text-weight-medium">{{ $t('accountOrderDetail.sections.productInfo') }}</div>
        </q-card-section>

        <!-- 使用新的OrderSummary组件 -->
        <OrderSummary :orderInfo="state.orderInfo" />
      </q-card>



      <!-- 订单操作 -->
      <div class="q-mt-lg row justify-end q-gutter-sm print-hide">
        <q-btn v-if="state.orderInfo.status === 0" color="grey" outline :label="$t('accountOrderDetail.buttons.cancelOrder')" @click="cancelOrder" />
        <q-btn v-if="state.orderInfo.status === 0" color="primary" :label="$t('accountOrderDetail.buttons.payNow')" @click="payOrder" />
        <q-btn
          v-if="state.orderInfo.status === 10 && state.orderInfo.hasSupplement && state.orderInfo.supplementStatus === 0"
          color="primary"
          label="支付补款"
          @click="payOrderSupplement(state.orderInfo.supplementId)" />
        <q-btn v-if="state.orderInfo.status === 40" color="grey" outline :label="$t('accountOrderDetail.buttons.deleteOrder')" @click="deleteOrder" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { useQuasar } from 'quasar';
import OrderStatusProgress from '~/components/widgets/OrderStatusProgress.vue';
import OrderSummary from '~/components/order/OrderSummary.vue';
import OrderApi from '~/composables/orderApi';
import SupplementApi from '~/composables/supplementApi';
import { useI18n } from 'vue-i18n';

definePageMeta({
  middleware: 'auth', // 引入身份验证中间件
});

const $q = useQuasar();
const route = useRoute();
const router = useNuxtApp().$router;
const { t } = useI18n();

const loading = ref(true);

const state = reactive({
  orderInfo: {},
});

const displayStatus = computed(() => {
  //存在待补款则状态为待付款
  if (state.orderInfo.status === 10 && state.orderInfo.hasSupplement && state.orderInfo.supplementStatus === 0) {
    return 0;
  } else {
    return state.orderInfo.status;
  }
});

onMounted(() => {
  // 从路由参数获取订单ID
  const orderId = route.query.id;
  if (orderId) {
    getOrderDetail(orderId);
  } else {
    loading.value = false;
  }
});

// 获取订单详情
async function getOrderDetail(id) {
  loading.value = true;
  try {
    const { code, data } = await OrderApi.getOrderDetail(id);
    if (code === 0 && data) {
      state.orderInfo = data;
    } else {
      showNotify(t('accountOrderDetail.errors.fetchFailed'), 'negative');
    }
  } catch (error) {
    console.error(t('accountOrderDetail.errors.fetchError') + ':', error);
    showNotify(t('accountOrderDetail.errors.fetchError'), 'negative');
  } finally {
    loading.value = false;
  }
}

// 返回订单列表
function goBack() {
  router.push('/account/orders');
}



// 取消订单
function cancelOrder() {
  $q.dialog({
    title: t('accountOrderDetail.confirmations.cancelTitle'),
    message: t('accountOrderDetail.confirmations.cancelMessage'),
    cancel: true,
    persistent: true,
  }).onOk(() => {
    OrderApi.cancelOrder(state.orderInfo.id).then(({ code }) => {
      if (code === 0) {
        showNotify(t('accountOrderDetail.confirmations.cancelSuccess'), 'positive');
        getOrderDetail(state.orderInfo.id); // 刷新订单信息
      } else {
        showNotify(t('accountOrderDetail.confirmations.cancelFailed'), 'negative');
      }
    });
  });
}

// 支付订单
function payOrder() {
  router.push(`/pay?orderId=${state.orderInfo.id}`);
}

// 支付订单补款单
async function payOrderSupplement(supplementId) {
  const { code, data } = await SupplementApi.getDetail(supplementId);
  if (code === 0) {
    if (data.payOrderId) {
      // 跳转到支付页面
      navigateTo({
        path: '/pay',
        query: { pId: data.payOrderId, ps: PayOrderSources.ORDER },
      });
    }
  }
}

// 确认收货
function confirmReceive() {
  $q.dialog({
    title: t('accountOrderDetail.confirmations.receiptTitle'),
    message: t('accountOrderDetail.confirmations.receiptMessage'),
    cancel: true,
    persistent: true,
  }).onOk(() => {
    OrderApi.receiveOrder(state.orderInfo.id).then(({ code }) => {
      if (code === 0) {
        showNotify(t('accountOrderDetail.confirmations.receiptSuccess'), 'positive');
        getOrderDetail(state.orderInfo.id); // 刷新订单信息
      } else {
        showNotify(t('accountOrderDetail.confirmations.receiptFailed'), 'negative');
      }
    });
  });
}

// 评价商品
function commentOrder() {
  router.push(`/account/comment?orderId=${state.orderInfo.id}`);
}

// 删除订单
function deleteOrder() {
  $q.dialog({
    title: t('accountOrderDetail.confirmations.deleteTitle'),
    message: t('accountOrderDetail.confirmations.deleteMessage'),
    cancel: true,
    persistent: true,
  }).onOk(() => {
    OrderApi.deleteOrder(state.orderInfo.id).then(({ code }) => {
      if (code === 0) {
        showNotify(t('accountOrderDetail.confirmations.deleteSuccess'), 'positive');
        router.push('/account/orders'); // 返回订单列表
      } else {
        showNotify(t('accountOrderDetail.confirmations.deleteFailed'), 'negative');
      }
    });
  });
}

// 打印订单
function printOrder() {
  // 保存当前页面的滚动位置
  const scrollPosition = window.scrollY;

  // 保存原始文档标题
  const originalTitle = document.title;

  // 设置文档标题为订单编号，这将成为默认的PDF文件名
  if (state.orderInfo && state.orderInfo.no) {
    // 根据当前语言环境设置不同的前缀
    const prefix = t('accountOrderDetail.title');
    document.title = `${prefix}_${state.orderInfo.no}`;
  }

  // 添加打印样式
  const style = document.createElement('style');
  style.id = 'print-style';
  style.innerHTML = `
    @media print {
      body * {
        visibility: hidden;
      }
      .order-detail, .order-detail * {
        visibility: visible;
      }
      .order-detail {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
      }
      .q-btn, .print-hide {
        display: none !important;
      }
      .q-card {
        box-shadow: none !important;
        border: 1px solid #ddd;
        margin-bottom: 15px !important;
      }
    }
  `;
  document.head.appendChild(style);

  // 调用打印功能
  window.print();

  // 打印完成后移除打印样式并恢复原始标题
  setTimeout(() => {
    const printStyle = document.getElementById('print-style');
    if (printStyle) {
      printStyle.remove();
    }
    // 恢复原始文档标题
    document.title = originalTitle;
    // 恢复滚动位置
    window.scrollTo(0, scrollPosition);
  }, 500);
}

// 格式化日期时间
function formatDateTime(timestamp) {
  if (!timestamp) return '';
  const date = new Date(timestamp);
  return date
    .toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    })
    .replace(/\//g, '-');
}

// 注意：formatAddress 函数已移除，因为收货信息区域已被移除

// 获取订单状态文本
function getOrderStatus(order) {
  switch (order.status) {
    case 0:
      return t('accountOrders.status.unpaid');
    case 10:
      if (order.hasSupplement && order.supplementStatus === 0) {
        return '待补款';
      } else {
        return '待采购';
      }

    case 20:
      return t('accountOrders.status.purchasing');
    case 30:
      return t('accountOrders.status.warehoused');
    case 40:
      return t('accountOrders.status.cancelled');
    default:
      return t('accountOrders.status.unknown');
  }
}

// 获取订单状态对应的颜色
function getStatusColor(status) {
  switch (status) {
    case 0:
      return 'orange';
    case 10:
      return 'blue';
    case 20:
      return 'teal';
    case 30:
      return 'teal';
    case 40:
      return 'grey';
    case 50:
      return 'deep-orange';
    case 100:
      return 'positive';
    case -1:
      return 'grey';
    default:
      return 'grey';
  }
}
</script>

<style lang="scss" scoped>
.order-detail {
  max-width: 1200px;
  margin: 0 auto;

  .ellipsis-2-lines {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    min-height: 2em;
    line-height: 1.2;
  }

  .word-break-all {
    word-break: break-word;
    overflow-wrap: break-word;
  }

  .nowrap {
    white-space: nowrap;
  }

  .order-progress-section {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
    padding: 16px 0;
    margin: 16px 0;
    overflow: hidden; /* 防止内容溢出 */
  }

  .product-card {
    overflow: hidden;

    .product-table {
      width: 100%;
      table-layout: fixed;

      th {
        background-color: #f5f5f5;
        font-weight: 500;
        font-size: 14px;
        padding: 12px 8px;
      }

      td {
        padding: 12px 8px;
        vertical-align: middle;
        word-break: break-word;
      }

      .price-column {
        white-space: nowrap;
      }
    }

    .product-image {
      border: 1px solid #eee;
      transition: transform 0.2s;

      &:hover {
        transform: scale(1.05);
      }
    }

    .product-name {
      font-weight: 500;
      color: #333;
      font-size: 14px;
    }

    .product-props {
      color: #999;
      font-size: 12px;
    }

    .price-column {
      color: #ff6b00;
    }
  }

  .mobile-product-list {
    .product-item {
      padding: 16px 0;

      .product-name {
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 4px;
      }
    }
  }

  .price-summary {
    .price-row {
      padding: 4px 0;
      font-size: 14px;
      align-items: center;
    }

    .discount-price {
      color: #ff6b00;
    }

    .total-price {
      font-size: 16px;
      padding-top: 8px;
      margin-top: 8px;
      border-top: 1px dashed #eee;
    }

    .text-right {
      text-align: right;
    }

    @media (max-width: 599px) {
      .col-6 {
        padding: 0 4px;
      }
    }
  }

  @media (max-width: 599px) {
    .q-card {
      border-radius: 8px;
      margin-bottom: 16px;
    }

    .q-card__section {
      padding: 12px;
    }

    .product-card {
      .bg-grey-2 {
        padding: 10px 12px;
      }

      .q-card__section + .order-summary {
        padding-top: 0;
      }
    }

    .order-progress-section {
      padding: 12px 8px; /* 增加水平内边距 */
      margin: 12px 4px; /* 减小外边距，增加可用空间 */
    }

    .product-image {
      width: 60px !important;
      height: 60px !important;
    }
  }

  // 金额显示样式
  .order-amount,
  .price-amount {
    :deep(.primary-currency) {
      white-space: nowrap;
      display: inline-block;
      font-weight: 500;

      :deep(.default-currency) {
        font-size: 0.85em;
        color: #666;
        opacity: 0.85;
        font-weight: normal;
        display: inline-block;
        margin-left: 4px;
        white-space: normal;
      }
    }
  }

  // 订单金额样式
  .order-amount {
    :deep(.primary-currency) {
      font-size: 1.1rem;
    }
  }

  // 商品价格样式
  .price-amount {
    :deep(.primary-currency) {
      font-size: 0.95rem;
    }
  }
}

// 打印样式
@media print {
  .print-hide {
    display: none !important;
  }

  .order-detail {
    padding: 0 !important;

    .q-card {
      margin-bottom: 20px;
      page-break-inside: avoid;
      box-shadow: none !important;
      border: 1px solid #ddd;
    }

    .q-card__section {
      padding: 10px !important;
    }

    .bg-grey-2 {
      background-color: #f5f5f5 !important;
      -webkit-print-color-adjust: exact !important;
      print-color-adjust: exact !important;
    }

    .order-progress-section {
      box-shadow: none !important;
      border: 1px solid #ddd;
    }
  }
}
</style>
