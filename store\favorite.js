import { defineStore } from 'pinia';
import { useAuthStore } from './auth';
import Favorite<PERSON><PERSON> from '../composables/favoriteApi';
export const useFavoriteStore = defineStore({
  id: 'favorite',
  state: () => {
    return {
      list: [],
    };
  },
  actions: {
    // 获取心愿单列表
    async getList() {
      if (useAuthStore().isLogin) {
        const response = await FavoriteApi.getFavoriteList();
        if (response.code === 0) {
          this.list = response.data; 
        }
      }
    },
    async addToWishlist(payload) {
      if (useAuthStore().isLogin) {
        const wishlistItems = this.list.find((spuId) => spuId === payload.spu.id);
        if (wishlistItems) {
        } else {
          const {code,data} = await FavoriteApi.createFavorite({ spuId: payload.spu.id });
          if(code === 0){
            this.list.push(payload.spu.id);
          }
        }
      }
    },
    async removeFromWishlist(productId) {
      if (useAuthStore().isLogin) {
        await FavoriteApi.deleteFavorite({ spuId: productId });
        this.list = this.list.filter((spuId) => spuId !== productId);
      }
    },

    clearWishlist() {
      this.list = [];
    },

    setInitialWhishlist(payload) {
      this.list = payload;
    },
    removeWishlistItem(payload) {
      const index = this.list.indexOf(payload);
      this.list.splice(index, 1);
    },
  },
  getters: {
    wishlistItems: (state) => {
      return state.list;
    },
  },
  persist: process.client && {
    storage: localStorage,
    paths: ['wishlist'],
  },
});
