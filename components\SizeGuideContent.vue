<template>
  <div class="size-guide-content">
    <!-- 选择器区域 -->
    <div class="selectors-row q-gutter-md q-mb-lg">
      <q-select
        v-model="selectedCategory"
        :options="categoryOptions"
        option-value="value"
        option-label="label"
        emit-value
        map-options
        :label="$t('sizeGuide.category')"
        style="min-width: 200px"
        outlined
        dense
      />
      <q-select
        v-model="selectedCountry"
        :options="countryOptions"
        option-value="value"
        option-label="label"
        emit-value
        map-options
        :label="$t('sizeGuide.country')"
        style="min-width: 150px"
        outlined
        dense
      />
      <q-select
        v-model="selectedUnit"
        :options="unitOptions"
        option-value="value"
        option-label="label"
        emit-value
        map-options
        :label="$t('sizeGuide.unit')"
        style="min-width: 120px"
        outlined
        dense
      />
    </div>



    <!-- 尺码表格 -->
    <div class="size-table-container q-mb-md">
      <q-table
        :rows="tableRows"
        :columns="tableColumns"
        row-key="index"
        flat
        bordered
        :pagination="{ rowsPerPage: 0 }"
        hide-pagination
        class="size-table"
      />
    </div>

    <!-- 底部说明 -->
    <div class="notice-text text-grey-7 text-caption">
      {{ $t('sizeGuide.notice') }}
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'

// Props
const props = defineProps({
  defaultCategory: {
    type: String,
    default: "women-top"
  }
})

// 国际化
const { t } = useI18n()

// 响应式数据
const selectedUnit = ref('CM')
const selectedCountry = ref('US')
const selectedCategory = ref(props.defaultCategory)

// 添加监听器来处理选择变化
watch(selectedCategory, (newVal) => {
  // 当类型变化时，检查当前选择的国家是否还有效
  const currentData = SIZE_DATA[newVal]
  if (currentData && !currentData.body.Country[selectedCountry.value]) {
    // 如果当前国家不支持新类型，切换到第一个支持的国家
    const supportedCountries = Object.keys(currentData.body.Country)
    if (supportedCountries.length > 0) {
      selectedCountry.value = supportedCountries[0]
    }
  }

  // 检查单位是否还有效（主要是戒指类型的英寸问题）
  if (newVal === 'ring' && selectedUnit.value === 'IN') {
    selectedUnit.value = 'CM'
  }
})



// 完整的数据结构 - 按照用户提供的格式，但key改为横线格式
const SIZE_DATA = {
  "women-top": {
    header: {
      CM: ["Bust (cm)", "Waist (cm)", "Hip (cm)"],
      IN: ["Bust (inch)", "Waist (inch)", "Hip (inch)"],
      Basic: ["Size"]
    },
    body: {
      Type: {
        CM: {
          "Bust (cm)": ["78-82", "82-86", "86-90", "90-94", "94-98", "98-102", "102-106", "106-110"],
          "Waist (cm)": ["62-66", "66-70", "70-74", "74-78", "78-82", "82-86", "86-90", "90-94"],
          "Hip (cm)": ["84-88", "88-92", "92-96", "96-100", "100-104", "104-108", "108-112", "112-116"]
        },
        IN: {
          "Bust (inch)": ["30.71-32.28", "32.28-33.86", "33.86-35.43", "35.43-37.01", "37.01-38.58", "38.58-40.16", "40.16-41.73", "41.73-43.31"],
          "Waist (inch)": ["24.41-25.98", "25.98-27.56", "27.56-29.13", "29.13-30.71", "30.71-32.28", "32.28-33.86", "33.86-35.43", "35.43-37.01"],
          "Hip (inch)": ["33.07-34.65", "34.65-36.22", "36.22-37.8", "37.8-39.37", "39.37-40.94", "40.94-42.52", "42.52-44.09", "44.09-45.67"]
        }
      },
      Basic: {
        Size: ["XS", "S", "M", "L", "XL", "XXL", "3XL", "4XL"]
      },
      Country: {
        US: ["0-2", "4-6", "8-10", "12-14", "16-18", "20-22", "24-26", "28-30"],
        UK: ["6", "8", "10", "12", "14", "16", "18", "20"],
        EU: ["32", "34", "36", "38", "40", "42", "44", "46"],
        JP: ["5", "7", "9", "11", "13", "15", "17", "19"],
        AU: ["4", "6-8", "10-12", "14-16", "18-20", "22-24", "26-28", "30-32"],
        RU: ["40/42", "42/44", "46/48", "50/52", "54/56", "58/60", "62/64", "66/68"],
        BR: ["PP", "P", "M", "G", "GG", "XGG", "XGGG", "XGGGG"],
        IN: ["S", "M", "L", "XL", "XXL", "3XL", "4XL", "5XL"],
        CA: ["XS", "S", "M", "L", "XL", "XXL", "3XL", "4XL"],
        KR: ["44", "55", "66", "77", "88", "99", "110", "121"],
        NZ: ["6", "8", "10", "12", "14", "16", "18", "20"]
      }
    }
  },
  "men-top": {
    header: {
      CM: ["Bust (cm)", "Waist (cm)", "Hip (cm)"],
      IN: ["Bust (inch)", "Waist (inch)", "Hip (inch)"],
      Basic: ["Size"]
    },
    body: {
      Type: {
        CM: {
          "Bust (cm)": ["82-87", "88-93", "94-99", "100-105", "106-111", "112-117", "118-123", "124-129"],
          "Waist (cm)": ["66-71", "72-77", "78-83", "84-89", "90-95", "96-101", "102-107", "108-113"],
          "Hip (cm)": ["82-87", "88-93", "94-99", "100-105", "106-111", "112-117", "118-123", "124-129"]
        },
        IN: {
          "Bust (inch)": ["32.28-34.25", "34.65-36.61", "37.01-38.98", "39.37-41.34", "41.73-43.7", "44.09-46.06", "46.46-48.43", "48.82-50.79"],
          "Waist (inch)": ["25.98-27.95", "28.35-30.31", "30.71-32.68", "33.07-35.04", "35.43-37.4", "37.8-39.76", "40.16-42.13", "42.52-44.49"],
          "Hip (inch)": ["32.28-34.25", "34.65-36.61", "37.01-38.98", "39.37-41.34", "41.73-43.7", "44.09-46.06", "46.46-48.43", "48.82-50.79"]
        }
      },
      Basic: {
        Size: ["XS", "S", "M", "L", "XL", "XXL", "3XL", "4XL"]
      },
      Country: {
        US: ["34", "36", "38", "40", "42", "44", "46", "48"],
        UK: ["34", "36", "38", "40", "42", "44", "46", "48"],
        EU: ["44", "46", "48", "50", "52", "54", "56", "58"],
        JP: ["S", "M", "L", "LL", "3L", "4L", "5L", "6L"],
        AU: ["87", "92", "97", "102", "107", "112", "117", "122"],
        RU: ["44/46", "46/48", "50/52", "54/56", "58/60", "62/64", "66/68", "70/72"],
        BR: ["PP", "P", "M", "G", "GG", "XGG", "XGGG", "XGGGG"],
        IN: ["S", "M", "L", "XL", "XXL", "3XL", "4XL", "5XL"],
        CA: ["XS", "S", "M", "L", "XL", "XXL", "3XL", "4XL"],
        KR: ["90", "95", "100", "105", "110", "115", "120", "125"],
        NZ: ["S", "M", "L", "XL", "2XL", "3XL", "4XL", "5XL"]
      }
    }
  },
  "women-pants": {
    header: {
      CM: ["Waist Range (cm)", "Hip Range (cm)", "Inseam (cm)"],
      IN: ["Waist Range (inches)", "Hip Range (inches)", "Inseam (inches)"],
      Basic: ["Size"]
    },
    body: {
      Type: {
        CM: {
          "Waist Range (cm)": ["56-59", "58-61", "63-66", "68-71", "73-76", "78-81", "83-86", "88-91"],
          "Hip Range (cm)": ["81-84", "84-86", "89-91", "94-97", "99-102", "104-107", "109-112", "114-117"],
          "Inseam (cm)": ["97-99", "99-102", "102-104", "104-107", "107-109", "109-112", "112-114", "114-117"]
        },
        IN: {
          "Waist Range (inches)": ["22-23", "23-24", "25-26", "23-25", "25-27", "23-26", "25-28", "23-27"],
          "Hip Range (inches)": ["32-33", "33-34", "35-36", "37-38", "39-40", "41-42", "43-44", "45-46"],
          "Inseam (inches)": ["38-39", "40-41", "41-42", "42-43", "43-44", "44-45", "45-46", "-"]
        }
      },
      Basic: {
        Size: ["XXS", "XS", "S", "M", "L", "XL", "XXL", "XXXL"]
      },
      Country: {
        US: ["0", "2", "4", "6", "8", "10", "12", "14"],
        UK: ["2", "4", "6", "8", "10", "12", "14", "16"],
        EU: ["30/32", "30/34", "34/36", "36/38", "38/40", "40/42", "42/44", "44/46"],
        JP: ["3S", "5S", "7M", "9L", "11LL", "13EL", "15EE", "17"],
        AU: ["4", "6", "8", "10", "12", "14", "16", "18"],
        RU: ["38-40", "40-42", "42-44", "44-46", "46-48", "48-50", "50-52", "52-54"],
        BR: ["PP", "P", "M", "G", "GG", "XG", "XXG", "N/A"],
        IN: ["2", "4", "6", "8", "10", "12", "14", "16"],
        CA: ["0", "2", "4", "6", "8", "10", "12", "14"],
        KR: ["44", "55", "66", "77", "88", "99", "110", "121"],
        NZ: ["4S", "5S", "7M", "9L", "11LL", "13EL", "15EE", "17"]
      }
    }
  },
  "men-pants": {
    header: {
      CM: ["Waist Range (cm)", "Hip Range (cm)", "Inseam (cm)"],
      IN: ["Waist Range (inches)", "Hip Range (inches)", "Inseam (inches)"],
      Basic: ["Size"]
    },
    body: {
      Type: {
        CM: {
          "Waist Range (cm)": ["71-74", "76-79", "81-84", "86-89", "91-94", "97-99", "102-104"],
          "Hip Range (cm)": ["84-87", "89-92", "94-97", "99-102", "104-107", "109-112", "114-117"],
          "Inseam (cm)": ["76-79", "79-82", "82-84", "84-87", "87-89", "89-91", "91-94"]
        },
        IN: {
          "Waist Range (inches)": ["28-29", "30-31", "32-33", "34-35", "36-37", "38-39", "40-41"],
          "Hip Range (inches)": ["33-34", "35-36", "37-38", "39-40", "41-42", "43-44", "45-46"],
          "Inseam (inches)": ["30-31", "31-32", "32-33", "33-34", "34-35", "35-36", "36-37"]
        }
      },
      Basic: {
        Size: ["XS", "S", "M", "L", "XL", "XXL", "XXXL"]
      },
      Country: {
        US: ["28", "30", "32", "34", "36", "38", "40"],
        UK: ["28", "30", "32", "34", "36", "38", "40"],
        EU: ["44", "46", "48", "50", "52", "54", "56"],
        JP: ["28", "30", "32", "34", "36", "38", "40"],
        AU: ["28", "30", "32", "34", "36", "38", "40"],
        RU: ["44", "46", "48", "50", "52", "54", "56"],
        BR: ["PP", "P", "M", "G", "GG", "XG", "XXG"],
        IN: ["28", "30", "32", "34", "36", "38", "40"],
        CA: ["28", "30", "32", "34", "36", "38", "40"],
        KR: ["90", "95", "100", "105", "110", "115", "120"],
        NZ: ["28", "30", "32", "34", "36", "38", "40"]
      }
    }
  },
  "women-shoes": {
    header: {
      CM: ["Foot Length (cm)"],
      IN: ["Foot Length (inches)"],
      Basic: ["Size"]
    },
    body: {
      Type: {
        CM: {
          "Foot Length (cm)": ["22.0-22.5", "22.5-23.0", "23.0-23.5", "23.5-24.0", "24.0-24.5", "24.5-25.0", "25.0-25.5", "25.5-26.0", "26.0-26.5", "26.5-27.0", "27.0-27.5", "27.5-28.0"]
        },
        IN: {
          "Foot Length (inches)": ["8.66-8.86", "8.86-9.06", "9.06-9.25", "9.25-9.45", "9.45-9.65", "9.65-9.84", "9.84-10.04", "10.04-10.24", "10.24-10.43", "10.43-10.63", "10.63-10.83", "10.83-11.02"]
        }
      },
      Basic: {
        Size: ["35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46"]
      },
      Country: {
        US: ["4.5", "5", "6", "7", "8", "8.5", "9.5", "10", "10.5", "11.5", "12", "13"],
        UK: ["2.5", "3", "4", "5", "6", "6.5", "7.5", "8", "9", "10", "10.5", "11.5"],
        EU: ["35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "N/A"],
        JP: ["21.5", "22", "22.5", "23", "24", "24.5", "25", "25.5", "26", "27", "27.5", "N/A"],
        AU: ["3.5", "4", "5", "6", "7", "7.5", "8.5", "9", "10", "10.5", "11.5", "N/A"],
        RU: ["34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "N/A"],
        BR: ["33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "N/A"],
        IN: ["2.5", "3.5", "4", "5", "6", "6.5", "7.5", "8.5", "9.5", "10", "11", "N/A"],
        CA: ["4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "N/A"],
        KR: ["220", "225", "230", "235", "240", "245", "250", "255", "260", "265", "270", "N/A"],
        NZ: ["4", "5", "6", "7", "8", "8.5", "9.5", "10", "11", "11.5", "12", "N/A"]
      }
    }
  },
  "men-shoes": {
    header: {
      CM: ["Foot Length (cm)"],
      IN: ["Foot Length (inches)"],
      Basic: ["Size"]
    },
    body: {
      Type: {
        CM: {
          "Foot Length (cm)": ["23.0-23.5", "23.5-24.0", "24.0-24.5", "24.5-25.0", "25.0-25.5", "25.5-26.0", "26.0-26.5", "26.5-27.0", "27.0-27.5", "27.5-28.0", "28.0-28.5", "28.5-29.0", "29.0-29.5", "29.5-30.0"]
        },
        IN: {
          "Foot Length (inches)": ["9.06-9.25", "9.25-9.45", "9.45-9.65", "9.65-9.84", "9.84-10.04", "10.04-10.24", "10.24-10.43", "10.43-10.63", "10.63-10.83", "10.83-11.02", "11.02-11.22", "11.22-11.42", "11.42-11.61", "11.61-11.81"]
        }
      },
      Basic: {
        Size: ["36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49"]
      },
      Country: {
        US: ["5.5", "6", "6.5", "7", "7.5", "8", "8.5", "9", "9.5", "10", "10.5", "11", "11.5", "12"],
        UK: ["4.5", "5", "5.5", "6", "6.5", "7", "7.5", "8", "8.5", "9", "9.5", "10", "10.5", "11"],
        EU: ["36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49"],
        JP: ["22.5", "23", "23.5", "24", "24.5", "25", "25.5", "26", "26.5", "27", "27.5", "28", "28.5", "29"],
        AU: ["4.5", "5", "5.5", "6", "6.5", "7", "7.5", "8", "8.5", "9", "9.5", "10", "10.5", "11"],
        RU: ["35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48"],
        BR: ["34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "N/A", "N/A", "N/A"],
        IN: ["3.5", "4.5", "5.5", "6.5", "7.5", "8", "9", "10", "11", "12", "13", "14", "N/A", "N/A"],
        CA: ["5.5", "6", "7", "7.5", "8", "9", "9.5", "10", "10.5", "11", "12", "13", "14.5", "15"],
        KR: ["230", "235", "240", "245", "250", "255", "260", "265", "270", "275", "280", "285", "290", "295"],
        NZ: ["4", "5", "6", "7", "7.5", "8.5", "9", "10", "10.5", "11", "12", "13", "N/A", "N/A"]
      }
    }
  },
  ring: {
    header: {
      CM: ["Circumference (mm)", "diameter (mm)"],
      IN: ["Circumference (inches)", "diameter (inches)"],
      Basic: ["Chinese Size"]
    },
    body: {
      Type: {
        CM: {
          "Circumference (mm)": ["49.3", "51.8", "54.4", "56.9", "59.5", "62.1", "64.6", "67.2"],
          "diameter (mm)": ["15.7", "16.5", "17.3", "18.1", "18.9", "19.8", "20.6", "21.4"]
        },
        IN: {
          "Circumference (inches)": ["N/A", "N/A", "N/A", "N/A", "N/A", "N/A", "N/A", "N/A"],
          "diameter (inches)": ["N/A", "N/A", "N/A", "N/A", "N/A", "N/A", "N/A", "N/A"]
        }
      },
      Basic: {
        Size: ["9", "12", "14", "16", "18", "20", "23", "25"]
      },
      Country: {
        US: ["5", "6", "7", "8", "9", "10", "11", "12"],
        UK: ["J 1/2", "L 1/2", "O", "Q", "S", "T 1/2", "V 1/2", "Y"],
        JP: ["9", "12", "14", "16", "18", "20", "23", "25"],
        DE: ["15.75", "16.5", "17.25", "18", "19", "20", "20.75", "21.25"],
        FR: ["49", "51.5", "54", "56.5", "59", "61.5", "64", "66.5"],
        SW: ["9", "11.5", "14", "16.5", "19", "21.5", "24", "27.5"]
      }
    }
  }
}

// 计算属性 - 单位选项（固定内容，只改变禁用状态）
const unitOptions = computed(() => {
  return [
    {
      label: t('sizeGuide.units.cm'),
      value: 'CM',
      disable: false // cm 始终可用
    },
    {
      label: t('sizeGuide.units.in'),
      value: 'IN',
      disable: selectedCategory.value === 'ring' // 只有戒指类型禁用英寸
    }
  ]
})

// 国家选项（固定内容，只改变禁用状态）
const countryOptions = computed(() => {
  // 固定的所有国家列表
  const allCountries = [
    { label: t('sizeGuide.countries.US'), value: 'US' },
    { label: t('sizeGuide.countries.UK'), value: 'UK' },
    { label: t('sizeGuide.countries.EU'), value: 'EU' },
    { label: t('sizeGuide.countries.JP'), value: 'JP' },
    { label: t('sizeGuide.countries.AU'), value: 'AU' },
    { label: t('sizeGuide.countries.RU'), value: 'RU' },
    { label: t('sizeGuide.countries.BR'), value: 'BR' },
    { label: t('sizeGuide.countries.IN'), value: 'IN' },
    { label: t('sizeGuide.countries.CA'), value: 'CA' },
    { label: t('sizeGuide.countries.KR'), value: 'KR' },
    { label: t('sizeGuide.countries.NZ'), value: 'NZ' },
    { label: t('sizeGuide.countries.DE'), value: 'DE' },
    { label: t('sizeGuide.countries.FR'), value: 'FR' },
    { label: t('sizeGuide.countries.SW'), value: 'SW' }
  ]

  const currentData = SIZE_DATA[selectedCategory.value]

  // 根据当前服装类型设置禁用状态
  return allCountries.map(country => ({
    ...country,
    disable: !currentData || !currentData.body.Country[country.value]
  }))
})

// 商品类型选项（固定内容，不会改变）
const categoryOptions = computed(() => {
  // 固定的所有类型列表，不会动态改变
  return [
    { label: t('sizeGuide.categories.womenTop'), value: "women-top" },
    { label: t('sizeGuide.categories.menTop'), value: "men-top" },
    { label: t('sizeGuide.categories.womenPants'), value: "women-pants" },
    { label: t('sizeGuide.categories.menPants'), value: "men-pants" },
    { label: t('sizeGuide.categories.womenShoes'), value: "women-shoes" },
    { label: t('sizeGuide.categories.menShoes'), value: "men-shoes" },
    { label: t('sizeGuide.categories.ring'), value: "ring" }
  ]
})

// 表格列定义
const tableColumns = computed(() => {
  const currentData = SIZE_DATA[selectedCategory.value]
  if (!currentData) {
    return []
  }

  // 获取国家标签 - 使用国际化
  const countryLabel = t(`sizeGuide.countries.${selectedCountry.value}`) || selectedCountry.value

  const columns = [
    {
      name: 'size',
      label: 'Size',
      field: 'size',
      align: 'center'
    },
    {
      name: 'country',
      label: countryLabel,
      field: 'country',
      align: 'center'
    }
  ]

  // 添加测量部位列
  const typeData = currentData.body.Type[selectedUnit.value] || {}

  Object.keys(typeData).forEach(measurementType => {
    const fieldName = measurementType.toLowerCase().replace(/[^a-z]/g, '')
    columns.push({
      name: fieldName,
      label: measurementType,
      field: fieldName,
      align: 'center'
    })
  })

  return columns
})

// 表格数据
const tableRows = computed(() => {
  const currentData = SIZE_DATA[selectedCategory.value]
  if (!currentData) {
    return []
  }

  const sizes = currentData.body.Basic.Size || []
  const countrySizes = currentData.body.Country[selectedCountry.value] || []
  const typeData = currentData.body.Type[selectedUnit.value] || {}

  const rows = sizes.map((size, index) => {
    const row = {
      index,
      size,
      country: countrySizes[index] || '-'
    }

    // 添加测量数据
    Object.keys(typeData).forEach(measurementType => {
      const fieldName = measurementType.toLowerCase().replace(/[^a-z]/g, '')
      row[fieldName] = typeData[measurementType][index] || '-'
    })

    return row
  })

  return rows
})


</script>

<style lang="scss" scoped>
.size-guide-content {
  .selectors-row {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;

    @media (max-width: 599px) {
      flex-direction: column;

      .q-select {
        min-width: 100% !important;
      }
    }
  }

  .size-table-container {
    overflow-x: auto;

    .size-table {
      min-width: 600px;

      :deep(.q-table__top) {
        padding: 0;
      }

      :deep(th) {
        background-color: #f5f5f5;
        font-weight: 600;
        text-align: center;
      }

      :deep(td) {
        text-align: center;
        padding: 8px 12px;
      }

      :deep(tbody tr:nth-child(even)) {
        background-color: #fafafa;
      }
    }
  }

  .notice-text {
    line-height: 1.5;
    padding: 12px;
    background-color: #f9f9f9;
    border-radius: 4px;
    border-left: 3px solid #1976d2;
  }
}
</style>