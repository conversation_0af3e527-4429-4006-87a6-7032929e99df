<template>
  <div class="my-warehouse">
    <!-- 标题区域 -->
    <div class="bg-grey-2 q-px-md q-py-sm">
      <div class="row items-center">
        <q-icon name="warehouse" size="xs" color="blue-grey" class="q-mr-xs" />
        <span class="text-subtitle1 text-weight-medium">{{ $t('warehouse.myWarehouse') }}</span>
      </div>
    </div>

    <div class="q-pa-md">
      <div class="row justify-between items-center q-mb-md flex-wrap">
        <!-- 左侧可以放置筛选选项或其他控件 -->
        <div class="col-12 col-sm-auto">
          <!-- 这里可以添加筛选选项 -->
        </div>

        <!-- 搜索框 -->
        <div class="col-12 col-sm-auto q-mt-sm-none q-mt-md">
          <div class="search-container">
            <q-input v-model="state.spuName" outlined dense clearable :placeholder="$t('warehouse.searchPlaceholder')" @keyup.enter="handleSearch" class="search-input">
              <template #append>
                <q-btn flat dense icon="search" @click="handleSearch" />
              </template>
            </q-input>
          </div>
        </div>
      </div>

      <q-separator />

      <!-- 库存列表 -->
      <div class="q-mt-md">
        <!-- 桌面视图表格 -->
        <div v-if="!$q.screen.lt.sm">
          <q-table
            :rows="tableData"
            :columns="columns"
            row-key="id"
            selection="multiple"
            v-model:selected="selected"
            :pagination="pagination"
            :visible-columns="showCols"
            :loading="loading"
            binary-state-sort
            hide-pagination
            spinner-color="primary"
            class="warehouse-table"
            dense>
            <template #loading>
              <div class="absolute-full bg-white bg-opacity-60 flex flex-center" style="z-index: 1">
                <div class="column items-center">
                  <q-spinner color="primary" size="3em" />
                  <div class="q-mt-sm text-primary text-body1">{{ $t('warehouse.loading') }}</div>
                </div>
              </div>
            </template>

            <!-- 自定义表头模板 -->
            <template #header="props">
              <q-tr :props="props">
                <q-th auto-width>
                  <q-checkbox size="xs" v-model="props.selected" />
                </q-th>
                <q-th v-for="col in props.cols" :key="col.name" :props="props">
                  {{ col.label }}
                </q-th>
              </q-tr>
            </template>

            <template #body="props">
              <q-tr>
                <q-td auto-width>
                  <q-checkbox v-model="props.selected" size="xs" />
                </q-td>
                <q-td>
                  <div class="row items-start q-ml-sm">
                    <q-img :src="getThumbnailUrl(props.row.picUrl, '80x80')" :alt="$t('warehouse.productImage')" width="80px" height="80px" class="rounded-borders q-mr-sm" spinner-color="primary" />
                    <div class="column no-wrap" style="width: 300px; overflow: hidden">
                      <div class="text-weight-bold text-dark q-mb-xs ellipsis-3-lines" style="height: 60px; line-height: 20px">
                        {{ props.row.spuName }}
                      </div>
                      <div class="text-caption text-grey-8">{{ formattedProperties(props.row.properties) }}</div>
                    </div>
                  </div>
                </q-td>
                <q-td key="count" :props="props">
                  {{ props.row.count }}
                </q-td>
                <q-td key="weight" :props="props">
                  {{ props.row.weight }}
                </q-td>
                <q-td key="volume" :props="props">
                  {{ props.row.volume }}
                </q-td>
                <q-td key="inTime" :props="props">
                  {{ formatTimestampToWesternDate(props.row.inTime) }}
                </q-td>
                <q-td key="expiredTime" :props="props">
                  {{ formatTimestampToWesternDate(props.row.expiredTime) }}
                </q-td>
                <q-td key="action" :props="props" class="text-center">
                  <q-btn flat round dense color="primary" icon="visibility" size="sm" @click.stop="viewStockDetail(props.row.id)" />
                </q-td>
              </q-tr>
            </template>
          </q-table>

          <!-- PC端分页控件 -->
          <div class="row justify-between items-center q-mt-md flex-wrap" v-if="pagination.rowsNumber > 0">
            <div class="col-12 col-sm-auto q-mb-sm q-mb-sm-none">
              <div class="row justify-end justify-sm-start">
                <div class="text-caption text-grey-8">
                  {{ $t('warehouse.totalRecords', { total: pagination.rowsNumber, current: pagination.page, pages: Math.ceil(pagination.rowsNumber / pagination.rowsPerPage) || 1 }) }}
                </div>
              </div>
            </div>
            <div class="col-12 col-sm-auto">
              <div class="row justify-end">
                <q-pagination
                  v-model="pagination.page"
                  :max="Math.ceil(pagination.rowsNumber / pagination.rowsPerPage)"
                  :max-pages="$q.screen.lt.sm ? 3 : 6"
                  boundary-links
                  direction-links
                  :dense="$q.screen.lt.sm"
                  @update:model-value="onPageChange" />
              </div>
            </div>
          </div>
        </div>

        <!-- 移动端卡片视图 -->
        <div v-else class="mobile-warehouse-list">
          <div v-if="loading" class="column items-center q-py-xl">
            <q-spinner color="primary" size="3em" />
            <div class="q-mt-sm text-grey-7">{{ $t('warehouse.loading') }}</div>
          </div>

          <div v-else-if="tableData.length === 0" class="column items-center q-py-xl">
            <q-icon name="inventory_2" size="3em" color="grey-5" />
            <div class="text-grey-7 q-mt-sm">{{ $t('warehouse.noStockItems') }}</div>
          </div>

          <div v-else>
            <q-card v-for="item in tableData" :key="item.id" class="warehouse-card q-mb-md">
              <q-card-section class="q-py-sm">
                <div class="row justify-between items-center">
                  <q-checkbox v-model="selected" :val="item" size="xs" />
                  <q-badge color="primary" class="q-px-sm q-py-xs">
                    {{ formatTimestampToWesternDate(item.inTime) }}
                  </q-badge>
                </div>
              </q-card-section>

              <q-separator />

              <q-card-section class="q-pa-md">
                <div class="row no-wrap">
                  <!-- 商品图片 -->
                  <div class="product-img">
                    <q-img :src="getThumbnailUrl(item.picUrl, '80x80')" style="width: 80px; height: 80px" class="rounded-borders" spinner-color="primary" :alt="$t('warehouse.productImage')" />
                  </div>

                  <!-- 商品信息 -->
                  <div class="product-info q-ml-md">
                    <div class="text-weight-medium ellipsis-2-lines">{{ item.spuName }}</div>
                    <div class="text-caption text-grey-7 q-mt-xs">{{ formattedProperties(item.properties) }}</div>
                    <div class="row justify-between items-center q-mt-sm">
                      <div class="text-caption">
                        {{ $t('warehouse.quantity') }}: <span class="text-weight-medium">{{ item.count }}</span>
                      </div>
                      <div class="text-caption">
                        {{ $t('warehouse.weight') }}: <span class="text-weight-medium">{{ item.weight }}</span>
                      </div>
                    </div>
                    <div class="row justify-between items-center q-mt-xs">
                      <div class="text-caption">
                        {{ $t('warehouse.volume') }}: <span class="text-weight-medium">{{ item.volume }}</span>
                      </div>
                      <div class="text-caption">
                        {{ $t('warehouse.expiry') }}: <span class="text-weight-medium">{{ formatTimestampToWesternDate(item.expiredTime) }}</span>
                      </div>
                    </div>
                    <div class="row justify-end q-mt-sm">
                      <q-btn flat dense color="primary" icon="visibility" size="sm" :label="$t('warehouse.viewDetails')" @click.stop="viewStockDetail(item.id)" />
                    </div>
                  </div>
                </div>
              </q-card-section>
            </q-card>
          </div>

          <!-- 移动端分页 -->
          <div class="row justify-center q-mt-md" v-if="tableData.length > 0">
            <q-pagination
              v-model="pagination.page"
              :max="Math.ceil(pagination.rowsNumber / pagination.rowsPerPage)"
              :max-pages="3"
              boundary-links
              direction-links
              dense
              @update:model-value="onPageChange" />
          </div>
        </div>

        <!-- 重量汇总和提交按钮部分 -->
        <div class="q-mt-md">
          <!-- PC端视图 -->
          <div v-if="!$q.screen.lt.sm">
            <!-- 重量信息部分 -->
            <div class="q-mb-sm text-right">
              <div class="text-body1">
                {{ $t('warehouse.totalItemWeight') }}: <span class="text-weight-medium">{{ calculateTotalWeight() }}g</span>
              </div>
              <div class="text-body1">
                {{ $t('warehouse.estimatedPackagingWeight') }}: <span class="text-weight-medium">{{ calculatePackagingWeight() }}g</span>
              </div>
            </div>

            <!-- 余额和提交部分 -->
            <div class="q-pa-md bg-blue-1 rounded-borders row justify-between items-center">
              <div class="text-left row items-center">
                <q-checkbox v-model="selectAll" size="xs" :label="$t('warehouse.selectAll')" class="q-mr-xl select-all-checkbox" />
                <div class="text-body1">
                  {{ $t('warehouse.balance') }}: <span class="text-weight-medium text-primary">$ {{ userBalance }}</span>
                </div>
              </div>
              <div class="text-right">
                <div class="text-body1">
                  {{ $t('warehouse.estimatedParcelWeight') }}: <span class="text-weight-medium">{{ calculateTotalParcelWeight() }}kg</span>
                </div>
              </div>
              <div>
                <q-btn icon-left="local_shipping" :label="$t('warehouse.submitParcel')" color="primary" class="submit-btn" :disable="selected.length === 0" @click="handleSubmit" />
              </div>
            </div>
          </div>

          <!-- 移动端视图 -->
          <div v-else class="mobile-action-area">
            <!-- 重量信息部分 -->
            <q-card class="q-mb-md">
              <q-card-section>
                <div class="text-subtitle2 text-weight-medium q-mb-sm">{{ $t('warehouse.weightInfo') }}</div>
                <div class="row justify-between q-mb-xs">
                  <div>{{ $t('warehouse.totalItemWeight') }}:</div>
                  <div class="text-weight-medium">{{ calculateTotalWeight() }}kg</div>
                </div>
                <div class="row justify-between q-mb-xs">
                  <div>{{ $t('warehouse.estimatedPackagingWeight') }}:</div>
                  <div class="text-weight-medium">{{ calculatePackagingWeight() }}kg</div>
                </div>
                <div class="row justify-between text-primary">
                  <div>{{ $t('warehouse.estimatedParcelWeight') }}:</div>
                  <div class="text-weight-medium">{{ calculateTotalParcelWeight() }}kg</div>
                </div>
              </q-card-section>
            </q-card>
          </div>

          <!-- 移动端底部固定操作栏 -->
          <div v-if="$q.screen.lt.sm" class="mobile-fixed-bottom bg-white q-pa-sm">
            <div class="row items-center justify-between">
              <div class="row items-center">
                <q-checkbox v-model="selectAll" size="xs" :label="$t('warehouse.selectAll')" class="select-all-checkbox" />
                <div class="q-ml-md">
                  <div class="text-caption">{{ $t('warehouse.balance') }}:</div>
                  <div class="text-weight-medium text-primary">$ {{ userBalance }}</div>
                </div>
              </div>
              <q-btn icon-left="local_shipping" :label="$t('warehouse.submitParcel')" color="primary" class="submit-btn-mobile" :disable="selected.length === 0" @click="handleSubmit" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import StockApi from '~/composables/stockApi';
import { formattedProperties } from '~/utils/productUtils';
import { useQuasar } from 'quasar';
import { useI18n } from 'vue-i18n';
import { useParcelStore } from '../../store/parcel';

definePageMeta({
  middleware: 'auth', // 引入身份验证中间件
});

const $q = useQuasar();
const parcelStore = useParcelStore();
const { t } = useI18n();

// 用户余额（实际应用中通常从 API 获取）
const userBalance = ref(0);

// 可以在这里添加表格显示选项

const loading = ref(false);

// 统一的分页对象，用于 Quasar 表格和 API
const pagination = ref({
  sortBy: 'desc',
  descending: false,
  page: 1,
  rowsPerPage: 5, // 默认每页数量
  rowsNumber: 0, // 总行数（将从 API 更新）
});

// 表格数据列表
const tableData = ref([]);

// 搜索参数
const state = reactive({
  loadStatus: '',
  spuName: '',
});

const selected = ref([]);

const columns = [
  { name: 'selection', label: '11', align: 'center', field: 'selection', required: false },
  { name: 'spuName', label: t('warehouse.productName'), align: 'left', field: 'spuName', required: false },
  { name: 'pic', label: t('warehouse.image'), align: 'left', field: 'picUrl', required: false },
  { name: 'detail', align: 'center', label: t('warehouse.productDetails'), sortable: false },
  { name: 'id', align: 'center', label: 'ID', field: 'id', sortable: false, hide: true, required: false },
  { name: 'count', align: 'center', label: t('warehouse.quantity'), field: 'count', sortable: false },
  { name: 'weight', align: 'center', label: t('warehouse.weight'), field: 'weight', sortable: false },
  { name: 'volume', align: 'center', label: t('warehouse.volume'), field: 'volume', sortable: false },
  { name: 'status', align: 'center', label: t('warehouse.status'), field: 'status', sortable: false },
  { name: 'inTime', align: 'center', label: t('warehouse.inboundDate'), field: 'inTime', sortable: false },
  { name: 'expiredTime', align: 'center', label: t('warehouse.expiryDate'), field: 'expiredTime', sortable: false },
  { name: 'action', align: 'center', label: t('warehouse.actions') },
];

const showCols = ['detail', 'count', 'weight', 'volume', 'inTime', 'expiredTime', 'action'];

onMounted(() => {
  getStockList();
  // 模拟获取用户余额 - 在真实应用中，这将是一个 API 调用
  userBalance.value = 0; // 示例值
});

// 获取库存列表
async function getStockList(page = pagination.value.page, rowsPerPage = pagination.value.rowsPerPage) {
  loading.value = true;
  state.loadStatus = 'loading';

  const { code, data } = await StockApi.getStockPage({
    pageNo: page,
    pageSize: rowsPerPage,
    spuName: state.spuName,
  });

  loading.value = false;

  if (code === 0) {
    // 更新表格数据
    tableData.value = data.list || [];

    // 更新分页信息
    pagination.value.rowsNumber = data.total;

    console.log('获取的数据:', tableData.value);
  }
}

// 处理分页切换
function onPageChange(page) {
  pagination.value.page = page; // 更新当前页码
  getStockList(page, pagination.value.rowsPerPage); // 刷新当前页数据
}

// 计算选中物品的总重量
function calculateTotalWeight() {
  if (!selected.value || selected.value.length === 0) return '0';

  const totalWeight = selected.value.reduce((sum, item) => {
    return sum + (parseInt(item.weight) || 0);
  }, 0);

  return totalWeight;
}

// 计算包装材料重量（估计为总重量的 10%）
function calculatePackagingWeight() {
  // const totalWeight = parseFloat(calculateTotalWeight());
  // const packagingWeight = totalWeight * 0.1; // 包装重量为总重量的 10%

  // return packagingWeight.toFixed(2);

  if (!selected.value || selected.value.length === 0) return '0';

  const totalPrePackageWeight = selected.value.reduce((sum, item) => {
    return sum + (parseInt(item.prePackageWeight) || 0);
  }, 0);

  return totalPrePackageWeight;
}

// 计算包装总重量（物品 + 包装）
function calculateTotalParcelWeight() {
  const totalWeight = parseFloat(calculateTotalWeight());
  const packagingWeight = parseFloat(calculatePackagingWeight());

  return (totalWeight + packagingWeight).toFixed(2);
}

// 处理搜索按钮点击
function handleSearch() {
  // 搜索时重置分页到第一页
  pagination.value.page = 1;
  getStockList();
}

// 全选/取消全选功能
const selectAll = ref(false);

// 监听 selectAll 变化
watch(selectAll, (newVal) => {
  if (newVal) {
    // 全选
    selected.value = [...tableData.value];
  } else {
    // 取消全选
    selected.value = [];
  }
});

// 监听 selected 变化
watch(
  selected,
  (newVal) => {
    // 如果选中数量等于表格数据数量，则设置全选为 true
    if (newVal.length === tableData.value.length && tableData.value.length > 0) {
      selectAll.value = true;
    } else {
      selectAll.value = false;
    }
  },
  { deep: true }
);

// 处理提交按钮点击
function handleSubmit() {
  if (selected.value.length === 0) return;
  // 获取选中项目的 ID
  const selectedIds = selected.value.map((item) => ({ stockId: item.id, count: item.count, maxCount: item.count }));
  // 保存选中的商品ID到store
  parcelStore.selectProducts = selectedIds;
  // 跳转到创建包裹页面
  navigateTo('/account/parcel-create');
}

// 查看库存详情
function viewStockDetail(id) {
  if (!id) return;
  navigateTo(`/account/stock-detail?id=${id}`);
}
</script>

<style lang="scss" scoped>
.my-warehouse {
  /* 多行文本省略号样式 */
  .ellipsis-3-lines {
    display: -webkit-box;
    -webkit-line-clamp: 3; /* 限制为3行 */
    line-clamp: 3; /* 标准属性以提高兼容性 */
    -webkit-box-orient: vertical;
    box-orient: vertical; /* 标准属性以提高兼容性 */
    overflow: hidden;
    text-overflow: ellipsis; /* 超出部分显示省略号 */
    word-break: keep-all;
    white-space: normal; /* 允许换行 */
  }

  .ellipsis-2-lines {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    min-height: 2em;
    line-height: 1.2;
  }

  /* 表格样式 */
  .warehouse-table {
    :deep(.q-table th) {
      font-size: 14px; /* 表头文字大小调整为14px */
      padding: 8px 4px; /* 减小表头填充 */
    }

    :deep(.q-table td) {
      padding: 8px 4px; /* 减小表格单元格填充 */
    }
  }

  :deep(.q-checkbox) {
    font-size: 0.8em; /* 选择框小号 */
  }

  /* 全选框标签样式 */
  .select-all-checkbox :deep(.q-checkbox__label) {
    font-size: 1rem; /* 与 text-body1 一致的字体大小 */
    line-height: 1.5rem;
    font-weight: 400;
  }

  /* 搜索框和按钮容器 */
  .search-container {
    display: flex;
    align-items: center;
    width: 100%;

    @media (max-width: 599px) {
      justify-content: space-between;
    }
  }

  /* 搜索框样式 */
  .search-input {
    width: 100%;
    max-width: 280px;
    flex: 1;

    @media (max-width: 599px) {
      max-width: none;
      flex: 1;
    }

    :deep(.q-field__control) {
      height: 36px;
      min-height: 36px;
    }
  }

  /* 提交按钮样式 */
  .submit-btn {
    border-radius: 4px;
    font-weight: 500;
    padding: 8px 24px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 3px 5px rgba(0, 0, 0, 0.2);
      transform: translateY(-1px);
    }
  }

  .submit-btn-mobile {
    border-radius: 4px;
    font-weight: 500;
    padding: 6px 16px;
  }

  /* 移动端卡片样式 */
  .mobile-warehouse-list {
    .warehouse-card {
      border-radius: 8px;
      overflow: hidden;
      transition: all 0.2s ease;

      &:hover {
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
      }

      .product-img {
        width: 80px;
        flex-shrink: 0;
      }

      .product-info {
        flex: 1;
        min-width: 0;
      }
    }
  }

  /* 移动端底部固定操作栏 */
  .mobile-fixed-bottom {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.1);
    padding-bottom: env(safe-area-inset-bottom, 0);
    padding-bottom: constant(safe-area-inset-bottom, 0); /* iOS 11.0 */
    border-top: 1px solid rgba(0, 0, 0, 0.05);
  }

  /* 移动端底部空间 - 为固定底栏留出空间 */
  .mobile-action-area {
    margin-bottom: 60px;
  }

  /* 确保页面内容不被底部固定栏遮挡 */
  .mobile-warehouse-list {
    padding-bottom: 70px;
  }

  /* 响应式辅助类 */
  .q-mt-sm-none {
    @media (min-width: 600px) {
      margin-top: 0;
    }
  }
}
</style>
