<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Payment Result</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      background-color: #f9f9f9;
      font-family: Arial, sans-serif;
    }

    .container {
      /* margin-top: -200px; */
      text-align: center;
      padding: 40px;
      background: #ffffff;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
      border-radius: 10px;
      width: 80%;
      max-width: 300px;
    }

    .container h1 {
      font-size: 28px;
      margin-bottom: 20px;
    }

    .container h1.success {
      color: #28a745;
    }

    .container h1.cancelled {
      color: #ff4d4f;
    }

    .container button {
      background-color: #007bff;
      color: white;
      border: none;
      padding: 12px 24px;
      font-size: 18px;
      border-radius: 5px;
      cursor: pointer;
      margin-top: 20px;
      transition: background-color 0.3s ease;
    }

    .container button:hover {
      background-color: #0056b3;
    }

    @media (max-width: 768px) {
      .container h1 {
        font-size: 24px;
      }

      .container button {
        font-size: 16px;
        padding: 10px 20px;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <h1 id="result-text" class="cancelled"></h1>
    <button onclick="handleClose()">Confirm</button>
  </div>

  <script>
    // 获取 URL 参数
    const urlParams = new URLSearchParams(window.location.search);
    const type = urlParams.get('t');

    // 根据 type 参数更新页面内容
    const resultText = document.getElementById('result-text');
    if (type === '1') {
      resultText.textContent = 'Payment Completed ';
      resultText.className = 'success';
    } else if (type === '2') {
      resultText.textContent = 'Payment Canceled';
      resultText.className = 'cancelled';
    } else {
      resultText.textContent = 'Unknown Status';
      resultText.className = 'cancelled';
    }

    function handleClose() {
      window.opener?.postMessage(type === '1' ? 'payment-completed' : 'payment-cancelled', '*');
      window.close();
    }
  </script>
</body>
</html>
