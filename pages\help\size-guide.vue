<template>
  <div class="size-guide-page">
    <!-- 页面头部 -->
    <div class="page-header q-pa-lg bg-grey-1">
      <div class="container">
        <h1 class="text-h4 text-weight-bold q-mb-md">{{ $t('sizeGuide.title') }}</h1>
        <p class="text-body1 text-grey-7 q-mb-none">{{ $t('sizeGuide.description') }}</p>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="page-content q-pa-lg">
      <div class="container">
        <!-- 尺码助手工具 -->
        <q-card class="q-mb-xl">
          <q-card-section class="bg-primary text-white">
            <div class="text-h6">{{ $t('sizeGuide.toolTitle') }}</div>
            <div class="text-body2">{{ $t('sizeGuide.toolDescription') }}</div>
          </q-card-section>
          
          <q-card-section>
            <SizeGuideContent />
          </q-card-section>
        </q-card>

        <!-- 使用说明 -->
        <q-card class="q-mb-xl">
          <q-card-section class="bg-grey-2">
            <div class="text-h6">{{ $t('sizeGuide.howToUse.title') }}</div>
          </q-card-section>
          
          <q-card-section>
            <div class="usage-steps">
              <div 
                v-for="(step, index) in usageSteps" 
                :key="index"
                class="step-item q-mb-md"
              >
                <div class="step-number">{{ index + 1 }}</div>
                <div class="step-content">
                  <div class="step-title text-weight-medium">{{ step.title }}</div>
                  <div class="step-description text-grey-7">{{ step.description }}</div>
                </div>
              </div>
            </div>
          </q-card-section>
        </q-card>

        <!-- 测量指南 -->
        <q-card class="q-mb-xl">
          <q-card-section class="bg-grey-2">
            <div class="text-h6">{{ $t('sizeGuide.measureGuide.title') }}</div>
          </q-card-section>
          
          <q-card-section>
            <div class="measure-tips">
              <div 
                v-for="(tip, index) in measureTips" 
                :key="index"
                class="tip-item q-mb-md"
              >
                <q-icon :name="tip.icon" color="primary" size="24px" class="q-mr-sm" />
                <div>
                  <div class="tip-title text-weight-medium">{{ tip.title }}</div>
                  <div class="tip-description text-grey-7">{{ tip.description }}</div>
                </div>
              </div>
            </div>
          </q-card-section>
        </q-card>

        <!-- 注意事项 -->
        <q-card>
          <q-card-section class="bg-orange-1">
            <div class="text-h6 text-orange-8">
              <q-icon name="warning" class="q-mr-sm" />
              {{ $t('sizeGuide.important.title') }}
            </div>
          </q-card-section>
          
          <q-card-section>
            <ul class="important-notes">
              <li v-for="(note, index) in importantNotes" :key="index" class="q-mb-sm">
                {{ note }}
              </li>
            </ul>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import SizeGuideContent from '~/components/SizeGuideContent.vue'
import { useI18n } from 'vue-i18n'
import { computed } from 'vue'

const { t } = useI18n()

// 页面元数据
useHead({
  title: computed(() => t('sizeGuide.title')),
  meta: [
    {
      name: 'description',
      content: computed(() => t('sizeGuide.description'))
    }
  ]
})

// 使用步骤
const usageSteps = computed(() => [
  {
    title: t('sizeGuide.howToUse.step1.title'),
    description: t('sizeGuide.howToUse.step1.description')
  },
  {
    title: t('sizeGuide.howToUse.step2.title'),
    description: t('sizeGuide.howToUse.step2.description')
  },
  {
    title: t('sizeGuide.howToUse.step3.title'),
    description: t('sizeGuide.howToUse.step3.description')
  },
  {
    title: t('sizeGuide.howToUse.step4.title'),
    description: t('sizeGuide.howToUse.step4.description')
  }
])

// 测量指南
const measureTips = computed(() => [
  {
    icon: 'straighten',
    title: t('sizeGuide.measureGuide.tip1.title'),
    description: t('sizeGuide.measureGuide.tip1.description')
  },
  {
    icon: 'accessibility',
    title: t('sizeGuide.measureGuide.tip2.title'),
    description: t('sizeGuide.measureGuide.tip2.description')
  },
  {
    icon: 'schedule',
    title: t('sizeGuide.measureGuide.tip3.title'),
    description: t('sizeGuide.measureGuide.tip3.description')
  }
])

// 重要注意事项
const importantNotes = computed(() => [
  t('sizeGuide.important.note1'),
  t('sizeGuide.important.note2'),
  t('sizeGuide.important.note3'),
  t('sizeGuide.important.note4')
])
</script>

<style lang="scss" scoped>
.size-guide-page {
  min-height: 100vh;
  background-color: #fafafa;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  border-bottom: 1px solid #e0e0e0;
}

.usage-steps {
  .step-item {
    display: flex;
    align-items: flex-start;
    
    .step-number {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background-color: #1976d2;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      margin-right: 16px;
      flex-shrink: 0;
    }
    
    .step-content {
      flex: 1;
      
      .step-title {
        margin-bottom: 4px;
      }
    }
  }
}

.measure-tips {
  .tip-item {
    display: flex;
    align-items: flex-start;
    
    .tip-title {
      margin-bottom: 4px;
    }
  }
}

.important-notes {
  margin: 0;
  padding-left: 20px;
  
  li {
    line-height: 1.6;
  }
}

@media (max-width: 599px) {
  .page-header,
  .page-content {
    padding: 16px;
  }
  
  .container {
    padding: 0;
  }
}
</style>
