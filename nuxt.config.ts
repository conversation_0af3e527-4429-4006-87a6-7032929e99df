/**
 * Nuxt 3 配置文件
 *
 * 电商购物网站配置
 * - 支持多语言、多货币
 * - 集成Quasar UI组件库
 * - 配置Pinia状态管理
 * - 优化SEO和性能
 *
 * @see https://nuxt.com/docs/api/configuration/nuxt-config
 */
export default defineNuxtConfig({
  // Nuxt兼容性日期
  compatibilityDate: '2024-11-01',

  // 开发工具配置 - 仅在开发环境启用
  devtools: { enabled: true },

  // 开发服务器配置
  devServer: {
    port: 3001, // 开发服务器端口
    host: '0.0.0.0', // 允许外部访问（局域网访问）
  },

  // Vite构建工具配置
  vite: {
    // 开发服务器配置
    server: {
      hmr: {
        clientPort: 3001, // HMR热更新端口，需与devServer端口一致
      },
    },

    // 全局变量定义 - 在构建时注入环境变量到客户端代码
    define: {
      'globalConfig.tenantId': JSON.stringify(process.env.TENANT_ID),
      'globalConfig.sk': JSON.stringify(process.env.SK),
      'globalConfig.payHide': process.env.PAY_HIDE === 'true',
      'globalConfig.payHideUrl': JSON.stringify(process.env.PAY_HIDE_URL),
      'globalConfig.payReturnUrl': JSON.stringify(process.env.PAY_RETURN_URL),
      'globalConfig.enableCaptcha': process.env.ENABLE_CAPTCHA === 'true',
      'globalConfig.turnstileKey': JSON.stringify(process.env.CAPTCHA_SITE_KEY),
    },

    // CSS预处理器配置
    css: {
      preprocessorOptions: {
        scss: {
          // 全局注入SCSS变量文件，所有组件都可以使用这些变量
          additionalData: '@use "@/assets/scss/_variables.scss" as *;',
        },
      },
    },
  },

  // 运行时配置 - 可在客户端和服务端访问的配置
  runtimeConfig: {
    public: {
      // API服务配置
      baseUrl: process.env.SERVER_BASE_URL || '',
      apiPath: process.env.SERVER_API_PATH || '',

      // 应用基础配置
      shopName: process.env.SHOP_NAME || 'CNFans',
      webUrl: process.env.WEB_URL || 'http://localhost:3001',
      supportEmail: process.env.SUPPORT_EMAIL || '<EMAIL>',

      // 验证码配置
      enableCaptcha: String(process.env.ENABLE_CAPTCHA).trim().toLowerCase() === 'true',
      turnstile: {
        siteKey: process.env.CAPTCHA_SITE_KEY,
        addValidateEndpoint: true,
      },
    },
  },
  // 路由规则 - 控制每个路由的渲染模式和缓存策略
  routeRules: {
    // 首页 - SPA模式，提升交互性能
    '/': { ssr: false },

    // 用户相关页面 - SPA模式，需要登录状态
    '/account/**': { ssr: false },
    '/order/**': { ssr: false },
    '/pay/**': { ssr: false },
    '/cart': { ssr: false },
    '/login': { ssr: false },
    '/loading': { ssr: false },
    '/product/**': { ssr: false },
    '/products/**': { ssr: false },
    '/shipping-calculator': { ssr: false },
    '/notice/**': { ssr: false },
    '/activities': { ssr: false },

    // 搜索和聊天 - SPA模式，实时交互
    '/search/**': { ssr: false },
    '/chat/**': { ssr: false },
    '/diy-order': { ssr: false },

    // 帮助页面 - 可以考虑启用SSR提升SEO
    '/help/**': { ssr: false },

    // 静态页面 - 可以启用SSR和静态生成
    // '/about': { ssr: true, prerender: true },
    // '/privacy': { ssr: true, prerender: true },
    // '/terms': { ssr: true, prerender: true },
  },
  // 应用配置 - HTML头部和全局设置
  app: {
    head: {
      // 全局脚本 - 图标字体脚本
      script: [{ src: '/fonts/iconfont.js', type: 'text/javascript' }],
      // 全局链接 - 可以在这里添加favicon等
      link: [
        // 如果需要额外的字体或样式链接可以在这里添加
      ],
    },
  },

  // 全局CSS文件 - 按加载顺序排列
  css: [
    '~/assets/scss/main.scss', // 主样式文件
    '~/assets/scss/custom.scss', // 自定义样式
    '~/assets/fonts/iconfont.css', // 图标字体样式
    'quasar/dist/quasar.prod.css', // Quasar UI样式
  ],
  // Nuxt模块配置 - 扩展Nuxt功能的模块
  modules: [
    '@pinia/nuxt', // Pinia状态管理
    '@pinia-plugin-persistedstate/nuxt', // Pinia状态持久化
    '@nuxtjs/turnstile', // Cloudflare Turnstile验证码
    'nuxt-quasar-ui', // Quasar UI组件库
    '@nuxt/eslint', // ESLint代码检查
  ],
  // Quasar UI配置 - UI组件库设置
  quasar: {
    // Quasar插件 - 按需加载的功能插件
    plugins: [
      'BottomSheet', // 底部弹出层
      'Dialog', // 对话框
      'Loading', // 加载指示器
      'LoadingBar', // 顶部加载条
      'Notify', // 通知消息
      'Dark', // 暗色主题
    ],

    // 额外资源配置
    extras: {
      // 图标字体 - 选择需要的图标库
      fontIcons: [
        'material-icons', // Material Design图标
        'ionicons-v4', // Ionicons图标
        'fontawesome-v6', // FontAwesome图标
      ],
    },

    // 组件默认配置
    components: {
      defaults: {
        QBtn: {
          // 可以在这里设置按钮的默认属性
          // unelevated: true,
        },
      },
    },
  },

  // 插件配置 - 自定义插件
  plugins: [
    // 客户端通知插件
    { src: './plugins/showNotify.client.js', mode: 'client' },
    // 全局配置插件
    { src: './plugins/globalConfig.js' },
    // 性能监控插件（仅客户端）
    { src: './plugins/performance-monitoring.client.js', mode: 'client' },
  ],
});
