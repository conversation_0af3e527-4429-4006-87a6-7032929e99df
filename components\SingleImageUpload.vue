<template>
  <div class="single-image-upload">
    <!-- 已上传的图片 -->
    <div v-if="imageUrl && !uploading" class="uploaded-image-container">
      <q-img
        :src="imageUrl"
        :ratio="ratio"
        class="uploaded-image"
        @click="previewImage"
        fit="cover"
      >
        <template v-slot:loading>
          <div class="absolute-full flex flex-center">
            <q-spinner-facebook color="primary" size="2em" />
          </div>
        </template>

        <template v-slot:error>
          <div class="absolute-full bg-grey-3 flex flex-center column">
            <q-icon name="broken_image" color="grey-6" size="2em" />
            <div class="text-caption text-grey-6 q-mt-xs">加载失败</div>
          </div>
        </template>
      </q-img>

      <!-- 删除按钮 -->
      <q-btn
        v-if="!disabled"
        icon="close"
        size="xs"
        round
        color="negative"
        class="delete-btn"
        @click="removeImage"
      />
    </div>

    <!-- 上传中状态 -->
    <div v-else-if="uploading" class="upload-placeholder">
      <q-spinner-facebook color="primary" size="3em" />
      <div class="text-caption q-mt-sm">上传中...</div>
    </div>

    <!-- 上传按钮 -->
    <div v-else class="upload-button" @click="triggerFileInput">
      <q-icon name="add_photo_alternate" size="2em" color="primary" />
      <div class="text-caption q-mt-sm">点击上传图片</div>
    </div>

    <!-- 隐藏的文件输入 -->
    <input
      ref="fileInputRef"
      type="file"
      :accept="accept"
      style="display: none"
      @change="onFileInputChange"
    />

    <!-- 图片预览对话框 -->
    <q-dialog v-model="previewDialog" maximized>
      <q-card class="preview-card">
        <q-card-section class="q-pa-none flex flex-center" style="height: 100vh; background: rgba(0,0,0,0.8);">
          <q-img
            :src="imageUrl"
            style="max-width: 95vw; max-height: 95vh; object-fit: contain;"
            fit="contain"
            @click="previewDialog = false"
          />
        </q-card-section>
        <q-card-actions class="absolute-top-right q-pa-md">
          <q-btn
            flat
            round
            icon="close"
            color="white"
            size="lg"
            @click="previewDialog = false"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
import { ref, watch, computed } from 'vue';
import { useQuasar } from 'quasar';
import { uploadFile as uploadFileApi } from '~/composables/fileApi';

// Props
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  maxFileSize: {
    type: Number,
    default: 10 * 1024 * 1024 // 10MB
  },
  accept: {
    type: String,
    default: 'image/*'
  },
  disabled: {
    type: Boolean,
    default: false
  },
  ratio: {
    type: Number,
    default: 1
  },
  width: {
    type: String,
    default: '120px'
  },
  height: {
    type: String,
    default: '120px'
  }
});

// Emits
const emit = defineEmits(['update:modelValue']);

// Quasar
const $q = useQuasar();

// Refs
const fileInputRef = ref(null);
const imageUrl = ref('');
const uploading = ref(false);
const previewDialog = ref(false);

// Computed
const maxFileSizeText = computed(() => {
  const size = props.maxFileSize / (1024 * 1024);
  return `${size}MB`;
});

// Watch
watch(() => props.modelValue, (newVal) => {
  imageUrl.value = newVal || '';
}, { immediate: true });

watch(imageUrl, (newVal) => {
  if (newVal !== props.modelValue) {
    emit('update:modelValue', newVal);
  }
});

// Methods
const validateFile = (file) => {
  // 检查文件类型
  if (!file.type.startsWith('image/')) {
    $q.notify({
      color: 'negative',
      message: '只能上传图片文件',
      icon: 'error'
    });
    return false;
  }

  // 检查文件大小
  if (file.size > props.maxFileSize) {
    $q.notify({
      color: 'negative',
      message: `文件大小不能超过 ${maxFileSizeText.value}`,
      icon: 'error'
    });
    return false;
  }

  return true;
};

const uploadFile = async (file) => {
  uploading.value = true;

  try {
    console.log('开始上传文件:', file.name);

    // 调用上传API
    const response = await uploadFileApi(file);

    console.log('上传响应:', response);

    if (response.code === 0 && response.data) {
      const uploadedUrl = response.data.trim();
      console.log('获取到图片URL:', uploadedUrl);
      
      if (uploadedUrl && (uploadedUrl.startsWith('http://') || uploadedUrl.startsWith('https://') || uploadedUrl.startsWith('/'))) {
        imageUrl.value = uploadedUrl;
        
        $q.notify({
          color: 'positive',
          message: '图片上传成功',
          icon: 'check'
        });
      } else {
        throw new Error('服务器返回的图片URL无效: ' + uploadedUrl);
      }
    } else {
      throw new Error(response.msg || '上传失败，响应码: ' + response.code);
    }
  } catch (error) {
    console.error('上传失败:', error);

    $q.notify({
      color: 'negative',
      message: error.message || '图片上传失败',
      icon: 'error'
    });
  } finally {
    uploading.value = false;
  }
};

const removeImage = () => {
  imageUrl.value = '';
};

const previewImage = () => {
  if (imageUrl.value) {
    previewDialog.value = true;
  }
};

const triggerFileInput = () => {
  if (!props.disabled) {
    fileInputRef.value?.click();
  }
};

const onFileInputChange = (event) => {
  const file = event.target.files?.[0];
  if (file && validateFile(file)) {
    uploadFile(file);
  }
  event.target.value = '';
};
</script>

<style scoped>
.single-image-upload {
  display: inline-block;
}

.uploaded-image-container {
  position: relative;
  width: v-bind(width);
  height: v-bind(height);
  border-radius: 8px;
  overflow: hidden;
  border: 2px solid #e0e0e0;
  transition: all 0.3s ease;
}

.uploaded-image-container:hover {
  border-color: #1976d2;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.upload-placeholder,
.upload-button {
  width: v-bind(width);
  height: v-bind(height);
  border: 2px dashed #e0e0e0;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #fafafa;
}

.upload-button:hover {
  border-color: #1976d2;
  background-color: #f0f8ff;
}

.upload-placeholder {
  cursor: default;
  border-style: solid;
  border-color: #1976d2;
  background-color: #f0f8ff;
}

.delete-btn {
  position: absolute;
  top: -6px;
  right: -6px;
  z-index: 99;
  background-color: white !important;
  box-shadow: 0 2px 8px rgba(0,0,0,0.2);
  border: 1px solid #e0e0e0;
}

.preview-card {
  background: transparent;
}

.preview-card .q-card__section {
  cursor: pointer;
}
</style>
