/**
 * 货币显示相关样式
 */

// 统一的金额文字颜色样式
.currency-text {
  color: #f44336; // 红色，与text-red相同
  font-weight: 500; // 中等粗细
}

// 默认币种样式
.default-currency {
  font-size: 0.85em;
  color: #666;
  opacity: 0.85;
  font-weight: normal;
  display: inline-block;
  margin-left: 4px;
  white-space: normal;
  word-break: break-word;
}

// 允许在区域内换行
.allow-wrap {
  display: inline-block;
  white-space: normal;
  word-break: break-word;
}

// 金额列样式
.amount-column {
  min-width: 140px;
  width: 100%; // 确保撑满整个列的空间
  display: block; // 使用块级元素撑满整个列
  text-align: center; // 文本居中
  overflow: hidden;
  text-overflow: ellipsis;
  color: #f44336; // 统一使用红色
  font-weight: 500; // 中等粗细

  // 确保主币种不换行
  .primary-currency {
    white-space: nowrap;
    display: inline-block;
    width: 100%; // 确保撑满整个父元素的宽度
    text-align: center; // 文本居中
    color: #f44336; // 统一使用红色
    font-weight: 500; // 中等粗细
  }
}

// 移动端金额样式
.mobile-amount {
  min-width: 100px;
  display: inline-block;
  width: 100%; // 确保撑满整个列的空间
  text-align: center; // 文本居中
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #f44336; // 统一使用红色
  font-weight: 500; // 中等粗细

  // 确保主币种不换行
  &.primary-currency {
    white-space: nowrap;
    display: inline-block;
    width: 100%; // 确保撑满整个父元素的宽度
    text-align: center; // 文本居中
    color: #f44336; // 统一使用红色
    font-weight: 500; // 中等粗细
  }
}

// 价格金额样式
.price-amount {
  min-width: 140px;
  display: inline-block;
  width: 100%; // 确保撑满整个列的空间
  text-align: center; // 文本居中
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #f44336; // 统一使用红色
  font-weight: 500; // 中等粗细

  // 确保主币种不换行
  &.primary-currency {
    white-space: nowrap;
    display: inline-block;
    width: 100%; // 确保撑满整个父元素的宽度
    text-align: center; // 文本居中
    color: #f44336; // 统一使用红色
    font-weight: 500; // 中等粗细
  }
}

// 移动端价格金额样式
.mobile-price-amount {
  min-width: 100px;
  display: inline-block;
  width: 100%; // 确保撑满整个列的空间
  text-align: center; // 文本居中
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #f44336; // 统一使用红色
  font-weight: 500; // 中等粗细

  // 确保主币种不换行
  &.primary-currency {
    white-space: nowrap;
    display: inline-block;
    width: 100%; // 确保撑满整个父元素的宽度
    text-align: center; // 文本居中
    color: #f44336; // 统一使用红色
    font-weight: 500; // 中等粗细
  }
}
