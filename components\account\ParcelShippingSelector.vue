<template>
  <div class="q-mb-lg">
    <div class="text-h6 q-mb-sm">物流方式</div>
    <div class="text-caption text-grey-8 q-mb-md">请选择您希望使用的物流方式，不同物流方式有不同的价格和时效</div>

    <div class="row items-center q-mb-md">
      <div v-if="modelValue" class="col-grow bg-blue-1 rounded-borders q-pa-md">
        <div class="row items-center justify-between">
          <div>
            <div class="text-body1 text-weight-bold">
              {{ modelValue.name }}
              <q-badge v-if="modelValue.recommended" color="green" class="q-ml-sm">推荐</q-badge>
            </div>
            <div class="text-body2">单价: ¥{{ modelValue.pricePerKg }}/kg | 预计送达: {{ modelValue.estimatedDelivery }}</div>
          </div>
          <q-btn flat round color="primary" icon="edit" @click="openDialog" />
        </div>
      </div>
      <div v-else class="col-grow bg-blue-1 rounded-borders q-pa-md text-center">
        <q-btn color="primary" label="选择物流方式" @click="openDialog" />
      </div>
    </div>

    <!-- 物流方式选择弹窗 -->
    <q-dialog v-model="shippingDialog">
      <q-card style="width: 700px; max-width: 90vw">
        <q-card-section class="row items-center">
          <div class="text-h6">选择物流方式</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-card-section class="q-pt-none">
          <q-list separator>
            <q-item
              v-for="shipping in shippingMethods"
              :key="shipping.id"
              clickable
              :active="tempSelectedShipping && tempSelectedShipping.id === shipping.id"
              active-class="bg-blue-1"
              @click="selectAndConfirmShipping(shipping)">
              <q-item-section>
                <q-item-label class="text-weight-bold">
                  {{ shipping.name }}
                  <q-badge v-if="shipping.recommended" color="green" class="q-ml-sm">推荐</q-badge>
                </q-item-label>
                <q-item-label caption> 单价: ¥{{ shipping.pricePerKg }}/kg | 预计送达: {{ shipping.estimatedDelivery }} </q-item-label>
                <q-item-label caption>
                  {{ shipping.description }}
                </q-item-label>
              </q-item-section>
              <q-item-section side>
                <q-icon name="check" v-if="tempSelectedShipping && tempSelectedShipping.id === shipping.id" color="primary" />
              </q-item-section>
            </q-item>
          </q-list>
        </q-card-section>

        <!-- 不需要底部按钮，用户可以点击右上角叉号取消 -->
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
import { ref } from 'vue';

// 定义组件接收的属性
const props = defineProps({
  modelValue: {
    type: Object,
    default: null,
  },
  shippingMethods: {
    type: Array,
    required: true,
    default: () => [],
  },
  parcelWeight: {
    type: [Number, String],
    default: 0,
  },
});

// 定义组件向外发出的事件
const emit = defineEmits(['update:modelValue', 'calculate-fee']);

// 弹窗状态
const shippingDialog = ref(false);
const tempSelectedShipping = ref(null);

// 打开物流方式选择弹窗
function openDialog() {
  tempSelectedShipping.value = props.modelValue;
  shippingDialog.value = true;
}

// 选择物流方式并确认
function selectAndConfirmShipping(shipping) {
  tempSelectedShipping.value = shipping;
  emit('update:modelValue', shipping);
  emit('calculate-fee');
  shippingDialog.value = false;
}
</script>

<style lang="scss" scoped>
/* 可以添加特定于此组件的样式 */
</style>
