<template>
  <HeaderSimple />
  <Breadcrumbs :breadcrumbs="breadcrumbs" />

  <div class="cookies-page">
    <div class="cookies-container">
      <div class="cookies-header">
        <h1 class="cookies-title">{{ $t('title.cookiePolicy') || 'Cookie政策' }}</h1>
        <p class="cookies-date">{{ $t('text.lastUpdated') || '最后更新日期' }}: {{ lastUpdated }}</p>
      </div>

      <div class="cookies-content">
        <section class="cookies-section">
          <h2>1. {{ $t('cookiePolicy.introduction.title') }}</h2>
          <p>{{ $t('cookiePolicy.introduction.content1') }}</p>
          <p>{{ $t('cookiePolicy.introduction.content2') }}</p>
        </section>

        <section class="cookies-section">
          <h2>2. {{ $t('cookiePolicy.types.title') }}</h2>
          <p>{{ $t('cookiePolicy.types.content') }}</p>

          <h3>{{ $t('cookiePolicy.types.essential.title') }}</h3>
          <p>{{ $t('cookiePolicy.types.essential.content') }}</p>

          <h3>{{ $t('cookiePolicy.types.performance.title') }}</h3>
          <p>{{ $t('cookiePolicy.types.performance.content') }}</p>

          <h3>{{ $t('cookiePolicy.types.functional.title') }}</h3>
          <p>{{ $t('cookiePolicy.types.functional.content') }}</p>

          <h3>{{ $t('cookiePolicy.types.targeting.title') }}</h3>
          <p>{{ $t('cookiePolicy.types.targeting.content') }}</p>

          <h3>{{ $t('cookiePolicy.types.thirdParty.title') }}</h3>
          <p>{{ $t('cookiePolicy.types.thirdParty.content') }}</p>
        </section>

        <section class="cookies-section">
          <h2>3. {{ $t('cookiePolicy.purposes.title') }}</h2>
          <p>{{ $t('cookiePolicy.purposes.content') }}</p>
          <ul>
            <li>{{ $t('cookiePolicy.purposes.item1') }}</li>
            <li>{{ $t('cookiePolicy.purposes.item2') }}</li>
            <li>{{ $t('cookiePolicy.purposes.item3') }}</li>
            <li>{{ $t('cookiePolicy.purposes.item4') }}</li>
            <li>{{ $t('cookiePolicy.purposes.item5') }}</li>
            <li>{{ $t('cookiePolicy.purposes.item6') }}</li>
            <li>{{ $t('cookiePolicy.purposes.item7') }}</li>
          </ul>
        </section>

        <section class="cookies-section">
          <h2>4. {{ $t('cookiePolicy.control.title') }}</h2>
          <p>{{ $t('cookiePolicy.control.content1') }}</p>
          <ul>
            <li><a href="https://support.google.com/chrome/answer/95647" target="_blank" rel="noopener noreferrer">Google Chrome</a></li>
            <li><a href="https://support.mozilla.org/en-US/kb/enhanced-tracking-protection-firefox-desktop" target="_blank" rel="noopener noreferrer">Mozilla Firefox</a></li>
            <li><a href="https://support.apple.com/guide/safari/manage-cookies-and-website-data-sfri11471/mac" target="_blank" rel="noopener noreferrer">Safari</a></li>
            <li>
              <a href="https://support.microsoft.com/en-us/microsoft-edge/delete-cookies-in-microsoft-edge-63947406-40ac-c3b8-57b9-2a946a29ae09" target="_blank" rel="noopener noreferrer"
                >Microsoft Edge</a
              >
            </li>
          </ul>
          <p>{{ $t('cookiePolicy.control.content2') }}</p>
          <ul>
            <li><a href="https://www.aboutcookies.org/" target="_blank" rel="noopener noreferrer">AboutCookies.org</a></li>
            <li><a href="https://www.allaboutcookies.org/" target="_blank" rel="noopener noreferrer">AllAboutCookies.org</a></li>
          </ul>
        </section>

        <section class="cookies-section">
          <h2>5. {{ $t('cookiePolicy.doNotTrack.title') }}</h2>
          <p>{{ $t('cookiePolicy.doNotTrack.content') }}</p>
        </section>

        <section class="cookies-section">
          <h2>6. {{ $t('cookiePolicy.updates.title') }}</h2>
          <p>{{ $t('cookiePolicy.updates.content') }}</p>
        </section>

        <section class="cookies-section">
          <h2>7. {{ $t('cookiePolicy.contact.title') }}</h2>
          <p>{{ $t('cookiePolicy.contact.content') }}</p>
          <p>Email: <EMAIL></p>
        </section>

        <section class="cookies-section">
          <h2>8. {{ $t('cookiePolicy.consent.title') }}</h2>
          <p>{{ $t('cookiePolicy.consent.content1') }}</p>
          <p>{{ $t('cookiePolicy.consent.content2') }}</p>
        </section>
      </div>
    </div>
  </div>

  <Footer />
</template>

<script setup>
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const breadcrumbs = [{ label: t('title.cookiePolicy') || 'Cookie政策', to: '/cookies' }];
const lastUpdated = '2023-12-01';
</script>

<style lang="scss" scoped>
.cookies-page {
  max-width: 1200px;
  width: 100%;
  margin: 30px auto 50px;
  padding: 0 20px;
}

.cookies-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  padding: 40px;
}

.cookies-header {
  margin-bottom: 30px;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 20px;
}

.cookies-title {
  font-size: 2rem;
  font-weight: 600;
  color: #1976d2;
  margin-bottom: 10px;
  text-align: center;
}

.cookies-date {
  text-align: center;
  color: #666;
  font-size: 14px;
}

.cookies-content {
  font-size: 16px;
  line-height: 1.6;
  color: #333;
}

.cookies-section {
  margin-bottom: 30px;

  h2 {
    font-size: 1.5rem;
    font-weight: 500;
    color: #1976d2;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid #eee;
  }

  h3 {
    font-size: 1.2rem;
    font-weight: 500;
    color: #333;
    margin: 20px 0 10px;
  }

  p {
    margin-bottom: 15px;
  }

  ul {
    margin-left: 20px;
    margin-bottom: 15px;

    li {
      margin-bottom: 8px;

      a {
        color: #1976d2;
        text-decoration: none;

        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
}

/* 平板电脑样式 */
@media (max-width: 1023px) {
  .cookies-container {
    padding: 30px;
  }

  .cookies-title {
    font-size: 1.8rem;
  }

  .cookies-section {
    h2 {
      font-size: 1.3rem;
    }

    h3 {
      font-size: 1.1rem;
    }
  }
}

/* 手机样式 */
@media (max-width: 599px) {
  .cookies-page {
    margin: 20px auto 30px;
    padding: 0 10px;
  }

  .cookies-container {
    padding: 20px;
    box-shadow: 0 1px 8px rgba(0, 0, 0, 0.1);
  }

  .cookies-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
  }

  .cookies-title {
    font-size: 1.5rem;
  }

  .cookies-content {
    font-size: 14px;
  }

  .cookies-section {
    margin-bottom: 20px;

    h2 {
      font-size: 1.2rem;
      margin-bottom: 10px;
    }

    h3 {
      font-size: 1rem;
      margin: 15px 0 8px;
    }

    p {
      margin-bottom: 10px;
    }
  }
}
</style>
