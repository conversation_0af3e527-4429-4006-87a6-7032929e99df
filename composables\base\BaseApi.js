/**
 * 基础API类
 * 
 * 提供统一的HTTP请求方法和错误处理
 * 所有API类都应该继承此基类
 * 
 * 功能：
 * - 统一的请求拦截器
 * - 统一的响应拦截器
 * - 统一的错误处理
 * - 自动添加认证头
 * - 请求重试机制
 * - 请求缓存支持
 */

export class BaseApi {
  constructor(baseURL = '') {
    this.baseURL = baseURL;
    this.defaultOptions = {
      timeout: 30000,        // 30秒超时
      retry: 3,              // 重试3次
      retryDelay: 1000,      // 重试延迟1秒
    };
  }

  /**
   * 统一的HTTP请求方法
   * @param {string} url - 请求URL
   * @param {object} options - 请求选项
   * @returns {Promise} 请求结果
   */
  async request(url, options = {}) {
    const mergedOptions = {
      ...this.defaultOptions,
      ...options,
      baseURL: this.baseURL || this.getBaseURL(),
    };

    // 添加请求拦截器
    mergedOptions.onRequest = this.createRequestInterceptor(mergedOptions.onRequest);
    
    // 添加响应拦截器
    mergedOptions.onResponse = this.createResponseInterceptor(mergedOptions.onResponse);
    
    // 添加错误拦截器
    mergedOptions.onResponseError = this.createErrorInterceptor(mergedOptions.onResponseError);

    try {
      const response = await $fetch(url, mergedOptions);
      return response;
    } catch (error) {
      console.error('API Request failed:', {
        url,
        options: mergedOptions,
        error
      });
      throw this.handleError(error);
    }
  }

  /**
   * 创建请求拦截器
   */
  createRequestInterceptor(customInterceptor) {
    return ({ request, options }) => {
      // 添加认证头
      const token = this.getAuthToken();
      if (token) {
        options.headers = {
          ...options.headers,
          Authorization: `Bearer ${token}`
        };
      }

      // 添加通用头部
      options.headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        ...options.headers,
      };

      // 添加租户ID（如果需要）
      const tenantId = this.getTenantId();
      if (tenantId) {
        options.headers['X-Tenant-ID'] = tenantId;
      }

      // 执行自定义拦截器
      if (customInterceptor) {
        customInterceptor({ request, options });
      }

      // 记录请求日志
      if (process.env.NODE_ENV === 'development') {
        console.log('API Request:', {
          method: options.method || 'GET',
          url: request,
          headers: options.headers,
          body: options.body
        });
      }
    };
  }

  /**
   * 创建响应拦截器
   */
  createResponseInterceptor(customInterceptor) {
    return ({ response }) => {
      // 记录响应日志
      if (process.env.NODE_ENV === 'development') {
        console.log('API Response:', {
          status: response.status,
          url: response.url,
          data: response._data
        });
      }

      // 执行自定义拦截器
      if (customInterceptor) {
        customInterceptor({ response });
      }

      // 统一处理响应格式
      return response._data;
    };
  }

  /**
   * 创建错误拦截器
   */
  createErrorInterceptor(customInterceptor) {
    return ({ response, error }) => {
      // 记录错误日志
      console.error('API Error:', {
        status: response?.status,
        url: response?.url,
        error: response?._data || error
      });

      // 处理特定状态码
      if (response?.status === 401) {
        this.handleUnauthorized();
      } else if (response?.status === 403) {
        this.handleForbidden();
      } else if (response?.status === 500) {
        this.handleServerError();
      }

      // 执行自定义拦截器
      if (customInterceptor) {
        customInterceptor({ response, error });
      }

      // 抛出格式化的错误
      throw this.formatError(response, error);
    };
  }

  /**
   * GET请求
   */
  get(url, params = {}, options = {}) {
    return this.request(url, { 
      method: 'GET', 
      params,
      ...options 
    });
  }

  /**
   * POST请求
   */
  post(url, body = {}, options = {}) {
    return this.request(url, { 
      method: 'POST', 
      body,
      ...options 
    });
  }

  /**
   * PUT请求
   */
  put(url, body = {}, options = {}) {
    return this.request(url, { 
      method: 'PUT', 
      body,
      ...options 
    });
  }

  /**
   * DELETE请求
   */
  delete(url, options = {}) {
    return this.request(url, { 
      method: 'DELETE',
      ...options 
    });
  }

  /**
   * PATCH请求
   */
  patch(url, body = {}, options = {}) {
    return this.request(url, { 
      method: 'PATCH', 
      body,
      ...options 
    });
  }

  /**
   * 文件上传
   */
  upload(url, file, options = {}) {
    const formData = new FormData();
    formData.append('file', file);

    return this.request(url, {
      method: 'POST',
      body: formData,
      headers: {
        // 不设置Content-Type，让浏览器自动设置
      },
      ...options
    });
  }

  /**
   * 获取基础URL
   */
  getBaseURL() {
    const config = useRuntimeConfig();
    return config.public.baseUrl + config.public.apiPath;
  }

  /**
   * 获取认证令牌
   */
  getAuthToken() {
    if (process.client) {
      return useCookie('token').value;
    }
    return null;
  }

  /**
   * 获取租户ID
   */
  getTenantId() {
    if (typeof globalConfig !== 'undefined' && globalConfig.tenantId) {
      return globalConfig.tenantId;
    }
    return null;
  }

  /**
   * 处理未授权错误
   */
  handleUnauthorized() {
    if (process.client) {
      // 清除认证信息
      const token = useCookie('token');
      token.value = null;
      
      // 跳转到登录页
      const router = useRouter();
      const route = useRoute();
      router.push(`/login?redirect=${encodeURIComponent(route.fullPath)}`);
    }
  }

  /**
   * 处理禁止访问错误
   */
  handleForbidden() {
    if (process.client) {
      useNuxtApp().$showNotify({
        msg: '您没有权限访问此资源',
        type: 'negative'
      });
    }
  }

  /**
   * 处理服务器错误
   */
  handleServerError() {
    if (process.client) {
      useNuxtApp().$showNotify({
        msg: '服务器内部错误，请稍后重试',
        type: 'negative'
      });
    }
  }

  /**
   * 格式化错误信息
   */
  formatError(response, error) {
    if (response?._data) {
      return new Error(response._data.message || response._data.msg || '请求失败');
    }
    
    if (error?.message) {
      return new Error(error.message);
    }
    
    return new Error('网络错误，请检查网络连接');
  }

  /**
   * 处理通用错误
   */
  handleError(error) {
    // 网络错误
    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      return new Error('网络连接失败，请检查网络设置');
    }
    
    // 超时错误
    if (error.name === 'AbortError') {
      return new Error('请求超时，请稍后重试');
    }
    
    return error;
  }
}

// 创建默认实例
export const baseApi = new BaseApi();

// 导出便捷方法
export const { get, post, put, delete: del, patch, upload } = baseApi;
