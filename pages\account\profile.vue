<template>
  <div>
    <!-- 标题区域 -->
    <div class="bg-grey-2 q-px-md q-py-sm">
      <div class="row items-center">
        <q-icon name="person" size="xs" color="primary" class="q-mr-xs" />
        <span class="text-subtitle1 text-weight-medium">{{ $t('profile.title') }}</span>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="q-pa-md">
      <!-- 个人信息卡片 -->
      <q-card flat bordered class="q-mb-md">
        <q-card-section>
          <div class="row items-center q-mb-sm">
            <div class="text-subtitle1 text-weight-medium">
              <q-icon name="badge" color="primary" size="xs" class="q-mr-xs" />
              {{ $t('profile.sections.basicInfo') }}
            </div>
            <q-space />
            <q-btn v-if="!editingBasic" color="primary" flat icon="edit" :label="$t('profile.buttons.edit')" @click="startEditBasic" dense />
          </div>

          <!-- 查看模式 -->
          <div v-if="!editingBasic" class="row q-col-gutter-md">
            <div class="col-12 col-md-6">
              <div class="row q-mb-md">
                <div class="col-4 text-grey-7">{{ $t('profile.fields.username') }}</div>
                <div class="col-8">{{ userInfo.name || $t('profile.values.notSet') }}</div>
              </div>
              <div class="row q-mb-md">
                <div class="col-4 text-grey-7">{{ $t('profile.fields.nickname') }}</div>
                <div class="col-8">{{ userInfo.nickname || $t('profile.values.notSet') }}</div>
              </div>
              <div class="row q-mb-md">
                <div class="col-4 text-grey-7">{{ $t('profile.fields.gender') }}</div>
                <div class="col-8">{{ getGenderText(userInfo.gender) }}</div>
              </div>
            </div>
            <div class="col-12 col-md-6">
              <div class="row q-mb-md">
                <div class="col-4 text-grey-7">{{ $t('profile.fields.avatar') }}</div>
                <div class="col-8">
                  <q-avatar size="60px">
                    <img v-if="userInfo.avatar" :src="userInfo.avatar" :alt="$t('profile.values.userAvatar')" />
                    <q-icon v-else name="person" size="40px" color="grey-5" />
                  </q-avatar>
                </div>
              </div>
            </div>
          </div>

          <!-- 编辑模式 -->
          <q-form v-else ref="basicFormRef" @submit="saveBasicInfo" class="edit-form">
            <div class="row q-col-gutter-lg">
              <div class="col-12 col-md-6">
                <div class="form-group">
                  <div class="form-label">{{ $t('profile.fields.username') }}</div>
                  <q-input
                    v-model="basicForm.name"
                    :rules="[(val) => !!val || $t('profile.validations.usernameRequired')]"
                    outlined
                    dense
                    class="form-control"
                    :placeholder="$t('profile.placeholders.username')" />
                </div>

                <div class="form-group">
                  <div class="form-label">{{ $t('profile.fields.nickname') }}</div>
                  <q-input v-model="basicForm.nickname" outlined dense class="form-control" :placeholder="$t('profile.placeholders.nickname')" />
                </div>

                <div class="form-group">
                  <div class="form-label">{{ $t('profile.fields.gender') }}</div>
                  <q-select v-model="basicForm.gender" :options="genderOptions" outlined dense emit-value map-options class="form-control" />
                </div>
              </div>

              <div class="col-12 col-md-6">
                <div class="form-group">
                  <div class="form-label">{{ $t('profile.fields.avatar') }}</div>
                  <div class="avatar-upload">
                    <q-avatar size="60px" class="q-mr-md">
                      <img v-if="avatarPreview" :src="avatarPreview" :alt="$t('profile.values.avatarPreview')" />
                      <img v-else-if="userInfo.avatar" :src="userInfo.avatar" :alt="$t('profile.values.userAvatar')" />
                      <q-icon v-else name="person" size="40px" color="grey-5" />
                    </q-avatar>
                    <q-file v-model="avatarFile" :label="$t('profile.buttons.selectImage')" outlined dense accept=".jpg,.jpeg,.png" @update:model-value="previewAvatar" class="avatar-upload-btn">
                      <template #prepend>
                        <q-icon name="photo" />
                      </template>
                    </q-file>
                  </div>
                  <div class="text-caption text-grey-7 q-mt-sm">{{ $t('profile.tips.avatarFormat') }}</div>
                </div>
              </div>
            </div>

            <div class="form-actions">
              <q-btn flat :label="$t('profile.buttons.cancel')" color="grey-7" class="q-mr-sm" @click="cancelEditBasic" />
              <q-btn type="submit" :label="$t('profile.buttons.save')" color="primary" :loading="savingBasic" />
            </div>
          </q-form>
        </q-card-section>
      </q-card>

      <!-- 联系方式卡片 -->
      <q-card flat bordered class="q-mb-md">
        <q-card-section>
          <div class="row items-center q-mb-sm">
            <div class="text-subtitle1 text-weight-medium">
              <q-icon name="contact_phone" color="primary" size="xs" class="q-mr-xs" />
              {{ $t('profile.sections.contactInfo') }}
            </div>
            <q-space />
            <q-btn v-if="!editingContact" color="primary" flat icon="edit" :label="$t('profile.buttons.edit')" @click="startEditContact" dense />
          </div>

          <!-- 查看模式 -->
          <div v-if="!editingContact" class="row q-col-gutter-md">
            <div class="col-12 col-md-6">
              <div class="row q-mb-md">
                <div class="col-4 text-grey-7">{{ $t('profile.fields.email') }}</div>
                <div class="col-8">
                  {{ userInfo.email }}
                  <q-badge v-if="userInfo.status === 1" color="positive" class="q-ml-sm">{{ $t('profile.values.verified') }}</q-badge>
                  <q-badge v-else color="negative" class="q-ml-sm">{{ $t('profile.values.unverified') }}</q-badge>
                </div>
              </div>
              <div class="row q-mb-md">
                <div class="col-4 text-grey-7">{{ $t('profile.fields.mobile') }}</div>
                <div class="col-8">{{ userInfo.mobile || $t('profile.values.notSet') }}</div>
              </div>
            </div>
          </div>

          <!-- 编辑模式 -->
          <q-form v-else ref="contactFormRef" @submit="saveContactInfo" class="edit-form">
            <div class="row q-col-gutter-lg">
              <div class="col-12 col-md-8">
                <div class="form-group">
                  <div class="form-label">{{ $t('profile.fields.email') }}</div>
                  <div class="email-display">
                    {{ userInfo.email }}
                    <q-badge v-if="userInfo.status === 1" color="positive" class="q-ml-sm">{{ $t('profile.values.verified') }}</q-badge>
                    <q-badge v-else color="negative" class="q-ml-sm">{{ $t('profile.values.unverified') }}</q-badge>
                    <div v-if="userInfo.status !== 1" class="q-mt-xs">
                      <q-btn flat dense size="sm" color="primary" :label="$t('profile.buttons.sendVerifyEmail')" @click="sendVerifyEmail" />
                    </div>
                  </div>
                </div>

                <div class="form-group">
                  <div class="form-label">{{ $t('profile.fields.mobile') }}</div>
                  <div v-if="!changingMobile" class="mobile-display">
                    {{ userInfo.mobile || $t('profile.values.notSet') }}
                    <q-btn flat dense size="sm" color="primary" :label="$t('profile.buttons.modify')" @click="changingMobile = true" class="q-ml-sm" />
                  </div>
                  <div v-else class="mobile-edit">
                    <div class="row q-col-gutter-sm">
                      <div class="col-4 col-sm-3">
                        <q-select v-model="contactForm.phoneCode" :options="phoneCodeOptions" outlined dense map-options emit-value class="form-control" />
                      </div>
                      <div class="col-8 col-sm-9">
                        <q-input
                          v-model="contactForm.mobile"
                          :placeholder="$t('profile.placeholders.mobile')"
                          outlined
                          dense
                          class="form-control"
                          :rules="[(val) => !!val || $t('profile.validations.mobileRequired')]" />
                      </div>
                    </div>
                    <div class="row q-col-gutter-sm q-mt-sm">
                      <div class="col-8 col-sm-9">
                        <q-input
                          v-model="contactForm.code"
                          :placeholder="$t('profile.placeholders.verificationCode')"
                          outlined
                          dense
                          class="form-control"
                          :rules="[(val) => !!val || $t('profile.validations.codeRequired')]" />
                      </div>
                      <div class="col-4 col-sm-3">
                        <q-btn :label="sendCodeBtnText" color="primary" class="full-width verification-btn" :disable="sendingCode || countdown > 0" @click="sendMobileCode" size="sm" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="form-actions">
              <q-btn flat :label="$t('profile.buttons.cancel')" color="grey-7" class="q-mr-sm" @click="cancelEditContact" />
              <q-btn type="submit" :label="$t('profile.buttons.save')" color="primary" :loading="savingContact" />
            </div>
          </q-form>
        </q-card-section>
      </q-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { useQuasar } from 'quasar';
import { useUserStore } from '~/store/user';
import UserApi from '~/composables/userApi';
import AuthApi from '~/composables/authApi';
import { useI18n } from 'vue-i18n';

const $q = useQuasar();
const userStore = useUserStore();
const { t } = useI18n();

// 用户信息
const userInfo = ref({
  name: '',
  nickname: '',
  gender: 0,
  mobile: '',
  email: '',
  avatar: '',
  status: 0, // 邮箱验证状态
});

// 性别选项
const genderOptions = [
  { label: t('profile.gender.secret'), value: 0 },
  { label: t('profile.gender.male'), value: 1 },
  { label: t('profile.gender.female'), value: 2 },
];

// 国际电话区号选项
const phoneCodeOptions = [
  { label: `+86 (${t('profile.countries.china')})`, value: '+86' },
  { label: `+1 (${t('profile.countries.usa')})`, value: '+1' },
  { label: `+81 (${t('profile.countries.japan')})`, value: '+81' },
  { label: `+82 (${t('profile.countries.korea')})`, value: '+82' },
  { label: `+44 (${t('profile.countries.uk')})`, value: '+44' },
  { label: `+61 (${t('profile.countries.australia')})`, value: '+61' },
  { label: `+65 (${t('profile.countries.singapore')})`, value: '+65' },
];

// 基本信息编辑状态
const editingBasic = ref(false);
const basicFormRef = ref(null);
const savingBasic = ref(false);
const basicForm = reactive({
  name: '',
  nickname: '',
  gender: 0,
});

// 头像上传
const avatarFile = ref(null);
const avatarPreview = ref(null);

// 联系方式编辑状态
const editingContact = ref(false);
const contactFormRef = ref(null);
const savingContact = ref(false);
const changingMobile = ref(false);
const contactForm = reactive({
  phoneCode: '+86',
  mobile: '',
  code: '',
});

// 验证码发送
const sendingCode = ref(false);
const countdown = ref(0);
const sendCodeBtnText = computed(() => {
  return countdown.value > 0 ? t('profile.buttons.resendAfter', { seconds: countdown.value }) : t('profile.buttons.getCode');
});

// 页面加载时获取用户信息
onMounted(async () => {
  await getUserInfo();
});

// 获取用户信息
async function getUserInfo() {
  try {
    await userStore.getUserInfo();
    userInfo.value = userStore.userInfo;
  } catch (error) {
    console.error('获取用户信息失败', error);
    $q.notify({
      color: 'negative',
      message: t('profile.notifications.getUserInfoFailed'),
      icon: 'error',
    });
  }
}

// 获取性别文本
function getGenderText(gender) {
  const option = genderOptions.find((opt) => opt.value === gender);
  return option ? option.label : t('profile.gender.secret');
}

// 开始编辑基本信息
function startEditBasic() {
  basicForm.name = userInfo.value.name || '';
  basicForm.nickname = userInfo.value.nickname || '';
  basicForm.gender = userInfo.value.gender || 0;
  editingBasic.value = true;
}

// 取消编辑基本信息
function cancelEditBasic() {
  editingBasic.value = false;
  avatarFile.value = null;
  avatarPreview.value = null;
}

// 预览头像
function previewAvatar(file) {
  if (!file) {
    avatarPreview.value = null;
    return;
  }

  // 检查文件大小
  if (file.size > 2 * 1024 * 1024) {
    $q.notify({
      color: 'negative',
      message: t('profile.notifications.imageSizeLimit'),
      icon: 'error',
    });
    avatarFile.value = null;
    return;
  }

  // 创建预览URL
  const reader = new FileReader();
  reader.onload = (e) => {
    avatarPreview.value = e.target.result;
  };
  reader.readAsDataURL(file);
}

// 保存基本信息
async function saveBasicInfo() {
  try {
    // 表单验证
    const isValid = await basicFormRef.value.validate();
    if (!isValid) return;

    savingBasic.value = true;

    // 构建请求数据
    const formData = new FormData();
    formData.append('name', basicForm.name);
    formData.append('nickname', basicForm.nickname);
    formData.append('gender', basicForm.gender);

    if (avatarFile.value) {
      formData.append('avatar', avatarFile.value);
    }

    // 调用API更新用户信息
    const { code, msg } = await UserApi.updateUser(formData);

    if (code === 0) {
      $q.notify({
        color: 'positive',
        message: t('profile.notifications.basicInfoUpdated'),
        icon: 'check',
      });

      // 重新获取用户信息
      await getUserInfo();
      editingBasic.value = false;
      avatarFile.value = null;
      avatarPreview.value = null;
    } else {
      $q.notify({
        color: 'negative',
        message: msg || t('profile.notifications.updateFailed'),
        icon: 'error',
      });
    }
  } catch (error) {
    console.error('保存基本信息失败', error);
    $q.notify({
      color: 'negative',
      message: t('profile.notifications.saveFailed'),
      icon: 'error',
    });
  } finally {
    savingBasic.value = false;
  }
}

// 开始编辑联系方式
function startEditContact() {
  contactForm.phoneCode = '+86';
  contactForm.mobile = '';
  contactForm.code = '';
  changingMobile.value = false;
  editingContact.value = true;
}

// 取消编辑联系方式
function cancelEditContact() {
  editingContact.value = false;
  changingMobile.value = false;
}

// 发送手机验证码
async function sendMobileCode() {
  if (!contactForm.mobile) {
    $q.notify({
      color: 'negative',
      message: '请输入手机号码',
      icon: 'error',
    });
    return;
  }

  try {
    sendingCode.value = true;

    // 调用发送验证码API
    const { code, msg } = await AuthApi.sendMailCode({
      mobile: contactForm.mobile,
      phoneCode: contactForm.phoneCode,
      type: 'update_mobile',
    });

    if (code === 0) {
      $q.notify({
        color: 'positive',
        message: '验证码已发送',
        icon: 'check',
      });

      // 开始倒计时
      countdown.value = 60;
      const timer = setInterval(() => {
        countdown.value--;
        if (countdown.value <= 0) {
          clearInterval(timer);
        }
      }, 1000);
    } else {
      $q.notify({
        color: 'negative',
        message: msg || '发送验证码失败',
        icon: 'error',
      });
    }
  } catch (error) {
    console.error('发送验证码失败', error);
    $q.notify({
      color: 'negative',
      message: '发送验证码失败，请重试',
      icon: 'error',
    });
  } finally {
    sendingCode.value = false;
  }
}

// 发送邮箱验证邮件
async function sendVerifyEmail() {
  try {
    const { code, msg } = await AuthApi.resend({
      email: userInfo.value.email,
    });

    if (code === 0) {
      $q.notify({
        color: 'positive',
        message: '验证邮件已发送，请查收',
        icon: 'check',
      });
    } else {
      $q.notify({
        color: 'negative',
        message: msg || '发送验证邮件失败',
        icon: 'error',
      });
    }
  } catch (error) {
    console.error('发送验证邮件失败', error);
    $q.notify({
      color: 'negative',
      message: '发送验证邮件失败，请重试',
      icon: 'error',
    });
  }
}

// 保存联系方式
async function saveContactInfo() {
  try {
    // 表单验证
    const isValid = await contactFormRef.value.validate();
    if (!isValid) return;

    savingContact.value = true;

    if (changingMobile.value) {
      // 更新手机号码
      const { code, msg } = await UserApi.updateUserMobile({
        mobile: contactForm.mobile,
        phoneCode: contactForm.phoneCode,
        code: contactForm.code,
      });

      if (code === 0) {
        $q.notify({
          color: 'positive',
          message: '手机号码更新成功',
          icon: 'check',
        });

        // 重新获取用户信息
        await getUserInfo();
        editingContact.value = false;
      } else {
        $q.notify({
          color: 'negative',
          message: msg || '更新失败，请重试',
          icon: 'error',
        });
      }
    } else {
      // 如果没有修改任何内容，直接关闭编辑模式
      editingContact.value = false;
    }
  } catch (error) {
    console.error('保存联系方式失败', error);
    $q.notify({
      color: 'negative',
      message: '保存失败，请重试',
      icon: 'error',
    });
  } finally {
    savingContact.value = false;
  }
}
</script>

<style lang="scss" scoped>
.q-card {
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

  .q-card-section {
    padding: 16px;
  }

  .text-subtitle1 {
    font-size: 1rem;
    font-weight: 500;
  }
}

// 表单样式
.edit-form {
  margin-top: 12px;

  .form-group {
    margin-bottom: 16px;
  }

  .form-label {
    font-size: 0.85rem;
    color: #666;
    margin-bottom: 4px;
  }

  .form-control {
    width: 100%;

    .q-field__control {
      height: 36px;
    }
  }

  .avatar-upload {
    display: flex;
    align-items: center;

    .avatar-upload-btn {
      max-width: 180px;
    }
  }

  .email-display,
  .mobile-display {
    padding: 4px 0;
  }

  .mobile-edit {
    .verification-btn {
      height: 36px;
    }
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
    padding-top: 12px;
    border-top: 1px solid rgba(0, 0, 0, 0.05);

    .q-btn {
      min-width: 80px;
    }
  }
}

// 移动端优化
@media (max-width: 599px) {
  .q-card-section {
    padding: 12px;
  }

  .row.q-mb-md {
    margin-bottom: 8px;
  }

  .text-h6 {
    font-size: 1.1rem;
  }

  .text-subtitle1 {
    font-size: 1rem;
  }

  // 在移动端视图下调整布局
  .col-4,
  .col-8 {
    padding-top: 4px;
    padding-bottom: 4px;
  }

  .edit-form {
    .form-group {
      margin-bottom: 12px;
    }

    .form-label {
      font-size: 0.8rem;
      margin-bottom: 2px;
    }

    .form-control {
      .q-field__control {
        height: 32px;
      }
    }

    .form-actions {
      margin-top: 12px;
      padding-top: 10px;
    }
  }
}
</style>
