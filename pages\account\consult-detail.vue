<template>
  <div class="consult-detail">
    <!-- 标题区域 -->
    <div class="bg-grey-2 q-px-md q-py-sm rounded-borders">
      <div class="row justify-between items-center">
        <div class="row items-center">
          <q-btn flat dense color="primary" icon="arrow_back" @click="router.push('/account/msgConsult')" class="q-mr-sm" />
          <span class="text-subtitle1 text-weight-medium">咨询详情</span>
        </div>
        <q-btn flat dense color="primary" to="/account/msgConsult" label="返回咨询列表" class="q-px-sm" />
      </div>
    </div>

    <div class="q-pa-md">
      <q-card class="consult-card q-mb-md">
        <q-card-section class="bg-grey-2">
          <div class="row justify-between items-center">
            <div class="row items-center">
              <q-badge :color="getConsultTypeColor(consult.type)" text-color="white" class="q-mr-sm">
                {{ getConsultTypeName(consult.type) }}
              </q-badge>
              <span class="text-subtitle1 text-weight-medium">{{ consult.title }}</span>
            </div>
            <q-badge :color="getStatusColor(consult.status)">
              {{ getStatusName(consult.status) }}
            </q-badge>
          </div>
        </q-card-section>

        <q-card-section>
          <div class="row q-col-gutter-md">
            <div class="col-12 col-md-6">
              <div class="text-caption text-grey-7">咨询编号</div>
              <div>{{ consult.id }}</div>
            </div>
            <div class="col-12 col-md-6">
              <div class="text-caption text-grey-7">创建时间</div>
              <div>{{ formatDateTime(consult.createTime) }}</div>
            </div>
            <div class="col-12 col-md-6" v-if="consult.relatedId">
              <div class="text-caption text-grey-7">{{ consult.type === 'order' ? '相关订单' : '相关转运单' }}</div>
              <div>
                <q-btn
                  flat
                  dense
                  color="primary"
                  :label="consult.relatedId"
                  :to="consult.type === 'order' ? `/account/order-detail/${consult.relatedId}` : `/account/parcel-detail/${consult.relatedId}`" />
              </div>
            </div>
            <div class="col-12 col-md-6" v-if="consult.lastReplyTime">
              <div class="text-caption text-grey-7">最后回复时间</div>
              <div>{{ formatDateTime(consult.lastReplyTime) }}</div>
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- 聊天记录 -->
      <q-card class="chat-card">
        <q-card-section class="bg-grey-2">
          <div class="row items-center">
            <q-icon name="chat" color="primary" size="xs" class="q-mr-xs" />
            <div class="text-subtitle2">对话记录</div>
          </div>
        </q-card-section>

        <q-card-section class="chat-container">
          <q-scroll-area ref="chatScrollArea" style="height: 400px">
            <div class="q-pa-md">
              <!-- 初始咨询内容 -->
              <div class="row">
                <div class="col-auto">
                  <q-avatar size="40px">
                    <img :src="userAvatar" alt="用户头像" />
                  </q-avatar>
                </div>
                <div class="col q-ml-md">
                  <div class="row items-center">
                    <div class="text-weight-medium">{{ userName }}</div>
                    <div class="text-caption text-grey-7 q-ml-sm">{{ formatDateTime(consult.createTime) }}</div>
                  </div>
                  <div class="chat-bubble user-bubble q-mt-xs">
                    {{ consult.content }}
                  </div>
                  <div v-if="consult.attachments && consult.attachments.length > 0" class="q-mt-sm">
                    <div class="text-caption text-grey-7 q-mb-xs">附件：</div>
                    <div class="row q-col-gutter-sm">
                      <div v-for="(attachment, index) in consult.attachments" :key="index" class="col-auto">
                        <q-btn outline color="primary" :label="getAttachmentName(attachment)" :icon="getAttachmentIcon(attachment)" size="sm" @click="downloadAttachment(attachment)" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 回复消息 -->
              <div v-for="(message, index) in messages" :key="index" class="row q-mt-lg">
                <template v-if="message.isAdmin">
                  <div class="col-auto">
                    <q-avatar size="40px" color="primary" text-color="white">
                      <q-icon name="support_agent" />
                    </q-avatar>
                  </div>
                  <div class="col q-ml-md">
                    <div class="row items-center">
                      <div class="text-weight-medium">客服</div>
                      <div class="text-caption text-grey-7 q-ml-sm">{{ formatDateTime(message.time) }}</div>
                    </div>
                    <div class="chat-bubble admin-bubble q-mt-xs">
                      {{ message.content }}
                    </div>
                    <div v-if="message.attachments && message.attachments.length > 0" class="q-mt-sm">
                      <div class="text-caption text-grey-7 q-mb-xs">附件：</div>
                      <div class="row q-col-gutter-sm">
                        <div v-for="(attachment, attachIndex) in message.attachments" :key="attachIndex" class="col-auto">
                          <q-btn outline color="primary" :label="getAttachmentName(attachment)" :icon="getAttachmentIcon(attachment)" size="sm" @click="downloadAttachment(attachment)" />
                        </div>
                      </div>
                    </div>
                  </div>
                </template>
                <template v-else>
                  <div class="col-auto order-last">
                    <q-avatar size="40px">
                      <img :src="userAvatar" alt="用户头像" />
                    </q-avatar>
                  </div>
                  <div class="col q-mr-md text-right">
                    <div class="row items-center justify-end">
                      <div class="text-caption text-grey-7 q-mr-sm">{{ formatDateTime(message.time) }}</div>
                      <div class="text-weight-medium">{{ userName }}</div>
                    </div>
                    <div class="chat-bubble user-bubble q-mt-xs float-right">
                      {{ message.content }}
                    </div>
                    <div v-if="message.attachments && message.attachments.length > 0" class="q-mt-sm text-left">
                      <div class="text-caption text-grey-7 q-mb-xs">附件：</div>
                      <div class="row q-col-gutter-sm">
                        <div v-for="(attachment, attachIndex) in message.attachments" :key="attachIndex" class="col-auto">
                          <q-btn outline color="primary" :label="getAttachmentName(attachment)" :icon="getAttachmentIcon(attachment)" size="sm" @click="downloadAttachment(attachment)" />
                        </div>
                      </div>
                    </div>
                  </div>
                </template>
              </div>
            </div>
          </q-scroll-area>
        </q-card-section>

        <!-- 回复输入框 -->
        <q-card-section v-if="consult.status !== 'closed'" class="reply-container">
          <q-form @submit="sendReply" ref="replyForm">
            <q-input v-model="replyContent" type="textarea" outlined autogrow placeholder="请输入回复内容..." :rules="[(val) => (!!val && val.length <= 500) || '内容不能为空且不超过500个字符']" />
            <div class="text-caption text-right text-grey-7 q-mt-xs">{{ replyContent.length }}/500</div>
            <div class="row justify-between items-center q-mt-sm">
              <q-file
                v-model="replyAttachments"
                label="添加附件"
                outlined
                dense
                multiple
                accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx"
                max-files="3"
                max-file-size="5242880"
                @rejected="onRejected"
                class="col-grow">
                <template #prepend>
                  <q-icon name="attach_file" />
                </template>
                <template #hint> 最多3个文件，每个不超过5MB </template>
              </q-file>
              <div class="col-auto q-ml-md">
                <q-btn color="primary" type="submit" label="发送回复" />
              </div>
            </div>
          </q-form>
        </q-card-section>

        <q-card-section v-else class="closed-notice">
          <div class="text-center text-grey-7">
            <q-icon name="info" size="24px" class="q-mr-xs" />
            <span>此咨询已关闭，无法继续回复</span>
          </div>
        </q-card-section>
      </q-card>

      <!-- 底部按钮 -->
      <div class="row justify-end q-mt-md q-gutter-sm">
        <q-btn v-if="consult.status !== 'closed'" color="negative" outline label="关闭咨询" @click="confirmCloseConsult" />
        <q-btn color="primary" outline label="返回列表" @click="router.push('/account/msgConsult')" />
      </div>
    </div>

    <!-- 关闭咨询确认弹窗 -->
    <q-dialog v-model="closeConsultDialog" persistent>
      <q-card>
        <q-card-section class="row items-center">
          <q-avatar icon="warning" color="warning" text-color="white" />
          <span class="q-ml-sm">确认关闭咨询</span>
        </q-card-section>

        <q-card-section> 确定要关闭此咨询吗？关闭后将无法继续回复。 </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="取消" color="primary" v-close-popup />
          <q-btn flat label="确认关闭" color="negative" @click="closeConsult" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useQuasar } from 'quasar';
import { date } from 'quasar';
// import ConsultApi from '~/composables/consultApi'; // 实际项目中取消注释

const $q = useQuasar();
const router = useRouter();
const route = useRoute();

// 咨询详情
const consult = ref({
  id: '',
  type: '',
  title: '',
  content: '',
  createTime: null,
  lastReplyTime: null,
  status: '',
  relatedId: null,
  attachments: [],
});

// 用户信息
const userName = ref('用户名');
const userAvatar = ref('/images/avatar-placeholder.png');

// 聊天记录
const messages = ref([]);
const chatScrollArea = ref(null);

// 回复相关
const replyForm = ref(null);
const replyContent = ref('');
const replyAttachments = ref([]);

// 关闭咨询确认弹窗
const closeConsultDialog = ref(false);

// 加载咨询详情
const loadConsultDetail = async () => {
  try {
    const consultId = route.params.id;

    // 实际项目中使用API调用
    // const response = await ConsultApi.getConsultDetail(consultId);
    // consult.value = response.data.consult;
    // messages.value = response.data.messages;

    // 模拟数据
    await new Promise((resolve) => setTimeout(resolve, 500));

    // 模拟咨询详情
    consult.value = {
      id: consultId || 'CSL100001',
      type: 'order',
      title: '关于订单 #ORD10001 的发货问题',
      content: '您好，我的订单已经付款一周了，但是还没有发货，请问什么时候能发货？',
      createTime: Date.now() - 7 * 24 * 60 * 60 * 1000,
      lastReplyTime: Date.now() - 6 * 24 * 60 * 60 * 1000,
      status: 'replied',
      relatedId: 'ORD10001',
      attachments: [{ name: '订单截图.jpg', url: '#', type: 'image/jpeg', size: 1024 * 1024 }],
    };

    // 模拟聊天记录
    messages.value = [
      {
        isAdmin: true,
        content: '您好，感谢您的咨询。我们已经查询到您的订单，由于商品缺货，导致发货延迟。我们预计将在3天内发货，给您带来不便，敬请谅解。',
        time: Date.now() - 6 * 24 * 60 * 60 * 1000,
        attachments: [],
      },
      {
        isAdmin: false,
        content: '好的，谢谢您的回复。请问有没有具体的发货时间？',
        time: Date.now() - 5 * 24 * 60 * 60 * 1000,
        attachments: [],
      },
      {
        isAdmin: true,
        content: '您好，我们预计将在本周五（5月20日）发货，届时会通过短信通知您。如有任何变动，我们会及时告知。',
        time: Date.now() - 4 * 24 * 60 * 60 * 1000,
        attachments: [{ name: '发货通知.pdf', url: '#', type: 'application/pdf', size: 512 * 1024 }],
      },
    ];

    // 滚动到底部
    scrollToBottom();
  } catch (error) {
    console.error('加载咨询详情失败', error);
    $q.notify({
      color: 'negative',
      message: '加载咨询详情失败，请稍后重试',
      icon: 'error',
    });
  }
};

// 发送回复
const sendReply = async () => {
  try {
    // 实际项目中使用API调用
    // await ConsultApi.replyConsult(consult.value.id, {
    //   content: replyContent.value,
    //   attachments: replyAttachments.value
    // });

    // 模拟发送回复
    await new Promise((resolve) => setTimeout(resolve, 500));

    // 添加到聊天记录
    messages.value.push({
      isAdmin: false,
      content: replyContent.value,
      time: Date.now(),
      attachments: replyAttachments.value.map((file) => ({
        name: file.name,
        url: '#',
        type: file.type,
        size: file.size,
      })),
    });

    // 清空输入框
    replyContent.value = '';
    replyAttachments.value = [];

    // 更新最后回复时间
    consult.value.lastReplyTime = Date.now();

    // 滚动到底部
    scrollToBottom();

    $q.notify({
      color: 'positive',
      message: '回复已发送',
      icon: 'check',
    });
  } catch (error) {
    console.error('发送回复失败', error);
    $q.notify({
      color: 'negative',
      message: '发送失败，请稍后重试',
      icon: 'error',
    });
  }
};

// 确认关闭咨询
const confirmCloseConsult = () => {
  closeConsultDialog.value = true;
};

// 关闭咨询
const closeConsult = async () => {
  try {
    // 实际项目中使用API调用
    // await ConsultApi.closeConsult(consult.value.id);

    // 模拟关闭
    await new Promise((resolve) => setTimeout(resolve, 500));

    consult.value.status = 'closed';

    $q.notify({
      color: 'positive',
      message: '咨询已关闭',
      icon: 'check',
    });
  } catch (error) {
    console.error('关闭咨询失败', error);
    $q.notify({
      color: 'negative',
      message: '操作失败，请稍后重试',
      icon: 'error',
    });
  }
};

// 滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    if (chatScrollArea.value) {
      chatScrollArea.value.setScrollPosition('vertical', 100000);
    }
  });
};

// 下载附件
const downloadAttachment = (attachment) => {
  // 实际项目中使用API调用
  // window.open(attachment.url, '_blank');

  // 模拟下载
  $q.notify({
    color: 'info',
    message: `正在下载：${attachment.name}`,
    icon: 'file_download',
  });
};

// 获取附件名称
const getAttachmentName = (attachment) => {
  if (!attachment.name) return '附件';
  if (attachment.name.length > 15) {
    return attachment.name.substring(0, 12) + '...';
  }
  return attachment.name;
};

// 获取附件图标
const getAttachmentIcon = (attachment) => {
  const type = attachment.type || '';

  if (type.startsWith('image/')) {
    return 'image';
  } else if (type.includes('pdf')) {
    return 'picture_as_pdf';
  } else if (type.includes('word') || type.includes('doc')) {
    return 'description';
  } else if (type.includes('excel') || type.includes('sheet')) {
    return 'table_chart';
  }

  return 'attach_file';
};

// 文件上传被拒绝处理
const onRejected = (rejectedEntries) => {
  $q.notify({
    type: 'negative',
    message: rejectedEntries.length > 1 ? `${rejectedEntries.length} 个文件不符合要求` : '文件不符合要求',
  });
};

// 获取咨询类型颜色
const getConsultTypeColor = (type) => {
  const colors = {
    order: 'primary',
    transfer: 'purple',
    product: 'teal',
    aftersale: 'orange',
    complaint: 'red',
    other: 'blue-grey',
  };
  return colors[type] || 'grey';
};

// 获取咨询类型名称
const getConsultTypeName = (type) => {
  const names = {
    order: '订单咨询',
    transfer: '转运咨询',
    product: '商品咨询',
    aftersale: '售后服务',
    complaint: '投诉建议',
    other: '其他问题',
  };
  return names[type] || '未知类型';
};

// 获取状态颜色
const getStatusColor = (status) => {
  const colors = {
    pending: 'orange',
    replied: 'green',
    closed: 'grey',
  };
  return colors[status] || 'grey';
};

// 获取状态名称
const getStatusName = (status) => {
  const names = {
    pending: '待回复',
    replied: '已回复',
    closed: '已关闭',
  };
  return names[status] || '未知状态';
};

// 格式化日期时间
const formatDateTime = (timestamp) => {
  if (!timestamp) return '';
  const dateObj = new Date(timestamp);
  return date.formatDate(dateObj, 'YYYY-MM-DD HH:mm');
};

// 页面加载时获取咨询详情
onMounted(() => {
  loadConsultDetail();
});
</script>

<style lang="scss" scoped>
.consult-detail {
  .consult-card,
  .chat-card {
    border-radius: 8px;
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
  }

  .chat-container {
    padding: 0;
  }

  .chat-bubble {
    padding: 12px 16px;
    border-radius: 12px;
    max-width: 80%;
    word-break: break-word;
    display: inline-block;

    &.user-bubble {
      background-color: #e3f2fd;
      color: #0d47a1;
    }

    &.admin-bubble {
      background-color: #f5f5f5;
      color: #333;
    }
  }

  .reply-container {
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    padding-top: 16px;
  }

  .closed-notice {
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    padding: 16px;
  }

  // 响应式调整
  @media (max-width: 599px) {
    .chat-bubble {
      max-width: 90%;
    }
  }
}
</style>
