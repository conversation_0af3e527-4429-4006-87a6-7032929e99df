/**
 * 将后端返回的商品数据转换为前端模板数据格式
 *
 * @param backendData 后端返回的商品数据
 * @returns 转换后的前端商品数据
 */
export function convertBackendProductToTemplate(backendData) {
  if (!backendData) return null;

  // 合并所有图片到一个数组中
  const images = [];
  const propertyList = convertProductPropertyList(backendData.skus);

  // 添加商品的主图 picUrl
  if (backendData.picUrl) {
    images.push({
      image_id: images.length,
      id: backendData.id,
      alt: backendData.name, // 主图的 alt 描述
      src: backendData.picUrl,
      variant_id: null, // 主图没有关联特定的 SKU
    });
  }

  // 添加轮播图 sliderPicUrls
  if (backendData.sliderPicUrls && Array.isArray(backendData.sliderPicUrls)) {
    backendData.sliderPicUrls.forEach((url, index) => {
      images.push({
        image_id: images.length,
        id: backendData.id,
        alt: backendData.name, // 轮播图的 alt 描述
        src: url,
        variant_id: null, // 轮播图没有关联特定的 SKU
      });
    });
  }

  // 添加每个 SKU 的图片
  if (backendData.skus && Array.isArray(backendData.skus)) {
    backendData.skus.forEach((sku) => {
      if (sku.picUrl) {
        images.push({
          image_id: images.length,
          id: backendData.id,
          alt: backendData.name, //`SKU 图片 (${sku.properties.map((prop) => prop.valueName).join(', ')})`, // 使用 SKU 属性生成 alt 描述
          src: sku.picUrl,
          variant_id: sku.id, // 关联到具体的 SKU
        });
      }
    });
  }

  // 返回转换后的模板商品数据
  return {
    id: backendData.id,
    title: backendData.name,
    introduction: backendData.introduction,
    keyword: backendData.keyword,
    description: backendData.description || '', // 简介
    type: '', // 类型字段后端没有，设置为空
    brand: '', // 品牌字段后端没有，设置为空
    collection: [], // 集合字段后端没有，设置为空数组
    category: backendData.categoryId, // 后端的 categoryId 可以映射为具体分类名称，这里需额外逻辑处理
    price: backendData.price, // 转换为元
    marketPrice: backendData.marketPrice,
    discount: backendData.price < backendData.marketPrice ? (((backendData.marketPrice - backendData.price) / backendData.marketPrice) * 100).toFixed(0) : '', // 折扣百分比
    stock: backendData.stock, // 库存
    scores: backendData.scores,
    newest: backendData.newest,
    sale: backendData.sale,
    hot: backendData.hot,
    new: false, // 是否新品，后端没有，默认为 false
    tags: [], // 标签后端没有，可以添加需要的标签逻辑
    variants: backendData.skus.map((sku) => ({
      variant_id: sku.id,
      id: backendData.id,
      sku: sku.id, // SKU 编号
      size: sku.properties.find((prop) => prop.propertyName === '尺寸')?.valueName || '', // 取尺寸属性值
      color: sku.properties.find((prop) => prop.propertyName === '颜色')?.valueName || '', // 取颜色属性值
      image_id: sku.id, // 假设 SKU 编号作为图片关联 ID
    })),
    images, // 所有图片按顺序组织
    skus: backendData.skus,
    propertyList: propertyList,
  };
}

/**
 * 从商品 SKU 数组中，转换出商品属性的数组
 *
 * 类似结构：[{
 *    id: // 属性的编号
 *    name: // 属性的名字
 *    values: [{
 *      id: // 属性值的编号
 *      name: // 属性值的名字
 *    }]
 * }]
 *
 * @param skus 商品 SKU 数组
 */
export function convertProductPropertyList(skus) {
  let result = [];
  if (!skus) {
    return result;
  }
  for (const sku of skus) {
    if (!sku.properties) {
      continue;
    }
    for (const property of sku.properties) {
      // ① 先处理属性
      let resultProperty = result.find((item) => item.id === property.propertyId);
      if (!resultProperty) {
        resultProperty = {
          id: property.propertyId,
          name: property.propertyName,
          values: [],
        };
        result.push(resultProperty);
      }
      // ② 再处理属性值
      let resultValue = resultProperty.values.find((item) => item.id === property.valueId);
      if (!resultValue) {
        resultProperty.values.push({
          id: property.valueId,
          name: property.valueName,
        });
      }
    }
  }
  return result;
}

/**
 * 将后端数据格式转换为前端模板需要的数据格式
 * @param {Array} spus - 后端商品数据列表
 * @returns {Array} 转换后的商品数据列表
 */
export function formatProducts(spus) {
  if (!spus || !Array.isArray(spus)) {
    // console.error('spus 数据不存在或格式不正确');
    return []; // 确保返回值至少为空数组
  }

  return spus.map((product) => ({
    id: product.id,
    title: product.name,
    description: '', // 无对应字段，置空
    type: '', // 无对应字段，置空
    brand: '', // 无对应字段，置空
    collection: [], // 无对应字段，置空
    category: '', // 无对应字段，置空
    price: product.price, // 分转元
    marketPrice: product.marketPrice,
    discount: '', // 无对应字段，置空
    stock: product.stock,
    scores: product.scores,
    newest: product.newest,
    sale: product.sale,
    hot: product.hot,
    new: false, // 无对应字段，置空
    tags: [], // 无对应字段，置空
    variants: [], // 无对应字段，置空
    picUrl: product.picUrl,
    images: [
      {
        image_id: 1,
        id: product.id,
        alt: '',
        src: product.picUrl, // 对应图片 URL
        variant_id: [],
      },
    ],
  }));
}

// export function formattedProperties(properties) {
//   return properties.map((prop) => `${prop.propertyName}: ${prop.valueName}`).join(' , ');
// }

export function formattedProperties(properties) {
  if (!properties) {
    return '';
  }
  return properties
    .filter((prop) => prop.propertyName !== prop.valueName) // 过滤掉属性名和属性值一样的字段
    .map((prop) => `${prop.propertyName}: ${prop.valueName}`)
    .join(' , ');
}

// 生成 URL 友好的 slug 方法
export function generateSlug(name) {
  if (!name) {
    return ''; // 如果 name 为空，则返回空字符串
  }
  // return name
  //   .toLowerCase()
  //   .replace(/&/g, 'and') // `&` → `and`
  //   .replace(/\//g, '-') // `/` → `-`
  //   .replace(/[^a-z0-9-]+/g, '-') // 其他字符 → `-`
  //   .replace(/-+/g, '-') // 多个 `-` 变成单个 `-`
  //   .replace(/^-|-$/g, ''); // 移除首尾 `-`

  return name
    .toLowerCase() // 仅针对英文部分小写，不影响汉字
    .replace(/&/g, 'and') // `&` → `and`
    .replace(/\//g, '-') // `/` → `-`，防止 URL 解析错误
    .replace(/[^a-z0-9\p{Script=Han}-]+/gu, '-') // 允许汉字，去除其他特殊字符
    .replace(/-+/g, '-') // 多个 `-` 变成单个 `-`
    .replace(/^-|-$/g, ''); // 移除首尾 `-`
}

/**
 * 获取缩略图URL
 * @param {string} url - 原始图片URL
 * @param {string} size - 缩略图尺寸，例如 '80x80'
 * @returns {string} 缩略图URL
 */
// export function getThumbnailUrl(url, size = '80x80') {
//   if (!url) {
//     return '/images/placeholder.png'; // 返回默认占位图
//   }

//   // 如果URL已经包含缩略图参数，则不再添加
//   if (url.includes('?x-oss-process=image')) {
//     return url;
//   }

//   // 添加阿里云OSS缩略图处理参数
//   return `${url}?x-oss-process=image/resize,m_pad,h_${size.split('x')[0]},w_${size.split('x')[1]}`;
// }
