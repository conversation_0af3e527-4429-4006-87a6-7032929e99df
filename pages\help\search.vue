<template>
  <Header />
  <div class="help-search-page">
    <div class="help-search-container">
      <!-- 使用共享的标题和搜索框组件 -->
      <HelpHeader :title="$t('help.search.title')" v-model="searchQuery" @search="performSearch" />

      <div class="help-content-wrapper">
        <!-- 左侧导航菜单 -->
        <HelpSidebar :categories="helpCategories" />

        <!-- 右侧内容区域 -->
        <div class="help-content">
          <!-- 面包屑导航 -->
          <div class="breadcrumbs q-mb-md">
            <q-breadcrumbs separator=">" class="text-grey">
              <q-breadcrumbs-el :label="$t('help.title')" to="/help" />
              <q-breadcrumbs-el :label="$t('help.search.title')" />
            </q-breadcrumbs>
          </div>

          <!-- 搜索结果 -->
          <div v-if="!loading && searchResults.length > 0" class="search-results">
            <p class="results-count">{{ $t('help.search.resultsCount', { count: totalResults }) }}</p>

            <div class="search-results-list">
              <div v-for="(result, index) in searchResults" :key="index" class="search-result-item" @click="navigateToArticle(result)">
                <div class="result-icon">
                  <q-icon name="article" size="xs" color="primary" />
                </div>
                <div class="result-content">
                  <div class="result-header">
                    <h3 class="result-title">{{ result.title }}</h3>
                    <span class="result-category">
                      <q-icon name="folder" size="xs" class="q-mr-xs" />
                      {{ result.categoryTitle }}
                    </span>
                  </div>
                  <p class="result-snippet" v-if="result.snippet">
                    <span v-html="highlightSearchTerms(result.snippet)"></span>
                  </p>
                  <div class="result-meta">
                    <span class="result-date" v-if="result.updateTime">
                      <q-icon name="update" size="xs" class="q-mr-xs" />
                      {{ formatDate(result.updateTime) }}
                    </span>
                    <span class="result-views" v-if="result.viewCount">
                      <q-icon name="visibility" size="xs" class="q-mr-xs" />
                      {{ result.viewCount }} {{ $t('help.detail.views') }}
                    </span>
                  </div>
                </div>
                <div class="result-arrow">
                  <q-icon name="chevron_right" size="xs" color="grey" />
                </div>
              </div>
            </div>

            <!-- 分页 -->
            <div class="pagination-container" v-if="totalPages > 1">
              <q-pagination v-model="currentPage" :max="totalPages" :max-pages="5" boundary-links direction-links @update:model-value="handlePageChange" />
              <span class="pagination-info">{{ $t('help.pagination.info', { total: totalResults, current: currentPage, pages: totalPages }) }}</span>
            </div>
          </div>

          <!-- 无搜索结果 -->
          <div v-else-if="!loading && searchPerformed" class="no-results">
            <div class="no-results-icon">
              <q-icon name="search_off" size="5rem" color="grey-5" />
            </div>
            <h2 class="no-results-title">{{ $t('help.search.noResults', { query: searchQuery }) }}</h2>
            <div class="search-suggestions">
              <h3 class="suggestions-title">{{ $t('help.search.suggestions.title') }}</h3>
              <div class="suggestions-list">
                <div class="suggestion-item">
                  <q-icon name="spellcheck" color="primary" size="sm" class="q-mr-sm" />
                  <span>{{ $t('help.search.suggestions.checkSpelling') }}</span>
                </div>
                <div class="suggestion-item">
                  <q-icon name="text_fields" color="primary" size="sm" class="q-mr-sm" />
                  <span>{{ $t('help.search.suggestions.trySimpler') }}</span>
                </div>
                <div class="suggestion-item">
                  <q-icon name="swap_horiz" color="primary" size="sm" class="q-mr-sm" />
                  <span>{{ $t('help.search.suggestions.tryRelated') }}</span>
                </div>
                <div class="suggestion-item">
                  <q-icon name="category" color="primary" size="sm" class="q-mr-sm" />
                  <span>{{ $t('help.search.suggestions.browseCategories') }}</span>
                </div>
              </div>
            </div>
            <q-btn color="primary" :to="'/help'" class="browse-all-btn q-mt-lg" unelevated rounded no-caps icon="home" :label="$t('help.search.browseAllHelp')" />
          </div>

          <!-- 加载中状态 -->
          <div v-else-if="loading" class="loading-state">
            <q-spinner-dots color="primary" size="4em" />
            <h3 class="loading-text q-mt-md">{{ $t('help.search.loading') }}</h3>
          </div>

          <!-- 初始状态 -->
          <div v-else class="initial-state">
            <div class="initial-icon">
              <q-icon name="search" size="4rem" color="primary" />
            </div>
            <h2 class="initial-title">{{ $t('help.search.initialText') }}</h2>
          </div>
        </div>
      </div>
    </div>
  </div>
  <Footer />
</template>

<script setup>
import { ref, onMounted, watch, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import HelpApi from '~/composables/helpApi';
import { useHelpStore } from '~/store/help';
import HelpSidebar from '~/components/help/HelpSidebar.vue';
import HelpHeader from '~/components/help/HelpHeader.vue';

const route = useRoute();
const router = useRouter();
const { locale } = useI18n();

// 日期格式化函数
const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return new Intl.DateTimeFormat(locale.value, {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  }).format(date);
};

// 状态变量
const searchQuery = ref('');
const searchResults = ref([]);
const loading = ref(false);
const searchPerformed = ref(false);
const currentPage = ref(1);
const totalResults = ref(0);
const totalPages = ref(1);
const pageSize = ref(10);

// 使用Pinia Store获取帮助分类数据
const helpStore = useHelpStore();

// 获取帮助分类数据
const helpCategories = computed(() => helpStore.getCategories);

// 从URL获取搜索参数
onMounted(() => {
  if (route.query.q) {
    searchQuery.value = route.query.q;
    performSearch();
  }

  // 如果store中没有数据，则获取数据
  if (!helpStore.isInitialized) {
    helpStore.fetchCategories();
  }
});

// 监听路由变化，更新搜索参数
watch(
  () => route.query.q,
  (newQuery) => {
    if (newQuery && newQuery !== searchQuery.value) {
      searchQuery.value = newQuery;
      performSearch();
    }
  }
);

// 执行搜索
const performSearch = async () => {
  if (!searchQuery.value.trim()) return;

  loading.value = true;
  searchPerformed.value = true;

  try {
    // 更新URL，但不触发路由变化
    router.replace(
      {
        path: '/help/search',
        query: {
          q: searchQuery.value,
          page: currentPage.value > 1 ? currentPage.value : undefined,
        },
      },
      { replace: true }
    );

    // 调用搜索API
    const { code, data } = await HelpApi.search({
      keyword: searchQuery.value,
      page: currentPage.value,
      pageSize: pageSize.value,
      language: locale.value,
    });

    if (code === 0 && data) {
      searchResults.value = data.list.map((item) => {
        // 处理搜索结果，尝试从store中获取分类信息
        const categoryCode = item.categoryCode || findCategoryCodeByArticleId(item.id);
        return {
          ...item,
          categoryCode: categoryCode,
          categoryTitle: getCategoryTitle(categoryCode),
        };
      });
      totalResults.value = data.total;
      totalPages.value = Math.ceil(data.total / pageSize.value);
    } else {
      searchResults.value = [];
      totalResults.value = 0;
      totalPages.value = 1;
    }
  } catch (error) {
    console.error('Search failed:', error);
    searchResults.value = [];
    totalResults.value = 0;
    totalPages.value = 1;
  } finally {
    loading.value = false;
  }
};

// 处理分页变化
const handlePageChange = (page) => {
  currentPage.value = page;
  performSearch();

  // 滚动到页面顶部
  window.scrollTo({ top: 0, behavior: 'smooth' });
};

// 获取分类标题
const getCategoryTitle = (categoryCode) => {
  if (!categoryCode) return '未分类';

  const categories = helpStore.getCategories;
  const category = categories.find((cat) => cat.code === categoryCode);
  return category ? category.title : categoryCode;
};

// 根据文章ID查找分类Code
const findCategoryCodeByArticleId = (articleId) => {
  if (!articleId) return '';

  const categories = helpStore.getCategories;

  // 遍历所有分类
  for (const category of categories) {
    if (category.items && Array.isArray(category.items)) {
      // 在分类的文章列表中查找匹配的文章
      const article = category.items.find((item) => item.id === articleId);
      if (article) {
        return category.code;
      }
    }
  }

  return '';
};

// 导航到文章详情页
const navigateToArticle = (article) => {
  if (!article) return;

  // 如果有分类Code，使用常规路径
  if (article.categoryCode) {
    router.push(`/help/${article.categoryCode}/${article.code}`);
  } else {
    // 尝试从store中查找分类Code
    const categoryCode = findCategoryCodeByArticleId(article.id);
    if (categoryCode) {
      router.push(`/help/${categoryCode}/${article.code}`);
    } else {
      // 如果仍然找不到分类Code，使用一个默认值
      console.warn(`无法找到文章 ${article.id} 的分类Code，使用默认值`);
      router.push(`/help/uncategorized/${article.code}`);
    }
  }
};

// 高亮搜索词
const highlightSearchTerms = (text) => {
  if (!text || !searchQuery.value.trim()) return text;

  const terms = searchQuery.value.trim().split(/\s+/);
  let highlightedText = text;

  terms.forEach((term) => {
    if (term.length < 2) return; // 忽略过短的词

    const regex = new RegExp(`(${term})`, 'gi');
    highlightedText = highlightedText.replace(regex, '<span class="highlight">$1</span>');
  });

  return highlightedText;
};
</script>

<style lang="scss" scoped>
.help-search-page {
  background-color: #f5f7fa;
  padding: 30px 0 50px;
}

.help-search-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

.help-content-wrapper {
  display: flex;
  gap: 30px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.help-content {
  flex-grow: 1;
  padding: 20px;
  overflow-y: auto;
  min-height: 600px;
}

.results-count {
  font-size: 14px;
  color: #666;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #eee;
}

.search-results-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.search-result-item {
  display: flex;
  padding: 8px 10px;
  border-radius: 6px;
  border: 1px solid #eee;
  transition: all 0.3s ease;
  cursor: pointer;
  align-items: center;
  min-height: 48px;

  &:hover {
    background-color: #f9f9f9;
    border-color: #ddd;

    .result-title {
      color: var(--q-primary);
    }

    .result-arrow {
      opacity: 1;
      transform: translateX(0);
    }
  }

  .result-icon {
    margin-right: 8px;
  }

  .result-content {
    flex: 1;
    min-width: 0; // 防止内容溢出
  }

  .result-header {
    display: flex;
    align-items: center;
    margin-bottom: 2px;
    flex-wrap: wrap;
    gap: 6px;
  }

  .result-title {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin: 0;
    transition: color 0.3s ease;
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .result-category {
    font-size: 11px;
    color: #666;
    background-color: #f5f5f5;
    padding: 1px 6px;
    border-radius: 10px;
    display: inline-flex;
    align-items: center;
  }

  .result-snippet {
    font-size: 12px;
    color: #666;
    margin: 2px 0;
    line-height: 1.4;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    -webkit-box-orient: vertical;

    :deep(.highlight) {
      background-color: rgba(255, 213, 79, 0.4);
      padding: 0 2px;
      border-radius: 2px;
      font-weight: 500;
    }
  }

  .result-meta {
    display: flex;
    gap: 12px;
    font-size: 11px;
    color: #999;
    margin-top: 2px;

    .result-date,
    .result-views {
      display: flex;
      align-items: center;
    }
  }

  .result-arrow {
    margin-left: 6px;
    opacity: 0.5;
    transition: all 0.3s ease;
    transform: translateX(-4px);
  }
}

.pagination-container {
  margin-top: 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;

  :deep(.q-pagination) {
    .q-btn {
      border-radius: 4px;
    }
  }

  .pagination-info {
    font-size: 14px;
    color: #666;
  }
}

.no-results,
.loading-state,
.initial-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
  color: #666;
  padding: 40px 20px;
}

.no-results-icon,
.initial-icon {
  margin-bottom: 24px;
  opacity: 0.8;
}

.no-results-title,
.initial-title,
.loading-text {
  font-size: 20px;
  font-weight: 500;
  color: #333;
  margin: 0 0 24px;
}

.search-suggestions {
  max-width: 500px;
  margin: 0 auto 32px;
  background-color: #f9f9f9;
  padding: 20px;
  border-radius: 8px;

  .suggestions-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 16px;
    color: #333;
    text-align: center;
  }

  .suggestions-list {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .suggestion-item {
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #555;
    }
  }
}

.browse-all-btn {
  font-weight: 500;
  letter-spacing: 0.5px;
  padding: 8px 20px;
}

@media (max-width: 1023px) {
  .help-content-wrapper {
    flex-direction: column;
  }

  .help-content {
    padding: 16px;
  }
}

@media (max-width: 767px) {
  .search-result-item {
    padding: 6px 8px;
    min-height: 40px;

    .result-icon {
      margin-right: 6px;
    }

    .result-title {
      font-size: 13px;
    }

    .result-snippet {
      font-size: 12px;
      -webkit-line-clamp: 1;
      line-clamp: 1;
    }

    .result-category {
      font-size: 10px;
      padding: 1px 4px;
    }

    .result-meta {
      font-size: 10px;
      gap: 8px;
    }
  }

  .no-results-title,
  .initial-title {
    font-size: 18px;
  }

  .search-suggestions {
    padding: 16px;

    .suggestions-title {
      font-size: 15px;
    }

    .suggestions-list {
      .suggestion-item {
        font-size: 13px;
      }
    }
  }
}
</style>
