<template>
  <Header />
  <div class="help-detail-page">
    <div class="help-detail-container">
      <!-- 添加标题和搜索框 -->
      <HelpHeader :title="helpContent ? helpContent.title : ''" />

      <div class="help-content-wrapper">
        <!-- 左侧导航菜单 -->
        <HelpSidebar :categories="helpCategories" :current-category-code="currentCategoryCode" />

        <!-- 右侧内容区域 -->
        <div class="help-content">
          <div v-if="helpContent" class="help-article">
            <!-- 面包屑导航 -->
            <div class="breadcrumbs q-mb-md">
              <q-breadcrumbs separator=">" class="text-grey">
                <q-breadcrumbs-el :label="$t('help.title')" to="/help" />
                <q-breadcrumbs-el :label="currentCategory?.title" :to="`/help/category/${currentCategoryCode}`" />
                <q-breadcrumbs-el :label="helpContent.title" />
              </q-breadcrumbs>
            </div>

            <!-- 文章标题 -->
            <h1 class="article-title">{{ helpContent.title }}</h1>

            <!-- 文章元信息 -->
            <div class="article-meta">
              <div class="meta-item">
                <q-icon name="update" size="xs" class="q-mr-xs" />
                <span>{{ $t('help.detail.updatedAt') }}: {{ formatTimestampToWesternDate(helpContent.updateTime) }}</span>
              </div>
              <div class="meta-item">
                <q-icon name="visibility" size="xs" class="q-mr-xs" />
                <span>{{ helpContent.browseCount }} {{ $t('help.detail.views') }}</span>
              </div>
            </div>

            <!-- 文章内容 -->
            <div class="article-content" v-html="helpContent.content"></div>

            <!-- 文章评价 -->
            <div class="article-feedback">
              <p class="feedback-title">{{ $t('help.detail.feedback.title') }}</p>
              <div class="feedback-buttons">
                <q-btn
                  outline
                  :color="feedback === 'helpful' ? 'positive' : 'grey'"
                  icon="thumb_up"
                  :label="$t('help.detail.feedback.helpful')"
                  class="gt-xs:q-mr-sm"
                  @click="submitFeedback('helpful')"
                  :disable="feedback !== null"
                  :title="feedback !== null ? '您已提交过反馈' : ''">
                  <q-badge v-if="helpContent.helpfulCount > 0" color="positive" floating>{{ helpContent.helpfulCount }}</q-badge>
                </q-btn>
                <q-btn
                  outline
                  :color="feedback === 'not-helpful' ? 'negative' : 'grey'"
                  icon="thumb_down"
                  :label="$t('help.detail.feedback.notHelpful')"
                  @click="submitFeedback('not-helpful')"
                  :disable="feedback !== null"
                  :title="feedback !== null ? '您已提交过反馈' : ''">
                  <q-badge v-if="helpContent.unhelpfulCount > 0" color="negative" floating>{{ helpContent.unhelpfulCount }}</q-badge>
                </q-btn>
              </div>
            </div>

            <!-- 相关文章 -->
            <div v-if="relatedArticles.length > 0" class="related-articles">
              <h3 class="related-title">{{ $t('help.detail.relatedArticles') }}</h3>
              <q-list bordered separator>
                <q-item v-for="(article, index) in relatedArticles" :key="index" :to="article.link" clickable v-ripple class="related-article-item">
                  <q-item-section avatar>
                    <q-icon name="article" color="primary" />
                  </q-item-section>
                  <q-item-section>
                    <q-item-label>{{ article.title }}</q-item-label>
                  </q-item-section>
                  <q-item-section side>
                    <q-icon name="chevron_right" color="grey" />
                  </q-item-section>
                </q-item>
              </q-list>
            </div>
          </div>

          <!-- 加载中状态 -->
          <div v-else-if="loading" class="loading-state">
            <q-spinner color="primary" size="3em" />
            <div class="q-mt-md">{{ $t('help.detail.loading') }}</div>
          </div>

          <!-- 错误状态 -->
          <div v-else class="error-state">
            <q-icon name="error_outline" color="negative" size="3em" />
            <div class="q-mt-md">{{ $t('help.detail.notFound') }}</div>
            <q-btn color="primary" to="/help" class="q-mt-md"> {{ $t('help.detail.backToHelp') }} </q-btn>
          </div>
        </div>
      </div>
    </div>
  </div>
  <Footer />
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute } from 'vue-router';
import { useQuasar } from 'quasar';
import HelpSidebar from '~/components/help/HelpSidebar.vue';
import HelpHeader from '~/components/help/HelpHeader.vue';
import HelpApi from '~/composables/helpApi';
import { useHelpStore } from '~/store/help';

const $q = useQuasar();
const route = useRoute();

// 当前分类和文章code
const currentCategoryCode = computed(() => route.params.category);
const currentItemCode = computed(() => route.params.item);

// 状态
const loading = ref(true);
const helpContent = ref(null);
const feedback = ref(null);

// 使用Pinia Store获取帮助分类数据
const helpStore = useHelpStore();
const helpCategories = computed(() => helpStore.getCategories);

// 帮助内容数据
const helpContentData = {
  account: {
    register: {
      title: '如何注册新账户',
      updatedAt: '2023-08-15',
      views: 5678,
      content: `
        <h2>注册新账户的步骤</h2>
        <p>在我们的平台注册账户非常简单，只需按照以下步骤操作：</p>
        <ol>
          <li>访问我们的网站首页，点击右上角的"登录/注册"按钮。</li>
          <li>在弹出的登录框中，点击"注册新账户"。</li>
          <li>填写您的手机号码，并点击"获取验证码"。</li>
          <li>输入您收到的短信验证码。</li>
          <li>设置您的登录密码（密码长度为8-20位，必须包含字母和数字）。</li>
          <li>阅读并同意用户协议和隐私政策。</li>
          <li>点击"完成注册"按钮。</li>
        </ol>

        <h2>注册成功后</h2>
        <p>注册成功后，您将自动登录到您的账户。我们建议您立即完成以下操作：</p>
        <ul>
          <li>完善您的个人资料，包括姓名、性别、生日等信息。</li>
          <li>添加您的常用收货地址。</li>
          <li>绑定您的邮箱，以便接收订单通知和促销信息。</li>
          <li>设置安全问题，以便在忘记密码时进行身份验证。</li>
        </ul>

        <h2>注册常见问题</h2>
        <h3>1. 为什么我没有收到验证码？</h3>
        <p>可能的原因：</p>
        <ul>
          <li>您输入的手机号码有误。</li>
          <li>短信可能被运营商拦截或延迟。</li>
          <li>您的手机可能设置了短信拦截。</li>
        </ul>
        <p>解决方法：检查手机号码是否正确，或尝试点击"重新发送"按钮。如果问题仍然存在，请联系客服。</p>

        <h3>2. 提示手机号已注册怎么办？</h3>
        <p>如果系统提示您的手机号已注册，说明您之前已经注册过账户。您可以：</p>
        <ul>
          <li>直接使用该手机号登录（如果您记得密码）。</li>
          <li>点击"忘记密码"进行密码重置。</li>
        </ul>

        <h3>3. 注册时提示"系统繁忙"怎么办？</h3>
        <p>这可能是由于网络问题或系统临时维护导致的。请稍后再试，或尝试使用不同的网络环境。</p>
      `,
    },
    password: {
      title: '修改密码',
      updatedAt: '2023-07-20',
      views: 4321,
      content: `
        <h2>如何修改登录密码</h2>
        <p>为了保障账户安全，我们建议您定期更换密码。修改密码的步骤如下：</p>
        <ol>
          <li>登录您的账户。</li>
          <li>点击右上角的用户头像，进入"个人中心"。</li>
          <li>在左侧菜单中选择"账户安全"。</li>
          <li>点击"修改密码"按钮。</li>
          <li>输入您的当前密码。</li>
          <li>输入新密码并确认（密码长度为8-20位，必须包含字母和数字）。</li>
          <li>点击"确认修改"按钮。</li>
        </ol>
        <p>修改成功后，系统会自动退出登录，您需要使用新密码重新登录。</p>

        <h2>忘记密码怎么办</h2>
        <p>如果您忘记了密码，可以通过以下步骤重置：</p>
        <ol>
          <li>在登录页面点击"忘记密码"。</li>
          <li>输入您注册时使用的手机号码。</li>
          <li>点击"获取验证码"，并输入您收到的短信验证码。</li>
          <li>设置新密码并确认。</li>
          <li>点击"确认"按钮完成密码重置。</li>
        </ol>

        <h2>密码安全建议</h2>
        <ul>
          <li>密码长度建议不少于12位。</li>
          <li>密码应包含大小写字母、数字和特殊符号。</li>
          <li>避免使用生日、手机号等容易被猜测的信息作为密码。</li>
          <li>不要在不同网站使用相同的密码。</li>
          <li>定期更换密码（建议每3个月更换一次）。</li>
          <li>不要将密码告诉他人或在不安全的地方记录密码。</li>
        </ul>

        <h2>常见问题</h2>
        <h3>1. 修改密码后无法登录怎么办？</h3>
        <p>请确认您输入的新密码正确，注意大小写。如果仍然无法登录，请使用"忘记密码"功能重置密码。</p>

        <h3>2. 多次输入错误密码账户被锁定怎么办？</h3>
        <p>为了保护您的账户安全，连续多次输入错误密码后，账户会被临时锁定30分钟。您可以等待30分钟后再尝试登录，或者使用"忘记密码"功能重置密码。</p>
      `,
    },
  },
  order: {
    modify: {
      title: '修改订单',
      updatedAt: '2023-09-05',
      views: 3456,
      content: `
        <h2>订单修改规则</h2>
        <p>为了保证订单处理和物流配送的效率，订单修改受到以下限制：</p>
        <ul>
          <li>只有在"待付款"状态的订单才能进行修改。</li>
          <li>已付款的订单无法直接修改，需要取消订单后重新下单。</li>
          <li>部分特殊商品（如预售商品、定制商品等）的订单可能无法修改。</li>
        </ul>

        <h2>如何修改待付款订单</h2>
        <p>如果您的订单还未支付，可以按照以下步骤修改：</p>
        <ol>
          <li>登录您的账户。</li>
          <li>进入"个人中心" > "我的订单"。</li>
          <li>找到需要修改的订单，确认其状态为"待付款"。</li>
          <li>点击订单右侧的"修改订单"按钮。</li>
          <li>在修改页面中，您可以：
            <ul>
              <li>修改收货地址</li>
              <li>修改收货人信息</li>
              <li>修改配送方式</li>
              <li>添加/修改订单备注</li>
            </ul>
          </li>
          <li>确认修改无误后，点击"保存修改"按钮。</li>
        </ol>
        <p>注意：修改订单可能会导致运费、配送时间等发生变化，请在确认修改前仔细核对。</p>

        <h2>已付款订单的修改方法</h2>
        <p>如果您的订单已经付款，但尚未发货，您可以尝试以下方法：</p>
        <ol>
          <li>联系客服，说明您的订单号和需要修改的内容。</li>
          <li>如果修改内容较大（如更换商品、更改配送地址等），客服可能会建议您取消订单后重新下单。</li>
          <li>如果是小幅修改（如修改订单备注、联系电话等），客服可能会直接帮您处理。</li>
        </ol>

        <h2>订单取消后重新下单</h2>
        <p>如果您需要对已付款订单进行较大修改，可能需要取消订单后重新下单：</p>
        <ol>
          <li>联系客服申请取消订单。</li>
          <li>等待退款到账（一般1-7个工作日，具体以银行实际处理时间为准）。</li>
          <li>重新下单并选择正确的商品、地址等信息。</li>
        </ol>
        <p>注意：取消订单可能会导致您错过促销活动、优惠券等优惠，请谨慎操作。</p>

        <h2>常见问题</h2>
        <h3>1. 修改订单会影响发货时间吗？</h3>
        <p>是的，修改订单可能会导致订单重新进入处理流程，从而延长发货时间。</p>

        <h3>2. 修改订单后能否再次使用已使用的优惠券？</h3>
        <p>如果您取消订单后重新下单，已使用的优惠券是否可以再次使用取决于优惠券的具体规则。一次性优惠券取消订单后通常不会返还，限时优惠券如果在有效期内可能可以再次使用。</p>

        <h3>3. 部分发货的订单能否修改？</h3>
        <p>对于已部分发货的订单，已发货部分无法修改，未发货部分可以联系客服尝试修改。</p>
      `,
    },
  },
  shipping: {
    tracking: {
      title: '物流跟踪',
      updatedAt: '2023-08-25',
      views: 6789,
      content: `
        <h2>如何跟踪我的包裹</h2>
        <p>我们提供多种方式让您随时了解包裹的配送状态：</p>
        <ol>
          <li><strong>网站订单详情页</strong>：登录账户后，在"我的订单"中点击对应订单，即可查看最新物流状态。</li>
          <li><strong>APP订单跟踪</strong>：在我们的手机APP中，"订单"页面提供实时物流跟踪功能。</li>
          <li><strong>短信通知</strong>：系统会在包裹发出、即将送达等关键节点向您发送短信通知。</li>
          <li><strong>物流公司官网</strong>：您可以使用订单详情页提供的物流单号，前往对应物流公司官网查询。</li>
        </ol>

        <h2>物流状态说明</h2>
        <table border="1" cellpadding="8" cellspacing="0" style="width: 100%; border-collapse: collapse;">
          <tr>
            <th>状态</th>
            <th>说明</th>
          </tr>
          <tr>
            <td>待发货</td>
            <td>订单已确认，等待商家发货</td>
          </tr>
          <tr>
            <td>已发货</td>
            <td>商家已发货，包裹在途中</td>
          </tr>
          <tr>
            <td>运输中</td>
            <td>包裹正在配送途中</td>
          </tr>
          <tr>
            <td>派送中</td>
            <td>包裹已到达目的地城市，正在进行最后一公里配送</td>
          </tr>
          <tr>
            <td>已签收</td>
            <td>包裹已被签收</td>
          </tr>
          <tr>
            <td>配送异常</td>
            <td>配送过程中遇到问题，如无人签收、地址错误等</td>
          </tr>
        </table>

        <h2>常见物流问题</h2>
        <h3>1. 物流信息长时间未更新怎么办？</h3>
        <p>物流信息更新可能存在延迟，特别是在物流高峰期或特殊天气情况下。如果超过48小时未更新，您可以：</p>
        <ul>
          <li>联系我们的客服，提供订单号咨询具体情况。</li>
          <li>直接联系物流公司，提供物流单号查询。</li>
        </ul>

        <h3>2. 包裹显示已签收但我没有收到怎么办？</h3>
        <p>如果系统显示包裹已签收，但您确认未收到，可能是以下原因：</p>
        <ul>
          <li>包裹被代签（家人、门卫、邻居等）。</li>
          <li>包裹被放置在快递柜或代收点。</li>
          <li>系统更新错误。</li>
          <li>极少数情况下可能存在冒领情况。</li>
        </ul>
        <p>建议您：</p>
        <ol>
          <li>询问家人或门卫是否代签。</li>
          <li>查看是否有短信通知包裹被放在快递柜。</li>
          <li>联系配送员确认投递位置。</li>
          <li>如确认包裹丢失，请联系客服申请赔偿。</li>
        </ol>

        <h3>3. 如何修改收货地址？</h3>
        <p>如果包裹尚未发货，您可以在订单详情页修改收货地址。如果已发货，您需要：</p>
        <ol>
          <li>立即联系客服，提供订单号和新地址。</li>
          <li>客服会尝试联系物流公司修改，但不保证一定能成功。</li>
          <li>如无法修改，可能需要等包裹退回后重新发货。</li>
        </ol>
      `,
    },
    address: {
      title: '收货地址管理',
      updatedAt: '2023-07-10',
      views: 3210,
      content: `
        <h2>如何添加新的收货地址</h2>
        <p>您可以在个人中心添加和管理多个收货地址，步骤如下：</p>
        <ol>
          <li>登录您的账户。</li>
          <li>点击右上角的用户头像，进入"个人中心"。</li>
          <li>在左侧菜单中选择"收货地址"。</li>
          <li>点击"添加新地址"按钮。</li>
          <li>填写收货人姓名、手机号码、所在地区和详细地址等信息。</li>
          <li>如需设为默认地址，请勾选"设为默认地址"。</li>
          <li>点击"保存"按钮完成添加。</li>
        </ol>

        <h2>如何修改收货地址</h2>
        <p>如果您需要修改已保存的收货地址，请按照以下步骤操作：</p>
        <ol>
          <li>进入"个人中心" > "收货地址"。</li>
          <li>找到需要修改的地址，点击右侧的"编辑"按钮。</li>
          <li>在弹出的编辑框中修改相关信息。</li>
          <li>点击"保存"按钮完成修改。</li>
        </ol>

        <h2>如何删除收货地址</h2>
        <p>如果您不再需要某个收货地址，可以将其删除：</p>
        <ol>
          <li>进入"个人中心" > "收货地址"。</li>
          <li>找到需要删除的地址，点击右侧的"删除"按钮。</li>
          <li>在弹出的确认框中点击"确认删除"。</li>
        </ol>
        <p>注意：默认地址无法直接删除，您需要先设置其他地址为默认地址，然后才能删除原默认地址。</p>

        <h2>设置默认收货地址</h2>
        <p>默认收货地址会在您下单时自动选中，设置方法如下：</p>
        <ol>
          <li>进入"个人中心" > "收货地址"。</li>
          <li>找到需要设为默认的地址，点击右侧的"设为默认"按钮。</li>
          <li>或者在编辑/添加地址时，勾选"设为默认地址"选项。</li>
        </ol>

        <h2>收货地址填写建议</h2>
        <ul>
          <li>收货人姓名应与身份证上的姓名一致，特别是购买需要实名的商品时。</li>
          <li>手机号码务必准确，这是配送员联系您的主要方式。</li>
          <li>详细地址应尽量具体，包含小区名称、楼栋号、单元号和门牌号等信息。</li>
          <li>如有特殊情况（如小区门禁密码、配送时间要求等），可在地址备注中说明。</li>
        </ul>

        <h2>常见问题</h2>
        <h3>1. 为什么我无法添加新地址？</h3>
        <p>可能的原因：</p>
        <ul>
          <li>您已达到地址数量上限（一般为20个）。</li>
          <li>填写的信息不完整或格式不正确。</li>
          <li>系统临时故障。</li>
        </ul>
        <p>解决方法：删除不常用的地址，或联系客服处理。</p>

        <h3>2. 下单后如何修改收货地址？</h3>
        <p>如果订单尚未付款，您可以在订单详情页修改收货地址。如果已付款但尚未发货，请立即联系客服申请修改。如果订单已发货，请参考"物流跟踪"中的相关说明。</p>
      `,
    },
  },
  payment: {
    coupon: {
      title: '优惠券使用说明',
      updatedAt: '2023-09-10',
      views: 8765,
      content: `
        <h2>优惠券类型</h2>
        <p>我们平台提供多种类型的优惠券，主要包括：</p>
        <table border="1" cellpadding="8" cellspacing="0" style="width: 100%; border-collapse: collapse;">
          <tr>
            <th>类型</th>
            <th>说明</th>
            <th>使用限制</th>
          </tr>
          <tr>
            <td>满减券</td>
            <td>订单满一定金额后可减免一定金额<br>例如：满300减50</td>
            <td>需要达到指定的订单金额才能使用</td>
          </tr>
          <tr>
            <td>折扣券</td>
            <td>按订单金额的一定比例进行折扣<br>例如：9折券</td>
            <td>通常有最高优惠限额</td>
          </tr>
          <tr>
            <td>无门槛券</td>
            <td>无需满足任何条件即可使用的优惠券</td>
            <td>优惠金额通常较小</td>
          </tr>
          <tr>
            <td>品类券</td>
            <td>仅适用于特定品类商品的优惠券</td>
            <td>只能用于指定品类的商品</td>
          </tr>
          <tr>
            <td>店铺券</td>
            <td>仅适用于特定店铺的优惠券</td>
            <td>只能用于指定店铺的商品</td>
          </tr>
        </table>

        <h2>如何获取优惠券</h2>
        <p>您可以通过以下渠道获取优惠券：</p>
        <ul>
          <li><strong>新用户注册</strong>：新用户注册成功后会获得新人专享优惠券。</li>
          <li><strong>优惠券中心</strong>：在"个人中心"的"优惠券"页面，可以领取平台发放的优惠券。</li>
          <li><strong>活动领取</strong>：参与平台举办的各类活动可获得优惠券。</li>
          <li><strong>店铺领取</strong>：在店铺页面可领取店铺专属优惠券。</li>
          <li><strong>购物返券</strong>：部分订单完成后会返还优惠券。</li>
          <li><strong>客服发放</strong>：特殊情况下，客服可能会向您发放补偿性质的优惠券。</li>
        </ul>

        <h2>如何使用优惠券</h2>
        <p>在下单过程中使用优惠券的步骤如下：</p>
        <ol>
          <li>将商品加入购物车并进入结算页面。</li>
          <li>在结算页面中，找到"优惠券"选项。</li>
          <li>点击"选择优惠券"，系统会自动显示可用于当前订单的优惠券列表。</li>
          <li>选择一张优惠券（通常系统会默认选择优惠力度最大的券）。</li>
          <li>确认订单信息无误后，点击"提交订单"。</li>
        </ol>

        <h2>优惠券使用规则</h2>
        <ul>
          <li>每个订单通常只能使用一张优惠券。</li>
          <li>优惠券有使用期限，过期后将自动失效。</li>
          <li>部分优惠券不适用于特价商品、促销商品或特定品类商品。</li>
          <li>优惠券不能与其他特定活动同时使用（如秒杀、团购等）。</li>
          <li>优惠券不能用于支付运费、税费等额外费用。</li>
          <li>优惠券不能兑换现金，不可转让。</li>
          <li>取消订单后，未过期的优惠券通常会自动退回。</li>
        </ul>

        <h2>常见问题</h2>
        <h3>1. 为什么我的优惠券无法使用？</h3>
        <p>可能的原因：</p>
        <ul>
          <li>优惠券已过期。</li>
          <li>订单金额未达到优惠券使用门槛。</li>
          <li>购买的商品不在优惠券适用范围内。</li>
          <li>当前活动不支持使用优惠券。</li>
          <li>优惠券已被使用或已作废。</li>
        </ul>

        <h3>2. 使用优惠券后退货，优惠券会返还吗？</h3>
        <p>这取决于优惠券的类型和退货情况：</p>
        <ul>
          <li>如果是全部退货，且优惠券未过期，通常会返还优惠券。</li>
          <li>如果是部分退货，通常不返还优惠券。</li>
          <li>一次性优惠券（如新人券）通常不会返还。</li>
        </ul>

        <h3>3. 为什么系统显示我有优惠券，但结算时看不到？</h3>
        <p>这可能是因为您的优惠券不适用于当前订单。建议您：</p>
        <ul>
          <li>查看优惠券的使用条件（如适用品类、最低消费金额等）。</li>
          <li>确认优惠券是否在有效期内。</li>
          <li>检查当前商品是否属于特殊商品（如秒杀商品、特价商品等）。</li>
        </ul>
      `,
    },
  },
  return: {
    refund: {
      title: '退款说明',
      updatedAt: '2023-08-30',
      views: 7654,
      content: `
        <h2>退款方式</h2>
        <p>根据您的支付方式不同，退款方式也有所不同：</p>
        <table border="1" cellpadding="8" cellspacing="0" style="width: 100%; border-collapse: collapse;">
          <tr>
            <th>支付方式</th>
            <th>退款方式</th>
            <th>预计到账时间</th>
          </tr>
          <tr>
            <td>微信支付</td>
            <td>原路退回至微信支付账户</td>
            <td>1-3个工作日</td>
          </tr>
          <tr>
            <td>支付宝</td>
            <td>原路退回至支付宝账户</td>
            <td>1-3个工作日</td>
          </tr>
          <tr>
            <td>银行卡</td>
            <td>原路退回至银行卡</td>
            <td>3-15个工作日（具体以银行到账时间为准）</td>
          </tr>
          <tr>
            <td>平台余额</td>
            <td>退回至平台余额</td>
            <td>实时到账</td>
          </tr>
          <tr>
            <td>组合支付</td>
            <td>按原支付方式分别退回</td>
            <td>以各支付方式的到账时间为准</td>
          </tr>
        </table>

        <h2>退款流程</h2>
        <p>退款流程因退款原因不同而略有差异：</p>

        <h3>未发货订单取消退款</h3>
        <ol>
          <li>在"我的订单"中找到需要取消的订单。</li>
          <li>点击"取消订单"按钮。</li>
          <li>选择取消原因，点击"确认取消"。</li>
          <li>系统审核通过后，退款将按原支付方式退回。</li>
        </ol>

        <h3>已发货商品退货退款</h3>
        <ol>
          <li>在"我的订单"中找到需要退货的订单。</li>
          <li>点击"申请退货"按钮。</li>
          <li>填写退货原因、上传商品照片等信息。</li>
          <li>等待商家审核（通常1-3个工作日）。</li>
          <li>审核通过后，按照商家提供的退货地址寄回商品。</li>
          <li>商家收到退货并确认无误后，系统将安排退款。</li>
        </ol>

        <h3>商品质量问题退款</h3>
        <ol>
          <li>在"我的订单"中找到问题商品的订单。</li>
          <li>点击"申请退款"按钮。</li>
          <li>选择"商品质量问题"，并上传问题照片。</li>
          <li>等待商家审核（通常1-3个工作日）。</li>
          <li>如商家同意，可能会直接退款或要求退货。</li>
          <li>如需退货，请按照商家提供的地址寄回商品。</li>
        </ol>

        <h2>退款金额说明</h2>
        <p>退款金额的计算方式如下：</p>
        <ul>
          <li><strong>全部退货</strong>：商品金额 + 运费（如商家承担）</li>
          <li><strong>部分退货</strong>：退货商品金额（不含运费）</li>
          <li><strong>优惠券</strong>：按照优惠券规则处理，一般情况下：
            <ul>
              <li>全部退货：未过期的优惠券可能返还</li>
              <li>部分退货：优惠券通常不返还</li>
            </ul>
          </li>
          <li><strong>满减活动</strong>：如退货后订单金额低于满减条件，需扣除相应的优惠金额</li>
        </ul>

        <h2>常见问题</h2>
        <h3>1. 退款为什么还没到账？</h3>
        <p>退款到账时间因支付方式不同而异：</p>
        <ul>
          <li>微信/支付宝：通常1-3个工作日</li>
          <li>银行卡：通常3-15个工作日，具体以银行实际处理时间为准</li>
          <li>平台余额：通常实时到账</li>
        </ul>
        <p>如超过预期到账时间仍未收到退款，请联系客服处理。</p>

        <h3>2. 退款金额为什么与支付金额不一致？</h3>
        <p>可能的原因：</p>
        <ul>
          <li>部分退货：只退还退货商品的金额</li>
          <li>优惠扣除：如使用了满减、优惠券等，退款时需按比例扣除</li>
                    <li>运费不退：部分情况下运费不予退还</li>
          <li>退货费用：如因非商品质量问题退货，可能需要承担退货运费</li>
        </ul>

        <h3>3. 退款被拒绝怎么办？</h3>
        <p>如果您的退款申请被拒绝，您可以：</p>
        <ol>
          <li>查看拒绝原因，如有可能，调整申请内容重新提交。</li>
          <li>联系商家沟通，说明具体情况。</li>
          <li>如与商家协商不成，可申请平台客服介入处理。</li>
          <li>提供更多证据（如商品问题的照片、视频等）支持您的申请。</li>
        </ol>
      `,
    },
  },
};

// 当前分类
const currentCategory = computed(() => {
  return helpCategories.value.find((category) => category.code === currentCategoryCode.value) || null;
});

// 获取帮助内容
onMounted(async () => {
  try {
    loading.value = true;

    // 先获取帮助分类数据（如果还没有加载）
    if (!helpStore.isInitialized) {
      await helpStore.fetchCategories();
    }

    // 使用API获取详情数据
    const { code, data } = await HelpApi.getDetail(currentCategoryCode.value, currentItemCode.value);

    if (code === 0 && data) {
      helpContent.value = data;

      // 检查用户是否已经提交过反馈
      if (hasSubmittedFeedback(data.id)) {
        // 从localStorage获取用户之前的反馈类型
        try {
          const feedbackData = localStorage.getItem('help_article_feedback');
          if (feedbackData) {
            const feedbackMap = JSON.parse(feedbackData);
            feedback.value = feedbackMap[data.id];
          }
        } catch (error) {
          console.error('Error retrieving feedback status:', error);
        }
      }
    } else {
      // 如果API调用失败，尝试使用模拟数据（仅用于开发阶段）
      if (process.env.NODE_ENV === 'development' && helpContentData[currentCategoryCode.value] && helpContentData[currentCategoryCode.value][currentItemCode.value]) {
        console.warn('使用模拟数据，仅用于开发阶段');
        helpContent.value = helpContentData[currentCategoryCode.value][currentItemCode.value];

        // 检查用户是否已经提交过反馈
        if (hasSubmittedFeedback(helpContent.value.id)) {
          // 从localStorage获取用户之前的反馈类型
          try {
            const feedbackData = localStorage.getItem('help_article_feedback');
            if (feedbackData) {
              const feedbackMap = JSON.parse(feedbackData);
              feedback.value = feedbackMap[helpContent.value.id];
            }
          } catch (error) {
            console.error('Error retrieving feedback status:', error);
          }
        }
      } else {
        helpContent.value = null;
      }
    }

    loading.value = false;
  } catch (error) {
    console.error('Failed to fetch help content:', error);
    loading.value = false;
  }
});

// 监听路由变化，更新内容
watch([currentCategoryCode, currentItemCode], async () => {
  feedback.value = null; // 重置反馈状态

  try {
    loading.value = true;

    // 使用API获取数据
    const { code, data } = await HelpApi.getDetail(currentCategoryCode.value, currentItemCode.value);

    if (code === 0 && data) {
      helpContent.value = data;

      // 检查用户是否已经提交过反馈
      if (hasSubmittedFeedback(data.id)) {
        // 从localStorage获取用户之前的反馈类型
        try {
          const feedbackData = localStorage.getItem('help_article_feedback');
          if (feedbackData) {
            const feedbackMap = JSON.parse(feedbackData);
            feedback.value = feedbackMap[data.id];
          }
        } catch (error) {
          console.error('Error retrieving feedback status:', error);
        }
      }
    }
    loading.value = false;
  } catch (error) {
    console.error('Failed to fetch help content:', error);
    loading.value = false;
  }
});

// 相关文章
const relatedArticles = computed(() => {
  // 如果API返回了相关文章ID数组
  if (helpContent.value && helpContent.value.relatedArticles && Array.isArray(helpContent.value.relatedArticles)) {
    const result = [];
    const relatedIds = helpContent.value.relatedArticles;

    // 从store中获取所有分类
    const allCategories = helpCategories.value || [];

    // 遍历所有分类和文章，找到匹配ID的文章
    allCategories.forEach((category) => {
      if (category.items && Array.isArray(category.items)) {
        category.items.forEach((article) => {
          // 如果文章ID在相关文章ID数组中
          if (article.id && relatedIds.includes(article.id)) {
            result.push({
              id: article.id,
              title: article.title,
              link: `/help/${category.code}/${article.code}`,
            });
          }
        });
      }
    });
    return result;
  }
  return [];
});

// 检查用户是否已经提交过反馈
const hasSubmittedFeedback = (articleId) => {
  try {
    const feedbackData = localStorage.getItem('help_article_feedback');
    if (feedbackData) {
      const feedbackMap = JSON.parse(feedbackData);
      return feedbackMap[articleId] !== undefined;
    }
  } catch (error) {
    console.error('Error checking feedback status:', error);
  }
  return false;
};

// 保存用户反馈状态到localStorage
const saveFeedbackStatus = (articleId, type) => {
  try {
    let feedbackMap = {};
    const feedbackData = localStorage.getItem('help_article_feedback');
    if (feedbackData) {
      feedbackMap = JSON.parse(feedbackData);
    }
    feedbackMap[articleId] = type;
    localStorage.setItem('help_article_feedback', JSON.stringify(feedbackMap));
  } catch (error) {
    console.error('Error saving feedback status:', error);
  }
};

// 提交反馈
const submitFeedback = async (type) => {
  // 如果用户已经提交过反馈，则不允许再次提交
  if (helpContent.value && hasSubmittedFeedback(helpContent.value.id)) {
    $q.notify({
      color: 'warning',
      message: '您已经提交过反馈，感谢您的参与！',
      icon: 'info',
      position: 'top',
      timeout: 2000,
    });
    return;
  }

  feedback.value = type;

  try {
    // 使用API提交反馈 - 直接使用文章ID
    const { code, message } = await HelpApi.submitFeedback({
      articleId: helpContent.value.id,
      isHelpful: type === 'helpful',
    });

    if (code !== 0) {
      console.error('提交反馈失败:', message);
      // 显示错误提示
      $q.notify({
        color: 'negative',
        message: '反馈提交失败，请稍后再试',
        icon: 'error',
        position: 'top',
        timeout: 2000,
      });
      return;
    }

    // 保存反馈状态到localStorage
    if (helpContent.value) {
      saveFeedbackStatus(helpContent.value.id, type);
    }
  } catch (error) {
    console.error('提交反馈出错:', error);

    // 在开发环境下模拟API调用
    if (process.env.NODE_ENV === 'development') {
      console.log('开发环境模拟反馈提交:', {
        articleId: helpContent.value ? helpContent.value.id : 'unknown',
        isHelpful: type === 'helpful',
      });

      // 在开发环境下也保存反馈状态
      if (helpContent.value) {
        saveFeedbackStatus(helpContent.value.id, type);
      }
    }
  }

  // 显示感谢提示
  $q.notify({
    color: 'positive',
    message: '感谢您的反馈！',
    icon: 'thumb_up',
    position: 'top',
    timeout: 2000,
  });
};
</script>

<style lang="scss" scoped>
.help-detail-page {
  background-color: #f5f7fa;
  padding: 20px 0 40px;
}

.help-detail-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

.help-content-wrapper {
  display: flex;
  gap: 30px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.help-sidebar {
  width: 280px;
  flex-shrink: 0;
  background-color: #f9f9f9;
  border-right: 1px solid #eee;
  padding: 20px 0;
}

.help-menu {
  .help-category-header {
    font-weight: bold;
    color: #333;
  }
}

.help-submenu {
  padding-left: 16px;

  .q-item {
    border-radius: 4px;
    margin: 2px 8px;
    padding: 8px 16px;

    &.active-help-item {
      background-color: rgba(25, 118, 210, 0.1);
      color: #1976d2;
      font-weight: 500;
    }
  }
}

.contact-support {
  padding: 0 16px;

  .contact-title {
    font-size: 14px;
    color: #666;
    margin-bottom: 12px;
    text-align: center;
  }
}

.help-content {
  flex-grow: 1;
  padding: 30px;
  overflow-y: auto;
}

.breadcrumbs {
  font-size: 14px;
}

.article-title {
  font-size: 28px;
  font-weight: bold;
  color: #333;
  margin: 16px 0;
}

.article-meta {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  color: #666;
  font-size: 14px;

  .meta-item {
    display: flex;
    align-items: center;
  }
}

.article-content {
  font-size: 16px;
  line-height: 1.7;
  color: #333;

  :deep(h2) {
    font-size: 22px;
    font-weight: bold;
    margin: 30px 0 16px;
    color: #333;
  }

  :deep(h3) {
    font-size: 18px;
    font-weight: bold;
    margin: 24px 0 12px;
    color: #1976d2;
  }

  :deep(p) {
    margin-bottom: 16px;
  }

  :deep(ul),
  :deep(ol) {
    margin-bottom: 16px;
    padding-left: 24px;

    li {
      margin-bottom: 8px;
    }
  }

  :deep(table) {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 16px;

    th,
    td {
      border: 1px solid #ddd;
      padding: 8px;
      text-align: left;
    }

    th {
      background-color: #f2f2f2;
      font-weight: bold;
    }

    tr:nth-child(even) {
      background-color: #f9f9f9;
    }
  }

  :deep(img) {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
    margin: 16px 0;
  }

  :deep(a) {
    color: #1976d2;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}

.article-feedback {
  margin: 40px 0;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
  text-align: center;

  .feedback-title {
    font-size: 16px;
    color: #333;
    margin-bottom: 16px;
  }

  .feedback-buttons {
    display: flex;
    justify-content: center;
    gap: 16px;
  }
}

.related-articles {
  margin-top: 40px;
}

.related-title {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  margin-bottom: 16px;
}

.related-article-item {
  transition: background-color 0.3s;

  &:hover {
    background-color: #f5f5f5;
  }
}

.loading-state,
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
  color: #666;
}

@media (max-width: 1023px) {
  .help-content-wrapper {
    flex-direction: column;
  }

  .help-sidebar {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid #eee;
  }
}

@media (max-width: 767px) {
  .article-title {
    font-size: 22px;
  }

  .article-meta {
    flex-direction: column;
    gap: 8px;
  }

  .help-content {
    padding: 20px 16px;
  }

  .article-content {
    font-size: 15px;

    :deep(h2) {
      font-size: 20px;
    }

    :deep(h3) {
      font-size: 16px;
    }
  }

  .article-feedback {
    padding: 16px;

    .feedback-buttons {
      flex-direction: column;
      gap: 8px;

      .q-btn {
        width: 100%;
        justify-content: center;

        .q-badge {
          right: 8px;
        }
      }
    }
  }
}
</style>
