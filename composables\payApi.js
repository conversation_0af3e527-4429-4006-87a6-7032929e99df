const PayApi = {
  // 获得支付订单
  getOrder: (id, sync) => {
    return useClientGet('/pay/order/get', {
      params: { id, sync },
    });
  },
  // 提交支付订单
  submitOrder: (data) => {
    return useClientPost('/pay/order/submit', {
      body: data,
      custom: {
        showError: true,
      },
    });
  },

  // 获得指定应用的开启的支付渠道编码列表
  getEnableChannelCodeList: (appId) => {
    return useClientGet('/pay/channel/get-enable-code-list', {
      params: { appId },
    });
  },

  // 获得指定应用的开启的支付渠道编码列表
  getEnablePayChannelList: (appId) => {
    return useClientGet('/pay/channel/get-enable-pay-list', {
      params: { appId },
    });
  },

  // 获取钱包
  getPayWallet() {
    return useClientGet('/pay/wallet/get', {
      custom: {
        showLoading: false,
        auth: true,
      },
    });
  },
  // 获得钱包流水分页
  getWalletTransactionPage: (params) => {
    const queryString = Object.keys(params)
      .map((key) => encodeURIComponent(key) + '=' + params[key])
      .join('&');
    return useClientGet(`/pay/wallet-transaction/page?${queryString}`, {});
  },
  // 获得钱包流水统计
  getWalletTransactionSummary: (params) => {
    const queryString = `createTime=${params.createTime[0]}&createTime=${params.createTime[1]}`;
    return useClientGet(`/pay/wallet-transaction/get-summary?${queryString}`, {});
  },
  // 获得钱包充值套餐列表
  getWalletRechargePackageList: () => {
    return useClientGet('/pay/wallet-recharge-package/list', {
      custom: {
        showError: false,
        showLoading: false,
      },
    });
  },
  // 创建钱包充值记录（发起充值）
  createWalletRecharge: (data) => {
    return useClientPost('/pay/wallet-recharge/create', {
      body: data,
    });
  },
  // 获得钱包充值记录分页
  getWalletRechargePage: (params) => {
    return useClientGet('/pay/wallet-recharge/page', {
      params,
      custom: {
        showError: false,
        showLoading: false,
      },
    });
  },
};

export default PayApi;
