<template>
  <Header />
  <div class="blog-list-page">
    <div class="blog-container">
      <h1 class="page-title">博客文章</h1>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container text-center q-pa-xl">
        <q-spinner-dots size="50px" color="primary" />
        <div class="q-mt-md">加载中...</div>
      </div>

      <!-- 博客列表 -->
      <div v-else class="blog-list">
        <div v-for="blog in blogs" :key="blog.id" class="blog-item q-mb-md">
          <div class="row q-col-gutter-md">
            <!-- 博客图片 -->
            <div class="col-12 col-md-4">
              <q-img :src="blog.picUrl || 'https://cdn.quasar.dev/img/mountains.jpg'" :ratio="16 / 9" class="rounded-borders blog-image" fit="cover" />
            </div>

            <!-- 博客内容 -->
            <div class="col-12 col-md-8">
              <div class="blog-content">
                <h2 class="blog-title">
                  <a href="javascript:void(0)" @click="goToDetail(blog)" class="blog-link">
                    {{ blog.title }}
                  </a>
                </h2>
                <div class="blog-meta q-mb-sm row justify-between items-center">
                  <div>
                    <q-icon name="event" size="xs" class="q-mr-xs" />
                    <span class="text-grey-7">{{ formatDate(blog.publishTime) }}</span>
                    <q-icon name="person" size="xs" class="q-ml-md q-mr-xs" />
                    <span class="text-grey-7">{{ blog.author }}</span>
                  </div>
                  <div>
                    <q-icon name="visibility" size="xs" class="q-mr-xs" />
                    <span class="text-grey-7">{{ blog.browseCount }} 阅读</span>
                  </div>
                </div>
                <p class="blog-summary">{{ blog.introduction }}</p>
                <q-btn flat color="primary" @click="goToDetail(blog)" class="read-more-btn q-mt-sm">
                  阅读全文
                  <q-icon name="arrow_forward" size="xs" class="q-ml-xs" />
                </q-btn>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-container q-mt-lg">
        <q-pagination
          v-model="state.pagination.pageNo"
          :max="totalPages"
          :max-pages="5"
          boundary-numbers
          direction-links
          @update:model-value="onPageChange"
          class="justify-center"
        />
      </div>
    </div>
  </div>
  <Footer />
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { date } from 'quasar';
import ArticleApi from '../../composables/articleApi';

const router = useRouter();
const route = useRoute();

// 状态管理
const loading = ref(false);
const blogs = ref([]);
const state = ref({
  pagination: {
    pageNo: 1,
    pageSize: 2,
    total: 0
  }
});

// 从URL获取页码
onMounted(() => {
  if (route.query.page) {
    state.value.pagination.pageNo = parseInt(route.query.page) || 1;
  }
  fetchBlogs();
});

// 计算总页数
const totalPages = computed(() => {
  return Math.ceil(state.value.pagination.total / state.value.pagination.pageSize);
});

// 处理页码变化
const onPageChange = (newPage) => {
  state.value.pagination.pageNo = newPage;
  router.push({
    path: '/blog',
    query: { page: newPage },
  });
  fetchBlogs();
};

// 获取博客列表
const fetchBlogs = async () => {
  try {
    loading.value = true;
    const params = {
      pageNo: state.value.pagination.pageNo,
      pageSize: state.value.pagination.pageSize,
      categoryId: ArticleCategoryTypeEnum.BLOG, // 博客分类ID，与活动页面的categoryId不同
    };

    const { code, data } = await ArticleApi.getPage(params);
    if (code === 0 && data) {
      blogs.value = data.list || [];
      state.value.pagination.total = data.total || 0;
    }
  } catch (error) {
    console.error('获取博客列表失败:', error);
  } finally {
    loading.value = false;
  }
};

// 跳转到详情页面
const goToDetail = (blog) => {
  const detailState = useState('blogDetail');
  detailState.value = {
    id: blog.id,
    title: blog.title,
    author: blog.author,
    categoryId: blog.categoryId,
    picUrl: blog.picUrl,
    introduction: blog.introduction,
    content: blog.content,
    createTime: blog.createTime,
    browseCount: blog.browseCount,
    spuId: blog.spuId,
    publishTime: blog.publishTime
  };
  navigateTo(`/blog/detail/${blog.id}`);
};

// 格式化日期
const formatDate = (timestamp) => {
  if (!timestamp) return '';
  return date.formatDate(new Date(timestamp), 'YYYY-MM-DD');
};
</script>

<style lang="scss" scoped>
.blog-list-page {
  padding: 20px 0;
}

.blog-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

.page-title {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 30px;
  color: #333;
  text-align: center;
}

.blog-item {
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  padding: 16px;

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  }
}

.blog-image {
  border-radius: 6px;
  height: 100%;
  min-height: 200px;
}

.blog-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.blog-title {
  font-size: 20px;
  font-weight: bold;
  margin-top: 0;
  margin-bottom: 10px;
  line-height: 1.3;
}

.blog-link {
  color: #333;
  text-decoration: none;
  transition: color 0.2s ease;

  &:hover {
    color: #1976d2;
  }
}

.blog-meta {
  font-size: 13px;
  color: #666;
}

.blog-summary {
  flex-grow: 1;
  margin: 10px 0;
  color: #555;
  font-size: 14px;
  line-height: 1.5;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
}

.blog-tags {
  margin-top: auto;
}

.read-more-btn {
  align-self: flex-start;
  padding: 8px 12px;
  font-size: 14px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin: 40px 0 20px;
}

@media (max-width: 767px) {
  .page-title {
    font-size: 24px;
    margin-bottom: 20px;
  }

  .blog-item {
    padding: 12px;
  }

  .blog-image {
    min-height: 180px;
    margin-bottom: 12px;
  }

  .blog-title {
    font-size: 18px;
    margin-bottom: 8px;
  }

  .blog-summary {
    -webkit-line-clamp: 2;
    line-clamp: 2;
    margin: 8px 0;
  }

  .blog-meta {
    font-size: 12px;
  }

  .pagination-container {
    margin: 30px 0 15px;
  }
}
</style>
