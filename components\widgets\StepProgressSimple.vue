<template>
  <div class="custom-stepper" :class="{ 'compact-mode': $q.screen.lt.sm }">
    <q-stepper v-model="current" flat>
      <q-step
        v-for="step in steps"
        :key="step.name"
        :name="step.name"
        :title="step.title"
        :icon="getIcon(step)"
        :color="step.name < current ? completedColor : step.name === current ? activeColor : inactiveColor"></q-step>
    </q-stepper>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue';

const props = defineProps({
  currentStep: {
    type: [String, Number],
    required: true, // 当前步骤
  },
  activeColor: {
    type: String,
    default: 'green', // 激活步骤的颜色
  },
  completedColor: {
    type: String,
    default: 'green', // 已完成步骤的颜色
  },
  inactiveColor: {
    type: String,
    default: 'grey-5', // 未完成步骤的颜色
  },
});

const current = ref(Number(props.currentStep));

const steps = [
  { name: 1, title: '提交并支付', icon: 'edit' }, // 合并提交订单和支付订单费用
  { name: 2, title: '平台代购', icon: 'edit' },
  { name: 3, title: '提交包裹', icon: 'edit' },
  { name: 4, title: '确认收货', icon: 'edit' },
];

// onMounted(() => {
//   const stepperContent = document.querySelector('.q-stepper__content');
//   if (stepperContent) {
//     stepperContent.style.display = 'none';
//   }
// });

// 动态获取图标
const getIcon = (step) => {
  if (step.name < current.value) {
    return 'done'; // 已完成的步骤显示对号
  }
  if (step.name === current.value) {
    return step.icon; // 当前步骤显示传入的图标
  }
  return ''; // 未完成的步骤可以显示默认或空图标
};

watch(
  () => props.currentStep,
  (newStep) => {
    current.value = Number(newStep); // 监听传入的 step 参数变化
  }
);
</script>

<style lang="scss" scoped>
.custom-stepper {
  ::v-deep(.q-stepper__content) {
    display: none !important;
  }

  // 紧凑模式样式
  &.compact-mode {
    transform: scale(0.85);
    transform-origin: left center;

    ::v-deep(.q-stepper__tab) {
      padding: 8px 12px;

      .q-stepper__title {
        font-size: 0.8rem;
      }

      .q-stepper__dot {
        width: 20px;
        height: 20px;

        .q-icon {
          font-size: 14px;
        }
      }
    }

    ::v-deep(.q-stepper__line) {
      margin: 0 4px;
    }
  }
}
</style>
