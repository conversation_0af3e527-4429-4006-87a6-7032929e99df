<template>
  <div class="order-status-progress">
    <div class="progress-container">
      <div class="progress-step" v-for="(step, index) in steps" :key="step.name">
        <!-- 步骤圆点 -->
        <div
          class="step-circle"
          :class="{
            active: step.name === current,
            completed: step.name < current,
            inactive: step.name > current,
          }">
          <q-icon v-if="step.name < current" name="check" size="sm" color="white" />
          <span v-else-if="step.name === current"></span>
        </div>

        <!-- 步骤标题 -->
        <div
          class="step-title"
          :class="{
            active: step.name === current,
            completed: step.name < current,
            inactive: step.name > current,
          }">
          {{ step.title }}
        </div>

        <!-- 连接线 -->
        <div
          v-if="index < steps.length - 1"
          class="step-line"
          :class="{
            completed: steps[index + 1].name <= current,
            inactive: steps[index + 1].name > current,
          }"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const props = defineProps({
  currentStep: {
    type: [String, Number],
    required: true, // 当前步骤
  },
  activeColor: {
    type: String,
    default: 'primary', // 激活步骤的颜色
  },
  completedColor: {
    type: String,
    default: 'primary', // 已完成步骤的颜色
  },
  inactiveColor: {
    type: String,
    default: 'grey-5', // 未完成步骤的颜色
  },
});

const current = ref(Number(props.currentStep));

// 根据当前步骤映射到显示步骤
function mapStepToDisplay(step) {
  if (step === 0) return 0; // 待付款
  if (step === 10) return 10; // 平台采购
  if (step >= 20 && step < 30) return 20; // 待收货
  if (step >= 30) return 30; // 已入库
  return 0; // 默认待付款
}

// 订单流程步骤
const steps = [
  { name: 0, title: t('accountOrderDetail.progress.unpaid'), icon: 'payment' },
  { name: 10, title: t('accountOrderDetail.progress.purchasing'), icon: 'shopping_cart' },
  { name: 30, title: t('accountOrderDetail.progress.warehoused'), icon: 'inventory_2' },
];

watch(
  () => props.currentStep,
  (newStep) => {
    current.value = mapStepToDisplay(Number(newStep)); // 监听传入的 step 参数变化并映射
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
.order-status-progress {
  padding: 20px 0;

  .progress-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    max-width: 600px;
    margin: 0 auto;
  }

  .progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    flex: 1;

    &:first-child {
      align-items: flex-start;
      padding-left: 16px; /* 增加左侧第一个步骤的内边距 */
    }

    &:last-child {
      align-items: flex-end;
      padding-right: 16px; /* 增加右侧最后一个步骤的内边距 */
    }
  }

  .step-circle {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 8px;
    z-index: 2;

    &.active {
      background-color: var(--q-primary);
      border: 2px solid var(--q-primary);
    }

    &.completed {
      background-color: var(--q-primary);
      border: 2px solid var(--q-primary);
    }

    &.inactive {
      background-color: white;
      border: 2px solid #ccc;
    }
  }

  .step-title {
    font-size: 14px;
    text-align: center;

    &.active {
      color: var(--q-primary);
      font-weight: 500;
    }

    &.completed {
      color: var(--q-primary);
    }

    &.inactive {
      color: #999;
    }
  }

  .step-line {
    position: absolute;
    top: 12px;
    height: 2px;
    width: calc(100% - 16px); /* 减小宽度，避免与圆点重叠 */
    left: calc(50% + 8px); /* 调整起始位置 */
    z-index: 1;

    &.completed {
      background-color: var(--q-primary);
    }

    &.inactive {
      background-color: #ccc;
    }
  }

  @media (max-width: 599px) {
    .progress-container {
      max-width: 100%;
      padding: 0 8px; /* 增加整个容器的内边距 */
    }

    .progress-step {
      &:first-child {
        padding-left: 8px; /* 移动端下减小左侧内边距 */
      }

      &:last-child {
        padding-right: 8px; /* 移动端下减小右侧内边距 */
      }
    }

    .step-title {
      font-size: 12px;
    }

    .step-line {
      width: calc(100% - 24px); /* 移动端下进一步减小连接线宽度 */
      left: calc(50% + 12px); /* 移动端下调整连接线起始位置 */
    }
  }
}
</style>
