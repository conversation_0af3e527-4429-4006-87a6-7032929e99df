<template>
  <Header />
  <div class="faq-page">
    <div class="faq-container">
      <!-- 使用共享的标题和搜索框组件 -->
      <HelpHeader :title="$t('help.faq.pageTitle')" />

      <div class="faq-content-wrapper">
        <!-- 左侧导航菜单 -->
        <HelpSidebar :categories="categories" />

        <!-- 右侧内容区域 -->
        <div class="faq-content">
          <!-- FAQ标题 -->
          <h2 class="faq-title">{{ $t('help.faq.title') }}</h2>

          <!-- FAQ列表 -->
          <div class="faq-list">
            <q-list class="faq-items-list">
              <q-item v-for="(faq, index) in faqs" :key="index" :to="faq.link" clickable v-ripple class="faq-item">
                <q-item-section avatar>
                  <q-icon name="help_outline" color="primary" size="sm" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>{{ faq.title }}</q-item-label>
                  <q-item-label caption v-if="faq.categoryName">
                    <q-icon name="folder" size="xs" class="q-mr-xs" />
                    {{ faq.categoryName }}
                  </q-item-label>
                </q-item-section>
                <q-item-section side>
                  <q-icon name="chevron_right" color="grey" size="sm" />
                </q-item-section>
              </q-item>
            </q-list>

            <!-- 分页 -->
            <div class="pagination-wrapper q-mt-lg">
              <q-pagination v-model="currentPage" :max="totalPages" :max-pages="6" boundary-numbers direction-links @update:model-value="onPageChange" />
              <div class="pagination-info">
                {{ $t('help.pagination.info', { current: currentPage, pages: totalPages, total: totalFaqs }) }}
              </div>
            </div>

            <!-- 加载状态 -->
            <div v-if="loading" class="loading-wrapper">
              <q-spinner color="primary" size="3em" />
              <div class="q-mt-sm">{{ $t('help.loading') }}</div>
            </div>

            <!-- 无数据提示 -->
            <div v-if="!loading && faqs.length === 0" class="no-data-wrapper">
              <q-icon name="info" color="grey" size="3em" />
              <div class="q-mt-sm">{{ $t('help.faq.noData') }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <Footer />
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import HelpSidebar from '~/components/help/HelpSidebar.vue';
import HelpHeader from '~/components/help/HelpHeader.vue';
import { useHelpStore } from '~/store/help';

const route = useRoute();
const router = useRouter();
useI18n(); // 使用 useI18n 但不解构，因为我们只在模板中使用 $t
const helpStore = useHelpStore();

// 状态
const loading = ref(false);
const faqs = ref([]);
const categories = ref([]);
const currentPage = ref(1);
const pageSize = ref(10);
const totalFaqs = ref(0);

// 计算总页数
const totalPages = computed(() => {
  return Math.ceil(totalFaqs.value / pageSize.value) || 1;
});

// 获取FAQ列表
const fetchFaqs = async (page = 1) => {
  loading.value = true;

  try {
    // 获取分类数据（如果尚未加载）
    if (categories.value.length === 0) {
      await helpStore.fetchCategories();
      categories.value = helpStore.getCategories;
    }

    // 获取FAQ列表
    const result = await helpStore.fetchFaqs(page, pageSize.value);

    // 处理FAQ数据，添加分类名称和链接
    faqs.value = result.list.map((faq) => {
      // 查找对应的分类
      const category = categories.value.find((cat) => cat.code === faq.categoryCode);

      // 如果没有categoryCode，尝试从store中查找
      let categoryCode = faq.categoryCode;
      if (!categoryCode) {
        // 尝试在所有分类中查找该文章
        for (const cat of categories.value) {
          if (cat.items && Array.isArray(cat.items)) {
            const found = cat.items.find((item) => item.id === faq.id);
            if (found) {
              categoryCode = cat.code;
              break;
            }
          }
        }
      }

      // 如果仍然找不到分类，使用默认值
      if (!categoryCode) {
        categoryCode = 'uncategorized';
      }

      return {
        id: faq.id,
        title: faq.title,
        categoryName: category ? category.title : '',
        categoryCode: categoryCode,
        itemCode: faq.code,
        link: `/help/${categoryCode}/${faq.code}`,
      };
    });

    totalFaqs.value = result.total;
  } catch (error) {
    console.error('Failed to fetch FAQs:', error);
    faqs.value = [];
  } finally {
    loading.value = false;
  }
};

// 页码变化处理
const onPageChange = (page) => {
  router.push({
    path: route.path,
    query: { ...route.query, page },
  });
};

// 监听路由变化
watch(
  () => route.query.page,
  (newPage) => {
    const page = parseInt(newPage) || 1;
    currentPage.value = page;
    fetchFaqs(page);
  },
  { immediate: true }
);

// 初始化
onMounted(() => {
  // 从URL获取页码
  const page = parseInt(route.query.page) || 1;
  currentPage.value = page;
  fetchFaqs(page);
});
</script>

<style lang="scss" scoped>
.faq-page {
  background-color: #f5f7fa;
  padding: 20px 0 40px;
}

.faq-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

.faq-content-wrapper {
  display: flex;
  gap: 30px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.faq-content {
  flex-grow: 1;
  padding: 20px;
  overflow-y: auto;
}

.faq-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.faq-items-list {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
}

.faq-item {
  transition: all 0.3s;
  padding: 8px 12px;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background-color: #f5f5f5;
  }

  :deep(.q-item__section--avatar) {
    min-width: 40px;
  }

  :deep(.q-item__label) {
    font-size: 14px;
    line-height: 1.4;
    font-weight: 500;
    color: #333;
  }

  :deep(.q-item__label--caption) {
    font-size: 12px;
    margin-top: 4px;
    color: #666;
  }
}

.pagination-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  margin-top: 24px;

  :deep(.q-pagination) {
    .q-btn {
      border-radius: 4px;
      font-size: 13px;
      min-height: 32px;
      min-width: 32px;
      padding: 0 4px;
    }
  }
}

.pagination-info {
  font-size: 13px;
  color: #666;
  margin-top: 4px;
}

.loading-wrapper,
.no-data-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  color: #666;
}

@media (max-width: 1023px) {
  .faq-content-wrapper {
    flex-direction: column;
  }
}

@media (max-width: 767px) {
  .page-title {
    font-size: 26px;
  }

  .faq-content {
    padding: 16px;
  }

  .faq-title {
    font-size: 20px;
    margin-bottom: 16px;
    padding-bottom: 8px;
  }

  .faq-item {
    padding: 6px 10px;

    :deep(.q-item__label) {
      font-size: 13px;
    }

    :deep(.q-item__label--caption) {
      font-size: 11px;
    }
  }
}
</style>
