<template>
  <q-dialog ref="dialogRef" @hide="onDialogHide" persistent>
    <q-card class="address-form-dialog">
      <q-card-section class="dialog-header row items-center justify-between">
        <div class="text-h6 text-weight-bold">
          <q-icon name="location_on" class="q-mr-sm" />
          {{ form.id ? '编辑地址' : '新增地址' }}
        </div>
        <q-btn icon="close" flat round dense v-close-popup color="white" class="close-btn" />
      </q-card-section>

      <q-card-section class="q-pt-lg">
        <q-form @submit="submitForm" class="address-form q-gutter-y-sm">
          <!-- 标题和默认地址 -->
          <div class="row q-col-gutter-sm">
            <div class="col-12 col-sm-6">
              <q-input v-model="form.title" label="标题" outlined dense placeholder="如：家、公司" class="full-width" prefix>
                <template v-slot:prepend>
                  <q-icon name="bookmark" size="xs" color="grey-7" />
                </template>
              </q-input>
            </div>
            <div class="col-12 col-sm-6">
              <q-input v-model="form.name" label="收件人" outlined dense placeholder="请输入收件人姓名" class="full-width" :rules="[(val) => !!val || '请输入收件人姓名']">
                <template v-slot:prepend>
                  <q-icon name="person" size="xs" color="grey-7" />
                </template>
              </q-input>
            </div>
          </div>

          <!-- 电话号码 -->
          <div class="row q-col-gutter-sm">
            <div class="col-4 col-sm-3">
              <q-select
                v-model="form.phoneCode"
                :options="phoneOptions"
                outlined
                dense
                map-options
                emit-value
                option-label="label"
                option-value="value"
                @update:model-value="onPhoneCodeChange"
                placeholder="+">
                <template v-slot:prepend>
                  <q-icon name="flag" size="xs" color="grey-7" />
                </template>
              </q-select>
            </div>
            <div class="col-8 col-sm-9">
              <q-input v-model="form.mobile" label="联系电话" outlined dense placeholder="请输入联系电话" class="full-width" :rules="[(val) => !!val || '请输入联系电话']">
                <template v-slot:prepend>
                  <q-icon name="phone" size="xs" color="grey-7" />
                </template>
              </q-input>
            </div>
          </div>

          <!-- 国家和州/省 -->
          <div class="row q-col-gutter-sm">
            <div class="col-12 col-sm-6">
              <q-select
                v-model="form.countryCode"
                :options="countryOptions"
                label="国家/地区"
                outlined
                dense
                map-options
                emit-value
                option-label="label"
                option-value="value"
                @update:model-value="onCountryCodeChange"
                placeholder="请选择国家/地区"
                :rules="[(val) => !!val || '请选择国家/地区']">
                <template v-slot:prepend>
                  <q-icon name="public" size="xs" color="grey-7" />
                </template>
              </q-select>
            </div>
            <div class="col-12 col-sm-6">
              <q-select
                v-model="form.state"
                :options="stateOptions"
                label="省/州"
                outlined
                dense
                map-options
                emit-value
                option-label="label"
                option-value="value"
                @update:model-value="onStateChange"
                placeholder="请选择省/州"
                :rules="[(val) => !!val || '请选择省/州']"
                :loading="loadingStates">
                <template v-slot:prepend>
                  <q-icon name="location_city" size="xs" color="grey-7" />
                </template>
              </q-select>
            </div>
          </div>

          <!-- 城市和邮编 -->
          <div class="row q-col-gutter-sm">
            <div class="col-12 col-sm-6">
              <q-select
                v-model="form.city"
                :options="cityOptions"
                label="城市"
                outlined
                dense
                map-options
                emit-value
                option-label="label"
                option-value="value"
                placeholder="请选择城市"
                :rules="[(val) => !!val || '请选择城市']"
                :loading="loadingCities">
                <template v-slot:prepend>
                  <q-icon name="apartment" size="xs" color="grey-7" />
                </template>
              </q-select>
            </div>
            <div class="col-12 col-sm-6">
              <q-input v-model="form.postCode" label="邮政编码" outlined dense placeholder="请输入邮政编码" class="full-width" :rules="[(val) => !!val || '请输入邮政编码']">
                <template v-slot:prepend>
                  <q-icon name="markunread_mailbox" size="xs" color="grey-7" />
                </template>
              </q-input>
            </div>
          </div>

          <!-- 详细地址 -->
          <div class="row q-col-gutter-sm">
            <div class="col-12">
              <q-input
                v-model="form.detailAddress"
                label="详细地址"
                outlined
                dense
                type="textarea"
                placeholder="请输入详细地址"
                class="full-width"
                :rules="[(val) => !!val || '请输入详细地址']"
                autogrow
                rows="2">
                <template v-slot:prepend>
                  <q-icon name="home" size="xs" color="grey-7" />
                </template>
              </q-input>
            </div>
          </div>
          <div class="row q-col-gutter-sm">
            <div class="col-12 col-sm-4 flex items-end">
              <q-checkbox v-model="form.defaultStatus" label="设为默认地址" />
            </div>
          </div>

          <!-- 按钮 -->
          <div class="row justify-end q-gutter-sm q-mt-md">
            <q-btn label="取消" color="grey" flat v-close-popup @click="handleCancel" />
            <q-btn label="提交" color="primary" type="submit" :loading="submitting" />
          </div>
        </q-form>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useQuasar, useDialogPluginComponent } from 'quasar';
import AddressApi from '~/composables/addressApi';

const $q = useQuasar();
const { dialogRef, onDialogHide, onDialogOK, onDialogCancel } = useDialogPluginComponent();
const emit = defineEmits([...useDialogPluginComponent.emits, 'confirm', 'cancel']);

// 表单数据
const form = reactive({
  id: 0,
  title: '',
  name: '',
  defaultStatus: false,
  detailAddress: '',
  phoneCode: '',
  mobile: '',
  countryCode: '',
  countryId: null,
  state: '',
  city: null,
  postCode: '',
  areaId: null,
});

// 加载状态
const submitting = ref(false);
const loadingStates = ref(false);
const loadingCities = ref(false);

// 数据选项
const phoneOptions = ref([]);
const countryOptions = ref([]);
const stateOptions = ref([]);
const cityOptions = ref([]);

// 初始化数据
onMounted(async () => {
  await loadCountries();
});

// 加载国家列表
async function loadCountries() {
  try {
    const { code, data } = await AddressApi.getCountry();
    if (code === 0 && data) {
      // 处理电话区号选项
      phoneOptions.value = data
        .filter((item, index, self) => index === self.findIndex((t) => t.phoneCode === item.phoneCode))
        .map((item) => ({
          label: `+${item.phoneCode}`,
          value: item.phoneCode,
          countryCode: item.iso2,
          countryId: item.id,
        }));

      // 处理国家选项
      countryOptions.value = data.map((item) => ({
        label: item.name,
        value: item.iso2,
        phoneCode: item.phoneCode,
        id: item.id,
      }));
    }
  } catch (error) {
    console.error('获取国家列表失败:', error);
    $q.notify({
      color: 'negative',
      message: '获取国家列表失败',
      icon: 'error',
    });
  }
}

// 当选择手机区号时自动更新国家
async function onPhoneCodeChange(phoneCode) {
  const matchedOption = phoneOptions.value.find((item) => item.value === phoneCode);
  if (matchedOption) {
    form.countryCode = matchedOption.countryCode;
    form.countryId = matchedOption.countryId;
    await onCountryCodeChange(matchedOption.countryCode);
  }
}

// 当选择国家时调用接口获取州/省，并清空state和city的选项
async function onCountryCodeChange(countryCode) {
  loadingStates.value = true;
  try {
    const matchedCountry = countryOptions.value.find((item) => item.value === countryCode);
    if (matchedCountry) {
      form.countryId = matchedCountry.id;
      const { code, data } = await AddressApi.getState(matchedCountry.id);
      if (code === 0 && data) {
        stateOptions.value = data.map((item) => ({
          id: item.id,
          label: item.name,
          value: item.id,
        }));

        // 清空州和城市选择
        form.state = null;
        form.city = null;
        cityOptions.value = [];
      }
    }
  } catch (error) {
    console.error('获取州/省列表失败:', error);
    $q.notify({
      color: 'negative',
      message: '获取州/省列表失败',
      icon: 'error',
    });
  } finally {
    loadingStates.value = false;
  }
}

// 当state变更时，重新获取city的选项
async function onStateChange(stateId) {
  if (!stateId) return;

  loadingCities.value = true;
  try {
    const { code, data } = await AddressApi.getCity(stateId);
    if (code === 0 && data) {
      cityOptions.value = data.map((item) => ({
        id: item.id,
        label: item.name,
        value: item.id,
      }));
    }
  } catch (error) {
    console.error('获取城市列表失败:', error);
    $q.notify({
      color: 'negative',
      message: '获取城市列表失败',
      icon: 'error',
    });
  } finally {
    loadingCities.value = false;
  }
}

// 编辑情况下根据areaId获取城市列表和州id
async function getCityList(id) {
  try {
    const { code, data } = await AddressApi.getAreaTreeByChildId(id, 3);
    if (code === 0 && data) {
      if (data.length > 0) {
        cityOptions.value = data.map((item) => ({
          id: item.id,
          label: item.name,
          value: item.id,
        }));
        form.state = data[0].pid;
        form.city = id;
      } else {
        // 如果city获取失败说明是没有城市，id即是州id
        form.state = id;
      }
    }
  } catch (error) {
    console.error('获取城市列表失败:', error);
  }
}

// 打开对话框
async function openModal(id = null) {
  resetForm();

  if (id) {
    // 编辑模式，加载指定地址数据
    try {
      const { code, data } = await AddressApi.getAddress(id);
      if (code === 0 && data) {
        Object.assign(form, data);

        // 加载州的下拉选项
        await onCountryCodeChange(data.countryCode);

        // 加载城市的下拉选项
        if (data.areaId) {
          await getCityList(data.areaId);
        }
      }
    } catch (error) {
      console.error('获取地址详情失败:', error);
      $q.notify({
        color: 'negative',
        message: '获取地址详情失败',
        icon: 'error',
      });
    }
  }

  // 打开对话框
  dialogRef.value.show();
}

// 提交表单
async function submitForm() {
  submitting.value = true;

  try {
    // 设置areaId
    if (form.city) {
      form.areaId = form.city;
    } else if (form.state) {
      form.areaId = form.state;
    }

    // 强制转换defaultStatus为布尔类型
    form.defaultStatus = Boolean(form.defaultStatus);

    // 准备提交的数据
    const formData = { ...form };

    // 根据是否有ID决定是创建还是更新
    const apiMethod = form.id ? AddressApi.updateAddress : AddressApi.createAddress;
    const { code, data } = await apiMethod(formData);

    if (code === 0) {
      $q.notify({
        color: 'positive',
        message: '操作成功',
        icon: 'check_circle',
      });

      // 如果是新增，设置返回的ID
      if (!form.id && data) {
        form.id = data;
        formData.id = data;
      }

      // 发送确认事件
      emit('confirm', formData);
      onDialogOK(formData);
      resetForm();
    }
  } catch (error) {
    console.error('保存地址失败:', error);
    $q.notify({
      color: 'negative',
      message: '操作失败',
      icon: 'error',
    });
  } finally {
    submitting.value = false;
  }
}

// 取消操作
function handleCancel() {
  emit('cancel');
  onDialogCancel();
  resetForm();
}

// 重置表单
function resetForm() {
  Object.keys(form).forEach((key) => {
    form[key] = key === 'id' ? 0 : key === 'defaultStatus' ? false : key === 'phoneCode' ? '+86' : '';
  });
}

// 暴露方法给父组件
defineExpose({
  openModal,
});
</script>

<style lang="scss" scoped>
.address-form-dialog {
  width: 100%;
  max-width: 500px;
  border-radius: 12px;
  overflow: hidden;

  .dialog-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 16px 20px;

    .text-h6 {
      color: white;
      margin: 0;
      font-size: 1.1rem;
    }

    .close-btn {
      color: white;

      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
      }
    }
  }

  .q-card-section {
    padding: 16px 16px 8px 16px;
  }

  .text-subtitle1 {
    font-size: 1rem;
    font-weight: 500;
  }

  .address-form {
    .q-field {
      margin-bottom: 8px;
    }

    .q-field__control {
      height: 36px;
    }

    .q-checkbox {
      margin-top: 8px;
    }
  }

  // 移动端适配
  @media (max-width: 599px) {
    max-width: 95vw;

    .q-card-section {
      padding: 12px 12px 6px 12px;
    }

    .text-subtitle1 {
      font-size: 0.95rem;
    }

    .address-form {
      .q-field__control {
        height: 32px;
      }
    }
  }
}
</style>
