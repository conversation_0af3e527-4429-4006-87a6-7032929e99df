/**
 * 全局货币格式化插件
 * 提供在整个应用中使用的货币格式化功能
 */

import { useCurrencyStore } from '~/store/currency';
import { formatCurrency } from '~/utils/currencyUtils';

export default defineNuxtPlugin((nuxtApp) => {
  /**
   * 格式化金额为当前选择的货币格式
   * @param {number} amount - 金额（单位：分）
   * @param {Object} options - 格式化选项
   * @returns {string} 格式化后的金额字符串
   */
  const formatAmount = (amount, options = {}) => {
    const currencyStore = useCurrencyStore();
    return formatCurrency(
      amount, 
      currencyStore.selectedCurrency, 
      currencyStore.defaultCurrency,
      options
    );
  };

  /**
   * 格式化金额为默认货币格式（不考虑用户选择的货币）
   * @param {number} amount - 金额（单位：分）
   * @returns {string} 格式化后的金额字符串
   */
  const formatDefaultAmount = (amount) => {
    const currencyStore = useCurrencyStore();
    const defaultCurrency = currencyStore.defaultCurrency;
    return formatCurrency(
      amount, 
      defaultCurrency, 
      defaultCurrency,
      { showDefault: false }
    );
  };

  // 将方法添加到全局属性中
  nuxtApp.provide('formatAmount', formatAmount);
  nuxtApp.provide('formatDefaultAmount', formatDefaultAmount);
});
