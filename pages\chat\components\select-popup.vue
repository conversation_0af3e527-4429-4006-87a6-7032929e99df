<template>
  <q-dialog v-model="showDialog" position="bottom" @hide="emits('close')">
    <q-card class="select-popup-card">
      <q-card-section class="title-section">
        <div class="title">
          <span>{{ mode == 'goods' ? '我的浏览' : '我的订单' }}</span>
        </div>
        <q-btn flat round dense icon="close" @click="emits('close')" />
      </q-card-section>

      <q-card-section class="scroll-section">
        <q-scroll-area class="scroll-box">
          <q-infinite-scroll @load="loadmore" :offset="250" :scroll-target="$refs.scrollArea">
            <div class="item" v-for="item in state.pagination.data" :key="item.id" @click="emits('select', { type: mode, data: item })">
              <template v-if="mode == 'goods'">
                <GoodsItem :goodsData="item" />
              </template>
              <template v-if="mode == 'order'">
                <OrderItem :orderData="item" />
              </template>
            </div>

            <template v-slot:loading>
              <div class="row justify-center q-my-md">
                <q-spinner color="primary" size="40px" />
              </div>
            </template>
          </q-infinite-scroll>
        </q-scroll-area>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { reactive, watch, computed, ref } from 'vue';
import GoodsItem from './goods.vue';
import OrderItem from './order.vue';

const emits = defineEmits(['select', 'close']);
const props = defineProps({
  mode: {
    type: String,
    default: 'goods',
  },
  show: {
    type: Boolean,
    default: false,
  },
});

// 控制对话框显示
const showDialog = computed({
  get: () => props.show,
  set: (val) => {
    if (!val) emits('close');
  },
});

// 滚动区域引用
const scrollArea = ref(null);

// 监听mode变化
watch(
  () => props.mode,
  () => {
    state.pagination.data = [];
    if (props.mode) {
      getList(state.pagination.page);
    }
  }
);

// 监听show变化
watch(
  () => props.show,
  (newVal) => {
    if (newVal && state.pagination.data.length === 0) {
      getList(1);
    }
  }
);

const state = reactive({
  loadStatus: '',
  pagination: {
    data: [],
    current_page: 1,
    total: 1,
    last_page: 5, // 模拟有5页数据
  },
});

// 模拟商品数据
const mockGoods = [
  { id: 1, name: '日本进口零食礼包', price: 9900, picUrl: '/images/products/product1.jpg' },
  { id: 2, name: '韩国美妆套装', price: 29900, picUrl: '/images/products/product2.jpg' },
  { id: 3, name: '德国厨具套装', price: 59900, picUrl: '/images/products/product3.jpg' },
  { id: 4, name: '澳洲保健品礼盒', price: 39900, picUrl: '/images/products/product4.jpg' },
  { id: 5, name: '法国红酒礼盒', price: 89900, picUrl: '/images/products/product5.jpg' },
];

// 模拟订单数据
const mockOrders = [
  { id: 10001, orderNo: 'ORD20240501001', status: 1, amount: 9900, createTime: '2024-05-01 10:30:00' },
  { id: 10002, orderNo: 'ORD20240502002', status: 2, amount: 29900, createTime: '2024-05-02 14:20:00' },
  { id: 10003, orderNo: 'ORD20240503003', status: 3, amount: 59900, createTime: '2024-05-03 16:45:00' },
  { id: 10004, orderNo: 'ORD20240504004', status: 4, amount: 39900, createTime: '2024-05-04 09:15:00' },
  { id: 10005, orderNo: 'ORD20240505005', status: 5, amount: 89900, createTime: '2024-05-05 11:50:00' },
];

async function getList(page, pageSize = 5) {
  // 模拟加载状态
  state.loadStatus = 'loading';

  // 模拟API请求延迟
  await new Promise((resolve) => setTimeout(resolve, 500));

  // 根据模式选择不同的模拟数据
  const mockData = props.mode === 'goods' ? mockGoods : mockOrders;

  // 模拟分页数据
  const result = {
    list: mockData,
    current_page: page,
    total: mockData.length * 5, // 模拟总数
    last_page: 5, // 模拟总页数
  };

  // 合并数据
  const newList = page === 1 ? result.list : [...state.pagination.data, ...result.list];

  state.pagination = {
    ...result,
    data: newList,
  };

  // 更新加载状态
  if (state.pagination.current_page < state.pagination.last_page) {
    state.loadStatus = 'more';
  } else {
    state.loadStatus = 'noMore';
  }
}

function loadmore(index, done) {
  if (state.loadStatus !== 'noMore') {
    getList(state.pagination.current_page + 1).then(() => {
      done();
    });
  } else {
    done();
  }
}
</script>

<style lang="scss" scoped>
.select-popup-card {
  width: 100%;
  max-width: 100%;
  border-radius: 16px 16px 0 0;

  .title-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background: #fff;
    border-radius: 16px 16px 0 0;

    .title {
      font-size: 16px;
      font-weight: 500;
      position: relative;

      span {
        position: relative;

        &::after {
          content: '';
          display: block;
          width: 100%;
          height: 2px;
          position: absolute;
          left: 0;
          bottom: -8px;
          background: var(--q-primary);
        }
      }
    }
  }

  .scroll-section {
    padding: 0;
    height: 400px;

    .scroll-box {
      height: 100%;
    }

    .item {
      background: #fff;
      margin: 10px 16px 0;
      border-radius: 10px;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      }
    }
  }
}
</style>
