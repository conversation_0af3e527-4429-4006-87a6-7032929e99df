const TransferApi = {
  // 获取转运单列表
  getTransferPage(params) {
    return useClientGet('/agent/transfer/page', { params });
  },

  // 获取转运单详情
  getTransferDetail(id) {
    return useClientGet(`/agent/transfer/get?id=${id}`);
  },

  // 创建转运单
  createTransfer(data) {
    return useClientPost('/agent/transfer/create', {
      body: data,
    });
  },

  // 更新转运单
  updateTransfer(data) {
    return useClientPut('/agent/transfer/update', data);
  },

  // 取消转运单
  cancelTransfer(id) {
    return useClientDelete(`/agent/transfer/cancel?id=${id}`);
  },

  // 删除转运单（彻底删除）
  deleteTransfer(id) {
    return useClientDelete(`/agent/transfer/delete?id=${id}`);
  },
};

export default TransferApi;
