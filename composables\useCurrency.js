/**
 * 货币格式化组合式函数
 * 提供在组件中使用的货币格式化功能
 */

import { useCurrencyStore } from '~/store/currency';
import { formatCurrency } from '~/utils/currencyUtils';

/**
 * 使用货币格式化功能的组合式函数
 * @returns {Object} 包含货币格式化方法的对象
 */
export const useCurrency = () => {
  const currencyStore = useCurrencyStore();

  /**
   * 格式化金额为当前选择的货币格式
   * @param {number} amount - 金额（单位：分）
   * @param {Object} options - 格式化选项
   * @returns {string} 格式化后的金额字符串
   */
  const formatAmount = (amount, options = {}) => {
    return formatCurrency(
      amount, 
      currencyStore.selectedCurrency, 
      currencyStore.defaultCurrency,
      options
    );
  };

  /**
   * 格式化金额为默认货币格式（不考虑用户选择的货币）
   * @param {number} amount - 金额（单位：分）
   * @returns {string} 格式化后的金额字符串
   */
  const formatDefaultAmount = (amount) => {
    const defaultCurrency = currencyStore.defaultCurrency;
    return formatCurrency(
      amount, 
      defaultCurrency, 
      defaultCurrency,
      { showDefault: false }
    );
  };

  /**
   * 获取当前选择的货币信息
   * @returns {Object} 当前货币对象
   */
  const getCurrentCurrency = () => {
    return currencyStore.selectedCurrency;
  };

  /**
   * 获取默认货币信息
   * @returns {Object} 默认货币对象
   */
  const getDefaultCurrency = () => {
    return currencyStore.defaultCurrency;
  };

  /**
   * 设置当前货币
   * @param {Object} currency - 货币对象
   */
  const setCurrency = (currency) => {
    currencyStore.setCurrentCurrency(currency);
  };

  return {
    formatAmount,
    formatDefaultAmount,
    getCurrentCurrency,
    getDefaultCurrency,
    setCurrency
  };
};
