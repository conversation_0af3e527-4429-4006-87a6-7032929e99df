const UserApi = {
  // 获得基本信息
  getUserInfo: () => {
    return useClientGet('/member/user/get', {
      custom: {
        showLoading: false,
        auth: true,
      },
    });
  },

  //重置密码
  resetUserPassword: (data) => {
    return useClientPut('/member/user/reset-password-mail', {
      body: data,
    });
  },

  // 修改基本信息
  updateUser: (data) => {
    return useClientPut('/member/user/update', {
      body: data,
      custom: {
        auth: true,
        showSuccess: true,
        successMsg: '更新成功',
      },
    });
  },
  // 修改用户手机
  updateUserMobile: (data) => {
    return useClientPut('/member/user/update-mobile', {
      body: data,
      custom: {
        loadingMsg: '验证中',
        showSuccess: true,
        successMsg: '修改成功',
      },
    });
  },
  // 修改密码
  // updateUserPassword: (data) => {
  //   return useClientPut('/member/user/update-password', {
  //     body: data,
  //     custom: {
  //       loadingMsg: '验证中',
  //       showSuccess: true,
  //       successMsg: '修改成功',
  //     },
  //   });
  // },
  
  // 修改密码
  updateUserPassword: (data) => {
    return useClientPut('/member/user/update-user-password', {
      body: data,
      custom: {
        loadingMsg: '验证中',
        showSuccess: true,
        successMsg: '修改成功',
      },
    });
  },

  // 重置密码
  // resetUserPassword: (data) => {
  //   return useClientPut('/member/user/reset-password', {
  //     body: data,
  //     custom: {
  //       loadingMsg: '验证中',
  //       showSuccess: true,
  //       successMsg: '修改成功',
  //     },
  //   });
  // },

  // ==================== 支付密码相关接口 ====================

  // 设置支付密码（首次设置，需要邮件验证码）
  setPayPassword: (data) => {
    return useClientPut('/member/user/set-pay-password', {
      body: data,
      custom: {
        auth: true,
        loadingMsg: '设置中',
        showSuccess: true,
        successMsg: '支付密码设置成功',
      },
    });
  },

  // 修改支付密码（通过旧支付密码验证）
  updatePayPassword: (data) => {
    return useClientPut('/member/user/update-pay-password', {
      body: data,
      custom: {
        auth: true,
        loadingMsg: '修改中',
        showSuccess: true,
        successMsg: '支付密码修改成功',
      },
    });
  },

  // 重置支付密码（通过邮件验证码）
  resetPayPassword: (data) => {
    return useClientPut('/member/user/reset-pay-password', {
      body: data,
      custom: {
        loadingMsg: '重置中',
        showSuccess: true,
        successMsg: '支付密码重置成功',
      },
    });
  },

  // 验证支付密码
  verifyPayPassword: (data) => {
    return useClientPost('/member/user/verify-pay-password', {
      body: data,
      custom: {
        auth: true,
        showLoading: false,
      },
    });
  },

  // 更新补款授权设置
  updateAutoCompensateDiff: (enabled) => {
    return useClientPut('/member/user/update-auto-compensate-diff', {
      body: { autoCompensateDiff: enabled },
      custom: {
        auth: true,
        loadingMsg: '设置中',
        showSuccess: true,
        successMsg: enabled ? '已开启补款授权' : '已关闭补款授权',
      },
    });
  },

  // 发送支付密码重置邮件验证码
  sendPayPasswordEmailCode: (data) => {
    return useClientPost('/member/auth/send-mail-code', {
      body: {
        ...data,
        scene: 5, // 支付密码重置场景
      },
      custom: {
        loadingMsg: '发送中',
        showSuccess: true,
        successMsg: '验证码已发送到您的邮箱',
      },
    });
  },
};

export default UserApi;
