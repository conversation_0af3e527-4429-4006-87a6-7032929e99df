<template>
  <div class="create-parcel-page">
    <!-- 标题区域 -->
    <div class="bg-white q-px-md q-py-sm">
      <div class="row items-center justify-between">
        <div class="row items-center">
          <q-icon name="inventory_2" size="sm" class="q-mr-xs text-primary" />
          <span class="text-subtitle1 text-weight-medium">创建包裹</span>
        </div>
        <q-btn flat color="primary" icon-right="arrow_back" label="返回列表" to="/account/parcel" class="back-btn" />
      </div>
    </div>

    <div class="q-pa-xs">
      <!-- 步骤指示器 -->
      <div class="row justify-center q-mb-sm">
        <div class="col-12 col-md-10 col-lg-9">
          <q-stepper model-value="1" ref="stepper" color="primary" active-color="primary" done-color="positive" class="compact-stepper">
            <q-step :name="1" title="确认商品" icon="inventory_2" color="primary" :done="false"> </q-step>
            <q-step :name="3" title="选择物流" icon="flight_takeoff" color="primary" :done="false"> </q-step>
            <q-step :name="2" title="填写信息" icon="description" color="primary" :done="false"> </q-step>
            <q-step :name="4" title="确认支付" icon="payment" color="primary" :done="false"> </q-step>
          </q-stepper>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="row justify-center q-mt-md">
        <div class="col-12 col-md-10 col-lg-9">
          <!-- 已选商品列表 -->
          <div class="row items-center justify-between q-mb-xs">
            <div class="row items-center">
              <q-icon name="inventory_2" color="primary" size="xs" class="q-mr-xs" />
              <div class="text-subtitle2 text-weight-bold">已选商品</div>
            </div>
            <q-btn color="primary" outline icon="add" label="选择库存商品" dense @click="showStockSelector = true" class="select-stock-btn" />
          </div>
          <ParcelProductList :products="selectedProducts" :info="state.parcelInfo.info" @remove-product="removeProduct" @update-count="updateProductCount" class="q-mb-sm compact-card" />

          <!-- 库存商品选择弹窗 -->
          <StockSelector v-model="showStockSelector" :initial-selected="selectedProducts" @select="handleStockSelected" />

          <!-- 收货地址选择 -->
          <ParcelAddressSelector v-model="state.parcelInfo.address" :addresses="addresses" @update:model-value="handleAddressChange" @add-address="addNewAddress" class="q-mb-sm compact-card" />
          <!-- 物流方案选择 -->
          <ParcelLogisticsPlanSelector
            v-model="selectedTransportPlan"
            :shippingMethods="state.parcelInfo.logisticsPlans || []"
            :parcelWeight="state.parcelInfo.info.weight"
            :selectedProducts="selectedProducts"
            @update:model-value="getParcelInfo"
            class="q-mb-sm compact-card" />

          <!-- 报关信息 -->
          <ParcelCustomsInfo v-model="customsInfo" :selected-transport-plan="selectedTransportPlan" @validation-change="handleCustomsValidationChange" class="q-mb-sm compact-card" />

          <!-- 紧凑服务选择 -->
          <ParcelCompactServices
            :insurance-services="insuranceServices"
            :free-services="freeServices"
            :charge-services="chargeServices"
            :selected-insurance-services="selectedInsuranceServices"
            :selected-free-services="selectedFreeServices"
            :selected-charge-services="selectedChargeServices"
            @update:selected-insurance-services="updateInsuranceServices"
            @update:selected-free-services="updateFreeServices"
            @update:selected-charge-services="updateChargeServices"
            class="q-mb-sm compact-card" />

          <!-- 优惠码输入 -->
          <ParcelCouponCodeInput
            v-model="couponCode"
            :order-amnout="state.parcelInfo.price.deliveryPrice"
            :validated-coupon="validatedCoupon"
            @coupon-validated="handleCouponValidated"
            @coupon-cleared="handleCouponCleared"
            class="q-mb-sm compact-card" />

          <!-- 积分抵扣
          <ParcelPointsSelector v-model="pointsToUse" :available-points="userPoints" :max-discount-amount="maxPointsDiscountAmount" @update:model-value="getParcelInfo" class="q-mb-sm compact-card" />
 -->
          <div class="customs-info-section">
            <div class="row items-center q-mb-sm">
              <q-icon name="edit_note" color="primary" size="xs" class="q-mr-xs" />
              <div class="text-h6 text-weight-bold">备注信息</div>
            </div>

            <q-card flat bordered class="q-pa-md">
              <div class="row q-col-gutter-md">
                <div class="col-12">
                  <q-input v-model="state.parcelPayload.userRemark" type="textarea" label="备注内容" outlined dense hint="如果您对包裹有特殊的需求，请在此填写。" counter maxlength="200" autogrow />
                </div>
              </div>
            </q-card>
          </div>

          <!-- 费用汇总 -->
          <div class="customs-info-section">
            <div class="row items-center q-my-sm">
              <q-icon name="receipt_long" color="primary" size="xs" class="q-mr-xs" />
              <div class="text-h6 text-weight-bold">费用汇总</div>
            </div>
          </div>
          <q-card flat bordered class="q-my-sm summary-card compact-card">
            <q-card-section class="q-py-xs">
              <div class="row q-col-gutter-xs">
                <div class="col-12 col-sm-6">
                  <div class="row justify-between q-mb-xs">
                    <div class="text-caption">包裹总重量:</div>
                    <div class="text-caption">{{ (state.parcelInfo.info.weight || 0) + (state.parcelInfo.info.packageWeight || 0) }} g</div>
                  </div>
                  <div class="row justify-between q-mb-xs">
                    <div class="text-caption">基础运费:</div>
                    <div class="text-caption">{{ fen2yuan(state.parcelInfo.price.deliveryPrice || 0) }}</div>
                  </div>
                  <div class="row justify-between q-mb-xs" v-if="state.parcelInfo.price.insurancePrice > 0">
                    <div class="text-caption">保险费用:</div>
                    <div class="text-caption">{{ fen2yuan(state.parcelInfo.price.insurancePrice) }}</div>
                  </div>
                </div>
                <div class="col-12 col-sm-6">
                  <div class="row justify-between q-mb-xs" v-if="state.parcelInfo.price.servicePrice > 0">
                    <div class="text-caption">增值服务:</div>
                    <div class="text-caption">{{ fen2yuan(state.parcelInfo.price.servicePrice) }}</div>
                  </div>
                  <div class="row justify-between q-mb-xs" v-if="state.parcelInfo.price.platformPrice > 0">
                    <div class="text-caption">平台佣金:</div>
                    <div class="text-caption">{{ fen2yuan(state.parcelInfo.price.platformPrice) }}</div>
                  </div>
                  <div class="row justify-between q-mb-xs text-negative" v-if="state.parcelInfo.price.couponPrice > 0">
                    <div class="text-caption">优惠券抵扣:</div>
                    <div class="text-caption">- {{ fen2yuan(state.parcelInfo.price.couponPrice) }}</div>
                  </div>
                  <div class="row justify-between q-mb-xs text-negative" v-if="state.parcelInfo.price.pointPrice > 0">
                    <div class="text-caption">积分抵扣:</div>
                    <div class="text-caption">-{{ fen2yuan(state.parcelInfo.price.pointPrice) }}</div>
                  </div>
                </div>
              </div>
              <q-separator class="q-my-xs" />
              <div class="row justify-end text-weight-bold">
                <div class="text-body2">
                  总计: <span class="text-primary"> {{ fen2yuan(state.parcelInfo.price.payPrice || 0) }}</span>
                </div>
              </div>
            </q-card-section>
          </q-card>

          <!-- 提交按钮 -->
          <div class="row justify-end q-my-lg">
            <q-btn color="primary" label="提交包裹" icon-left="local_shipping" class="q-px-sm q-py-xs submit-btn" :disable="!isFormValid" @click="submitParcel" :loading="submitting">
              <template #loading>
                <q-spinner-dots />
              </template>
            </q-btn>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, reactive } from 'vue';
import { useQuasar } from 'quasar';
import { useRouter } from 'vue-router';
import { useParcelStore } from '../../store/parcel';
import ParcelApi from '../../composables/parcelApi';
import AddressApi from '../../composables/addressApi';
import { useServeStore } from '../../store/serve';
import { useCurrencyStore } from '~/store/currency';
// 导入组件
import ParcelProductList from '~/components/parcel/ParcelProductList.vue';
import ParcelAddressSelector from '~/components/parcel/ParcelAddressSelector.vue';
import ParcelCustomsInfo from '~/components/parcel/ParcelCustomsInfo.vue';
import ParcelLogisticsPlanSelector from '~/components/parcel/ParcelLogisticsPlanSelector.vue';
import ParcelCompactServices from '~/components/parcel/ParcelCompactServices.vue';
import ParcelCouponCodeInput from '~/components/parcel/ParcelCouponCodeInput.vue';
import StockSelector from '~/components/parcel/StockSelector.vue';
import { fen2yuan } from '../../utils/utils';
import { PayOrderSources } from '../../utils/constants';

// 防抖函数
function debounce(fn, delay) {
  let timer = null;
  return function () {
    const context = this;
    const args = arguments;
    clearTimeout(timer);
    timer = setTimeout(() => {
      fn.apply(context, args);
    }, delay);
  };
}

definePageMeta({
  middleware: 'auth', // 引入身份验证中间件
});

const $q = useQuasar();
const router = useRouter();
const parcelStore = useParcelStore();
const serveStore = useServeStore();
const currencyStore = useCurrencyStore();

// 步骤控制
// const step = ref(1);
const submitting = ref(false);
const showStockSelector = ref(false);

// 商品数据
const selectedProducts = ref([]);
const totalWeight = ref(0);
const totalParcelWeight = ref(0);
const totalParcelValue = ref(0);

// 地址数据
const addresses = ref([]);
const selectedAddress = ref(null);

// 报关信息 - 使用state.parcelPayload中的字段
const customsInfo = computed({
  get: () => ({
    declaredValue: state.parcelPayload.declareValue || 0, // 申报价值
    clearanceCode: state.parcelPayload.clearanceCode || '', // 清关代码
    declareContent: state.parcelPayload.declareContent || '', // 申报内容
  }),
  set: (val) => {
    state.parcelPayload.declareValue = val.declaredValue || 0;
    state.parcelPayload.clearanceCode = val.clearanceCode || '';
    state.parcelPayload.declareContent = val.declareContent || '';
    // 更新后重新计算价格
    // getParcelInfo();
  },
});

// 物流方案数据
const shippingMethods = ref([]);
const selectedTransportPlan = ref(null);

// 保险服务数据
const insuranceServices = computed(() => {
  return serveStore.list.filter((item) => item.type === 70);
});
const selectedInsuranceServices = ref([]);

// 免费服务
const freeServices = computed(() => {
  return serveStore.list.filter((item) => item.type === 50);
});
const selectedFreeServices = ref([]);

// 收费服务
const chargeServices = computed(() => {
  return serveStore.list.filter((item) => item.type === 60);
});
const selectedChargeServices = ref([]);

// 优惠券相关
const coupons = ref([]);
const selectedCoupon = ref(null);
const couponDiscountAmount = ref(0);

// 优惠码相关
const couponCode = ref('');
const validatedCoupon = ref(null);

// 积分相关
const userPoints = ref(5000); // 用户可用积分
const pointsToUse = ref(0); // 使用的积分数量
const pointsDiscountAmount = ref(0); // 积分抵扣金额
const maxPointsDiscountAmount = computed(() => {
  // 最大可抵扣金额为总费用的30%，或者API返回的最大可抵扣金额
  if (state.parcelInfo && state.parcelInfo.price && state.parcelInfo.price.maxPointDiscountAmount) {
    return state.parcelInfo.price.maxPointDiscountAmount;
  }
  return 0;
});

// 费用计算
const insuranceFee = ref(0);
const extraServicesFee = ref(0);

// 验证状态
const isCustomsValid = ref(true);

// 表单验证
const isFormValid = computed(() => {
  return selectedProducts.value.length > 0 && selectedAddress.value && selectedTransportPlan.value && isCustomsValid.value;
});

const state = reactive({
  parcelPayload: {
    userRemark: '', //用户备注
    addressId: null, //收货地址ID
    transportPlanId: null, //物流方案ID
    transportPlanFeeId: null, //物流方案价格ID
    insuranceServices: [], //保险服务ID列表
    freeServices: [], //免费服务ID列表
    chargeServices: [], //增值服务ID列表
    couponId: null, //优惠券ID
    couponCode: '', //优惠码
    points: null, //使用积分数量
    declareContent: null, //申报内容
    declareValue: 0, //申报价值
    clearanceCode: null, //清关代码
    pointStatus: false, //是否使用积分
  },
  parcelInfo: {
    items: [], //物品项列表
    price: {}, //价格信息
    address: {}, // 默认收货地址
    logisticsPlans: [], //物流方案列表
    coupons: [], //优惠券列表
    insuranceServices: [], // 保险列表
    freeServices: [], //免费服务列表
    chargeServices: [], // 增值服务列表
    info: {}, //包裹信息
  },
});

// 初始化数据
onMounted(async () => {
  console.log('选择的商品', parcelStore.selectProducts);

  // 获取用户收货地址列表
  await _getAddressList();

  //获取服务列表
  serveStore.fetchServices();

  // 如果从仓库页面传递了商品ID，则获取包裹信息
  if (parcelStore.selectProducts && parcelStore.selectProducts.length > 0) {
    await getParcelInfo();
  }
});

//包裹订单预计算 - 所有影响价格的操作都应该调用此方法
// 使用防抖函数避免频繁调用
const getParcelInfoDebounced = debounce(async () => {
  await _getParcelInfo();
}, 300);

async function getParcelInfo() {
  getParcelInfoDebounced();
}

// 实际执行包裹信息获取的函数
async function _getParcelInfo() {
  try {
    // 创建包含商品ID和数量的items数组
    const items =
      selectedProducts.value.length > 0
        ? selectedProducts.value.map((product) => ({
            stockId: product.id,
            count: product.count, // 添加商品数量
          }))
        : parcelStore.selectProducts.map((item) => ({
            stockId: item.stockId,
            count: item.count, // 默认数量为1
          }));

    // 更新state中的商品信息
    state.parcelInfo.items = items;

    // 构建API请求参数
    const params = {
      items: items,
      deliveryType: 1,
      pointStatus: pointsToUse.value > 0, // 是否使用积分
      pointAmount: pointsToUse.value, // 使用的积分数量
    };

    // 添加收货地址ID
    if (selectedAddress.value && selectedAddress.value.id) {
      params.addressId = selectedAddress.value.id;
      state.parcelPayload.addressId = selectedAddress.value.id;
    }

    // 添加优惠券ID
    if (selectedCoupon.value && selectedCoupon.value.id) {
      params.couponId = selectedCoupon.value.id;
      state.parcelPayload.couponId = selectedCoupon.value.id;
    }

    // 添加优惠码
    if (couponCode.value) {
      params.couponCode = couponCode.value;
      state.parcelPayload.couponCode = couponCode.value;
    }

    // 物流方案
    if (selectedTransportPlan.value && selectedTransportPlan.value.productId) {
      params.transportPlanId = selectedTransportPlan.value.productId;
      params.transportPlanFeeId = selectedTransportPlan.value.priceId;
      state.parcelPayload.transportPlanId = selectedTransportPlan.value.productId;
      state.parcelPayload.transportPlanFeeId = selectedTransportPlan.value.priceId;
      console.log('发送API请求，包含物流方案ID:', selectedTransportPlan.value.productId);
    } else {
      console.log('没有选中的物流方案，selectedTransportPlan.value:', selectedTransportPlan.value);
    }

    // 保险服务
    if (selectedInsuranceServices.value && selectedInsuranceServices.value.length > 0) {
      params.insuranceServices = selectedInsuranceServices.value;
      state.parcelPayload.insuranceServices = selectedInsuranceServices.value;
    }

    // 免费服务
    if (selectedFreeServices.value && selectedFreeServices.value.length > 0) {
      params.freeServices = selectedFreeServices.value;
      state.parcelPayload.freeServices = selectedFreeServices.value;
    }

    // 增值服务
    if (selectedChargeServices.value && selectedChargeServices.value.length > 0) {
      params.chargeServices = selectedChargeServices.value;
      state.parcelPayload.chargeServices = selectedChargeServices.value;
    }

    // 积分使用状态
    state.parcelPayload.pointStatus = pointsToUse.value > 0;
    state.parcelPayload.points = pointsToUse.value;

    // 调用API获取包裹信息和价格
    const { code, data } = await ParcelApi.settlementParcel(params);
    if (code !== 0) {
      $q.notify({
        color: 'negative',
        message: '获取包裹信息失败，请重试',
        icon: 'error',
      });
      return;
    }

    // 更新包裹信息
    state.parcelInfo = data;

    // 更新商品信息
    if (data.items && data.items.length > 0) {
      // 如果API返回了商品信息，使用API返回的数据更新selectedProducts
      selectedProducts.value = data.items.map((item) => ({
        id: item.stockId,
        count: item.count,
        maxCount: item.stockCount,
        spuName: item.spuName,
        picUrl: item.picUrl,
        properties: item.properties,
        weight: item.weight,
        volume: item.volume,
        inTime: item.inTime,
        value: item.value || 100, // 默认价值100元
      }));
    }

    // 更新价格信息
    if (data.price) {
      // 基础运费
      // baseShippingFee.value = data.price.transportFee || 0;
      // 保险费用
      insuranceFee.value = data.price.insuranceFee || 0;
      // 增值服务费用
      extraServicesFee.value = data.price.chargeServiceFee || 0;
      // 优惠券折扣
      couponDiscountAmount.value = data.price.couponAmount || 0;
      // 积分抵扣
      pointsDiscountAmount.value = data.price.pointAmount || 0;
      // 总重量
      totalWeight.value = data.totalWeight || 0;
      // 包裹总重量
      totalParcelWeight.value = data.parcelWeight || totalWeight.value * 1.1;
      // 包裹总价值
      totalParcelValue.value = data.parcelValue || 0;
    }

    // 更新物流方案
    if (data.logisticsPlans && data.logisticsPlans.length > 0) {
      console.log('物流方案更新成功:', data.logisticsPlans);
      shippingMethods.value = data.logisticsPlans;

      // 按价格排序，选择最便宜的方案作为默认方案
      const sortedPlans = [...data.logisticsPlans].sort((a, b) => {
        const feeA = parseFloat(a.totalFee || 0);
        const feeB = parseFloat(b.totalFee || 0);
        return feeA - feeB;
      });

      // 只有在没有选中方案时才自动选择最便宜的，避免覆盖用户的选择
      if (!selectedTransportPlan.value) {
        selectedTransportPlan.value = sortedPlans[0];
      } else {
        // 查找当前选中的方案是否在新列表中
        const currentPlan = data.logisticsPlans.find((plan) => plan.productId === selectedTransportPlan.value.productId && plan.priceId === selectedTransportPlan.value.priceId);
        if (currentPlan) {
          // 如果找到了，更新价格信息（保持用户的选择，但更新价格）
          selectedTransportPlan.value = currentPlan;
          console.log('更新选中物流方案的价格信息:', currentPlan);
        } else {
          // 如果当前选中的方案不在新列表中，重新选择最便宜的
          selectedTransportPlan.value = sortedPlans[0];
          console.log('当前选中的物流方案不存在，重新选择最便宜的:', sortedPlans[0]);
        }
      }
    } else {
      // 当logisticsPlans为null或空数组时，清空相关数据
      console.log('物流方案为空，清空相关数据');
      shippingMethods.value = [];
      selectedTransportPlan.value = null;
    }

    // 更新优惠券
    if (data.coupons && data.coupons.length > 0) {
      coupons.value = data.coupons;
    }

    console.log('包裹信息更新成功:', data);
  } catch (error) {
    console.error('获取包裹信息失败:', error);
    $q.notify({
      color: 'negative',
      message: '获取包裹信息失败，请重试',
      icon: 'error',
    });
  }
}

// 获取用户收货地址列表
async function _getAddressList() {
  try {
    // 这里应该调用获取地址列表的API
    const { code, data } = await AddressApi.getAddressList();
    if (code === 0 && data) {
      addresses.value = data;
      if (data.length > 0 && !selectedAddress.value) {
        selectedAddress.value = data[0];
      }
    }

    if (addresses.value.length > 0 && !selectedAddress.value) {
      selectedAddress.value = addresses.value[0];
    }
  } catch (error) {
    console.error('获取地址列表失败:', error);
    $q.notify({
      color: 'negative',
      message: '获取地址列表失败，请重试',
      icon: 'error',
    });
  }
}

// 处理库存选择
async function handleStockSelected(selectedStock) {
  if (selectedStock && selectedStock.length > 0) {
    // 更新选中的商品，确保每个商品都有maxCount属性
    selectedProducts.value = selectedStock.map((item) => ({
      ...item,
      maxCount: item.maxCount || item.count, // 如果没有maxCount，则默认为当前数量
    }));
    // 更新parcelStore中的selectIds
    parcelStore.selectProducts = selectedStock.map((item) => ({ stockId: item.id, count: item.count, maxCount: item.count }));
    // 重新获取包裹信息和价格
    await getParcelInfo();
  }
}

// 删除商品
async function removeProduct(product) {
  // 从选中商品列表中移除
  const index = selectedProducts.value.findIndex((item) => item.id === product.id);
  if (index !== -1) {
    selectedProducts.value.splice(index, 1);
    // 更新parcelStore中的selectIds
    parcelStore.selectProducts = selectedProducts.value.map((item) => ({ stockId: item.id, count: item.count }));
    // 重新获取包裹信息和价格
    await getParcelInfo();
  }
}

// 更新商品数量
async function updateProductCount({ product, newCount }) {
  // 查找商品并更新数量
  const index = selectedProducts.value.findIndex((item) => item.id === product.id);
  if (index !== -1) {
    // 确保数量不超过最大库存数量
    const maxCount = product.maxCount || product.count;
    if (newCount > maxCount) {
      newCount = maxCount;
      return;
    }

    // 更新商品数量
    selectedProducts.value[index].count = newCount;

    // 更新parcelStore中的selectIds
    parcelStore.selectProducts = selectedProducts.value.map((item) => ({ stockId: item.id, count: item.count }));

    // 重新获取包裹信息和价格
    await getParcelInfo();
  }
}

// 处理地址选择变更
async function handleAddressChange(address) {
  if (address && address.id) {
    selectedAddress.value = address;
    // 更新state中的addressId
    state.parcelPayload.addressId = address.id;
    // 重新计算价格
    await getParcelInfo();
  }
}

// 处理报关信息验证状态变化
function handleCustomsValidationChange(isValid) {
  isCustomsValid.value = isValid;
  console.log('报关信息验证状态:', isValid);
}

// 添加新地址
async function addNewAddress(address) {
  try {
    console.log('添加新地址:', address);

    // 确保地址有正确的ID
    if (!address.id) {
      console.error('新地址缺少ID:', address);
      $q.notify({
        color: 'negative',
        message: '地址数据异常，请重试',
        icon: 'error',
      });
      return;
    }

    // 添加到地址列表
    addresses.value.push(address);

    // 自动选择新添加的地址
    selectedAddress.value = address;

    console.log('地址添加成功，当前选中地址:', selectedAddress.value);

    // 重新获取包裹信息以更新物流方案
    await _getParcelInfo();

    $q.notify({
      color: 'positive',
      message: '地址添加成功',
      icon: 'check_circle',
    });
  } catch (error) {
    console.error('添加地址失败:', error);
    $q.notify({
      color: 'negative',
      message: '添加地址失败，请重试',
      icon: 'error',
    });
  }
}

// 更新保险服务选择
async function updateInsuranceServices(services) {
  selectedInsuranceServices.value = services;
  await getParcelInfo();
}

// 更新免费服务选择
async function updateFreeServices(services) {
  selectedFreeServices.value = services;
  await getParcelInfo();
}

// 更新增值服务选择
async function updateChargeServices(services) {
  selectedChargeServices.value = services;
  await getParcelInfo();
}

// 处理优惠码验证成功
async function handleCouponValidated(couponData) {
  validatedCoupon.value = couponData;
  await getParcelInfo();
}

// 处理优惠码清除
async function handleCouponCleared() {
  validatedCoupon.value = null;
  couponCode.value = '';
  await getParcelInfo();
}

// 提交包裹
async function submitParcel() {
  if (!isFormValid.value) {
    $q.notify({
      color: 'negative',
      message: '请完善必填信息',
      icon: 'warning',
    });
    return;
  }

  try {
    submitting.value = true;

    const items =
      selectedProducts.value.length > 0
        ? selectedProducts.value.map((product) => ({
            stockId: product.id,
            count: product.count, // 添加商品数量
          }))
        : parcelStore.selectProducts.map((item) => ({
            stockId: item.stockId,
            count: item.count, // 默认数量为1
          }));

    // 确保state.parcelPayload中的数据是最新的
    state.parcelPayload.addressId = selectedAddress.value.id;
    state.parcelPayload.transportPlanId = selectedTransportPlan.value.productId;
    state.parcelPayload.transportPlanFeeId = selectedTransportPlan.value.priceId;
    state.parcelPayload.couponId = selectedCoupon.value?.id;
    state.parcelPayload.couponCode = couponCode.value;
    state.parcelPayload.insuranceServices = selectedInsuranceServices.value;
    state.parcelPayload.freeServices = selectedFreeServices.value;
    state.parcelPayload.chargeServices = selectedChargeServices.value;
    state.parcelPayload.pointStatus = pointsToUse.value > 0;
    state.parcelPayload.points = pointsToUse.value;

    // 使用state.parcelPayload中的数据构建请求参数
    const { code, data, msg } = await ParcelApi.createParcel({
      items: items,
      couponId: state.parcelPayload.couponId,
      couponCode: state.parcelPayload.couponCode,
      userRemark: state.parcelPayload.userRemark,
      addressId: state.parcelPayload.addressId,
      transportPlanId: state.parcelPayload.transportPlanId,
      transportPlanFeeId: state.parcelPayload.transportPlanFeeId,
      insuranceServices: state.parcelPayload.insuranceServices,
      freeServices: state.parcelPayload.freeServices,
      chargeServices: state.parcelPayload.chargeServices,
      deliveryType: 1, //快递运输
      pointStatus: state.parcelPayload.pointStatus,
      points: state.parcelPayload.points,
      currency: currencyStore.getSelectedCurrency.currency,
      declareContent: state.parcelPayload.declareContent,
      declareValue: state.parcelPayload.declareValue,
      clearanceCode: state.parcelPayload.clearanceCode,
    });
    if (code !== 0) {
      // 错误信息提示
      $q.notify({
        color: 'negative',
        message: msg,
        icon: 'error',
      });
      return;
    }

    if (data.payOrderId && data.payOrderId > 0) {
      // sessionStorage.setItem('payOrderId', data.payOrderId);
      // navigateTo('/pay');
      navigateTo({
        path: '/pay',
        query: { pId: data.payOrderId, ps: PayOrderSources.PARCEL },
      });
    } else {
      navigateTo('/account/parcel', {
        params: {
          id: data.id,
        },
      });
    }
  } catch (error) {
    console.error('提交包裹失败:', error);
  } finally {
    submitting.value = false;
  }
}
</script>

<style lang="scss" scoped>
.create-parcel-page {
  // 紧凑卡片样式
  .compact-card {
    :deep(.text-h6) {
      font-size: 0.85rem !important;
      line-height: 1.2 !important;
      margin: 0 !important;
    }

    :deep(.q-icon) {
      font-size: 0.9rem !important;
    }

    :deep(.q-card-section) {
      padding: 8px !important;
    }

    :deep(.q-item) {
      min-height: auto;
      padding: 4px 8px;
    }

    :deep(.q-item__section--avatar) {
      min-width: auto;
      padding-right: 8px;
    }

    :deep(.q-field__control) {
      height: 36px;
      min-height: 36px;
    }

    :deep(.q-field__marginal) {
      height: 36px;
    }
  }

  // 统一组件标题样式
  :deep(.text-h6) {
    font-size: 0.85rem !important;
    line-height: 1.2 !important;
  }

  :deep(.q-icon) {
    font-size: 0.9rem !important;
  }

  .summary-card {
    border-radius: 4px;
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
    }

    .q-card-section {
      padding: 8px;
    }
  }

  .submit-btn,
  .select-stock-btn {
    // border-radius: 4px;
    // font-weight: 500;
    transition: all 0.2s ease;
    // font-size: 13px;

    &:hover {
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
      transform: translateY(-1px);
    }
  }

  // 返回按钮样式
  .back-btn {
    @media (max-width: 599px) {
      padding: 4px 8px;

      .q-icon {
        font-size: 1.2em;
      }
    }
  }

  // 步骤指示器样式
  .compact-stepper {
    box-shadow: none;

    .q-stepper__header {
      min-height: auto;
      padding: 8px 0;
    }

    .q-stepper__tab {
      padding: 8px 0;
      min-height: auto;
    }

    .q-stepper__tab--active {
      color: #1976d2;
      font-weight: 500;
    }

    .q-stepper__tab--done {
      color: #21ba45;
    }

    .q-stepper__dot {
      margin-right: 4px;
      height: 20px;
      width: 20px;
      font-size: 10px;
    }

    .q-stepper__title {
      font-size: 13px;
      font-weight: 500;
      line-height: 1.2;
    }

    .q-stepper__line {
      margin: 0 8px;
    }
  }

  // 响应式调整
  @media (max-width: 599px) {
    .compact-stepper {
      .q-stepper__tab {
        padding: 4px 0;
      }

      .q-stepper__title {
        font-size: 11px;
      }

      .q-stepper__dot {
        height: 18px;
        width: 18px;
        margin-right: 2px;
      }

      .q-stepper__line {
        margin: 0 4px;
      }
    }
  }
}
</style>
