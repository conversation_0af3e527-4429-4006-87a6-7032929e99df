<template>
  <div class="loading-page">
    <!-- <q-spinner size="40px" color="primary" /> -->
    <div class="text-primary text-h6 q-ml-sm">正在跳转到支付页面，请稍候...</div>
  </div>
</template>

<script setup>
// import { useHead } from '@nuxtjs/composition-api';

useHead({
  title: '跳转中...',
});
</script>

<style scoped>
.loading-page {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: #f5f5f5;
}
</style>
