import { defineNuxtPlugin } from '#app';
import { Notify } from 'quasar';

export default defineNuxtPlugin((nuxtApp) => {
  //调用全局的 showNotify
  nuxtApp.provide('showNotify', (params) => {
    if (params.type) {
      //预定义类型可以使用 positive negative warning and info
      Notify.create({
        type: params.type || 'positive',
        message: params.msg || 'Default message',
        position: params.position || 'top',
        timeout: params.time || 2000,
      });
    } else {
      Notify.create({
        message: params.msg || 'Default message',
        color: params.color || 'positive',
        icon: params.icon || 'info',
        position: params.position || 'top',
        timeout: params.time || 2000,
      });
    }
  });
});
