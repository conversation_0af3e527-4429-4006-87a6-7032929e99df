<template>
  <Header />
  <div class="transfer-page">
    <!-- 顶部横幅 -->
    <div class="banner">
      <div class="banner-content">
        <h1 class="banner-title">{{ $t('transfer.title') }}</h1>
        <p class="banner-subtitle">{{ $t('transfer.subtitle') }}</p>
        <p class="banner-description">{{ $t('transfer.description') }}</p>
        <div class="banner-cta">
          <q-btn color="primary" size="lg" :label="$t('transfer.cta.button')" @click="goToCreateTransfer" />
        </div>
      </div>
    </div>

    <!-- 转运流程 -->
    <div class="section steps-section">
      <div class="section-container">
        <h2 class="section-title">{{ $t('transfer.steps.title') }}</h2>
        <div class="steps-container">
          <div class="step-item" v-for="i in 6" :key="i">
            <div class="step-number">{{ i }}</div>
            <div class="step-content">
              <h3 class="step-title">{{ $t(`transfer.steps.step${i}.title`) }}</h3>
              <p class="step-description">{{ $t(`transfer.steps.step${i}.description`) }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 服务特色 -->
    <div class="section features-section">
      <div class="section-container">
        <h2 class="section-title">{{ $t('transfer.features.title') }}</h2>
        <div class="features-container">
          <div class="feature-item" v-for="i in 6" :key="i">
            <div class="feature-icon">
              <q-icon :name="featureIcons[i - 1]" size="48px" color="primary" />
            </div>
            <div class="feature-content">
              <h3 class="feature-title">{{ $t(`transfer.features.feature${i}.title`) }}</h3>
              <p class="feature-description">{{ $t(`transfer.features.feature${i}.description`) }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 行动召唤 -->
    <div class="section cta-section">
      <div class="section-container">
        <h2 class="section-title">{{ $t('transfer.cta.title') }}</h2>
        <p class="cta-subtitle">{{ $t('transfer.cta.subtitle') }}</p>
        <div class="cta-buttons">
          <q-btn color="primary" size="lg" :label="$t('transfer.cta.button')" @click="goToCreateTransfer" />
        </div>
      </div>
    </div>
  </div>
  <Footer />
</template>

<script setup>
import { useAuthStore } from '~/store/auth';
import { useRouter } from 'vue-router';
import { computed } from 'vue';

const authStore = useAuthStore();
const router = useRouter();

// 检查用户是否已登录
const isLoggedIn = computed(() => authStore.isLogin);

// 图标数组
const featureIcons = [
  'public', // 全球配送
  'inventory_2', // 专业包装
  'gps_fixed', // 实时追踪
  'local_shipping', // 多种运输方式
  'warehouse', // 仓储服务
  'support_agent', // 专业客服
];

// 跳转到创建转运单页面
const goToCreateTransfer = () => {
  router.push('/account/transfer-create');
};

// 跳转到登录页面
const goToLogin = () => {
  router.push('/login?redirect=/account/transfer/create');
};
</script>

<style lang="scss" scoped>
.transfer-page {
  background-color: #f5f7fa;
}

// 顶部横幅
.banner {
  background: linear-gradient(135deg, #1976d2, #64b5f6);
  color: white;
  padding: 60px 0;
  text-align: center;

  @media (max-width: 767px) {
    padding: 40px 0;
  }

  .banner-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  .banner-title {
    font-size: 36px;
    font-weight: bold;
    margin-bottom: 16px;

    @media (max-width: 767px) {
      font-size: 28px;
    }
  }

  .banner-subtitle {
    font-size: 20px;
    margin-bottom: 16px;

    @media (max-width: 767px) {
      font-size: 18px;
    }
  }

  .banner-description {
    font-size: 16px;
    max-width: 800px;
    margin: 0 auto 30px;
    line-height: 1.6;

    @media (max-width: 767px) {
      font-size: 14px;
    }
  }

  .banner-cta {
    margin-top: 20px;
  }
}

// 通用部分样式
.section {
  padding: 60px 0;

  @media (max-width: 767px) {
    padding: 40px 0;
  }

  &:nth-child(even) {
    background-color: white;
  }

  .section-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  .section-title {
    text-align: center;
    font-size: 28px;
    font-weight: bold;
    margin-bottom: 40px;
    color: #333;

    @media (max-width: 767px) {
      font-size: 24px;
      margin-bottom: 30px;
    }
  }
}

// 转运流程部分
.steps-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;

  @media (max-width: 991px) {
    grid-template-columns: repeat(2, 1fr);
  }

  @media (max-width: 599px) {
    grid-template-columns: 1fr;
  }
}

.step-item {
  display: flex;
  align-items: flex-start;
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .step-number {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--q-primary);
    color: white;
    font-weight: bold;
    font-size: 18px;
    margin-right: 16px;
    flex-shrink: 0;
  }

  .step-content {
    flex: 1;
  }

  .step-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #333;
  }

  .step-description {
    font-size: 14px;
    color: #666;
    line-height: 1.5;
  }
}

// 服务特色部分
.features-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;

  @media (max-width: 991px) {
    grid-template-columns: repeat(2, 1fr);
  }

  @media (max-width: 599px) {
    grid-template-columns: 1fr;
  }
}

.feature-item {
  background-color: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  text-align: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .feature-icon {
    margin-bottom: 16px;
  }

  .feature-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 12px;
    color: #333;
  }

  .feature-description {
    font-size: 14px;
    color: #666;
    line-height: 1.5;
  }
}

// 常见问题部分
.faq-container {
  max-width: 800px;
  margin: 0 auto;
}

.faq-item {
  margin-bottom: 16px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.faq-question {
  font-weight: 600;
  font-size: 16px;
  padding: 16px;
}

.faq-answer {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  padding: 16px;
  background-color: #f9f9f9;
}

// 行动召唤部分
.cta-section {
  text-align: center;
  background: linear-gradient(135deg, #1976d2, #64b5f6);
  color: white;
}

.cta-subtitle {
  font-size: 18px;
  max-width: 700px;
  margin: 0 auto 30px;
  line-height: 1.6;

  @media (max-width: 767px) {
    font-size: 16px;
  }
}

.cta-buttons {
  margin-top: 30px;
}

// 白色背景下的标题颜色
.features-section .section-title,
.steps-section .section-title {
  color: #333;
}

// 深色背景下的标题颜色
.cta-section .section-title {
  color: white;
}
</style>
