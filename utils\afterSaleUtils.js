/**
 * 售后相关工具函数
 */

import { AfterSaleStatusEnum, AfterSaleWayEnum, AfterSaleTypeEnum } from './constants';

/**
 * 获取售后状态文本
 * @param {Object} afterSale - 售后对象
 * @returns {string} 状态文本
 */
export function formatAfterSaleStatus(afterSale) {
  if (!afterSale || afterSale.status === undefined) {
    return '未知状态';
  }

  switch (afterSale.status) {
    case AfterSaleStatusEnum.APPLY:
      return '申请中';
    case AfterSaleStatusEnum.SELLER_AGREE:
      return '卖家通过';
    case AfterSaleStatusEnum.BUYER_DELIVERY:
      return '待卖家收货';
    case AfterSaleStatusEnum.WAIT_REFUND:
      return '等待平台退款';
    case AfterSaleStatusEnum.COMPLETE:
      return '完成';
    case AfterSaleStatusEnum.BUYER_CANCEL:
      return '买家取消售后';
    case AfterSaleStatusEnum.SELLER_DISAGREE:
      return '卖家拒绝';
    case AfterSaleStatusEnum.SELLER_REFUSE:
      return '卖家拒绝收货';
    default:
      return '未知状态';
  }
}

/**
 * 获取售后状态描述
 * @param {Object} afterSale - 售后对象
 * @returns {string} 状态描述
 */
export function formatAfterSaleStatusDescription(afterSale) {
  if (!afterSale || afterSale.status === undefined) {
    return '状态未知';
  }

  switch (afterSale.status) {
    case AfterSaleStatusEnum.APPLY:
      return '会员申请退款';
    case AfterSaleStatusEnum.SELLER_AGREE:
      return '商家同意退款';
    case AfterSaleStatusEnum.BUYER_DELIVERY:
      return '会员填写退货物流信息';
    case AfterSaleStatusEnum.WAIT_REFUND:
      return '商家收货';
    case AfterSaleStatusEnum.COMPLETE:
      return '商家确认退款';
    case AfterSaleStatusEnum.BUYER_CANCEL:
      return '会员取消退款';
    case AfterSaleStatusEnum.SELLER_DISAGREE:
      return '商家拒绝退款';
    case AfterSaleStatusEnum.SELLER_REFUSE:
      return '商家拒绝收货';
    default:
      return '状态未知';
  }
}

/**
 * 获取售后方式文本
 * @param {number} way - 售后方式
 * @returns {string} 方式文本
 */
export function formatAfterSaleWay(way) {
  switch (way) {
    case AfterSaleWayEnum.REFUND:
      return '仅退款';
    case AfterSaleWayEnum.RETURN_AND_REFUND:
      return '退货退款';
    default:
      return '未知方式';
  }
}

/**
 * 获取售后类型文本
 * @param {number} type - 售后类型
 * @returns {string} 类型文本
 */
export function formatAfterSaleType(type) {
  switch (type) {
    case AfterSaleTypeEnum.IN_SALE:
      return '售中退款';
    case AfterSaleTypeEnum.AFTER_SALE:
      return '售后退款';
    default:
      return '未知类型';
  }
}

/**
 * 获取售后状态颜色
 * @param {number} status - 售后状态
 * @returns {string} 颜色名称
 */
export function getAfterSaleStatusColor(status) {
  switch (status) {
    case AfterSaleStatusEnum.APPLY:
      return 'orange';
    case AfterSaleStatusEnum.SELLER_AGREE:
      return 'blue';
    case AfterSaleStatusEnum.BUYER_DELIVERY:
      return 'purple';
    case AfterSaleStatusEnum.WAIT_REFUND:
      return 'teal';
    case AfterSaleStatusEnum.COMPLETE:
      return 'positive';
    case AfterSaleStatusEnum.BUYER_CANCEL:
    case AfterSaleStatusEnum.SELLER_DISAGREE:
    case AfterSaleStatusEnum.SELLER_REFUSE:
      return 'negative';
    default:
      return 'grey';
  }
}

/**
 * 处理售后按钮显示逻辑
 * @param {Object} afterSale - 售后对象
 */
export function handleAfterSaleButtons(afterSale) {
  if (!afterSale) return;

  afterSale.buttons = [];

  // 申请中状态可以取消申请
  if (afterSale.status === AfterSaleStatusEnum.APPLY) {
    afterSale.buttons.push('cancel');
  }

  // 卖家同意后，需要填写退货信息（仅退货退款）
  if (afterSale.status === AfterSaleStatusEnum.SELLER_AGREE && afterSale.way === AfterSaleWayEnum.RETURN_AND_REFUND) {
    afterSale.buttons.push('delivery');
  }
}

/**
 * 获取售后进度步骤
 * @param {Object} afterSale - 售后对象
 * @returns {Object} 包含步骤列表和当前激活步骤的对象
 */
export function getAfterSaleSteps(afterSale) {
  if (!afterSale) {
    return { steps: [], active: 0 };
  }

  const steps = [
    { title: '提交申请' },
    { title: '处理中' },
    { title: '完成' }
  ];

  let active = 0;

  // 根据状态确定当前步骤
  if ([AfterSaleStatusEnum.APPLY].includes(afterSale.status)) {
    active = 0;
  } else if ([AfterSaleStatusEnum.SELLER_AGREE, AfterSaleStatusEnum.BUYER_DELIVERY].includes(afterSale.status)) {
    active = 1;
  } else if ([AfterSaleStatusEnum.WAIT_REFUND, AfterSaleStatusEnum.COMPLETE].includes(afterSale.status)) {
    active = 2;
  } else if ([AfterSaleStatusEnum.BUYER_CANCEL, AfterSaleStatusEnum.SELLER_DISAGREE, AfterSaleStatusEnum.SELLER_REFUSE].includes(afterSale.status)) {
    active = 2; // 失败状态也显示在最后一步
  }

  return { steps, active };
}

/**
 * 判断售后是否为失败状态
 * @param {number} status - 售后状态
 * @returns {boolean} 是否为失败状态
 */
export function isAfterSaleFailedStatus(status) {
  return [
    AfterSaleStatusEnum.BUYER_CANCEL,
    AfterSaleStatusEnum.SELLER_DISAGREE,
    AfterSaleStatusEnum.SELLER_REFUSE
  ].includes(status);
}

/**
 * 判断售后是否为成功状态
 * @param {number} status - 售后状态
 * @returns {boolean} 是否为成功状态
 */
export function isAfterSaleSuccessStatus(status) {
  return status === AfterSaleStatusEnum.COMPLETE;
}

/**
 * 判断售后是否为进行中状态
 * @param {number} status - 售后状态
 * @returns {boolean} 是否为进行中状态
 */
export function isAfterSaleProcessingStatus(status) {
  return [
    AfterSaleStatusEnum.APPLY,
    AfterSaleStatusEnum.SELLER_AGREE,
    AfterSaleStatusEnum.BUYER_DELIVERY,
    AfterSaleStatusEnum.WAIT_REFUND
  ].includes(status);
}
