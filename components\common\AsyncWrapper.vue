<template>
  <div class="async-wrapper">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <q-spinner 
        :color="loadingColor" 
        :size="loadingSize" 
        class="loading-spinner"
      />
      <p class="loading-text">{{ loadingText }}</p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <q-icon 
        :name="errorIcon" 
        :size="errorIconSize" 
        :color="errorColor" 
        class="error-icon"
      />
      <h3 class="error-title">{{ errorTitle }}</h3>
      <p class="error-message">{{ errorMessage }}</p>
      
      <!-- 错误操作按钮 -->
      <div class="error-actions">
        <q-btn 
          v-if="showRetry"
          color="primary" 
          @click="handleRetry"
          :loading="retrying"
          class="retry-btn"
        >
          {{ retryText }}
        </q-btn>
        <q-btn 
          v-if="showGoBack"
          flat 
          color="grey" 
          @click="handleGoBack"
          class="back-btn"
        >
          {{ goBackText }}
        </q-btn>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else-if="isEmpty" class="empty-container">
      <q-icon 
        :name="emptyIcon" 
        :size="emptyIconSize" 
        color="grey-5" 
        class="empty-icon"
      />
      <h3 class="empty-title">{{ emptyTitle }}</h3>
      <p class="empty-message">{{ emptyMessage }}</p>
      
      <!-- 空状态操作按钮 -->
      <div class="empty-actions">
        <slot name="empty-actions">
          <q-btn 
            v-if="showEmptyAction"
            color="primary" 
            @click="handleEmptyAction"
            class="empty-action-btn"
          >
            {{ emptyActionText }}
          </q-btn>
        </slot>
      </div>
    </div>

    <!-- 正常内容 -->
    <div v-else class="content-container">
      <slot />
    </div>
  </div>
</template>

<script setup>
/**
 * 异步组件包装器
 * 
 * 功能：
 * - 统一的加载状态显示
 * - 统一的错误处理和重试机制
 * - 统一的空状态显示
 * - 可自定义的操作按钮
 * 
 * 使用示例：
 * <AsyncWrapper 
 *   :loading="loading" 
 *   :error="error" 
 *   :isEmpty="!data.length"
 *   @retry="fetchData"
 * >
 *   <YourContent :data="data" />
 * </AsyncWrapper>
 */

const props = defineProps({
  // 加载状态
  loading: {
    type: Boolean,
    default: false
  },
  loadingText: {
    type: String,
    default: '加载中...'
  },
  loadingColor: {
    type: String,
    default: 'primary'
  },
  loadingSize: {
    type: String,
    default: '3em'
  },

  // 错误状态
  error: {
    type: [Error, String, Object],
    default: null
  },
  errorTitle: {
    type: String,
    default: '加载失败'
  },
  errorIcon: {
    type: String,
    default: 'error_outline'
  },
  errorIconSize: {
    type: String,
    default: '60px'
  },
  errorColor: {
    type: String,
    default: 'negative'
  },
  showRetry: {
    type: Boolean,
    default: true
  },
  retryText: {
    type: String,
    default: '重试'
  },
  showGoBack: {
    type: Boolean,
    default: false
  },
  goBackText: {
    type: String,
    default: '返回'
  },

  // 空状态
  isEmpty: {
    type: Boolean,
    default: false
  },
  emptyTitle: {
    type: String,
    default: '暂无数据'
  },
  emptyMessage: {
    type: String,
    default: '当前没有可显示的内容'
  },
  emptyIcon: {
    type: String,
    default: 'inbox'
  },
  emptyIconSize: {
    type: String,
    default: '60px'
  },
  showEmptyAction: {
    type: Boolean,
    default: false
  },
  emptyActionText: {
    type: String,
    default: '刷新'
  }
});

const emit = defineEmits([
  'retry',      // 重试事件
  'go-back',    // 返回事件
  'empty-action' // 空状态操作事件
]);

// 响应式状态
const retrying = ref(false);

// 计算属性
const errorMessage = computed(() => {
  if (!props.error) return '';
  
  if (typeof props.error === 'string') {
    return props.error;
  }
  
  if (props.error instanceof Error) {
    return props.error.message || '发生了未知错误';
  }
  
  if (typeof props.error === 'object' && props.error.message) {
    return props.error.message;
  }
  
  return '发生了未知错误，请稍后重试';
});

// 事件处理方法
const handleRetry = async () => {
  retrying.value = true;
  try {
    await emit('retry');
  } finally {
    retrying.value = false;
  }
};

const handleGoBack = () => {
  emit('go-back');
  // 如果没有监听器，默认使用路由返回
  if (!getCurrentInstance()?.vnode.props?.onGoBack) {
    const router = useRouter();
    router.back();
  }
};

const handleEmptyAction = () => {
  emit('empty-action');
};
</script>

<style lang="scss" scoped>
.async-wrapper {
  width: 100%;
  min-height: 200px;
}

.loading-container,
.error-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  min-height: 300px;

  .loading-spinner,
  .error-icon,
  .empty-icon {
    margin-bottom: 20px;
  }

  h3 {
    margin: 0 0 10px;
    color: #333;
    font-size: 18px;
    font-weight: 600;
  }

  p {
    margin: 0 0 20px;
    color: #666;
    font-size: 14px;
    line-height: 1.5;
    max-width: 400px;
  }

  .error-actions,
  .empty-actions {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
    justify-content: center;

    .retry-btn,
    .back-btn,
    .empty-action-btn {
      min-width: 80px;
    }
  }
}

.content-container {
  width: 100%;
}

// 响应式设计
@media (max-width: 768px) {
  .loading-container,
  .error-container,
  .empty-container {
    padding: 40px 15px;
    min-height: 250px;

    h3 {
      font-size: 16px;
    }

    p {
      font-size: 13px;
    }

    .error-actions,
    .empty-actions {
      flex-direction: column;
      align-items: center;

      .retry-btn,
      .back-btn,
      .empty-action-btn {
        width: 120px;
      }
    }
  }
}
</style>
