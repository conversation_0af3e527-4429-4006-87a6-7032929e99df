<template>
  <div class="q-pa-md">
    <h4>图片上传测试页面</h4>
    
    <div class="q-mb-md">
      <h6>当前上传的图片URLs:</h6>
      <pre>{{ JSON.stringify(uploadedUrls, null, 2) }}</pre>
    </div>
    
    <div class="q-mb-md">
      <h6>图片上传组件:</h6>
      <ImageUpload
        v-model="uploadedUrls"
        :max-files="5"
        :max-file-size="10 * 1024 * 1024"
        accept="image/*"
        :multiple="true"
        :show-drop-zone="true"
      />
    </div>
    
    <div class="q-mb-md">
      <q-btn 
        label="清空图片" 
        color="negative" 
        @click="clearImages"
      />
      <q-btn 
        label="测试工单弹窗" 
        color="primary" 
        class="q-ml-md"
        @click="showTicketDialog = true"
      />
    </div>
    
    <!-- 工单弹窗测试 -->
    <TicketDialog
      v-model="showTicketDialog"
      @success="onTicketSuccess"
    />
  </div>
</template>

<script setup>
import { ref, watch } from 'vue';
import ImageUpload from '~/components/ImageUpload.vue';
import TicketDialog from '~/components/TicketDialog.vue';

// 页面标题
definePageMeta({
  title: '图片上传测试'
});

// 响应式数据
const uploadedUrls = ref([]);
const showTicketDialog = ref(false);

// 监听上传的URLs变化
watch(uploadedUrls, (newVal, oldVal) => {
  if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
    console.log('测试页面 - uploadedUrls变化:', newVal);
  }
}, { deep: true });

// 方法
const clearImages = () => {
  uploadedUrls.value = [];
  console.log('已清空图片');
};

const onTicketSuccess = (ticket) => {
  console.log('工单创建成功:', ticket);
  showTicketDialog.value = false;
};
</script>

<style scoped>
pre {
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
}
</style>
