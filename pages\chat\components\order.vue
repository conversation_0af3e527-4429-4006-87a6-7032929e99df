<template>
  <div class="order-item" @click="$emit('click')">
    <div class="order-header">
      <div class="order-no">订单号：{{ orderData.no || '未知' }}</div>
      <div class="order-status" :class="getOrderStatusClass(orderData.status)">
        {{ getOrderStatusText(orderData.status) }}
      </div>
    </div>

    <div class="order-content">
      <div v-if="orderData.items && orderData.items.length" class="order-products">
        <div v-for="item in orderData.items" :key="item.id" class="product-item">
          <div class="product-image">
            <q-img :src="item.picUrl || '/images/placeholder.png'" :ratio="1" />
          </div>
          <div class="product-info">
            <div class="product-name">{{ item.spuName }}</div>
            <div class="product-sku" v-if="item.properties && item.properties.length">
              {{ item.properties.map((property) => property.valueName).join(' ') }}
            </div>
            <div class="product-price-qty">
              <span class="product-price">¥{{ formatPrice(item.price) }}</span>
              <span class="product-qty">x{{ item.count }}</span>
            </div>
          </div>
        </div>
      </div>

      <div v-else class="order-summary">
        <div class="summary-image">
          <q-img :src="orderData.picUrl || '/images/placeholder.png'" :ratio="1" />
        </div>
        <div class="summary-info">
          <div class="summary-title">订单摘要</div>
          <div class="summary-desc" v-if="orderData.description">{{ orderData.description }}</div>
        </div>
      </div>
    </div>

    <div class="order-footer">
      <div class="order-total">
        <span class="total-label">共 {{ orderData.productCount || 0 }} 件商品，总金额：</span>
        <span class="total-price">¥{{ formatPrice(orderData.payPrice) }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  orderData: {
    type: Object,
    default: () => ({
      id: '',
      no: '',
      status: 0,
      payPrice: 0,
      productCount: 0,
      items: [],
    }),
  },
});

defineEmits(['click']);

// 格式化价格
function formatPrice(price) {
  if (!price && price !== 0) return '0.00';
  return Number(price / 100).toFixed(2); // 假设价格是以分为单位存储的
}

// 获取订单状态文本
function getOrderStatusText(status) {
  const statusMap = {
    0: '待付款',
    1: '待发货',
    2: '待收货',
    3: '已完成',
    4: '已取消',
    5: '已关闭',
    10: '退款中',
    11: '退款成功',
    12: '退款失败',
  };
  return statusMap[status] || '未知状态';
}

// 获取订单状态样式类
function getOrderStatusClass(status) {
  if (status === 0) return 'warning-status'; // 待付款
  if (status === 1 || status === 2) return 'primary-status'; // 待发货、待收货
  if (status === 3) return 'success-status'; // 已完成
  if (status === 4 || status === 5) return 'info-status'; // 已取消、已关闭
  if (status === 10) return 'warning-status'; // 退款中
  if (status === 11) return 'success-status'; // 退款成功
  if (status === 12) return 'danger-status'; // 退款失败
  return '';
}
</script>

<style lang="scss" scoped>
.order-item {
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
  background-color: #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  }

  .order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    border-bottom: 1px solid #f5f5f5;

    .order-no {
      font-size: 14px;
      font-weight: 500;
      color: #333;
    }

    .order-status {
      font-size: 14px;
      font-weight: 500;

      &.warning-status {
        color: #faad14;
      }

      &.primary-status {
        color: #1976d2;
      }

      &.success-status {
        color: #52c41a;
      }

      &.danger-status {
        color: #ff3000;
      }

      &.info-status {
        color: #999;
      }
    }
  }

  .order-content {
    padding: 10px;

    .order-products {
      .product-item {
        display: flex;
        margin-bottom: 10px;
        padding-bottom: 10px;
        border-bottom: 1px solid #f5f5f5;

        &:last-child {
          margin-bottom: 0;
          padding-bottom: 0;
          border-bottom: none;
        }

        .product-image {
          width: 60px;
          height: 60px;
          margin-right: 10px;
          border-radius: 4px;
          overflow: hidden;
        }

        .product-info {
          flex: 1;
          display: flex;
          flex-direction: column;

          .product-name {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
          }

          .product-sku {
            font-size: 12px;
            color: #999;
            margin-bottom: 4px;
          }

          .product-price-qty {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .product-price {
              font-size: 14px;
              font-weight: 600;
              color: #ff3000;
            }

            .product-qty {
              font-size: 12px;
              color: #999;
            }
          }
        }
      }
    }

    .order-summary {
      display: flex;
      align-items: center;

      .summary-image {
        width: 60px;
        height: 60px;
        margin-right: 10px;
        border-radius: 4px;
        overflow: hidden;
      }

      .summary-info {
        flex: 1;

        .summary-title {
          font-size: 14px;
          font-weight: 500;
          color: #333;
          margin-bottom: 4px;
        }

        .summary-desc {
          font-size: 12px;
          color: #999;
        }
      }
    }
  }

  .order-footer {
    padding: 10px 15px;
    border-top: 1px solid #f5f5f5;
    display: flex;
    justify-content: flex-end;
    align-items: center;

    .order-total {
      .total-label {
        font-size: 12px;
        color: #666;
      }

      .total-price {
        font-size: 16px;
        font-weight: 600;
        color: #ff3000;
      }
    }
  }
}
</style>
