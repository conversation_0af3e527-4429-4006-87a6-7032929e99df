import { defineStore } from 'pinia';
export const useParcelStore = defineStore({
  id: 'settlement',
  state: () => ({
    selectProducts: [],
    list: [],
  }),
  actions: {},
  getters: {
    // cartTotalAmount: (state) => {
    //   return state.list.filter((item) => item.selected).reduce((total, item) => total + (item.price || item.sku.price) * item.count, 0);
    // },
    // cartTotalCount: (state) => {
    //   return state.list.filter((item) => item.selected).length;
    // },
    // cartTotalFreight: (state) => {
    //   // 创建一个 Map 来记录每个店铺的最大运费
    //   const shopFreightMap = new Map();
    //   // 遍历购物车中所有选中的商品
    //   state.list
    //     .filter((item) => item.selected) // 仅计算选中的商品
    //     .forEach((item) => {
    //       const shopName = item.spu.shopName || '未分组'; // 获取店铺名称
    //       const freight = item.spu.freight || 0; // 获取商品运费
    //       // 如果当前店铺还没有记录运费，或者当前商品运费更大，则更新
    //       if (!shopFreightMap.has(shopName) || shopFreightMap.get(shopName) < freight) {
    //         shopFreightMap.set(shopName, freight);
    //       }
    //     });
    //   // 将所有店铺的最大运费累加
    //   return Array.from(shopFreightMap.values()).reduce((total, freight) => total + freight, 0);
    // },
  },
  persist: process.client && {
    storage: sessionStorage,
    paths: ['parcel'],
  },
});
