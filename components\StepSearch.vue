<template>
  <!-- 搜索和步骤 -->
  <div class="step-search column">
    <!-- 标题和搜索 -->
    <h1 class="step-search-title">{{ $t('homeTitle') }}</h1>
    <div class="search-bar">
      <div class="search-input-container">
        <input type="search" v-model="searchText" :placeholder="$t('searchPlaceholder')" @keyup.enter="onSubmit" />

        <!-- 网站图标容器 - 当没有输入时显示 -->
        <div class="website-icons" v-show="!searchText.trim()">
          <i class="iconfont icon-taobao website-icon"></i>
          <i class="iconfont icon-tianmao website-icon"></i>
          <i class="iconfont icon-ali-1688 website-icon"></i>
          <i class="iconfont icon-jingdong website-icon"></i>
          <i class="iconfont icon-pinduoduo website-icon"></i>
          <i class="iconfont icon-weidian website-icon"></i>
          <i class="iconfont icon-xianyu website-icon"></i>
          <i class="iconfont icon-suningyigou website-icon"></i>
        </div>

        <i class="iconfont icon-zhaoxiangji zhaoxiangji gt-xs" @click="openImageSearch" />
      </div>
      <div class="search-button-container" @click="onSubmit">
        <button class="search-button">
          <i class="iconfont icon-sousuo sousuo" />
        </button>
      </div>
    </div>

    <!-- 分步流程 - 自定义实现 -->
    <div class="steps">
      <!-- PC端横向布局 -->
      <div class="steps-container desktop-steps gt-xs">
        <div v-for="(stepText, index) in stepTexts" :key="index" class="step-item">
          <div class="step-circle">
            <span class="step-number">{{ index + 1 }}</span>
          </div>
          <div class="step-label">{{ stepText }}</div>
          <div v-if="index < stepTexts.length - 1" class="step-line"></div>
        </div>
      </div>

      <!-- 移动端垂直布局 -->
      <div class="steps-container mobile-steps lt-sm">
        <div v-for="(stepText, index) in stepTexts" :key="index" class="step-item">
          <div class="step-content">
            <div class="step-circle">
              <span class="step-number">{{ index + 1 }}</span>
            </div>
            <div class="step-label">{{ stepText }}</div>
          </div>
          <!-- <div v-if="index < stepTexts.length - 1" class="step-line-vertical"></div> -->
        </div>
      </div>
    </div>
  </div>

  <!-- 隐藏的文件输入 -->
  <input ref="fileInputRef" type="file" accept="image/*" style="display: none" @change="onFileInputChange" />
</template>

<script setup>
import { ref } from 'vue';
import { useQuasar } from 'quasar';
import { useRouter } from 'vue-router';
import { analyzeSearchInput, buildSearchUrl } from '~/utils/searchUtils';

const { $t } = useNuxtApp();
const $q = useQuasar();
const router = useRouter();
const searchText = ref('');

// 步骤文本数据
const stepTexts = ref([$t('step1'), $t('step2'), $t('step3'), $t('step4')]);

// 以图搜物相关状态
const fileInputRef = ref(null);

// 关键词搜索
const onSubmit = () => {
  if (searchText.value.trim()) {
    // 分析搜索输入类型并构建相应的URL
    const analysis = analyzeSearchInput(searchText.value.trim());
    const searchUrl = buildSearchUrl(analysis);
    // 在新窗口打开搜索页面
    window.open(searchUrl, '_blank');
    searchText.value = '';
  } else {
    // 在新窗口打开搜索页面
    window.open('/search', '_blank');
  }
};

// 打开以图搜物文件选择
const openImageSearch = () => {
  triggerFileUpload();
};

// 触发文件选择
const triggerFileUpload = () => {
  fileInputRef.value?.click();
};

// 处理文件选择
const onFileInputChange = (event) => {
  console.log('📁 File input changed:', event.target.files);
  const file = event.target.files?.[0];
  if (file) {
    console.log('📁 Selected file:', file.name, file.type, file.size);
    handleFileSelection(file);
  }
  event.target.value = '';
};

// 处理文件选择
const handleFileSelection = (file) => {
  console.log('🔍 Handling file selection:', file.name);

  // 验证文件类型
  if (!file.type.startsWith('image/')) {
    console.log('❌ Invalid file type:', file.type);
    $q.notify({
      color: 'negative',
      message: '只能上传图片文件',
      icon: 'error',
    });
    return;
  }

  // 验证文件大小 (5MB)
  if (file.size > 5 * 1024 * 1024) {
    console.log('❌ File too large:', file.size);
    $q.notify({
      color: 'negative',
      message: '文件大小不能超过 5MB',
      icon: 'error',
    });
    return;
  }

  console.log('✅ File validation passed, starting image search');
  // 文件验证通过，立即跳转到搜索页面并传递文件信息
  startImageSearchWithFile(file);
};

// 选择文件后立即跳转到搜索页面
const startImageSearchWithFile = (file) => {
  console.log('🚀 Starting image search with file:', file.name);

  // 将文件信息存储到sessionStorage，供搜索页面使用
  const fileInfo = {
    name: file.name,
    size: file.size,
    type: file.type,
    lastModified: file.lastModified,
  };

  // 创建文件的临时URL
  const fileUrl = URL.createObjectURL(file);
  console.log('💾 Storing file info and URL in sessionStorage');

  // 存储文件信息和临时URL
  sessionStorage.setItem('imageSearchFile', JSON.stringify(fileInfo));
  sessionStorage.setItem('imageSearchFileUrl', fileUrl);

  console.log('🔄 Navigating to search page...');
  // 立即跳转到搜索页面（在同一窗口中）
  router.push('/search?type=image&status=uploading');
};
</script>

<style lang="scss" scoped>
//搜索和分步
.step-search {
  width: 100%;
  min-height: 800px;
  // max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
  background-image: url('/images/bg.webp');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  text-align: center;
  color: #fff;
  position: relative;

  .step-search-title {
    padding-top: 150px;
    margin-bottom: 30px;
    font-size: 36px;
    font-weight: bold;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    letter-spacing: 1px;
  }

  .search-bar {
    position: relative;
    width: 100%;
    max-width: 1000px;
    height: 60px;
    margin: 0 auto 40px;
    display: flex;
    justify-content: center;
    border-radius: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

    .search-input-container {
      flex: 1;
      position: relative;
    }

    .website-icons {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      display: flex;
      align-items: center;
      gap: 12px;
      pointer-events: none;
      z-index: 1;

      .website-icon {
        font-size: 24px;
        color: #ccc;
        opacity: 0.6;
        transition: all 0.3s ease;

        &:hover {
          opacity: 0.8;
          transform: scale(1.1);
        }
      }
    }

    input[type='search'] {
      width: 100%;
      height: 60px;
      border-radius: 30px 0 0 30px;
      border: 3px solid rgba(255, 255, 255, 0.3);
      padding: 0 60px 0 30px;
      font-size: 18px;
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(15px);
      transition: all 0.3s ease;
      text-align: left;

      &::placeholder {
        // color: transparent; // 隐藏placeholder，让图标显示
        // font-weight: 400;
      }

      &:focus::placeholder {
        color: #999; // 聚焦时显示placeholder
      }

      &:focus {
        outline: none;
        border-color: rgba(255, 255, 255, 0.8);
        background: white;
        box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
        text-align: left; // 输入时左对齐
      }

      // 当有内容时左对齐
      &:not(:placeholder-shown) {
        text-align: left;
      }
    }

    .zhaoxiangji {
      font-size: 28px;
      position: absolute;
      right: 15px;
      top: 50%;
      transform: translateY(-50%);
      color: #666;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        color: #0073e6;
        transform: translateY(-50%) scale(1.1);
      }
    }

    .search-button-container {
      width: 80px;
      height: 60px;
    }

    .search-button {
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #0073e6, #005bb5);
      border-radius: 0 30px 30px 0;
      border: 3px solid rgba(255, 255, 255, 0.3);
      border-left: none;
      cursor: pointer;
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
      transition: all 0.3s ease;

      &:hover {
        background: linear-gradient(135deg, #005bb5, #004494);
        // transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 115, 230, 0.4);
      }

      &:active {
        transform: translateY(0);
      }
    }

    .sousuo {
      font-size: 24px;
      color: #ffffff;
      transition: transform 0.2s ease;
    }
  }

  .steps {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    max-width: 1000px;
    margin: 35px auto 0;
    padding-bottom: 20px;

    .steps-container {
      width: 100%;
    }

    // PC端横向布局
    .desktop-steps {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 20px;

      .step-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        flex: 1;
        max-width: 200px;

        .step-circle {
          width: 40px;
          height: 40px;
          background: #cfc9c3;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 12px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
          }

          .step-number {
            font-size: 20px;
            font-weight: bold;
            color: white;
          }
        }

        .step-label {
          font-size: 16px;
          color: white;
          text-align: center;
          font-weight: 500;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .step-line {
          position: absolute;
          top: 25px;
          left: calc(100% + 10px);
          width: 20px;
          height: 2px;
          background: rgba(255, 255, 255, 0.4);
          z-index: 1;
        }
      }
    }

    // 移动端垂直布局
    .mobile-steps {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: 0;
      max-width: 300px;
      margin: 0 auto;

      .step-item {
        display: flex;
        flex-direction: column;
        width: 100%;
        position: relative;

        .step-content {
          display: flex;
          align-items: center;
          gap: 15px;
          padding: 12px 0;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 12px;
          margin-bottom: 8px;
          padding-left: 20px;
          backdrop-filter: blur(10px);
          transition: all 0.3s ease;

          &:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateX(5px);
          }

          .step-circle {
            width: 40px;
            height: 40px;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            flex-shrink: 0;

            .step-number {
              font-size: 16px;
              font-weight: bold;
              color: #0073e6;
            }
          }

          .step-label {
            font-size: 15px;
            color: white;
            font-weight: 500;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
          }
        }

        .step-line-vertical {
          width: 2px;
          height: 15px;
          background: rgba(255, 255, 255, 0.3);
          margin-left: 40px;
          margin-bottom: 8px;
        }
      }
    }
  }
}

@media (max-width: 599px) {
  .step-search {
    // height: auto;
    min-height: 600px;
    padding: 0 12px 20px;
    background-size: cover;
    background-position: center top;

    .step-search-title {
      font-size: 28px;
      padding-top: 80px;
      margin-bottom: 25px;
      line-height: 1.3;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .search-bar {
      height: 48px;
      max-width: 100%;
      margin: 0 auto 30px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

      input[type='search'] {
        height: 48px;
        font-size: 16px;
        padding: 0 50px 0 20px;
        border-radius: 24px 0 0 24px;
        box-shadow: none;
        border: 2px solid rgba(255, 255, 255, 0.2);
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        text-align: center;

        &::placeholder {
          color: transparent; // 隐藏placeholder，让图标显示
          font-size: 15px;
        }

        &:focus::placeholder {
          color: #999; // 聚焦时显示placeholder
        }

        &:focus {
          outline: none;
          border-color: #0073e6;
          background: white;
          text-align: left; // 输入时左对齐
        }

        // 当有内容时左对齐
        &:not(:placeholder-shown) {
          text-align: left;
        }
      }

      .website-icons {
        .website-icon {
          font-size: 20px;
        }
      }

      .zhaoxiangji {
        font-size: 24px;
        right: 15px;
        color: #666;
        transition: color 0.3s ease;

        &:hover {
          color: #0073e6;
        }
      }

      .search-button-container {
        width: 60px;
        height: 48px;
        border-radius: 0 24px 24px 0;
        background: linear-gradient(135deg, #0073e6, #005bb5);
        box-shadow: 0 2px 8px rgba(0, 115, 230, 0.3);
        transition: all 0.3s ease;

        &:hover {
          background: linear-gradient(135deg, #005bb5, #004494);
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 115, 230, 0.4);
        }

        .sousuo {
          font-size: 22px;
        }
      }
    }

    .steps {
      margin-top: 30px;
      padding: 0 10px;

      .mobile-steps {
        max-width: 280px;

        .step-item .step-content {
          padding: 8px 15px;
          margin-bottom: 6px;

          .step-circle {
            width: 35px;
            height: 35px;

            .step-number {
              font-size: 14px;
            }
          }

          .step-label {
            font-size: 14px;
          }
        }

        .step-line-vertical {
          height: 12px;
          margin-left: 32px;
          margin-bottom: 6px;
        }
      }
    }
  }
}

@media (min-width: 600px) and (max-width: 1023px) {
  .step-search {
    height: auto;
    padding-bottom: 30px;

    .steps {
      .desktop-steps {
        gap: 15px;

        .step-item {
          max-width: 150px;

          .step-circle {
            width: 45px;
            height: 45px;

            .step-number {
              font-size: 18px;
            }
          }

          .step-label {
            font-size: 15px;
          }

          .step-line {
            width: 15px;
          }
        }
      }
    }
  }
}
</style>
