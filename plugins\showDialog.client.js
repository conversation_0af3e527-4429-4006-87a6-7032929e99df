import { defineNuxtPlugin } from '#app';
import { Dialog } from 'quasar';

export default defineNuxtPlugin((nuxtApp) => {
  // 提供 alertDialog 方法
  const alertDialog = ({ title = 'Alert', msg = 'This is a alert message', persistent = false, onOk, onCancel, onDismiss }) => {
    Dialog.create({
      title,
      message: msg,
      persistent,
    })
      .onOk(() => {
        if (onOk) onOk();
      })
      .onCancel(() => {
        if (onCancel) onCancel();
      })
      .onDismiss(() => {
        if (onDismiss) onDismiss();
      });
  };

  // 提供 confirmDialog 方法
  const confirmDialog = ({ title = 'Confirm', msg = 'This is a confirm message', cancel = true, persistent = true, onOk, onCancel, onDismiss }) => {
    Dialog.create({
      title,
      message: msg,
      cancel,
      persistent,
    })
      .onOk(() => {
        if (onOk) onOk();
      })
      .onCancel(() => {
        if (onCancel) onCancel();
      })
      .onDismiss(() => {
        if (onDismiss) onDismiss();
      });
  };

  // 将方法注入到 Nuxt 全局
  nuxtApp.provide('showDialogAlert', alertDialog);
  nuxtApp.provide('showDialogConfirm', confirmDialog);
});
