<template>
  <div class="card skeleton border-0">
    <div class="card-img-top bg-light bg-img"></div>
    <div class="card-body">
      <div class="bg-light mb-2 mt-0" style="width: 50%; height: 15px"></div>
      <div class="bg-light mb-2" style="width: 80%; height: 13px"></div>
      <div class="bg-light mb-2" style="width: 70%; height: 13px"></div>
      <div class="bg-light mb-2" style="width: 80%; height: 13px"></div>
      <div class="d-flex justify-content-start align-items-end">
        <div class="bg-light me-2" style="width: 30%; height: 15px"></div>
        <div class="bg-light" style="width: 30%; height: 13px"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
// This component will be used to display the skeleton while data is loading
</script>

<style scoped>
.skeleton > * {
  animation: pulse-bg 1.5s infinite;
}

@keyframes pulse-bg {
  0% {
    background-color: #f5f5f5;
  }
  50% {
    background-color: #f1efef;
  }
  100% {
    background-color: #f5f5f5;
  }
}
@media (max-width: 767px) {
  .bg-img {
    width: 100%;
    height: 164px;
  }
}

@media (min-width: 768px) {
  .bg-img {
    width: 100%;
    height: 238px;
  }
}
</style>
