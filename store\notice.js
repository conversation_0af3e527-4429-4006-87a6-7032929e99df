import { defineStore } from 'pinia';
import NoticeApi from '~/composables/noticeApi';

export const useNoticeStore = defineStore('notice', {
  state: () => ({
    list: [], //
  }),
  getters: {
    getList: (state) => state.list,
  },
  actions: {
    async fetchNoticeList() {
      console.log('fetchNotices');
      const { code, data } = await NoticeApi.getList({ type: 1, size: 5 });
      if (code === 0) {
        this.list = data;
      }
    },
  },
});
