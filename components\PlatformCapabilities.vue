<template>
  <div class="platform-capabilities">
    <div class="capabilities-container">
      <div class="capability-item" v-for="(item, index) in capabilities" :key="index">
        <div class="capability-icon">
          <q-icon :name="item.icon" color="orange" size="60px" />
        </div>
        <div class="capability-number">{{ item.number }}</div>
        <div class="capability-text">{{ item.text }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const capabilities = ref([
  {
    icon: 'shopping_bag',
    color: 'orange',
    number: '海量商品',
    text: '支持国内大部分商品代购',
  },
  {
    icon: 'language',
    color: 'orange',
    number: '100+',
    text: '服务超过100家国家和地区',
  },
  {
    icon: 'flight_takeoff',
    color: 'orange',
    number: '1000+物流线路',
    text: '多种物流可供自由选择',
  },
  // {
  //   icon: 'headset_mic',
  //   color: 'orange',
  //   number: '专业服务',
  //   text: '采购人员实时响应快速服务',
  // },
]);
</script>

<style lang="scss" scoped>
.platform-capabilities {
  width: 100%;
  padding: 10px 0;
  background: #fafafa;

  .capabilities-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;

    @media (max-width: 1024px) {
      grid-template-columns: repeat(2, 1fr);
      gap: 30px;
    }

    @media (max-width: 600px) {
      grid-template-columns: 1fr;
      gap: 20px;
    }
  }

  .capability-item {
    text-align: center;
    padding: 20px 20px;
    background: transparent;
    transition: all 0.3s ease;
    min-height: 180px;
    display: flex;
    flex-direction: column;
    justify-content: center;

    &:hover {
      transform: translateY(-5px);

      .capability-icon .q-icon {
        transform: scale(1.1);
      }
    }
  }

  .capability-icon {
    margin-bottom: 24px;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 60px;

    .q-icon {
      transition: all 0.3s ease;
      filter: drop-shadow(0 2px 8px rgba(137, 177, 242, 0.3));
    }
  }

  .capability-number {
    font-size: 28px;
    font-weight: 600;
    color: #8fa7f0;
    margin-bottom: 16px;
    letter-spacing: -0.3px;

    @media (max-width: 600px) {
      font-size: 24px;
    }
  }

  .capability-text {
    font-size: 14px;
    color: #888;
    font-weight: 400;
    line-height: 1.4;
    max-width: 300px;
    margin: 0 auto;

    @media (max-width: 600px) {
      font-size: 13px;
      max-width: 140px;
    }
  }
}

// 添加动画效果
.capability-item {
  animation: fadeInUp 0.6s ease forwards;
  opacity: 0;
  transform: translateY(30px);

  @for $i from 1 through 4 {
    &:nth-child(#{$i}) {
      animation-delay: #{($i - 1) * 0.1}s;
    }
  }
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 深色模式支持
.body--dark {
  .platform-capabilities {
    background: #1a1a1a;

    .capability-text {
      color: #ccc;
    }
  }
}
</style>
