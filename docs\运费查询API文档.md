# 运费查询前端集成指南

## 概述

本文档介绍如何在前端项目中集成运费查询功能，包括 API 调用、数据处理和 UI 展示建议。

## API 接口

### 基础信息

- **接口地址**: `POST /app-api/agent/shipping-calculation/quote`
- **请求方式**: POST
- **内容类型**: application/json
- **权限要求**: 无需特殊权限

### 请求参数

```typescript
interface ShippingQuoteRequest {
  countryCode: string; // 必填：国家编码
  weight: number; // 必填：重量(克)
  categoryIds?: number[]; // 可选：商品分类ID列表
  length?: number; // 可选：长度(cm)
  width?: number; // 可选：宽度(cm)
  height?: number; // 可选：高度(cm)
  postalCode?: string; // 可选：邮编
  stateProvince?: string; // 可选：州/省
  city?: string; // 可选：城市
}
```

### 响应数据

```typescript
interface ShippingQuoteResponse {
  id: string;
  name: string;
  iconUrl: string;
  features: string;
  transitTime: string;
  taxInclude: boolean;
  lineTips: string[];
  feeDetail: FeeDetail;
  restrictions: Restrictions;
  available: boolean;
  unavailableReason?: string;
  sort: number;
  minDeclareValue: number;
  maxDeclareValue: number;
  defaultDeclareType: string;
  declarePerKg: number;
  declareRatio: string;
  iossEnabled: boolean;
  freeInsure: boolean;
  addressMaxLength?: number;
  tariffRate: number;
  prepayTariff: boolean;
  logisticsTimeliness: LogisticsTimeliness;
}

interface FeeDetail {
  weight: number;
  length?: number;
  width?: number;
  height?: number;
  currency: string;
  volumeWeight: number;
  chargeableWeight: number;
  total: string;
  freight: string;
  customsFee: string;
  fuelFee: string;
  airSurcharge: string;
  operationFee: string;
  serviceFee: string;
  feeFirst: string;
  feeContinue: string;
  additionalFee?: string;
  weightFirst: number;
  weightContinue: number;
  needVolumeCal: boolean;
  volumeBase: number;
  discount?: string;
  originalTotal?: string;
  currentTotal: string;
}
```

## 前端实现示例

### 1. API 调用封装

```typescript
// api/shipping.ts
import { request } from '@/utils/request';

export interface ShippingQuoteParams {
  countryCode: string;
  weight: number;
  categoryIds?: number[];
  length?: number;
  width?: number;
  height?: number;
  postalCode?: string;
  stateProvince?: string;
  city?: string;
}

export function getShippingQuotes(params: ShippingQuoteParams) {
  return request({
    url: '/app-api/agent/shipping-calculation/quote',
    method: 'post',
    data: params,
  });
}
```

### 2. React 组件示例

```tsx
// components/ShippingCalculator.tsx
import React, { useState } from 'react';
import { getShippingQuotes } from '@/api/shipping';

const ShippingCalculator: React.FC = () => {
  const [formData, setFormData] = useState({
    countryCode: 'US',
    weight: 250,
    length: '',
    width: '',
    height: '',
  });

  const [quotes, setQuotes] = useState([]);
  const [loading, setLoading] = useState(false);

  const handleSubmit = async () => {
    setLoading(true);
    try {
      const response = await getShippingQuotes({
        countryCode: formData.countryCode,
        weight: Number(formData.weight),
        length: formData.length ? Number(formData.length) : undefined,
        width: formData.width ? Number(formData.width) : undefined,
        height: formData.height ? Number(formData.height) : undefined,
      });

      setQuotes(response.data || []);
    } catch (error) {
      console.error('查询运费失败:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="shipping-calculator">
      <div className="form-section">
        <h3>包裹信息</h3>

        <div className="form-group">
          <label>目的国家 *</label>
          <select value={formData.countryCode} onChange={(e) => setFormData({ ...formData, countryCode: e.target.value })}>
            <option value="US">美国 (United States)</option>
            <option value="GB">英国 (United Kingdom)</option>
            <option value="DE">德国 (Germany)</option>
            {/* 更多国家选项 */}
          </select>
        </div>

        <div className="form-group">
          <label>重量(g) *</label>
          <input type="number" value={formData.weight} onChange={(e) => setFormData({ ...formData, weight: e.target.value })} placeholder="250" />
        </div>

        <div className="dimensions-group">
          <label>尺寸(cm)</label>
          <div className="dimensions-inputs">
            <input type="number" value={formData.length} onChange={(e) => setFormData({ ...formData, length: e.target.value })} placeholder="长" />
            <input type="number" value={formData.width} onChange={(e) => setFormData({ ...formData, width: e.target.value })} placeholder="宽" />
            <input type="number" value={formData.height} onChange={(e) => setFormData({ ...formData, height: e.target.value })} placeholder="高" />
          </div>
        </div>

        <button onClick={handleSubmit} disabled={loading}>
          {loading ? '计算中...' : '计算'}
        </button>
      </div>

      <div className="results-section">
        <h3>运输方案 {quotes.length > 0 && `(${quotes.length}个方案)`}</h3>

        {quotes.map((quote) => (
          <div key={quote.id} className="quote-card">
            <div className="quote-header">
              <img src={quote.iconUrl} alt={quote.name} />
              <div className="quote-info">
                <h4>{quote.name}</h4>
                <p className="transit-time">时效: {quote.transitTime}天</p>
              </div>
              <div className="quote-price">
                <span className="total">${quote.feeDetail.total}</span>
                <span className="currency">{quote.feeDetail.currency}</span>
              </div>
            </div>

            <div className="quote-details">
              <div className="fee-breakdown">
                <div>运费: ${quote.feeDetail.freight}</div>
                <div>操作费: ${quote.feeDetail.operationFee}</div>
                <div>服务费: ${quote.feeDetail.serviceFee}</div>
              </div>

              <div className="features">
                <p>{quote.features}</p>
              </div>

              {quote.feeDetail.needVolumeCal && (
                <div className="volume-info">
                  <p>体积重: {quote.feeDetail.volumeWeight}g</p>
                  <p>计费重量: {quote.feeDetail.chargeableWeight}g</p>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ShippingCalculator;
```

### 3. Vue 组件示例

```vue
<!-- components/ShippingCalculator.vue -->
<template>
  <div class="shipping-calculator">
    <div class="form-section">
      <h3>包裹信息</h3>

      <el-form :model="formData" label-width="100px">
        <el-form-item label="目的国家" required>
          <el-select v-model="formData.countryCode" placeholder="请选择国家">
            <el-option label="美国" value="US" />
            <el-option label="英国" value="GB" />
            <el-option label="德国" value="DE" />
          </el-select>
        </el-form-item>

        <el-form-item label="重量(g)" required>
          <el-input-number v-model="formData.weight" :min="1" placeholder="250" />
        </el-form-item>

        <el-form-item label="尺寸(cm)">
          <div class="dimensions-inputs">
            <el-input-number v-model="formData.length" placeholder="长" />
            <el-input-number v-model="formData.width" placeholder="宽" />
            <el-input-number v-model="formData.height" placeholder="高" />
          </div>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSubmit" :loading="loading"> 计算运费 </el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="results-section">
      <h3>
        运输方案 <span v-if="quotes.length">({{ quotes.length }}个方案)</span>
      </h3>

      <div v-for="quote in quotes" :key="quote.id" class="quote-card">
        <div class="quote-header">
          <img :src="quote.iconUrl" :alt="quote.name" />
          <div class="quote-info">
            <h4>{{ quote.name }}</h4>
            <p>时效: {{ quote.transitTime }}天</p>
          </div>
          <div class="quote-price">
            <span class="total">${{ quote.feeDetail.total }}</span>
          </div>
        </div>

        <div class="quote-details">
          <el-descriptions :column="3" size="small">
            <el-descriptions-item label="运费">${{ quote.feeDetail.freight }}</el-descriptions-item>
            <el-descriptions-item label="操作费">${{ quote.feeDetail.operationFee }}</el-descriptions-item>
            <el-descriptions-item label="服务费">${{ quote.feeDetail.serviceFee }}</el-descriptions-item>
          </el-descriptions>

          <p class="features">{{ quote.features }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { getShippingQuotes } from '@/api/shipping';

const formData = reactive({
  countryCode: 'US',
  weight: 250,
  length: null,
  width: null,
  height: null,
});

const quotes = ref([]);
const loading = ref(false);

const handleSubmit = async () => {
  loading.value = true;
  try {
    const response = await getShippingQuotes({
      countryCode: formData.countryCode,
      weight: formData.weight,
      length: formData.length,
      width: formData.width,
      height: formData.height,
    });

    quotes.value = response.data || [];
  } catch (error) {
    console.error('查询运费失败:', error);
  } finally {
    loading.value = false;
  }
};
</script>
```

## 样式建议

```css
.shipping-calculator {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.form-section {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.dimensions-inputs {
  display: flex;
  gap: 10px;
}

.quote-card {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  transition: box-shadow 0.2s;
}

.quote-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.quote-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.quote-header img {
  width: 40px;
  height: 40px;
  object-fit: contain;
}

.quote-info {
  flex: 1;
}

.quote-price .total {
  font-size: 20px;
  font-weight: bold;
  color: #1890ff;
}

.fee-breakdown {
  display: flex;
  gap: 20px;
  margin-bottom: 8px;
  font-size: 14px;
  color: #666;
}

.features {
  font-size: 14px;
  color: #888;
  line-height: 1.4;
}
```

## 注意事项

1. **重量单位**: API 接受的重量单位是克(g)，前端需要做好单位转换
2. **尺寸可选**: 长宽高是可选参数，不填写则不进行体积重计算
3. **错误处理**: 建议添加网络错误和数据异常的处理
4. **加载状态**: 查询过程中显示加载状态提升用户体验
5. **数据缓存**: 可以考虑对相同参数的查询结果进行短时间缓存
6. **响应式设计**: 确保在移动端也有良好的显示效果

## 测试建议

1. 测试不同国家的查询结果
2. 测试有无尺寸参数的差异
3. 测试网络异常情况的处理
4. 测试大重量包裹的限制提示
5. 验证费用计算的准确性
