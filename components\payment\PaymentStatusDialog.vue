<template>
  <q-dialog v-model="dialogVisible" persistent transition-show="scale" transition-hide="scale">
    <q-card class="payment-dialog-card">
      <q-card-section class="payment-dialog-header">
        <div class="payment-processing-title">
          <q-spinner color="primary" size="32px" class="q-mr-md" />
          <span>{{ $t('payment.paymentDialog.processing') }}</span>
        </div>
      </q-card-section>
      <q-separator />
      <q-card-section class="payment-dialog-content">
        <p class="payment-dialog-message">
          <span class="dialog-amount" v-html="formatAmount(amount)"></span>
        </p>
        <p class="payment-dialog-message q-mt-md">
          {{ $t('payment.paymentDialog.message') }}
        </p>
      </q-card-section>
      <q-card-actions align="center" class="payment-dialog-actions">
        <q-btn :label="$t('payment.paymentDialog.simpleCompleted')" color="primary" @click="handlePaymentCompleted" class="payment-dialog-btn" unelevated icon="check_circle" />
        <q-btn :label="$t('payment.paymentDialog.simpleReselect')" color="negative" @click="handlePaymentCancelled" class="payment-dialog-btn" unelevated icon="refresh" />
      </q-card-actions>
      <q-separator />
      <q-card-section class="payment-dialog-footer">
        <q-btn :label="$t('payment.paymentDialog.simpleAbandon')" flat color="grey-7" class="payment-dialog-link-btn" icon="close" dense @click="handlePaymentCancelled" />
        <q-btn :label="$t('payment.paymentDialog.problem')" flat color="grey-7" class="payment-dialog-link-btn" icon="help_outline" dense @click="showHelpDialog" />
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref, defineEmits, defineProps, computed } from 'vue';
import { useQuasar } from 'quasar';
import { useI18n } from 'vue-i18n';

const props = defineProps({
  // 支付金额（分）
  amount: {
    type: Number,
    required: true,
  },
  // 支付渠道名称
  channelName: {
    type: String,
    default: '',
  },
  // 格式化金额的函数
  formatAmount: {
    type: Function,
    required: true,
  },
  // 帮助信息
  helpMessage: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['payment-completed', 'payment-cancelled', 'help-click']);

const $q = useQuasar();
const { t } = useI18n();
const dialogVisible = ref(false);

// 获取帮助信息，如果未提供则使用默认值
const getHelpMessage = computed(() => {
  return props.helpMessage || t('payment.paymentDialog.defaultHelpMessage');
});

// 显示支付状态弹窗
function showDialog() {
  dialogVisible.value = true;
}

// 关闭弹窗
function closeDialog() {
  dialogVisible.value = false;
}

// 处理支付完成
function handlePaymentCompleted() {
  dialogVisible.value = false;
  emit('payment-completed');
}

// 处理支付取消
function handlePaymentCancelled() {
  dialogVisible.value = false;
  emit('payment-cancelled');
}

// 显示帮助对话框
function showHelpDialog() {
  $q.dialog({
    title: t('payment.paymentDialog.helpTitle'),
    message: getHelpMessage.value,
    ok: t('common.close'),
    class: 'payment-help-dialog',
  });
  emit('help-click');
}

// 暴露方法给父组件
defineExpose({
  showDialog,
  closeDialog,
});
</script>

<style lang="scss" scoped>
// 支付对话框样式
.payment-dialog-card {
  width: 400px;
  max-width: 90vw;
  border-radius: 8px; // 减小卡片圆角
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
  overflow: hidden;

  @media (max-width: 599px) {
    width: 90vw;
  }
}

.payment-dialog-header {
  padding: 20px;
  background-color: #f5f7fa;

  @media (max-width: 599px) {
    padding: 16px;
  }
}

.payment-processing-title {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: 500;
  color: #1976d2;

  @media (max-width: 599px) {
    font-size: 18px;
  }
}

.payment-dialog-content {
  padding: 24px 20px;

  @media (max-width: 599px) {
    padding: 16px;
  }
}

.payment-dialog-message {
  text-align: center;
  margin: 0;
  color: #666;
  font-size: 15px;
  line-height: 1.5;
}

.payment-dialog-actions {
  padding: 0 20px 20px;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 16px;

  @media (max-width: 599px) {
    padding: 0 16px 16px;
    gap: 12px;
  }
}

.payment-dialog-btn {
  min-width: 160px;
  height: 44px;
  font-size: 15px;
  font-weight: 500;
  border-radius: 4px; // 减小按钮圆角

  @media (max-width: 599px) {
    min-width: 140px;
    height: 40px;
    font-size: 14px;
  }
}

.payment-dialog-footer {
  padding: 12px 20px;
  display: flex;
  justify-content: center;
  gap: 24px;
  background-color: #f5f7fa;

  @media (max-width: 599px) {
    padding: 10px 16px;
    gap: 16px;
  }
}

.payment-dialog-link-btn {
  font-size: 13px;
  min-height: 32px;
  border-radius: 4px; // 减小按钮圆角

  @media (max-width: 599px) {
    font-size: 12px;
  }
}

.dialog-amount {
  display: inline-block;
  font-weight: 600;
  color: #1976d2;
  background-color: #f0f7ff;
  padding: 4px 10px;
  border-radius: 4px; // 减小金额显示的圆角
  margin: 0 4px;
  border: 1px solid rgba(25, 118, 210, 0.2);
}
</style>

<style>
/* 帮助弹窗样式 */
.payment-help-dialog .q-card {
  max-width: 400px;
  border-radius: 8px;
}
</style>
