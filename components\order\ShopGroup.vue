<template>
  <div class="q-mb-md">
    <!-- 店铺栏 -->
    <div v-if="shop.items.length > 0" class="row items-center q-mt-md bg-grey-1 shop-header rounded-borders">
      <div class="col-8 col-sm-10 text-bold text-subtitle1 q-py-sm">
        <q-icon name="store" color="primary" class="q-ml-lg q-mr-sm" size="20px" />
        <span class="text-primary">{{ shop.shopName }}</span>
      </div>
      <div v-if="shop.freight" class="col-4 col-sm-2 text-center q-pr-md">
        <span class="text-grey-8">运费：</span>
        <span class="text-red text-weight-medium primary-currency" v-html="shop.freight ? formatAmount(shop.freight, { allowWrap: false }) : ''"></span>
      </div>
    </div>

    <!-- 商品列表 -->
    <div v-for="(item, itemIndex) in shop.items" :key="item.id" class="row items-center q-py-sm product-row product-item">
      <!-- 商品名称与图片 - PC视图中图片在左，名称在右 -->
      <div class="col-12 col-sm-4 row justify-start q-px-md q-py-sm">
        <!-- 移动端视图 - 卡片样式 -->
        <div class="product-layout-mobile">
          <q-card flat bordered class="product-card-mobile">
            <q-card-section class="q-pa-md">
              <div class="row no-wrap items-start">
                <!-- 商品图片 -->
                <div class="product-image">
                  <NuxtLink :to="`/product/${item.spu.id}`">
                    <q-img :src="getThumbnailUrl(item.sku.picUrl || item.spu.picUrl, '80x80')" style="width: 80px; height: 80px" class="rounded-borders product-img" />
                  </NuxtLink>
                </div>

                <!-- 商品信息 -->
                <div class="column justify-around product-info-col q-ml-sm">
                  <div class="multiline-ellipsis">
                    <NuxtLink :to="`/product/${item.spu.id}`" class="product-name">
                      {{ item.spu.name }}
                    </NuxtLink>
                    <q-tooltip v-if="item.spu.name.length > 100">
                      {{ item.spu.name }}
                    </q-tooltip>
                  </div>
                  <div class="q-mt-sm text-grey-7 product-props">{{ formattedProperties(item.sku.properties) }}</div>
                </div>
              </div>

              <!-- 价格信息行 - 标签和数据在同一行 -->
              <div class="row justify-between items-center q-mt-md price-row">
                <div class="col-4">
                  <div class="row no-wrap items-center">
                    <span class="price-label text-grey-7 q-mr-xs">单价:</span>
                    <span class="price-value">¥{{ ((item.price || item.sku.price) / 100).toFixed(2) }}</span>
                  </div>
                </div>
                <div class="col-3 text-center">
                  <div class="row no-wrap items-center justify-center">
                    <span class="price-label text-grey-7 q-mr-xs">数量:</span>
                    <span class="price-value">{{ item.count }}</span>
                  </div>
                </div>
                <div class="col-5 text-center">
                  <div class="row no-wrap items-center justify-center">
                    <span class="price-label text-grey-7 q-mr-xs">金额:</span>
                    <span class="price-value text-red mobile-amount primary-currency" v-html="formatAmount((item.price || item.sku.price) * item.count, { allowWrap: false })"></span>
                  </div>
                </div>
              </div>

              <!-- 服务选择区域 -->
              <div class="row items-center q-mt-md service-row">
                <ServiceSelector v-model="item.selectedServices" :free-services="freeServices" :charge-services="chargeServices" @update:model-value="onServiceChange(item.id, $event)" />
              </div>

              <!-- 备注区域 -->
              <div class="row items-center q-mt-md memo-row" v-if="item.memo">
                <div class="col-12">
                  <div class="row no-wrap items-center">
                    <div class="memo-label text-grey-7 q-mr-sm">备注:</div>
                    <div class="memo-content text-grey-7 multiline-ellipsis-2">{{ item.memo }}</div>
                  </div>
                </div>
              </div>
            </q-card-section>
          </q-card>
        </div>

        <!-- PC端视图改为图片在左，名称在右 -->
        <div class="product-layout-desktop">
          <div class="row no-wrap">
            <div class="product-image-desktop">
              <NuxtLink :to="`/product/${item.spu.id}`">
                <q-img :src="getThumbnailUrl(item.sku.picUrl || item.spu.picUrl, '80x80')" style="width: 80px; height: 80px" class="rounded-borders product-img" />
              </NuxtLink>
            </div>

            <div class="column justify-around product-info-desktop q-ml-md">
              <div class="multiline-ellipsis">
                <NuxtLink :to="`/product/${item.spu.id}`" class="product-name">
                  {{ item.spu.name }}
                </NuxtLink>
                <q-tooltip v-if="item.spu.name.length > 100">
                  {{ item.spu.name }}
                </q-tooltip>
              </div>
              <div class="q-mt-sm text-grey-7 product-props">{{ formattedProperties(item.sku.properties) }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 备注 - 在移动设备上隐藏 -->
      <div class="col-12 col-sm-2 text-center text-grey-7 multiline-ellipsis-3 hide-on-mobile" :hint="item.memo">{{ item.memo }}</div>

      <!-- 服务 - 仅在PC视图中显示 -->
      <div class="col-4 col-sm-2 text-center hide-on-mobile">
        <div class="service-selector">
          <ServiceSelector v-model="item.selectedServices" :free-services="freeServices" :charge-services="chargeServices" @update:model-value="onServiceChange(item.id, $event)" />
        </div>
      </div>

      <!-- 移动端视图中的空白占位，保持布局一致 -->
      <div class="col-4 mobile-only-block"></div>

      <!-- 单价 - 仅PC视图显示 -->
      <div class="col-sm-1 text-center text-weight-medium nowrap hide-on-mobile">¥{{ ((item.price || item.sku.price) / 100).toFixed(2) }}</div>

      <!-- 数量修改 - 仅PC视图显示 -->
      <div class="col-sm-1 text-center hide-on-mobile">
        <span class="text-weight-medium">{{ item.count }}</span>
      </div>

      <!-- 金额 - 仅PC视图显示，增加宽度 -->
      <div class="col-sm-2 text-center text-red text-weight-medium hide-on-mobile amount-column">
        <span class="primary-currency" v-html="formatAmount((item.price || item.sku.price) * item.count, { allowWrap: false })"></span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue';
import ServiceSelector from '~/components/service/ServiceSelector.vue';
import { useCurrency } from '~/composables/useCurrency';

const props = defineProps({
  shop: {
    type: Object,
    required: true,
  },
  freeServices: {
    type: Array,
    default: () => [],
  },
  chargeServices: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(['update:services']);
const { formatAmount } = useCurrency();

// 处理服务变更
const onServiceChange = (itemId, services) => {
  emit('update:services', { itemId, services });
};
</script>

<style lang="scss" scoped>
// 店铺标题样式
.shop-header {
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

// 商品项样式
.product-item {
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s;
  background-color: #ffffff;

  &:hover {
    background-color: #f9f9f9;
  }

  .product-img {
    border: 1px solid #f0f0f0;
    transition: transform 0.2s;

    &:hover {
      transform: scale(1.05);
    }
  }

  .product-name {
    color: #333;
    font-weight: 500;
    text-decoration: none;

    &:hover {
      color: var(--q-primary);
      text-decoration: underline;
    }
  }

  .product-props {
    font-size: 12px;
  }
}

// 商品布局样式 - 响应式
.product-layout-mobile {
  display: flex;
  width: 100%;

  @media (min-width: 600px) {
    display: none;
  }
}

.product-layout-desktop {
  display: none;
  width: 100%;

  @media (min-width: 600px) {
    display: block;
  }

  .product-info-desktop {
    flex: 1;
    min-width: 0; // 防止flex子项溢出
  }
}

// 移动端卡片样式
.product-card-mobile {
  width: 100%;

  .product-name {
    font-size: 13px;
    line-height: 1.4;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.87);
  }

  .product-props {
    font-size: 12px;
    line-height: 1.3;
  }

  .price-row {
    border-top: 1px dashed #e0e0e0;
    padding-top: 8px;

    .row.no-wrap {
      min-height: 24px;
    }

    .price-label {
      font-size: 12px;
      line-height: 1.2;
      font-weight: 500;
      white-space: nowrap;
    }

    .price-value {
      font-size: 13px;
      line-height: 1.4;
      font-weight: 500;
      white-space: nowrap;
    }
  }

  .service-row {
    border-top: 1px dashed #e0e0e0;
    padding-top: 8px;
  }

  .memo-row {
    border-top: 1px dashed #e0e0e0;
    padding-top: 8px;
  }

  .memo-label {
    font-size: 12px;
    line-height: 1.2;
    font-weight: 500;
  }

  .memo-content {
    font-size: 12px;
    line-height: 1.4;
  }
}

// 服务选择相关样式
.service-selector {
  display: flex;
  flex-direction: column;
  align-items: center;
}

// 防止文本换行
.nowrap {
  white-space: nowrap;
}

// 多行文本省略
.multiline-ellipsis,
.multiline-ellipsis-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.multiline-ellipsis-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

// 移动端专用样式
.mobile-only-block {
  display: none !important;

  @media (max-width: 599px) {
    display: block !important;
  }
}

// PC端专用样式
.hide-on-mobile {
  @media (max-width: 599px) {
    display: none !important;
  }
}

// 金额列样式，增加宽度防止换行
.amount-column {
  min-width: 140px; // 增加最小宽度，确保双币种显示不会换行
  display: block; // 使用块级元素撑满整个列
  text-align: center; // 文本居中
  overflow: hidden;
  text-overflow: ellipsis;

  .primary-currency {
    white-space: nowrap;
    display: inline-block;
    width: 100%; // 确保撑满整个父元素的宽度
    text-align: center; // 文本居中

    :deep(.default-currency) {
      font-size: 0.8em;
      color: #666;
      opacity: 0.85;
      font-weight: normal;
      display: inline-block;
      margin-left: 4px;
      white-space: normal;
    }
  }
}

// 移动端金额样式
.mobile-amount {
  min-width: 100px; // 增加移动端金额最小宽度
  display: inline-block;
  width: 100%; // 确保撑满整个列的空间
  text-align: center; // 文本居中
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  &.primary-currency {
    white-space: nowrap;
    width: 100%; // 确保撑满整个父元素的宽度
    text-align: center; // 文本居中

    :deep(.default-currency) {
      font-size: 0.75em;
      color: #666;
      opacity: 0.85;
      font-weight: normal;
      display: inline-block;
      margin-left: 2px;
      white-space: normal;
    }
  }
}
</style>
