<template>
  <div class="hover-search-bar hover-search" :class="{ show: topSearchShow }">
    <div class="search-main">
      <h3 class="gt-xs">商品搜索</h3>
      <div class="search-input-container">
        <input type="search" v-model="searchText" placeholder="搜索商品或链接" />
        <i class="iconfont icon-zhaoxiangji zhaoxiangji gt-xs"></i>
      </div>
      <div class="search-button" @click="onSubmit">
        <i class="iconfont icon-sousuo sousuo"></i>
      </div>
    </div>
  </div>
</template>
<script setup>
import { analyzeSearchInput, buildSearchUrl } from '~/utils/searchUtils';

const topSearchShow = ref(false); // 滚动后顶部搜索栏展示
const searchText = ref(''); // 搜索文本

onMounted(() => {
  window.addEventListener('scroll', handleScroll);
});
// 组件卸载时清除定时器
onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll);
});

const handleScroll = () => {
  const top = document.documentElement.scrollTop || document.body.scrollTop;
  topSearchShow.value = top > 300;
};

const onSubmit = () => {
  if (searchText.value.trim()) {
    // 分析搜索输入类型并构建相应的URL
    const analysis = analyzeSearchInput(searchText.value.trim());
    const searchUrl = buildSearchUrl(analysis);
    navigateTo(searchUrl);
  } else {
    navigateTo('/search');
  }
};
</script>
<style lang="scss" scoped>
.hover-search-bar {
  width: 100%;
  height: 60px;

  .search-main {
    width: 100%;
    max-width: 800px;
    height: 40px;
    margin: 10px auto;
    position: relative;
    display: flex;
    align-items: center;
    padding: 0 16px;

    h3 {
      line-height: 40px;
      font-size: 16px;
      color: rgb(86, 84, 86);
      margin-right: 20px;
      white-space: nowrap;
    }

    .search-input-container {
      flex: 1;
      position: relative;
      display: flex;
      align-items: center;
    }

    input[type='search'] {
      width: 100%;
      height: 40px;
      line-height: 40px;
      border-radius: 20px 0 0 20px;
      border: 2px solid #0073e6;
      padding: 0 20px;
      font-size: 15px;
      border-right: none;
    }

    .zhaoxiangji {
      font-size: 24px;
      position: absolute;
      right: 10px;
      cursor: pointer;
      color: #707070;
    }

    .search-button {
      width: 60px;
      height: 40px;
      min-width: 60px;
      background-color: #0073e6;
      border-radius: 0 20px 20px 0;
      border: none;
      cursor: pointer;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .sousuo {
      font-size: 24px;
      color: #ffffff;
    }
  }
}
.hover-search {
  width: 100%;
  height: 60px;
  transform: translateY(-200px);
  background-color: #fff;
  position: fixed;
  top: 0;
  z-index: 9999;
  box-shadow: 0 0 10px 2px rgb(90 90 90 / 60%);
  transition: 0.35s;
}
.show {
  transform: translateY(0);
  -webkit-transform: translateZ(0);
  -moz-transform: translateZ(0);
  -ms-transform: translateZ(0);
  -o-transform: translateZ(0);
  transform: translateZ(0);
  top: 0;
}

@media (max-width: 599px) {
  .hover-search-bar {
    height: 50px;

    .search-main {
      height: 36px;
      margin: 7px auto;
      padding: 0 8px;

      input[type='search'] {
        height: 36px;
        line-height: 36px;
        font-size: 14px;
        padding: 0 10px;
      }

      .search-button {
        width: 50px;
        min-width: 50px;
        height: 36px;
      }

      .sousuo {
        font-size: 20px;
      }
    }
  }

  .hover-search {
    height: 50px;
  }
}
</style>
