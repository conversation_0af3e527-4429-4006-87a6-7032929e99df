<template>
  <q-dialog v-model="showToolsDialog" position="bottom" @hide="handleClose">
    <q-card class="tools-popup-card">
      <slot></slot>
      <div class="content">
        <!-- 只保留表情功能 -->
        <q-carousel v-model="emojiSlide" animated swipeable arrows control-color="primary" navigation padding height="280px" class="emoji-carousel">
          <q-carousel-slide v-for="(emojiGroup, index) in emojiGroups" :key="index" :name="index" class="emoji-slide">
            <div class="emoji-grid">
              <q-btn v-for="item in emojiGroup" :key="item.name" flat dense no-caps class="emoji-btn" @click="onEmoji(item)">
                <img :src="`/images/emoji/${item.file}`" class="emoji-img" />
              </q-btn>
            </div>
          </q-carousel-slide>
        </q-carousel>
      </div>
    </q-card>
  </q-dialog>
</template>

<script setup>
/**
 * 聊天工具
 */
import { ref, computed, watch } from 'vue';
import { emojiList } from '@/pages/chat/util/emoji';

const props = defineProps({
  // 工具模式
  toolsMode: {
    type: String,
    default: '',
  },
  // 控制工具菜单弹出
  showTools: {
    type: Boolean,
    default: false,
  },
});

const emits = defineEmits(['onEmoji', 'close']);

// 表情轮播当前页
const emojiSlide = ref(0);

// 将表情列表分组，每组20个
const emojiGroups = computed(() => {
  const groups = [];
  const groupSize = 20;

  for (let i = 0; i < emojiList.length; i += groupSize) {
    groups.push(emojiList.slice(i, i + groupSize));
  }

  return groups;
});

// 控制对话框显示
const showToolsDialog = computed({
  get: () => props.showTools,
  set: (val) => {
    if (!val) handleClose();
  },
});

// 监听showTools变化
watch(
  () => props.showTools,
  (newVal) => {
    showToolsDialog.value = newVal;
  }
);

// 关闭弹出工具菜单
function handleClose() {
  emits('close');
}

// 选择表情
function onEmoji(emoji) {
  emits('onEmoji', emoji);
}

// 图片上传和商品订单功能已完全移除
</script>

<style scoped lang="scss">
.tools-popup-card {
  width: 100%;
  max-width: 100%;
  border-radius: 16px 16px 0 0;

  .content {
    width: 100%;
    border-top: 1px solid #dfdfdf;
    padding: 10px 0 0;

    .emoji-carousel {
      width: 100%;

      .emoji-slide {
        padding: 10px;
      }

      .emoji-grid {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-around;

        .emoji-btn {
          margin: 5px;
          min-width: 40px;
          min-height: 40px;

          .emoji-img {
            width: 24px;
            height: 24px;
          }
        }
      }
    }

    .tools-grid {
      display: flex;
      justify-content: space-around;
      padding: 20px 0;

      .tool-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 33.3%;

        .hidden-input {
          ::v-deep(.q-field__native) {
            display: none;
          }
        }

        .tool-label {
          margin-top: 8px;
          font-size: 14px;
          color: #666;
        }
      }
    }

    .empty-tools-message {
      padding: 20px;
      text-align: center;
      color: #666;
      font-size: 14px;
      background-color: #f5f5f5;
      border-radius: 8px;
      margin: 15px;
    }
  }
}
</style>
