<template>
  <div class="payment-channel-selector">
    <div class="section-title q-mb-md">{{ $t('payment.selector.title') }}</div>

    <div class="q-gutter-y-sm">
      <div v-for="channel in availableChannels" :key="channel.id" class="col-12">
        <q-card flat bordered class="payment-channel-card cursor-pointer q-pa-md" :class="{ 'selected-channel': modelValue === channel.code }" @click="selectChannel(channel.code)">
          <div class="row items-center payment-channel-row">
            <div class="col-auto q-mr-sm radio-container">
              <q-radio v-model="selectedChannel" :val="channel.code" color="primary" :size="$q.screen.lt.sm ? 'sm' : 'md'" class="custom-radio" />
            </div>

            <div class="col payment-content">
              <!-- PC端布局 -->
              <template v-if="!$q.screen.lt.sm">
                <div class="row items-start">
                  <div class="col-auto channel-logo-container q-mr-md">
                    <q-img :src="channel.icon" :ratio="16 / 9" class="channel-logo" />
                  </div>
                  <div class="col">
                    <div class="channel-title q-mb-xs">{{ channel.title }}</div>
                  </div>
                </div>
                <div class="channel-description-text q-mt-xs channel-description">{{ channel.description }}</div>
                <div class="channel-description-text q-mt-xs channel-description" v-if="channel.additionalInfo">{{ channel.additionalInfo }}</div>
              </template>

              <!-- 移动端布局 -->
              <template v-else>
                <div class="row items-center justify-between full-width">
                  <div class="row items-center">
                    <div class="col-auto mobile-channel-logo-container q-mr-sm">
                      <q-img :src="channel.icon" :ratio="16 / 9" class="mobile-channel-logo" />
                    </div>
                    <div class="col" :class="{ 'pr-0': modelValue !== channel.code }">
                      <div class="channel-title">{{ channel.title }}</div>
                    </div>
                  </div>
                  <!-- 手续费显示在标题行右侧，仅在选中时显示 -->
                  <transition name="fade">
                    <div v-if="modelValue === channel.code" class="col-auto mobile-fee-display text-right active-fee">
                      <span class="fee-label">{{ $t('payment.selector.fee') }}</span>
                      <span class="fee-text text-weight-medium" v-html="formatAmount(calculateFeeAmountInCents(channel), { allowWrap: false })"></span>
                    </div>
                  </transition>
                </div>
                <div class="text-caption text-grey-7 q-mt-xs mobile-description q-pl-md">{{ channel.description }}</div>
                <div class="text-caption text-grey-7 q-mt-xs mobile-description q-pl-md" v-if="channel.additionalInfo">{{ channel.additionalInfo }}</div>
              </template>
            </div>
          </div>
          <!-- PC端手续费显示 -->
          <div class="row payment-fee-row justify-end" v-if="modelValue === channel.code && !$q.screen.lt.sm">
            <div class="fee-text">
              <span>{{ $t('payment.selector.fee') }} </span>
              <span class="text-weight-medium currency-text" v-html="formatAmount(calculateFeeAmountInCents(channel), { allowWrap: false })"></span>
              <q-tooltip>{{ $t('payment.selector.feeTooltip') }}</q-tooltip>
            </div>
          </div>
        </q-card>
      </div>
    </div>

    <div class="q-mt-md payment-summary">
      <q-card flat bordered class="payment-summary-card q-pa-md">
        <div class="row justify-between q-mb-sm">
          <div class="summary-label">{{ $t('payment.selector.orderAmount') }}</div>
          <div class="summary-value currency-text">
            <span class="amount-column" v-html="formatAmount(props.orderPrice, { allowWrap: false })"></span>
          </div>
        </div>
        <div class="row justify-between q-mb-sm" v-if="selectedChannelObj">
          <div class="summary-label">{{ $t('payment.selector.fee') }}</div>
          <div class="summary-value">
            <span class="amount-column currency-text" v-html="formatAmount(calculateFeeAmountInCents(selectedChannelObj), { allowWrap: false })"></span>
          </div>
        </div>
        <q-separator class="q-my-sm" />
        <div class="row justify-between">
          <div class="summary-total-label">{{ $t('payment.selector.actualPayment') }}</div>
          <div class="summary-total-value currency-text">
            <span class="amount-column" v-html="formatAmount(calculateActualAmountInCents(selectedChannelObj), { allowWrap: false })"></span
            ><span v-if="isRMB && selectedChannelObj" class="usd-amount">( ${{ formatUsdAmount(calculateActualAmountInCents(selectedChannelObj)) }})</span>
          </div>
        </div>
      </q-card>
    </div>
  </div>
</template>

<script setup>
/**
 * 支付渠道选择器组件
 *
 * 手续费计算公式说明：
 * 1. 实际付款金额 = (商品金额 + 固定手续费) / (1 - 费率)
 * 2. 手续费金额 = 实际付款金额 - 商品金额
 *
 * 例如：
 * - 商品金额：500元人民币 (约76.10美元，汇率1:6.5705)
 * - 费率：2.9%
 * - 固定手续费：0.3美元 (约1.97元人民币)
 *
 * 计算过程：
 * 1. 实际付款金额 = (76.10 + 0.3) / (1 - 0.029) = 76.40 / 0.971 = 78.68美元
 * 2. 手续费金额 = 78.68 - 76.10 = 2.58美元
 *
 * 这种计算方式符合支付行业标准，正确反映了手续费是基于最终交易金额的百分比这一事实。
 */
import { ref, watch, computed, onMounted, nextTick } from 'vue';
import { useQuasar } from 'quasar';
import { useCurrencyStore } from '~/store/currency';
import { useCurrency } from '~/composables/useCurrency';
import { useUserStore } from '~/store/user';
import { useI18n } from 'vue-i18n';

const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: null,
  },
  orderPrice: {
    type: Number,
    default: 0,
    description: '订单原始金额，单位：分，人民币CNY',
  },
  enableChannels: {
    type: Array,
    default: () => [],
    description: '可用的支付渠道列表',
  },
});

const $q = useQuasar();
const currencyStore = useCurrencyStore();
const { formatAmount } = useCurrency();
const { t } = useI18n();
const userWallet = useUserStore().userWallet;

// 用户钱包余额会在availableChannels计算属性中动态格式化

const payMethods = [
  {
    icon: '/images/payment/paypal.png',
    color: 'blue',
    title: t('payment.selector.paymentMethods.paypal.title'),
    value: 'paypal',
    disabled: true,
    description: t('payment.selector.paymentMethods.paypal.description'),
  },
  {
    icon: '/images/payment/stripe.png',
    color: 'blue',
    title: t('payment.selector.paymentMethods.stripe.title'),
    value: 'stripe',
    disabled: true,
    description: t('payment.selector.paymentMethods.stripe.description'),
  },
  {
    icon: 'wechat',
    color: 'green',
    title: t('payment.selector.paymentMethods.wechat.title'),
    value: 'wechat',
    disabled: true,
    description: t('payment.selector.paymentMethods.wechat.description'),
  },
  {
    icon: 'payment',
    color: 'blue',
    title: t('payment.selector.paymentMethods.alipay.title'),
    value: 'alipay',
    disabled: true,
    description: t('payment.selector.paymentMethods.alipay.description'),
  },
  {
    icon: '/images/payment/wallet.png',
    color: 'yellow',
    title: t('payment.selector.paymentMethods.wallet.title'),
    value: 'wallet',
    disabled: true,
    description: '', // 将在availableChannels计算属性中动态设置
  },
  {
    icon: '/images/payment/mock.png',
    color: 'yellow',
    title: '模拟支付',
    value: 'mock',
    disabled: true,
    description: 'Just For Test', // 将在availableChannels计算属性中动态设置
  },
];

const emit = defineEmits(['update:modelValue']);

// 内部选中的支付渠道
const selectedChannel = ref(props.modelValue);

// 渠道代码与支付方式的映射关系
const channelCodeMap = {
  paypal_pc: 0, // PayPal支付
  stripe_pc: 1, // Stripe支付
  wx_pc: 2, // 微信支付
  alipay_pc: 3, // 支付宝支付
  wallet: 4, // 余额支付
  mock: 5, // 模拟支付
};

// 可用的支付渠道
const availableChannels = computed(() => {
  // 初始化所有支付方式为禁用状态
  payMethods.forEach((method) => {
    method.disabled = true;
    method.sort = 999; // 默认排序值
    // 清除之前可能设置的费率信息
    delete method.feeRate;
    delete method.fixedFee;
    delete method.feeAmount;
  });

  // 处理所有可用的支付渠道
  if (props.enableChannels && props.enableChannels.length > 0) {
    props.enableChannels.forEach((channel) => {
      const methodIndex = channelCodeMap[channel.code];
      if (methodIndex !== undefined) {
        const method = payMethods[methodIndex];
        method.code = channel.code;
        method.disabled = false;
        method.id = channel.id;

        // 确保费率信息被正确转换为数字类型
        method.feeRate = channel.feeRate !== undefined && channel.feeRate !== null ? parseFloat(channel.feeRate) : undefined;
        method.fixedFee = channel.fixedFee !== undefined && channel.fixedFee !== null ? parseFloat(channel.fixedFee) : undefined;
        method.feeAmount = channel.feeAmount !== undefined && channel.feeAmount !== null ? parseFloat(channel.feeAmount) : undefined;
        method.sort = channel.sort || 999; // 如果没有排序值，默认为999

        // 为余额支付方式动态设置描述，包含当前币别下的余额
        if (channel.code === 'wallet') {
          const formattedBalance = formatAmount(userWallet.balance, { useHtml: false });
          method.description = t('payment.selector.paymentMethods.wallet.description') + formattedBalance;
        }

        // 检查描述中是否已经包含手续费信息
        const feeDescText = t('payment.selector.feeDescription');
        const feeText = t('payment.selector.fee').replace(':', '');
        const hasExistingFeeDesc = method.description && (method.description.includes(feeDescText) || method.description.includes(feeText + ':') || method.description.includes(feeText + '：'));

        if (!hasExistingFeeDesc) {
          // 生成手续费描述
          const feeDesc = generateFeeDescription(method);

          // 将手续费信息附加到原始描述后面
          if (feeDesc) {
            method.description = method.description ? `${method.description}, ${feeDesc}` : feeDesc;
          }
        }
      }
    });
  }

  // 根据sort字段排序，并过滤掉禁用的支付方式
  return [...payMethods].filter((method) => !method.disabled).sort((a, b) => (a.sort || 999) - (b.sort || 999));
});

// 选中的渠道对象
const selectedChannelObj = computed(() => {
  if (!selectedChannel.value) {
    return null;
  }
  return availableChannels.value.find((channel) => channel.code === selectedChannel.value);
});

// 判断当前选择的币种是否是人民币
const isRMB = computed(() => {
  return currencyStore.selectedCurrency.currency === 'CNY';
});

// 监听外部传入的值变化
watch(
  () => props.modelValue,
  (newValue) => {
    selectedChannel.value = newValue;
  }
);

// 监听内部选中值变化
watch(
  () => selectedChannel.value,
  (newValue) => {
    emit('update:modelValue', newValue);
  }
);

// 监听渠道列表变化
watch(
  () => props.enableChannels,
  (newChannels) => {
    if (newChannels && newChannels.length > 0 && !selectedChannel.value) {
      // 如果有可用渠道且当前未选择，则自动选择第一个
      selectedChannel.value = newChannels[0].code;
    }
  },
  { immediate: true }
);

// 监听币别变化，更新余额显示
watch(
  () => currencyStore.selectedCurrency,
  () => {
    // 强制重新计算availableChannels，以更新余额显示
    const currentChannel = selectedChannel.value;
    selectedChannel.value = null;
    nextTick(() => {
      selectedChannel.value = currentChannel;
    });
  }
);

// 选择支付渠道
function selectChannel(value) {
  selectedChannel.value = value;
}

// 组件挂载时获取货币列表
onMounted(async () => {
  // 强制刷新货币列表，确保有最新数据
  await currencyStore.fetchCurrencyList();
});

// 计算支付相关金额（分）- 基于原始金额(CNY)
function calculatePaymentAmount(channel, type = 'actual') {
  // 如果没有渠道，返回默认值
  if (!channel) {
    return type === 'fee' ? 0 : props.orderPrice;
  }

  // 检查费率和固定费用是否存在且有效
  const hasFeeRate = channel.feeRate !== undefined && channel.feeRate !== null && parseFloat(channel.feeRate) > 0;
  const hasFixedFee = channel.fixedFee !== undefined && channel.fixedFee !== null && parseFloat(channel.fixedFee) > 0;
  const hasFeeAmount = channel.feeAmount !== undefined && channel.feeAmount !== null && parseFloat(channel.feeAmount) > 0;

  // 如果没有有效的费率和固定费用，返回默认值
  if (!hasFeeRate && !hasFixedFee && !hasFeeAmount) {
    return type === 'fee' ? 0 : props.orderPrice;
  }

  // 获取原始金额（人民币分）
  const baseAmountInCNYCents = props.orderPrice;

  // 获取美元汇率
  const cnyToUsdRate = getCnyToUsdRate();

  // 将人民币金额转换为美元（美分）
  const baseAmountInUSDCents = Math.round((baseAmountInCNYCents / 100) * cnyToUsdRate * 100);

  // 计算实际支付金额（美分）和手续费（美分）
  let actualAmountInUSDCents = baseAmountInUSDCents;
  let feeAmountInUSDCents = 0;

  if (hasFeeRate) {
    const feeRate = parseFloat(channel.feeRate) / 100; // 将百分比转为小数
    let fixedFeeInUSDCents = 0;

    if (hasFixedFee) {
      fixedFeeInUSDCents = Math.round(parseFloat(channel.fixedFee));
    } else if (hasFeeAmount) {
      fixedFeeInUSDCents = Math.round(parseFloat(channel.feeAmount) * 100);
    }

    // 使用公式: 实际付款金额 = (商品金额 + 固定手续费) / (1 - 费率)
    actualAmountInUSDCents = Math.round((baseAmountInUSDCents + fixedFeeInUSDCents) / (1 - feeRate));

    // 计算手续费（美分）= 实际支付金额 - 商品金额
    feeAmountInUSDCents = actualAmountInUSDCents - baseAmountInUSDCents;
  } else if (hasFixedFee) {
    // 如果只有固定费用，没有费率
    feeAmountInUSDCents = Math.round(parseFloat(channel.fixedFee));
    actualAmountInUSDCents = baseAmountInUSDCents + feeAmountInUSDCents;
  } else if (hasFeeAmount) {
    // 兼容旧版属性
    feeAmountInUSDCents = Math.round(parseFloat(channel.feeAmount) * 100);
    actualAmountInUSDCents = baseAmountInUSDCents + feeAmountInUSDCents;
  }

  // 将美元金额转回人民币（分）
  const usdToCnyRate = 1 / cnyToUsdRate; // 美元对人民币汇率是人民币对美元汇率的倒数

  if (type === 'fee') {
    const feeAmountInCNYCents = Math.round((feeAmountInUSDCents / 100) * usdToCnyRate * 100);

    // 确保手续费至少为1分钱（如果有费率或固定费用）
    if (feeAmountInCNYCents <= 0 && (hasFeeRate || hasFixedFee || hasFeeAmount)) {
      return 1;
    }

    return feeAmountInCNYCents;
  } else {
    // 返回实际支付金额（人民币分）
    return Math.round((actualAmountInUSDCents / 100) * usdToCnyRate * 100);
  }
}

// 计算手续费金额（分）- 基于原始金额(CNY)
function calculateFeeAmountInCents(channel) {
  return calculatePaymentAmount(channel, 'fee');
}

// 计算实际支付金额（分）- 基于当前选择的币种
function calculateActualAmountInCents(channel) {
  return calculatePaymentAmount(channel, 'actual');
}

// 生成包含费率和固定费用信息的描述文本
function generateFeeDescription(channel) {
  if (!channel) return '';

  let feeDesc = '';

  // 添加费率信息
  if (channel.feeRate && channel.feeRate > 0) {
    feeDesc += feeDesc ? ', ' : '';
    feeDesc += `${t('payment.selector.feeDescription')} ${channel.feeRate}%`;
  }

  // 添加固定费用信息
  if (channel.fixedFee && channel.fixedFee > 0) {
    feeDesc += feeDesc ? ' + ' : `${t('payment.selector.feeDescription')} `;
    // 固定费用统一使用美元符号，因为固定费用是以美分为单位的，需要转换为美元显示
    const fixedFeeUsd = (channel.fixedFee / 100).toFixed(2);
    feeDesc += `$${fixedFeeUsd}`;

    // 添加转换后的金额说明（如果默认币种不是美元）
    if (currencyStore.defaultCurrency.currency !== 'USD') {
      // 使用正确的汇率计算：美元对人民币的汇率 = 1 / (人民币对美元的汇率)
      const cnyToUsdRate = getCnyToUsdRate();
      const usdToCnyRate = 1 / cnyToUsdRate;

      // 将美元分转换为人民币分
      const convertedFixedFee = Math.round((channel.fixedFee / 100) * usdToCnyRate * 100);
      const convertedFixedFeeFormatted = (convertedFixedFee / 100).toFixed(2);

      feeDesc += ` (${t('payment.selector.feeDescriptionApprox')}${currencyStore.defaultCurrency.symbol}${convertedFixedFeeFormatted})`;
    }
  } else if (channel.feeAmount && channel.feeAmount > 0) {
    // 兼容旧版属性
    feeDesc += feeDesc ? ' + ' : `${t('payment.selector.feeDescription')} `;
    const feeAmountFormatted = channel.feeAmount.toFixed(2);
    feeDesc += `$${feeAmountFormatted}`;

    // 添加转换后的金额说明（如果默认币种不是美元）
    if (currencyStore.defaultCurrency.currency !== 'USD') {
      // 使用正确的汇率计算：美元对人民币的汇率 = 1 / (人民币对美元的汇率)
      const cnyToUsdRate = getCnyToUsdRate();
      const usdToCnyRate = 1 / cnyToUsdRate;

      // 将美元转换为人民币
      const convertedFeeAmount = Math.round(parseFloat(channel.feeAmount) * usdToCnyRate * 100) / 100;
      const convertedFeeAmountFormatted = convertedFeeAmount.toFixed(2);

      feeDesc += ` (${t('payment.selector.feeDescriptionApprox')}${currencyStore.defaultCurrency.symbol}${convertedFeeAmountFormatted})`;
    }
  }

  return feeDesc;
}

// 将人民币金额转换为美元金额并格式化
function formatUsdAmount(amountInCents) {
  if (!amountInCents) {
    return '0.00';
  }

  // 获取人民币对美元的汇率
  const cnyToUsdRate = getCnyToUsdRate();

  // 将人民币分转换为美元
  const usdAmount = (amountInCents / 100) * cnyToUsdRate;

  // 格式化为两位小数
  return usdAmount.toFixed(2);
}

// 获取人民币对美元的汇率
function getCnyToUsdRate() {
  // 从货币列表中查找美元
  const usdCurrency = currencyStore.list.find((currency) => currency.currency === 'USD');

  if (usdCurrency) {
    // 人民币对美元的汇率，例如1元人民币=0.1505美元
    const rate = parseFloat(usdCurrency.rate);
    if (rate === 1 || rate === 0 || isNaN(rate)) {
      return 0.15; // 使用默认值
    }
    return rate;
  }

  // 如果找不到美元汇率，返回默认值0.15
  return 0.15;
}
</script>

<style lang="scss" scoped>
.payment-channel-selector {
  background-color: #f5f7fa;
  border-radius: 8px;
  padding: 16px;
  width: 100%;
  max-width: 800px;
  margin: 0 auto;

  @media (max-width: 599px) {
    padding: 10px;
    max-width: 100%;
    border-radius: 4px;

    .text-subtitle1 {
      font-size: 1rem;
      margin-bottom: 8px !important;
    }

    .q-gutter-y-sm > * {
      margin-top: 4px;
    }
  }
}

.payment-channel-card {
  transition: all 0.3s ease;
  border-radius: 4px;
  margin-bottom: 4px;
  background-color: #edf2fa;
  border-color: transparent !important;

  &:hover {
    background-color: #e1e9f7;
  }

  &.selected-channel {
    border-color: var(--q-primary) !important;
    background-color: #e1e9f7;
  }

  @media (max-width: 599px) {
    padding: 8px 12px !important;
    margin-bottom: 4px;
  }
}

.payment-channel-row {
  @media (max-width: 599px) {
    flex-wrap: nowrap;
  }
}

.payment-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.channel-logo-container {
  width: 100px;
  height: 40px;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}

.channel-logo {
  max-width: 100px;
  max-height: 40px;
  object-fit: contain;
  object-position: left center;
}

/* 移动端专用的图标容器和图标样式 */
.mobile-channel-logo-container {
  width: 60px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-top: 0;
}

.mobile-channel-logo {
  max-width: 60px;
  max-height: 30px;
  object-fit: contain;
  object-position: left center;
}

.payment-summary-card {
  background-color: #edf2fa;
  border-color: transparent !important;
  width: 100%;

  @media (max-width: 599px) {
    padding: 8px !important;
  }

  // 确保金额区域在移动端也能正确显示
  .row.justify-between {
    flex-wrap: nowrap;

    @media (max-width: 599px) {
      .text-body2,
      .text-body1 {
        font-size: 0.9rem;
      }

      .text-subtitle2,
      .text-subtitle1 {
        font-size: 0.9rem;
      }
    }
  }
}

.payment-fee-row {
  margin-top: 8px;

  @media (max-width: 599px) {
    margin-top: 4px;
    margin-left: 0 !important;
    padding-left: 0;
    width: 100%;
  }
}

.fee-text {
  font-size: 13px;

  @media (max-width: 599px) {
    font-size: 12px;
  }
}

/* 移动端手续费显示样式 */
.mobile-fee-display {
  margin-top: 0;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  min-width: 120px; /* 确保有足够的空间显示手续费 */

  .fee-label {
    font-size: 12px;
    color: #999;
    margin-right: 2px;
    white-space: nowrap;
  }

  .fee-text {
    color: #666;
    font-size: 13px;
    font-weight: 500;
    white-space: nowrap;
  }

  &.active-fee {
    .fee-label {
      color: #666;
    }

    .fee-text {
      color: #f44336;
      font-weight: 600;
    }
  }
}

/* 淡入淡出动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 单选框容器样式 */
.radio-container {
  display: flex;
  align-items: center;
  height: 30px;
}

/* 自定义单选框样式 */
:deep(.q-radio) {
  margin: 0;
}

:deep(.q-radio__inner) {
  width: 24px;
  height: 24px;

  @media (max-width: 599px) {
    width: 18px;
    height: 18px;
  }
}

:deep(.custom-radio) {
  .q-radio__inner--truthy {
    .q-radio__bg {
      border-color: var(--q-primary) !important;
      background: transparent !important;
    }

    .q-radio__check {
      transform: scale(0.75);
      background-color: var(--q-primary) !important;
    }
  }
}

/* 添加分隔线样式 */
:deep(.q-separator) {
  border-top: 1px dashed rgba(0, 0, 0, 0.1);
}

/* 金额列样式 */
.amount-column {
  display: inline-block;
  min-width: 100px;
  text-align: right;
  white-space: nowrap;
  overflow: visible;

  .primary-currency {
    white-space: nowrap;
    display: inline-block;

    :deep(.default-currency) {
      font-size: 0.8em;
      color: #666;
      opacity: 0.85;
      font-weight: normal;
      display: inline-block;
      margin-left: 4px;
      white-space: nowrap;
    }
  }
}

/* 统一的金额文字颜色样式 */
.currency-text {
  color: #f44336; /* 红色，与text-red相同 */
  font-weight: 500; /* 中等粗细 */
}

/* 美元金额样式 */
.usd-amount {
  font-size: 0.85em;
  color: #666;
  margin-left: 4px;
  font-weight: normal;
  display: inline-block;
  white-space: nowrap;

  @media (max-width: 599px) {
    font-size: 0.8em;
    margin-left: 2px;
  }
}

/* 描述文本样式 */
.channel-description {
  padding-left: 116px; /* 与logo容器宽度+间距对齐 */
}

/* 标题样式 */
.section-title {
  font-size: 18px;
  font-weight: 500;
  color: #333;

  @media (max-width: 599px) {
    font-size: 16px;
  }
}

/* 支付方式标题样式 */
.channel-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  line-height: 1.2;
  display: flex;
  align-items: center;
  height: 100%;

  @media (max-width: 599px) {
    font-size: 14px;
  }
}

/* 描述文本样式 */
.channel-description-text {
  font-size: 14px;
  color: #666;
  line-height: 1.4;

  @media (max-width: 599px) {
    font-size: 12px;
  }
}

/* 移动端描述文本样式 */
.mobile-description {
  font-size: 0.75rem;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2; /* 标准属性 */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  max-height: 2.6em;
  padding-left: 0; /* 移动端不需要缩进 */
  margin-left: 0 !important;
}

/* 支付摘要样式 */
.summary-label {
  font-size: 14px;
  color: #555;

  @media (max-width: 599px) {
    font-size: 13px;
  }
}

.summary-value {
  font-size: 14px;
  font-weight: 500;

  @media (max-width: 599px) {
    font-size: 13px;
  }
}

.summary-total-label {
  font-size: 16px;
  font-weight: 600;
  color: #333;

  @media (max-width: 599px) {
    font-size: 14px;
  }
}

.summary-total-value {
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  justify-content: flex-end;

  @media (max-width: 599px) {
    font-size: 14px;
  }
}
</style>
