<template>
  <div class="q-mb-lg">
    <div class="text-h6 q-mb-sm">收货地址</div>
    <div class="text-caption text-grey-8 q-mb-md">请选择您希望收到包裹的地址</div>

    <div class="row items-center q-mb-md">
      <div v-if="modelValue" class="col-grow bg-blue-1 rounded-borders q-pa-md">
        <div class="row items-center justify-between">
          <div>
            <div class="text-body1 text-weight-bold">{{ modelValue.name }} {{ modelValue.phone }}</div>
            <div class="text-body2">{{ modelValue.address }}, {{ modelValue.city }}, {{ modelValue.province }}, {{ modelValue.zipCode }}</div>
          </div>
          <q-btn flat round color="primary" icon="edit" @click="openDialog" />
        </div>
      </div>
      <div v-else class="col-grow bg-blue-1 rounded-borders q-pa-md text-center">
        <q-btn color="primary" label="选择收货地址" @click="openDialog" />
      </div>
    </div>

    <!-- 地址选择弹窗 -->
    <q-dialog v-model="addressDialog">
      <q-card style="width: 700px; max-width: 90vw">
        <q-card-section class="row items-center">
          <div class="text-h6">选择收货地址</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-card-section class="q-pt-none">
          <div class="row q-col-gutter-md">
            <div v-for="address in addresses" :key="address.id" class="col-12 col-md-6">
              <ParcelCard :selected="tempSelectedAddress && tempSelectedAddress.id === address.id" @click="selectAndConfirmAddress(address)">
                <div class="text-body1 text-weight-bold">{{ address.name }} {{ address.phone }}</div>
                <div class="text-body2">{{ address.address }}, {{ address.city }}, {{ address.province }}, {{ address.zipCode }}</div>
              </ParcelCard>
            </div>
          </div>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="添加新地址" color="primary" @click="addNewAddress" />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- 添加/编辑地址弹窗 -->
    <q-dialog v-model="addressFormDialog">
      <q-card style="width: 700px; max-width: 90vw">
        <q-card-section class="row items-center">
          <div class="text-h6">添加新地址</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-card-section class="q-pt-none">
          <ParcelAddressForm @submit="saveAddress" />
        </q-card-section>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { useQuasar } from 'quasar';
import ParcelCard from './ParcelCard.vue';
import ParcelAddressForm from './ParcelAddressForm.vue';

const $q = useQuasar();

// 定义组件接收的属性
const props = defineProps({
  modelValue: {
    type: Object,
    default: null,
  },
  addresses: {
    type: Array,
    required: true,
    default: () => [],
  },
});

// 定义组件向外发出的事件
const emit = defineEmits(['update:modelValue', 'add-address']);

// 弹窗状态
const addressDialog = ref(false);
const addressFormDialog = ref(false);
const tempSelectedAddress = ref(null);

// 打开地址选择弹窗
function openDialog() {
  tempSelectedAddress.value = props.modelValue;
  addressDialog.value = true;
}

// 选择地址并确认
function selectAndConfirmAddress(address) {
  tempSelectedAddress.value = address;
  emit('update:modelValue', address);
  addressDialog.value = false;
}

// 打开添加地址弹窗
function openAddressFormDialog() {
  addressFormDialog.value = true;
}

// 添加新地址
function addNewAddress() {
  // 打开添加地址弹窗
  openAddressFormDialog();
}

// 保存新地址
function saveAddress(address) {
  // 将新地址发送给父组件
  emit('add-address', address);

  // 显示成功提示
  $q.notify({
    color: 'positive',
    message: '地址添加成功',
    icon: 'check_circle',
  });

  // 关闭弹窗
  addressFormDialog.value = false;

  // 自动选择新添加的地址
  setTimeout(() => {
    // 假设新地址已经被添加到地址列表中
    const newAddress = props.addresses.find((a) => a.id === address.id);
    if (newAddress) {
      selectAndConfirmAddress(newAddress);
    }
  }, 300);
}
</script>

<style lang="scss" scoped>
/* 可以添加特定于此组件的样式 */
</style>
