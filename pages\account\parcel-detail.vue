<template>
  <div class="parcel-detail-page">
    <!-- 标题区域 -->
    <div class="bg-grey-2 q-px-md q-py-sm rounded-borders">
      <div class="row justify-between items-center">
        <div class="row items-center">
          <q-btn flat dense color="primary" icon="arrow_back" @click="router.push('/account/parcel')" class="q-mr-sm" />
          <span class="text-subtitle1 text-weight-medium">{{ $t('parcel.detail.title') }}</span>
        </div>
        <q-btn flat dense color="primary" to="/account/parcel" :label="$t('parcel.detail.backToList')" class="q-px-sm" />
      </div>
    </div>

    <div class="q-pa-md">
      <!-- 加载中状态 -->
      <div v-if="loading" class="column items-center q-py-xl">
        <q-spinner color="primary" size="3em" />
        <div class="q-mt-sm text-grey-7">{{ $t('parcel.detail.loading') }}</div>
      </div>

      <!-- 包裹不存在 -->
      <div v-else-if="!parcelDetail.id" class="column items-center q-py-xl">
        <q-icon name="error_outline" size="3em" color="grey-5" />
        <div class="text-grey-7 q-mt-sm">{{ $t('parcel.detail.notFound') }}</div>
        <q-btn color="primary" :label="$t('parcel.detail.backToList')" class="q-mt-md" @click="router.push('/account/parcel')" />
      </div>

      <!-- 包裹详情内容 -->
      <div v-else class="row justify-center">
        <div class="col-12 col-md-10 col-lg-9">
          <!-- 包裹状态卡片 -->
          <q-card flat bordered class="q-mb-lg status-card">
            <q-card-section class="bg-grey-2 q-py-sm">
              <div class="row justify-between items-center">
                <div class="text-subtitle2">{{ $t('parcel.detail.sections.status') }}</div>
                <q-badge :color="getStatusColor(parcelDetail.status)" class="status-badge q-py-xs q-px-sm">
                  {{ getParcelStatus(parcelDetail.status) }}
                </q-badge>
              </div>
            </q-card-section>

            <q-card-section>
              <div class="row q-col-gutter-md">
                <div class="col-sm-6">
                  <div class="text-grey-7 q-mb-xs">{{ $t('parcel.detail.sections.parcelNumber') }}</div>
                  <div class="text-weight-medium">{{ parcelDetail.no }}</div>
                </div>
                <div class="col-sm-6">
                  <div class="text-grey-7 q-mb-xs">{{ $t('parcel.detail.sections.createTime') }}</div>
                  <div>{{ formatDateTime(parcelDetail.createTime) }}</div>
                </div>

                <div class="col-12 col-sm-6" v-if="parcelDetail.trackingNumber">
                  <div class="text-grey-7 q-mb-xs">{{ $t('parcel.detail.sections.trackingNumber') }}</div>
                  <div class="row items-center">
                    <span class="text-weight-medium">{{ parcelDetail.trackingNumber }}</span>
                    <q-btn flat round dense icon="content_copy" size="sm" class="q-ml-xs" @click="copyToClipboard(parcelDetail.trackingNumber)" />
                  </div>
                </div>
                <div class="col-12 col-sm-6" v-if="parcelDetail.shippingMethod">
                  <div class="text-grey-7 q-mb-xs">{{ $t('parcel.detail.sections.shippingMethod') }}</div>
                  <div>{{ parcelDetail.shippingMethod }}</div>
                </div>
              </div>
            </q-card-section>

            <!-- 操作按钮 -->
            <q-card-actions align="right">
              <q-btn v-if="parcelDetail.status === 0" color="primary" :label="$t('parcel.actions.pay')" icon-left="payment" @click="payParcel(parcelDetail.payOrderId)" />
              <q-btn v-if="parcelDetail.status === 0" outline color="grey" :label="$t('parcel.actions.cancel')" class="q-ml-sm" @click="cancelParcel" />
              <q-btn v-if="parcelDetail.status === 20 || parcelDetail.status === 30" outline color="primary" :label="$t('parcel.actions.track')" icon-left="local_shipping" @click="showTrackingInfo" />
              <q-btn v-if="parcelDetail.status === 40" outline color="red" :label="$t('parcel.dialog.delete.title')" class="q-ml-sm" @click="deleteParcel" />
            </q-card-actions>
          </q-card>

          <!-- 物流跟踪卡片 -->
          <q-card v-if="parcelDetail.status === 20 || parcelDetail.status === 30" flat bordered class="q-mb-lg tracking-card">
            <q-card-section class="bg-grey-2 q-py-sm">
              <div class="row items-center">
                <q-icon name="local_shipping" color="primary" size="xs" class="q-mr-xs" />
                <div class="text-subtitle2">{{ $t('parcel.detail.sections.tracking') }}</div>
              </div>
            </q-card-section>

            <q-card-section>
              <q-timeline color="primary">
                <q-timeline-entry v-for="(track, index) in trackingInfo" :key="index" :title="track.status" :subtitle="formatDateTime(track.time)">
                  <div>{{ track.description }}</div>
                  <div class="text-grey-7">{{ track.location }}</div>
                </q-timeline-entry>
              </q-timeline>
            </q-card-section>
          </q-card>

          <!-- 商品信息卡片 -->
          <q-card flat bordered class="q-mb-lg">
            <q-card-section class="bg-grey-2 q-py-sm">
              <div class="row items-center">
                <q-icon name="inventory_2" color="primary" size="xs" class="q-mr-xs" />
                <div class="text-subtitle2">{{ $t('parcel.detail.sections.items') }}</div>
              </div>
            </q-card-section>

            <q-card-section class="q-pa-none">
              <q-list>
                <q-item v-for="(item, index) in parcelDetail.items" :key="item.id" :class="{ 'border-top': index > 0 }">
                  <q-item-section avatar>
                    <q-img :src="getThumbnailUrl(item.picUrl, '80x80')" style="width: 80px; height: 80px" class="rounded-borders" />
                  </q-item-section>

                  <q-item-section>
                    <q-item-label class="ellipsis-2-lines text-weight-medium">{{ item.spuName }}</q-item-label>
                    <q-item-label caption>{{ formattedProperties(item.properties) }}</q-item-label>
                    <q-item-label caption class="row justify-between q-mt-xs">
                      <span>{{ $t('parcel.detail.itemInfo.weight') }}: {{ item.weight || '0.00' }}{{ $t('parcel.parcelInfo.weightUnit') }}</span>
                      <span>{{ $t('parcel.detail.itemInfo.quantity') }}: x{{ item.count }}</span>
                    </q-item-label>
                  </q-item-section>
                </q-item>
              </q-list>
            </q-card-section>
          </q-card>

          <!-- 收货地址卡片 -->
          <q-card flat bordered class="q-mb-lg">
            <q-card-section class="bg-grey-2 q-py-sm">
              <div class="row items-center">
                <q-icon name="location_on" color="primary" size="xs" class="q-mr-xs" />
                <div class="text-subtitle2">{{ $t('parcel.detail.sections.address') }}</div>
              </div>
            </q-card-section>

            <q-card-section>
              <div class="row q-col-gutter-md">
                <div class="col-12 col-sm-6">
                  <div class="text-grey-7 q-mb-xs">{{ $t('parcel.detail.address.recipient') }}</div>
                  <div class="text-weight-medium">{{ parcelDetail.receiverName }}</div>
                </div>
                <div class="col-12 col-sm-6">
                  <div class="text-grey-7 q-mb-xs">{{ $t('parcel.detail.address.phone') }}</div>
                  <div>+{{ parcelDetail.receiverPhoneCode }} {{ parcelDetail.receiverMobile }}</div>
                </div>
                <div class="col-12">
                  <div class="text-grey-7 q-mb-xs">{{ $t('parcel.detail.address.address') }}</div>
                  <div>{{ parcelDetail.receiverDetailAddress }} {{ parcelDetail.receiverAreaName }} {{ parcelDetail.receiverPostCode }}</div>
                </div>
              </div>
            </q-card-section>
          </q-card>

          <!-- 报关信息卡片 -->
          <q-card flat bordered class="q-mb-lg">
            <q-card-section class="bg-grey-2 q-py-sm">
              <div class="row items-center">
                <q-icon name="description" color="primary" size="xs" class="q-mr-xs" />
                <div class="text-subtitle2">{{ $t('parcel.detail.sections.customs') }}</div>
              </div>
            </q-card-section>

            <q-card-section>
              <div class="row q-col-gutter-md">
                <div class="col-12 col-sm-6">
                  <div class="text-grey-7 q-mb-xs">{{ $t('parcel.detail.customs.declaredValue') }}</div>
                  <div>${{ fen2yuan(parcelDetail.declareValue) || '0.00' }}</div>
                </div>
                <div class="col-12 col-sm-6">
                  <div class="text-grey-7 q-mb-xs">{{ $t('parcel.detail.customs.clearanceCode') }}</div>
                  <div>{{ parcelDetail.clearanceCode || $t('parcel.detail.customs.none') }}</div>
                </div>
                <div class="col-12">
                  <div class="text-grey-7 q-mb-xs">{{ $t('parcel.detail.customs.description') }}</div>
                  <div>{{ parcelDetail.declareContent || $t('parcel.detail.customs.none') }}</div>
                </div>
              </div>
            </q-card-section>
          </q-card>

          <!-- 物流信息卡片 -->
          <q-card flat bordered class="q-mb-lg">
            <q-card-section class="bg-grey-2 q-py-sm">
              <div class="row items-center">
                <q-icon name="local_shipping" color="primary" size="xs" class="q-mr-xs" />
                <div class="text-subtitle2">{{ $t('parcel.detail.sections.logistics') }}</div>
              </div>
            </q-card-section>

            <q-card-section>
              <div class="row q-col-gutter-md">
                <div class="col-12 col-sm-6">
                  <div class="text-grey-7 q-mb-xs">{{ $t('parcel.detail.sections.shippingMethod') }}</div>
                  <div>{{ parcelDetail.transportPlanName || $t('parcel.detail.logistics.notSelected') }}</div>
                </div>
                <!-- <div class="col-12 col-sm-6">
                  <div class="text-grey-7 q-mb-xs">{{ $t('parcel.detail.logistics.estimatedDelivery') }}</div>
                  <div>{{ parcelDetail.estimatedDelivery || $t('parcel.detail.logistics.noInfo') }}</div>
                </div> -->
                <!-- <div class="col-12 col-sm-6">
                  <div class="text-grey-7 q-mb-xs">{{ $t('parcel.detail.logistics.weight') }}</div>
                  <div>{{ parcelDetail.weight || '0.00' }}{{ $t('parcel.parcelInfo.weightUnit') }}</div>
                </div> -->
                <div class="col-12 col-sm-6" v-if="parcelDetail.trackingNumber">
                  <div class="text-grey-7 q-mb-xs">{{ $t('parcel.detail.sections.trackingNumber') }}</div>
                  <div class="row items-center">
                    <span>{{ parcelDetail.trackingNumber }}</span>
                    <q-btn flat round dense icon="content_copy" size="sm" class="q-ml-xs" @click="copyToClipboard(parcelDetail.trackingNumber)" />
                  </div>
                </div>
              </div>
            </q-card-section>
          </q-card>

          <!-- 服务信息卡片 -->
          <q-card flat bordered class="q-mb-lg">
            <q-card-section class="bg-grey-2 q-py-sm">
              <div class="row items-center">
                <q-icon name="support_agent" color="primary" size="xs" class="q-mr-xs" />
                <div class="text-subtitle2">{{ $t('parcel.detail.sections.services') }}</div>
              </div>
            </q-card-section>

            <q-card-section>
              <div class="row q-col-gutter-md">
                <div class="col-12">
                  <div class="text-grey-7 q-mb-xs">{{ $t('parcel.detail.services.insurance') }}</div>
                  <div v-if="parcelDetail.insuranceServices && parcelDetail.insuranceServices.length > 0">
                    <q-chip v-for="service in parcelDetail.insuranceServices" :key="service.id" dense color="grey" text-color="white" class="q-mr-xs">
                      {{ service.name }} ￥{{ fen2yuanSimple(service.price) }}
                    </q-chip>
                  </div>
                  <div v-else>{{ $t('parcel.detail.services.notPurchased') }}</div>
                </div>

                <div class="col-12">
                  <div class="text-grey-7 q-mb-xs">{{ $t('parcel.detail.services.free') }}</div>
                  <div v-if="parcelDetail.freeServices && parcelDetail.freeServices.length > 0">
                    <q-chip v-for="service in parcelDetail.freeServices" :key="service.id" dense color="green" text-color="white" class="q-mr-xs">
                      {{ service.name }}
                    </q-chip>
                  </div>
                  <div v-else>{{ $t('parcel.detail.services.notSelected') }}</div>
                </div>

                <div class="col-12 col-sm-6">
                  <div class="text-grey-7 q-mb-xs">{{ $t('parcel.detail.services.valueAdded') }}</div>
                  <div v-if="parcelDetail.chargeServices && parcelDetail.chargeServices.length > 0">
                    <q-chip v-for="service in parcelDetail.chargeServices" :key="service.id" dense color="primary" text-color="white" class="q-mr-xs">
                      {{ service.name }} ￥{{ fen2yuanSimple(service.price) }}
                    </q-chip>
                  </div>
                  <div v-else>{{ $t('parcel.detail.services.notSelected') }}</div>
                </div>
              </div>
            </q-card-section>
          </q-card>

          <!-- 备注信息卡片 -->
          <q-card flat bordered class="q-mb-lg" v-if="parcelDetail.userRemark">
            <q-card-section class="bg-grey-2 q-py-sm">
              <div class="row items-center">
                <q-icon name="comment" color="primary" size="xs" class="q-mr-xs" />
                <div class="text-subtitle2">{{ $t('parcel.detail.sections.remarks') }}</div>
              </div>
            </q-card-section>

            <q-card-section>
              <div>{{ parcelDetail.userRemark }}</div>
            </q-card-section>
          </q-card>

          <!-- 费用信息卡片 -->
          <q-card flat bordered class="q-mb-lg">
            <q-card-section class="bg-grey-2 q-py-sm">
              <div class="row items-center">
                <q-icon name="receipt_long" color="primary" size="xs" class="q-mr-xs" />
                <div class="text-subtitle2">{{ $t('parcel.detail.sections.costs') }}</div>
              </div>
            </q-card-section>

            <q-card-section>
              <div class="row justify-between q-mb-sm">
                <div>{{ $t('parcel.detail.costs.baseShipping') }}:</div>
                <div>{{ fen2yuan(parcelDetail.deliveryPrice) }}</div>
              </div>
              <div class="row justify-between q-mb-sm" v-if="parcelDetail.insurancePrice > 0">
                <div>{{ $t('parcel.detail.costs.insurance') }}:</div>
                <div>{{ fen2yuan(parcelDetail.insurancePrice) }}</div>
              </div>
              <div class="row justify-between q-mb-sm" v-if="parcelDetail.servicePrice > 0">
                <div>{{ $t('parcel.detail.costs.extraServices') }}:</div>
                <div>{{ fen2yuan(parcelDetail.servicePrice) }}</div>
              </div>
              <div class="row justify-between q-mb-sm" v-if="parcelDetail.platformPrice > 0">
                <div>平台佣金:</div>
                <div>{{ fen2yuan(parcelDetail.platformPrice) }}</div>
              </div>
              <div class="row justify-between q-mb-sm text-negative">
                <div>{{ $t('parcel.detail.costs.couponDiscount') }}:</div>
                <div>- {{ fen2yuan(parcelDetail.couponPrice) }}</div>
              </div>
              <div class="row justify-between q-mb-sm text-negative">
                <div>{{ $t('parcel.detail.costs.pointsDiscount') }}:</div>
                <div>- {{ fen2yuan(parcelDetail.pointPrice) }}</div>
              </div>
              <q-separator class="q-my-md" />
              <div class="row justify-between text-weight-bold">
                <div>{{ $t('parcel.detail.costs.total') }}:</div>
                <div class="text-primary text-subtitle1">{{ fen2yuan(parcelDetail.payPrice) }}</div>
              </div>
            </q-card-section>
          </q-card>

          <!-- 支付信息卡片 -->
          <q-card flat bordered class="q-mb-lg" v-if="parcelDetail.payStatus">
            <q-card-section class="bg-grey-2 q-py-sm">
              <div class="row items-center">
                <q-icon name="receipt_long" color="primary" size="xs" class="q-mr-xs" />
                <div class="text-subtitle2">支付信息</div>
              </div>
            </q-card-section>

            <q-card-section>
              <div class="row justify-between q-mb-sm">
                <div>支付状态:</div>
                <div>{{ parcelDetail.payStatus ? '已支付' : '未支付' }}</div>
              </div>
              <div class="row justify-between q-mb-sm" v-if="parcelDetail.payChannelCode">
                <div>支付方式:</div>
                <div>{{ parcelDetail.payChannelCode }}</div>
              </div>
              <div class="row justify-between q-mb-sm" v-if="parcelDetail.payChannelCode">
                <div>支付时间:</div>
                <div>{{ formatDateTime(parcelDetail.payTime) }}</div>
              </div>
            </q-card-section>
          </q-card>

          <!-- 底部按钮 -->
          <div class="row justify-end q-mt-lg q-gutter-sm">
            <q-btn v-if="parcelDetail.status === 0" color="grey" outline :label="$t('parcel.actions.cancel')" @click="cancelParcel" />
            <q-btn v-if="parcelDetail.status === 0" color="primary" :label="$t('parcel.actions.pay')" @click="payParcel" />
            <q-btn v-if="parcelDetail.status === 20 || parcelDetail.status === 30" color="primary" outline :label="$t('parcel.actions.track')" @click="showTrackingInfo" />
            <q-btn v-if="parcelDetail.status === 40" color="grey" outline :label="$t('parcel.dialog.delete.title')" @click="deleteParcel" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useQuasar } from 'quasar';
import { useRouter, useRoute } from 'vue-router';
import { useI18n } from 'vue-i18n';
import ParcelApi from '~/composables/parcelApi';
import { fen2yuan, fen2yuanSimple } from '../../utils/utils';
import { PayOrderSources } from '../../utils/constants';

// 初始化国际化
const { t } = useI18n();

definePageMeta({
  middleware: 'auth', // 引入身份验证中间件
});

const $q = useQuasar();
const router = useRouter();
const route = useRoute();

// 获取包裹ID
const parcelId = ref(route.query.id);

// 数据状态
const loading = ref(true);
const parcelDetail = ref({});
const trackingInfo = ref([]);

// 初始化数据
onMounted(async () => {
  if (parcelId.value) {
    await getParcelDetail();
  } else {
    loading.value = false;
  }
});

// 获取包裹详情
async function getParcelDetail() {
  loading.value = true;

  try {
    // 调用API获取包裹详情
    const { code, data } = await ParcelApi.getParcelDetail(parcelId.value);

    if (code === 0 && data) {
      parcelDetail.value = data;

      // 如果包裹已发货，获取物流信息
      if (parcelDetail.value.status >= 1) {
        await getTrackingInfo();
      }
    }
  } catch (error) {
    console.error('获取包裹详情失败:', error);
    $q.notify({
      color: 'negative',
      message: t('parcel.errors.fetchFailed'),
      icon: 'error',
    });
  } finally {
    loading.value = false;
  }
}

// 获取物流信息
async function getTrackingInfo() {
  try {
    // 调用API获取物流信息
    const { code, data } = await ParcelApi.getParcelTracking(parcelId.value);

    if (code === 0) {
      trackingInfo.value = data;
    }
  } catch (error) {
    console.error('获取物流信息失败:', error);
  }
}

// 支付包裹
function payParcel(pId) {
  router.push({
    path: '/pay',
    query: { pId, ps: PayOrderSources.PARCEL },
  });
}

// 取消包裹
function cancelParcel() {
  $q.dialog({
    title: t('parcel.dialog.cancel.title'),
    message: t('parcel.dialog.cancel.message'),
    cancel: true,
    persistent: true,
  }).onOk(async () => {
    try {
      // 调用API取消包裹
      const { code } = await ParcelApi.cancelParcel(parcelId.value);

      if (code === 0) {
        $q.notify({
          color: 'positive',
          message: t('parcel.dialog.cancel.success'),
          icon: 'check_circle',
        });

        // 更新包裹状态
        parcelDetail.value.status = -1;
      } else {
        throw new Error('取消包裹失败');
      }
    } catch (error) {
      console.error('取消包裹失败:', error);
      $q.notify({
        color: 'negative',
        message: '取消包裹失败',
        icon: 'error',
      });
    }
  });
}

// 删除包裹
function deleteParcel() {
  $q.dialog({
    title: t('parcel.dialog.delete.title'),
    message: t('parcel.dialog.delete.message'),
    cancel: true,
    persistent: true,
  }).onOk(async () => {
    try {
      // 调用API删除包裹
      const { code } = await ParcelApi.deleteParcel(parcelId.value);

      if (code === 0) {
        $q.notify({
          color: 'positive',
          message: t('parcel.dialog.delete.success'),
          icon: 'check_circle',
        });

        // 返回包裹列表页
        router.push('/account/parcel');
      } else {
        throw new Error('删除包裹失败');
      }
    } catch (error) {
      console.error('删除包裹失败:', error);
      $q.notify({
        color: 'negative',
        message: '删除包裹失败',
        icon: 'error',
      });
    }
  });
}

// 显示物流信息
function showTrackingInfo() {
  if (!parcelDetail.value.trackingNumber) {
    $q.notify({
      color: 'warning',
      message: t('parcel.errors.noTracking'),
      icon: 'warning',
    });
    return;
  }

  // 如果已经有物流信息，直接滚动到物流跟踪卡片
  const trackingCard = document.querySelector('.tracking-card');
  if (trackingCard) {
    trackingCard.scrollIntoView({ behavior: 'smooth' });
  }
}

// 复制到剪贴板
function copyToClipboard(text) {
  navigator.clipboard
    .writeText(text)
    .then(() => {
      $q.notify({
        color: 'positive',
        message: t('parcel.detail.copy.success'),
        icon: 'content_copy',
        timeout: 1000,
      });
    })
    .catch(() => {
      $q.notify({
        color: 'negative',
        message: t('parcel.detail.copy.failed'),
        icon: 'error',
      });
    });
}

// 获取包裹状态文本
function getParcelStatus(statusCode) {
  switch (statusCode) {
    case 0:
      return t('parcel.status.unpaid');
    case 10:
      return t('parcel.status.unshipped');
    case 20:
      return t('parcel.status.shipping');
    case 30:
      return t('parcel.status.completed');
    case 40:
      return t('parcel.status.cancelled');
    default:
      return t('parcel.status.unknown');
  }
}

// 获取包裹状态对应的颜色
function getStatusColor(status) {
  switch (status) {
    case 0:
      return 'orange';
    case 10:
      return 'blue';
    case 20:
      return 'teal';
    case 30:
      return 'positive';
    case 40:
      return 'grey';
    default:
      return 'grey';
  }
}
</script>

<style lang="scss" scoped>
.parcel-detail-page {
  .status-card {
    border-radius: 8px;
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    }

    .status-badge {
      font-size: 12px;
    }
  }

  .tracking-card {
    border-radius: 8px;
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    }
  }

  // 卡片标题样式
  .q-card {
    .bg-grey-2 {
      .text-subtitle2 {
        font-size: 14px;
      }
    }
  }

  .border-top {
    border-top: 1px solid #e0e0e0;
  }

  .ellipsis-2-lines {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    min-height: 2em;
    line-height: 1.2;
  }

  // 响应式调整
  @media (max-width: 599px) {
    .q-timeline {
      padding-left: 20px;
      font-size: 13px;

      &__entry {
        padding-left: 30px;

        &__title {
          font-size: 14px;
        }

        &__subtitle {
          font-size: 12px;
        }
      }
    }

    // 移动端卡片内容调整
    .q-card-section {
      padding: 12px;
    }

    // 移动端字体调整
    .text-subtitle1 {
      font-size: 15px;
    }

    .text-subtitle2 {
      font-size: 13px;
    }
  }
}
</style>
