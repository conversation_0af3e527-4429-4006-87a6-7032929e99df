<template>
  <div class="q-mb-lg">
    <div class="text-h6 q-mb-sm">已选商品</div>
    <div class="text-caption text-grey-8 q-mb-md">以下是您从仓库中选择的商品，将被打包成一个包裹发送</div>

    <q-table :rows="products" :columns="productColumns" row-key="id" flat bordered dense>
      <template #body="props">
        <q-tr :props="props">
          <q-td>
            <div class="row items-start q-ml-sm">
              <q-img :src="getThumbnailUrl(props.row.picUrl, '80x80')" alt="商品图片" width="80px" height="80px" class="rounded-borders q-mr-sm" spinner-color="primary" />
              <div class="column no-wrap" style="width: 300px; overflow: hidden">
                <div class="text-weight-bold text-dark q-mb-xs ellipsis-3-lines" style="height: 60px; line-height: 20px">
                  {{ props.row.spuName }}
                </div>
                <div class="text-caption text-grey-8">{{ formattedProperties(props.row.properties) }}</div>
              </div>
            </div>
          </q-td>
          <q-td key="count" :props="props">
            {{ props.row.count }}
          </q-td>
          <q-td key="weight" :props="props"> {{ props.row.weight }}kg </q-td>
          <q-td key="volume" :props="props"> {{ props.row.volume }}m³ </q-td>
          <q-td key="inTime" :props="props">
            {{ formatTimestampToWesternDate(props.row.inTime) }}
          </q-td>
        </q-tr>
      </template>
    </q-table>

    <!-- 商品总重量信息 -->
    <div class="q-mt-sm text-right">
      <div class="text-body1">商品总重量: {{ totalWeight }}kg</div>
      <div class="text-body1">预估包装材料重量: {{ packagingWeight }}kg</div>
      <div class="text-body1 text-weight-bold">包裹总重量: {{ totalParcelWeight }}kg</div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { formatTimestampToWesternDate, getThumbnailUrl } from '~/utils/utils';
import { formattedProperties } from '~/utils/productUtils';

// 定义组件接收的属性
const props = defineProps({
  products: {
    type: Array,
    required: true,
    default: () => [],
  },
});

// 定义组件向外发出的事件
const emit = defineEmits(['update-total-weight', 'update-total-parcel-weight']);

// 商品表格列定义
const productColumns = [
  { name: 'detail', align: 'left', label: '商品详情', field: 'spuName' },
  { name: 'count', align: 'center', label: '数量', field: 'count' },
  { name: 'weight', align: 'center', label: '重量', field: 'weight' },
  { name: 'volume', align: 'center', label: '体积', field: 'volume' },
  { name: 'inTime', align: 'center', label: '入库日期', field: 'inTime' },
];

// 计算选中物品的总重量
const totalWeight = computed(() => {
  if (!props.products || props.products.length === 0) return '0.00';

  const weight = props.products.reduce((sum, item) => {
    return sum + (parseFloat(item.weight) || 0) * (parseInt(item.count) || 1);
  }, 0);

  // 向父组件发出总重量更新事件
  emit('update-total-weight', weight.toFixed(2));

  return weight.toFixed(2);
});

// 计算包装材料重量（估计为总重量的 10%）
const packagingWeight = computed(() => {
  const weight = parseFloat(totalWeight.value);
  const packaging = weight * 0.1; // 包装重量为总重量的 10%

  return packaging.toFixed(2);
});

// 计算包装总重量（物品 + 包装）
const totalParcelWeight = computed(() => {
  const weight = parseFloat(totalWeight.value);
  const packaging = parseFloat(packagingWeight.value);
  const total = weight + packaging;

  // 向父组件发出总包裹重量更新事件
  emit('update-total-parcel-weight', total.toFixed(2));

  return total.toFixed(2);
});
</script>

<style lang="scss" scoped>
/* 多行文本省略号样式 */
.ellipsis-3-lines {
  display: -webkit-box;
  -webkit-line-clamp: 3; /* 限制为3行 */
  line-clamp: 3; /* 标准属性以提高兼容性 */
  -webkit-box-orient: vertical;
  box-orient: vertical; /* 标准属性以提高兼容性 */
  overflow: hidden;
  text-overflow: ellipsis; /* 超出部分显示省略号 */
  word-break: break-all; /* 强制换行 */
  white-space: normal; /* 允许换行 */
}

/* 表格样式 */
:deep(.q-table th) {
  font-size: 14px; /* 表头文字大小调整为14px */
  padding: 8px 4px; /* 减小表头填充 */
}

:deep(.q-table td) {
  padding: 8px 4px; /* 减小表格单元格填充 */
}

:deep(.q-checkbox) {
  font-size: 0.8em; /* 选择框小号 */
}
</style>
