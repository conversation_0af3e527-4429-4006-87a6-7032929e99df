<template>
  <div class="shipping-selector-section">
    <div class="row items-center q-mb-sm">
      <q-icon name="flight_takeoff" color="primary" size="sm" class="q-mr-sm" />
      <div class="text-h6 text-weight-bold">物流方案</div>
    </div>
    <div class="text-caption text-grey-8 q-mb-md">请选择适合您的物流方案</div>

    <q-card flat bordered class="q-pa-md">
      <div class="row items-center">
        <div v-if="modelValue" class="col-grow">
          <div class="row items-center justify-between">
            <div>
              <div class="text-subtitle2 text-weight-medium">
                {{ modelValue.name }}
                <q-badge color="green" class="q-ml-sm">推荐</q-badge>
              </div>
              <div class="text-subtitle2">预计送达: {{ modelValue.transitTime }}</div>
              <div class="text-caption text-grey-8">{{ modelValue.description }}</div>
            </div>
            <div class="text-right">
              <div class="text-body1 text-primary text-weight-medium">¥{{ fen2yuan(modelValue.totalFee) }}</div>
              <q-btn flat round color="primary" icon="edit" @click="openDialog" />
            </div>
          </div>
        </div>
        <div v-else class="col-grow text-center">
          <q-btn color="primary" label="选择物流方案" @click="openDialog" />
        </div>
      </div>
    </q-card>

    <!-- 物流方案选择弹窗 -->
    <q-dialog v-model="shippingDialog" persistent>
      <q-card style="width: 900px; max-width: 95vw" class="shipping-dialog">
        <q-card-section class="dialog-header row items-center justify-between">
          <div class="text-h6 text-weight-bold">
            <q-icon name="local_shipping" class="q-mr-sm" />
            选择物流方案
          </div>
          <q-btn icon="close" flat round dense v-close-popup color="white" class="close-btn" />
        </q-card-section>

        <q-card-section class="q-pt-none q-pt-lg">
          <!-- 物流方案列表 - 按价格排序 -->
          <div class="shipping-methods-container">
            <div
              v-for="shipping in sortedShippingMethods"
              :key="`${shipping.productId}-${shipping.priceId}`"
              class="shipping-method-item"
              :class="{ selected: isSelected(shipping), expanded: expandedItems[getShippingKey(shipping)] }">
              <!-- 基础信息行 -->
              <div class="shipping-basic-row" @click="selectShipping(shipping)">
                <!-- 选中状态指示器 -->
                <div class="selection-radio">
                  <q-icon :name="isSelected(shipping) ? 'radio_button_checked' : 'radio_button_unchecked'" :color="isSelected(shipping) ? 'primary' : 'grey-5'" size="sm" />
                </div>

                <!-- 方案信息 -->
                <div class="shipping-content">
                  <div class="shipping-main">
                    <div class="shipping-title">
                      {{ shipping.name }}
                      <q-badge v-if="shipping.recommended" color="green" text-color="white" class="recommend-badge">推荐</q-badge>
                    </div>
                    <div class="shipping-price">¥{{ fen2yuan(shipping.totalFee) }}</div>
                  </div>

                  <div class="shipping-meta">
                    <div class="delivery-info">
                      <q-icon name="schedule" size="xs" />
                      <span>预计送达: {{ shipping.transitTime || shipping.estimatedDelivery }}</span>
                    </div>
                    <div class="delivery-info delivery-rate-info">
                      <q-icon name="check_circle_outline" size="xs" />
                      <span>妥投率: {{ formatDeliveryRate(shipping.deliveryRate) }}</span>
                    </div>

                    <!-- 特色服务标签 -->
                    <div class="service-tags" v-if="shipping.taxInclude || shipping.freeInsure">
                      <span v-if="shipping.taxInclude" class="service-tag tax-included">包税</span>
                      <span v-if="shipping.freeInsure" class="service-tag free-insurance">免费保险</span>
                    </div>
                  </div>
                </div>

                <!-- 展开/收起按钮 -->
                <div class="expand-button">
                  <q-btn
                    flat
                    round
                    color="primary"
                    :icon="expandedItems[getShippingKey(shipping)] ? 'expand_less' : 'expand_more'"
                    size="sm"
                    @click.stop="toggleExpand(shipping)"
                    :loading="loadingDetails[getShippingKey(shipping)]">
                    <q-tooltip>{{ expandedItems[getShippingKey(shipping)] ? '收起详情' : '展开详情' }}</q-tooltip>
                  </q-btn>
                </div>
              </div>

              <!-- 详细信息展开区域 -->
              <q-slide-transition>
                <div v-show="expandedItems[getShippingKey(shipping)]" class="shipping-details-expanded">
                  <q-separator />
                  <div class="details-content q-pa-md">
                    <div v-if="shippingDetails[getShippingKey(shipping)]" class="shipping-detail-info">
                      <!-- 参考运费试算页面的详细信息展示 -->
                      <div class="row q-col-gutter-md">
                        <!-- 费用详情 -->
                        <div class="col-12 col-md-6">
                          <h6 class="text-subtitle2 q-mb-sm">
                            <q-icon name="credit_card" class="q-mr-xs" />
                            费用详情
                          </h6>
                          <div class="fee-breakdown">
                            <div class="fee-item" v-if="shippingDetails[getShippingKey(shipping)].feeDetail">
                              <span>基础运费:</span>
                              <span class="fee-value">¥{{ fen2yuan(shippingDetails[getShippingKey(shipping)].feeDetail.freight || 0) }}</span>
                            </div>
                            <div class="fee-item" v-if="shippingDetails[getShippingKey(shipping)].feeDetail?.operationFee > 0">
                              <span>操作费:</span>
                              <span class="fee-value">¥{{ fen2yuan(shippingDetails[getShippingKey(shipping)].feeDetail.operationFee) }}</span>
                            </div>
                            <div class="fee-item" v-if="shippingDetails[getShippingKey(shipping)].feeDetail?.serviceFee > 0">
                              <span>服务费:</span>
                              <span class="fee-value">¥{{ fen2yuan(shippingDetails[getShippingKey(shipping)].feeDetail.serviceFee) }}</span>
                            </div>
                            <div class="fee-item" v-if="shippingDetails[getShippingKey(shipping)].feeDetail?.customsFee > 0">
                              <span>清关费:</span>
                              <span class="fee-value">¥{{ fen2yuan(shippingDetails[getShippingKey(shipping)].feeDetail.customsFee) }}</span>
                            </div>
                            <div class="fee-item" v-if="shippingDetails[getShippingKey(shipping)].feeDetail?.fuelFee > 0">
                              <span>燃油费:</span>
                              <span class="fee-value">¥{{ fen2yuan(shippingDetails[getShippingKey(shipping)].feeDetail.fuelFee) }}</span>
                            </div>
                            <q-separator class="q-my-sm" />
                            <div class="fee-item total-fee">
                              <span class="text-weight-bold">总费用:</span>
                              <span class="fee-value text-weight-bold text-primary">¥{{ fen2yuan(shipping.totalFee) }}</span>
                            </div>
                          </div>
                        </div>

                        <!-- 重量信息 -->
                        <div class="col-12 col-md-6">
                          <h6 class="text-subtitle2 q-mb-sm">
                            <q-icon name="scale" class="q-mr-xs" />
                            重量信息
                          </h6>
                          <div class="weight-info" v-if="shippingDetails[getShippingKey(shipping)].feeDetail">
                            <div class="weight-item">
                              <span>实际重量:</span>
                              <span class="weight-value">{{ shippingDetails[getShippingKey(shipping)].feeDetail.weight || 0 }}g</span>
                            </div>
                            <div class="weight-item" v-if="shippingDetails[getShippingKey(shipping)].feeDetail.volumeWeight">
                              <span>体积重:</span>
                              <span class="weight-value">{{ shippingDetails[getShippingKey(shipping)].feeDetail.volumeWeight || 0 }}g</span>
                            </div>
                            <div class="weight-item">
                              <span class="text-weight-bold">计费重量:</span>
                              <span class="weight-value text-weight-bold text-primary">{{ shippingDetails[getShippingKey(shipping)].feeDetail.chargeableWeight || 0 }}g</span>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- 产品特色和时效分布 -->
                      <div class="row q-col-gutter-md q-mt-md" v-if="shippingDetails[getShippingKey(shipping)].features || shippingDetails[getShippingKey(shipping)].timelinessInfo">
                        <!-- 产品特色 -->
                        <div v-if="shippingDetails[getShippingKey(shipping)].features" class="col-12 col-md-6">
                          <h6 class="text-subtitle2 q-mb-sm">
                            <q-icon name="star" class="q-mr-xs" />
                            产品特色
                          </h6>
                          <div class="features-content">
                            <p class="text-body2">{{ shippingDetails[getShippingKey(shipping)].features }}</p>
                          </div>
                        </div>

                        <!-- 时效信息 -->
                        <div v-if="shippingDetails[getShippingKey(shipping)].timelinessInfo?.timelinessInfos" class="col-12 col-md-6">
                          <h6 class="text-subtitle2 q-mb-sm">
                            <q-icon name="timeline" class="q-mr-xs" />
                            时效分布
                          </h6>
                          <div class="timeliness-chart">
                            <div class="delivery-rate q-mb-sm">
                              <span class="text-body2">妥投率: </span>
                              <span class="text-weight-bold text-positive">{{ formatDeliveryRate(shippingDetails[getShippingKey(shipping)].timelinessInfo.deliveryRate) }}</span>
                            </div>
                            <div class="timeliness-bars">
                              <div v-for="info in shippingDetails[getShippingKey(shipping)].timelinessInfo.timelinessInfos" :key="info.timeInterval" class="timeliness-bar-item">
                                <div class="time-interval">{{ info.timeInterval }}天</div>
                                <div class="progress-container">
                                  <q-linear-progress :value="parseFloat(info.rate) / 100" color="primary" size="8px" class="q-my-xs" />
                                </div>
                                <div class="rate-value">{{ info.rate }}%</div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div v-else class="text-center q-py-md">
                      <q-spinner-dots size="30px" color="primary" />
                      <div class="text-caption q-mt-sm">加载详细信息中...</div>
                    </div>
                  </div>
                </div>
              </q-slide-transition>
            </div>

            <div v-if="sortedShippingMethods.length === 0" class="text-center q-py-lg">
              <q-icon name="info" size="2rem" color="grey-7" />
              <div class="text-grey-7 q-mt-sm">暂无可用的物流方案</div>
            </div>
          </div>
        </q-card-section>

        <q-card-actions align="right" class="q-pt-none">
          <q-btn flat label="取消" color="grey" v-close-popup />
          <q-btn label="确认" color="primary" :disable="!tempSelectedShipping" @click="confirmShipping" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
import { ref, watch, computed, toRaw } from 'vue';
import { useQuasar } from 'quasar';
import { fen2yuan } from '../../utils/utils';
import ShippingApi from '../../composables/shippingApi';

const $q = useQuasar();

// 定义组件接收的属性
const props = defineProps({
  modelValue: {
    type: Object,
    default: null,
  },
  shippingMethods: {
    type: Array,
    required: true,
    default: () => [],
  },
  parcelWeight: {
    type: [Number, String],
    required: true,
    default: '0.00',
  },
  selectedProducts: {
    type: Array,
    default: () => [],
  },
});

// 定义组件向外发出的事件
const emit = defineEmits(['update:modelValue', 'calculate-fee']);

// 弹窗状态
const shippingDialog = ref(false);
const tempSelectedShipping = ref(props.modelValue);

// 展开状态管理
const expandedItems = ref({});
const shippingDetails = ref({});
const loadingDetails = ref({});

// 按价格排序的物流方案
const sortedShippingMethods = computed(() => {
  if (!props.shippingMethods || props.shippingMethods.length === 0) {
    return [];
  }

  return [...props.shippingMethods].sort((a, b) => {
    const feeA = parseFloat(a.totalFee);
    const feeB = parseFloat(b.totalFee);
    return feeA - feeB;
  });
});

// 生成物流方案的唯一键
function getShippingKey(shipping) {
  return `${shipping.productId}-${shipping.priceId}`;
}

// 格式化妥投率显示
function formatDeliveryRate(rate) {
  if (!rate) return '未知';

  // 如果已经是百分比格式（包含%），直接返回
  if (typeof rate === 'string' && rate.includes('%')) {
    return rate;
  }

  // 如果是数字，转换为百分比
  const numRate = parseFloat(rate);
  if (isNaN(numRate)) return '未知';

  // 如果数字大于1，认为已经是百分比数值
  if (numRate > 1) {
    return `${numRate}%`;
  }

  // 如果数字小于等于1，认为是小数，转换为百分比
  return `${(numRate * 100).toFixed(1)}%`;
}

// 打开物流方案选择弹窗
function openDialog() {
  // 验证是否已选择商品
  if (!props.selectedProducts || props.selectedProducts.length === 0) {
    // 使用Quasar的通知组件提示用户
    $q.notify({
      color: 'warning',
      message: '请先选择商品再选择物流方案',
      icon: 'warning',
      position: 'top',
    });
    return;
  }

  tempSelectedShipping.value = props.modelValue;
  shippingDialog.value = true;
}

// 检查是否选中
function isSelected(shipping) {
  if (!tempSelectedShipping.value || !shipping) return false;

  // 获取原始值，避免响应式对象比较问题
  const selected = toRaw(tempSelectedShipping.value);
  const current = toRaw(shipping);

  // 根据数据结构，使用 productId 和 priceId 来判断
  const result = selected.productId === current.productId && selected.priceId === current.priceId;

  return result;
}

// 选择物流方案
function selectShipping(shipping) {
  tempSelectedShipping.value = shipping;
}

// 切换展开/收起状态
async function toggleExpand(shipping) {
  const key = getShippingKey(shipping);

  // 如果当前是展开状态，则收起
  if (expandedItems.value[key]) {
    expandedItems.value[key] = false;
    return;
  }

  // 收起其他所有展开的项目（同时只能展开一行）
  Object.keys(expandedItems.value).forEach((k) => {
    expandedItems.value[k] = false;
  });

  // 展开当前项目
  expandedItems.value[key] = true;

  // 如果还没有获取过详细信息，则获取
  if (!shippingDetails.value[key]) {
    await fetchShippingDetail(shipping);
  }
}

// 获取物流方案详细信息
async function fetchShippingDetail(shipping) {
  const key = getShippingKey(shipping);

  try {
    loadingDetails.value[key] = true;

    // 调用API获取详细信息
    const { code, data } = await ShippingApi.getDetail(shipping.priceId);

    if (code === 0 && data) {
      // 转换API响应数据为前端需要的格式，参考运费试算页面的转换逻辑
      const transformedData = transformShippingDetail(data);
      shippingDetails.value[key] = transformedData;
    } else {
      console.error('获取物流详情失败:', code);
      // 设置一个默认的详情对象，避免一直显示加载状态
      shippingDetails.value[key] = {
        feeDetail: {
          freight: shipping.totalFee || 0,
          weight: 0,
          chargeableWeight: 0,
        },
      };
    }
  } catch (error) {
    console.error('获取物流详情失败:', error);
    // 设置一个默认的详情对象
    shippingDetails.value[key] = {
      feeDetail: {
        freight: shipping.totalFee || 0,
        weight: 0,
        chargeableWeight: 0,
      },
    };
  } finally {
    loadingDetails.value[key] = false;
  }
}

// 转换API响应数据为前端需要的格式
function transformShippingDetail(apiData) {
  const result = {
    feeDetail: {
      weight: apiData.feeDetail?.weight || 0,
      volumeWeight: apiData.feeDetail?.volumeWeight || 0,
      chargeableWeight: apiData.feeDetail?.chargeableWeight || 0,
      freight: parseFloat(apiData.feeDetail?.freight || '0'),
      operationFee: parseFloat(apiData.feeDetail?.operationFee || '0'),
      serviceFee: parseFloat(apiData.feeDetail?.serviceFee || '0'),
      customsFee: parseFloat(apiData.feeDetail?.customsFee || '0'),
      fuelFee: parseFloat(apiData.feeDetail?.fuelFee || '0'),
      total: parseFloat(apiData.feeDetail?.total || '0'),
    },
  };

  // 产品特色
  if (apiData.features) {
    result.features = apiData.features;
  }

  // 时效信息
  if (apiData.logisticsTimeliness || apiData.timelinessInfo) {
    result.timelinessInfo = apiData.logisticsTimeliness || apiData.timelinessInfo;
  }

  return result;
}

// 确认物流方案选择
function confirmShipping() {
  emit('update:modelValue', tempSelectedShipping.value);
  shippingDialog.value = false;
}

// 监听父组件传入的值变化
watch(
  () => props.modelValue,
  (newValue) => {
    tempSelectedShipping.value = newValue;
  },
  { immediate: true }
);

// 监听物流方案列表变化，确保选择最便宜的方案
watch(
  () => props.shippingMethods,
  (newMethods) => {
    if (!newMethods || newMethods.length === 0) {
      return;
    }

    // 如果当前没有选中的方案，自动选择最便宜的
    if (!props.modelValue) {
      const sortedMethods = [...newMethods].sort((a, b) => {
        const feeA = parseFloat(a.totalFee || 0);
        const feeB = parseFloat(b.totalFee || 0);
        return feeA - feeB;
      });

      // 通知父组件选择最便宜的方案
      emit('update:modelValue', sortedMethods[0]);
    } else {
      // 如果已经有选中的方案，检查是否需要更新价格信息
      const currentSelected = props.modelValue;
      const updatedPlan = newMethods.find((plan) => plan.productId === currentSelected.productId && plan.priceId === currentSelected.priceId);

      // 如果找到了对应的方案且价格有变化，更新选中的方案
      if (updatedPlan && updatedPlan.totalFee !== currentSelected.totalFee) {
        console.log('物流方案价格已更新，从', currentSelected.totalFee, '到', updatedPlan.totalFee);
        emit('update:modelValue', updatedPlan);
      }
    }
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
.shipping-selector-section {
  .q-card {
    border-radius: 8px;
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    }
  }
}

.shipping-dialog {
  border-radius: 12px;
  overflow: hidden;

  .dialog-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 16px 20px;

    .text-h6 {
      color: white;
      margin: 0;
      font-size: 1.1rem;
    }

    .close-btn {
      color: white;

      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
      }
    }
  }
}

.shipping-methods-container {
  max-height: 70vh;
  overflow-y: auto;
  padding: 0 4px;
}

.shipping-method-item {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  margin-bottom: 8px;
  transition: all 0.2s ease;
  background: white;
  overflow: hidden;

  &:hover {
    border-color: #3b82f6;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
  }

  &.selected {
    border-color: #3b82f6;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
  }

  &.expanded {
    border-color: #3b82f6;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
  }
}

.shipping-basic-row {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background: #f8fafc;
  }

  .shipping-method-item.selected & {
    background: #eff6ff;
  }
}

.selection-radio {
  flex-shrink: 0;
  margin-top: 2px;
}

.shipping-content {
  flex: 1;
  min-width: 0;
}

.shipping-main {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 6px;
}

.shipping-title {
  font-weight: 500;
  font-size: 15px;
  color: #1f2937;
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.recommend-badge {
  font-size: 10px;
  padding: 2px 6px;
}

.shipping-price {
  font-size: 16px;
  font-weight: 600;
  color: #dc2626;
  white-space: nowrap;
}

.expand-button {
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

.shipping-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.delivery-info {
  color: #6b7280;
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 4px;

  &.delivery-rate-info {
    min-width: 100px; // 固定妥投率位置，避免与其他标签重叠
  }
}

.service-tags {
  display: flex;
  gap: 4px;
}

.service-tag {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

.service-tag.tax-included {
  background: #dcfce7;
  color: #166534;
}

.service-tag.free-insurance {
  background: #dbeafe;
  color: #1e40af;
}

/* 详细信息展开区域样式 */
.shipping-details-expanded {
  background-color: #fafafa;
  border-top: 1px solid #e5e7eb;

  .details-content {
    background-color: #fff;
    margin: 0;
    border-radius: 0 0 8px 8px;
  }

  .fee-breakdown {
    .fee-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 4px 0;

      &.total-fee {
        padding-top: 8px;
        font-size: 1.1rem;
      }
    }

    .fee-value {
      font-weight: 500;
      color: #1976d2;
    }
  }

  .weight-info {
    .weight-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 4px 0;
    }

    .weight-value {
      font-weight: 500;
      color: #1976d2;
    }
  }

  .features-content {
    background-color: #f8f9fa;
    padding: 12px;
    border-radius: 8px;
    border-left: 4px solid #1976d2;
  }

  .timeliness-chart {
    .delivery-rate {
      padding: 8px 12px;
      background-color: #e8f5e8;
      border-radius: 6px;
    }

    .timeliness-bars {
      .timeliness-bar-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 6px 0;

        .time-interval {
          min-width: 60px;
          font-size: 0.875rem;
          color: #666;
        }

        .progress-container {
          flex: 1;
        }

        .rate-value {
          min-width: 40px;
          text-align: right;
          font-weight: 500;
          color: #1976d2;
        }
      }
    }
  }
}

/* 弹窗样式优化 */
.q-dialog .q-card {
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  max-width: 500px;
  width: 90vw;
}

.q-card-section {
  padding: 16px 20px;
}

.q-card-actions {
  padding: 12px 20px 16px;
  gap: 8px;
}

/* 移动端适配 */
@media (max-width: 600px) {
  .shipping-methods-container {
    max-height: 60vh;
  }

  .shipping-basic-row {
    padding: 10px;
    gap: 8px;
  }

  .shipping-main {
    flex-direction: column;
    gap: 4px;
  }

  .shipping-price {
    align-self: flex-end;
  }

  .shipping-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .shipping-details-expanded {
    .details-content {
      padding: 12px;
    }

    .row {
      .col-md-6 {
        margin-bottom: 16px;
      }
    }

    .timeliness-chart {
      .timeliness-bars {
        .timeliness-bar-item {
          flex-direction: column;
          align-items: stretch;
          gap: 4px;

          .time-interval {
            min-width: auto;
            text-align: center;
          }

          .rate-value {
            text-align: center;
          }
        }
      }
    }
  }
}
</style>
