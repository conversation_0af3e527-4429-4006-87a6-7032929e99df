<template>
  <div class="transfer-detail q-pa-md">
    <!-- 标题区域 -->
    <div class="bg-grey-2 q-px-md q-py-sm rounded-borders">
      <div class="row justify-between items-center">
        <div class="row items-center">
          <q-btn flat dense color="primary" icon="arrow_back" @click="goBack" class="q-mr-sm print-hide"></q-btn>
          <span class="text-subtitle1 text-weight-medium">{{ $t('transfer.common.transferDetail') }}</span>
        </div>
        <div class="row items-center print-hide">
          <q-btn flat dense color="primary" icon="print" @click="printTransfer" class="q-mr-sm" :label="$t('transfer.detail.print')"></q-btn>
          <q-btn flat dense color="primary" to="/account/transfer" :label="$t('transfer.common.backToList')" class="q-px-sm"></q-btn>
        </div>
      </div>
    </div>

    <!-- 加载中状态 -->
    <div v-if="loading" class="column items-center q-py-xl">
      <q-spinner color="primary" size="3em"></q-spinner>
      <div class="q-mt-sm text-grey-7">{{ $t('transfer.common.loading') }}</div>
    </div>

    <!-- 转运单不存在 -->
    <div v-else-if="!transferDetail" class="column items-center q-py-xl">
      <q-icon name="error_outline" size="3em" color="grey-5"></q-icon>
      <div class="q-mt-sm text-grey-7">{{ $t('transfer.common.noData') }}</div>
      <q-btn color="primary" :label="$t('transfer.common.returnToList')" class="q-mt-md" to="/account/transfer"></q-btn>
    </div>

    <!-- 详情内容 -->
    <div v-else class="detail-container">
      <!-- 基本信息卡片 -->
      <q-card class="q-mb-md detail-card">
        <q-card-section class="bg-grey-2">
          <div class="row justify-between items-center">
            <div class="text-subtitle2 text-weight-medium">{{ $t('transfer.detail.basicInfo') }}</div>
            <div class="row print-hide">
              <q-btn v-if="transferDetail.status === 0" flat round dense color="primary" icon="edit" @click="editTransfer" class="q-mr-xs">
                <q-tooltip>{{ $t('transfer.common.edit') }}</q-tooltip>
              </q-btn>
              <q-btn v-if="transferDetail.status === -1" flat round dense color="primary" icon="delete" @click="deleteTransfer">
                <q-tooltip>{{ $t('transfer.common.delete') }}</q-tooltip>
              </q-btn>
            </div>
          </div>
        </q-card-section>
        <q-card-section>
          <div class="row q-col-gutter-md">
            <div class="col-12 col-sm-6 col-md-4">
              <div class="row">
                <div class="col-5 text-grey-7">{{ $t('transfer.common.transferNumber') }}</div>
                <div class="col-7 row items-center">
                  <span class="ellipsis text-weight-medium">{{ transferDetail.no }}</span>
                  <q-btn flat dense round color="primary" icon="content_copy" size="xs" class="q-ml-xs print-hide" @click="copyToClipboard(transferDetail.no)">
                    <q-tooltip>{{ $t('transfer.common.copySuccess') }}</q-tooltip>
                  </q-btn>
                </div>
              </div>
            </div>

            <div class="col-12 col-sm-6 col-md-4">
              <div class="row">
                <div class="col-5 text-grey-7">{{ $t('transfer.common.createTime') }}</div>
                <div class="col-7">{{ formatDateTime(transferDetail.createTime) }}</div>
              </div>
            </div>

            <div class="col-12 col-sm-6 col-md-4">
              <div class="row">
                <div class="col-5 text-grey-7">{{ $t('transfer.edit.targetWarehouse') }}</div>
                <div class="col-7 ellipsis">{{ transferDetail.warehouseName }}</div>
              </div>
            </div>

            <div class="col-12 col-sm-6 col-md-4">
              <div class="row">
                <div class="col-5 text-grey-7">{{ $t('transfer.common.status') }}</div>
                <div class="col-7">
                  <q-badge :color="getStatusColor(transferDetail.status)" class="q-py-xs q-px-sm status-badge">
                    {{ getStatusText(transferDetail.status) }}
                  </q-badge>
                </div>
              </div>
            </div>

            <div class="col-12">
              <div class="row">
                <div class="col-12 col-sm-2 text-grey-7">{{ $t('transfer.detail.remarkInfo') }}</div>
                <div class="col-12 col-sm-10 word-break-all">
                  {{ transferDetail.remark || $t('transfer.common.noRemark') }}
                </div>
              </div>
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- 物品清单卡片 -->
      <q-card class="q-mb-md detail-card product-card">
        <q-card-section class="bg-grey-2">
          <div class="text-subtitle2 text-weight-medium">{{ $t('transfer.detail.itemList') }}</div>
        </q-card-section>
        <q-card-section>
          <!-- 桌面视图 - 表格 -->
          <div class="desktop-view">
            <q-table :rows="transferDetail.items || []" :columns="itemColumns" row-key="id" flat bordered hide-pagination :pagination="{ rowsPerPage: 0 }" class="items-table">
              <template #body-cell-categoryId="props">
                <q-td :props="props">
                  {{ props.value }}
                </q-td>
              </template>

              <template #body-cell-productName="props">
                <q-td :props="props">
                  {{ props.value }}
                </q-td>
              </template>

              <template #body-cell-count="props">
                <q-td :props="props" class="text-center"> {{ props.value }} 件 </q-td>
              </template>

              <template #body-cell-weight="props">
                <q-td :props="props" class="text-center">
                  {{ props.value ? props.value + ' kg' : '未知' }}
                </q-td>
              </template>

              <template #body-cell-price="props">
                <q-td :props="props" class="text-right">
                  {{ props.value ? '¥' + fen2yuan(props.value) : '未知' }}
                </q-td>
              </template>
            </q-table>
          </div>

          <!-- 移动视图 - 卡片列表 -->
          <div class="mobile-view">
            <div v-for="(item, index) in transferDetail.items" :key="index" class="q-mb-md product-card">
              <div class="row justify-between items-center q-mb-sm">
                <div class="text-subtitle2">{{ $t('transfer.create.product') }} #{{ index + 1 }}</div>
              </div>

              <div class="row q-col-gutter-sm">
                <div class="col-12">
                  <div class="text-caption text-grey-7">{{ $t('transfer.create.productNameLabel') }}</div>
                  <div class="info-value">{{ item.productName }}</div>
                </div>

                <div class="col-6">
                  <div class="text-caption text-grey-7">{{ $t('transfer.create.categoryLabel') }}</div>
                  <div class="info-value">{{ item.categoryId }}</div>
                </div>

                <div class="col-6">
                  <div class="text-caption text-grey-7">{{ $t('transfer.create.quantityLabel') }}</div>
                  <div class="info-value">{{ item.count }} {{ $t('transfer.detail.pieces') }}</div>
                </div>

                <div class="col-6">
                  <div class="text-caption text-grey-7">{{ $t('transfer.create.weightLabel') }}</div>
                  <div class="info-value">{{ item.weight ? item.weight + ' kg' : $t('transfer.common.unknown') }}</div>
                </div>

                <div class="col-6">
                  <div class="text-caption text-grey-7">{{ $t('transfer.create.priceLabel') }}</div>
                  <div class="info-value">{{ item.price ? '¥' + fen2yuan(item.price) : $t('transfer.common.unknown') }}</div>
                </div>
              </div>
            </div>
          </div>

          <div class="row justify-center justify-md-end q-mt-md" v-if="transferDetail.status === 10">
            <q-btn color="primary" :label="$t('transfer.detail.applyForShipment')" icon-left="send" @click="applyForShipment"></q-btn>
          </div>
        </q-card-section>
      </q-card>

      <!-- 快递信息卡片 -->
      <q-card class="q-mb-md detail-card">
        <q-card-section class="bg-grey-2">
          <div class="row justify-between items-center">
            <div class="text-subtitle2 text-weight-medium">{{ $t('transfer.detail.expressInfo') }}</div>
            <q-btn outline color="primary" :label="$t('transfer.detail.trackExpress')" icon-left="search" dense class="track-btn print-hide" @click="trackExpress"></q-btn>
          </div>
        </q-card-section>
        <q-card-section>
          <div class="row q-col-gutter-md">
            <div class="col-12 col-sm-6 col-md-4">
              <div class="row">
                <div class="col-5 text-grey-7">{{ $t('transfer.common.expressCompany') }}</div>
                <div class="col-7 ellipsis">{{ transferDetail.logisticsId }}</div>
              </div>
            </div>

            <div class="col-12 col-sm-6 col-md-4">
              <div class="row">
                <div class="col-5 text-grey-7">{{ $t('transfer.common.expressNumber') }}</div>
                <div class="col-7 row items-center">
                  <span class="ellipsis">{{ transferDetail.logisticsNo }}</span>
                  <q-btn flat dense round color="primary" icon="content_copy" size="xs" class="q-ml-xs print-hide" @click="copyToClipboard(transferDetail.logisticsNo)">
                    <q-tooltip>{{ $t('transfer.common.copyNumber') }}</q-tooltip>
                  </q-btn>
                </div>
              </div>
            </div>

            <div class="col-12">
              <div class="row">
                <div class="col-12 col-sm-2 text-grey-7">{{ $t('transfer.detail.expressRemark') }}</div>
                <div class="col-12 col-sm-10 word-break-all">
                  {{ transferDetail.logisticsRemark || $t('transfer.common.noRemark') }}
                </div>
              </div>
            </div>
          </div>
        </q-card-section>
      </q-card>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useQuasar } from 'quasar';
import { useI18n } from 'vue-i18n';
import TransferApi from '~/composables/transferApi';
import { fen2yuan } from '~/utils/utils';

definePageMeta({
  middleware: 'auth', // 引入身份验证中间件
});

const $q = useQuasar();
const route = useRoute();
const router = useRouter();
const { t } = useI18n();
const transferId = route.query.id;

// 数据状态
const loading = ref(true);
const transferDetail = ref(null);

// 物品表格列定义
const itemColumns = [
  { name: 'categoryId', align: 'left', label: t('transfer.create.categoryLabel'), field: 'categoryId' },
  { name: 'productName', align: 'left', label: t('transfer.create.productNameLabel'), field: 'productName' },
  { name: 'count', align: 'center', label: t('transfer.create.quantityLabel'), field: 'count' },
  { name: 'weight', align: 'center', label: t('transfer.create.weightLabel'), field: 'weight' },
  { name: 'price', align: 'right', label: t('transfer.create.priceLabel'), field: 'price' },
];

// 页面加载时获取数据
onMounted(async () => {
  await getTransferDetail();
});

// 获取转运单详情
async function getTransferDetail() {
  loading.value = true;

  try {
    const { code, data } = await TransferApi.getTransferDetail(transferId);

    if (code === 0 && data) {
      transferDetail.value = data;
    }
  } finally {
    loading.value = false;
  }
}

// 获取状态文本
function getStatusText(status) {
  switch (status) {
    case 0:
      return t('transfer.status.pending');
    case 10:
      return t('transfer.status.inWarehouse');
    case 20:
      return t('transfer.status.readyToShip');
    case 30:
      return t('transfer.status.shipped');
    case 40:
      return t('transfer.status.completed');
    case -1:
      return t('transfer.status.cancelled');
    default:
      return t('transfer.status.unknown');
  }
}

// 获取状态颜色
function getStatusColor(status) {
  switch (status) {
    case 0:
      return 'orange';
    case 10:
      return 'blue';
    case 20:
      return 'teal';
    case 30:
      return 'purple';
    case 40:
      return 'positive';
    case -1:
      return 'grey';
    default:
      return 'grey';
  }
}

// 格式化日期时间
function formatDateTime(timestamp) {
  if (!timestamp) return '未知';
  const date = new Date(timestamp);
  return date
    .toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    })
    .replace(/\//g, '-');
}

// 复制到剪贴板
function copyToClipboard(text) {
  navigator.clipboard.writeText(text).then(
    () => {
      $q.notify({
        type: 'positive',
        message: t('transfer.common.copySuccess'),
        timeout: 1000,
      });
    },
    () => {
      $q.notify({
        type: 'negative',
        message: t('transfer.common.copyFailed'),
      });
    }
  );
}

// 查询物流
function trackExpress() {
  if (!transferDetail.value || !transferDetail.value.logisticsNo) {
    $q.notify({
      type: 'warning',
      message: t('transfer.detail.expressNumberNotExist'),
    });
    return;
  }

  // 这里可以根据不同的快递公司跳转到对应的查询页面
  // 这里以通用的快递100为例
  const url = `https://www.kuaidi100.com/chaxun?nu=${transferDetail.value.logisticsNo}`;
  window.open(url, '_blank');
}

// 申请发货
function applyForShipment() {
  $q.dialog({
    title: t('transfer.detail.applyForShipment'),
    message: t('transfer.detail.confirmApplyForShipment'),
    cancel: true,
    persistent: true,
  }).onOk(() => {
    // 这里可以调用申请发货的API
    $q.notify({
      type: 'positive',
      message: t('transfer.detail.applyForShipmentSuccess'),
    });
  });
}

// 编辑转运单
function editTransfer() {
  if (!transferDetail.value || transferDetail.value.status !== 0) {
    $q.notify({
      type: 'warning',
      message: t('transfer.detail.onlyPendingCanEdit'),
    });
    return;
  }

  // 跳转到编辑页面，并传递转运单ID
  router.push({
    path: '/account/transfer-edit',
    query: { id: transferId },
  });
}

// 返回列表页
function goBack() {
  router.push('/account/transfer');
}

// 打印转运单
function printTransfer() {
  // 保存当前页面的滚动位置
  const scrollPosition = window.scrollY;

  // 保存原始文档标题
  const originalTitle = document.title;

  // 设置文档标题为转运单编号，这将成为默认的PDF文件名
  if (transferDetail.value && transferDetail.value.no) {
    // 根据当前语言环境设置不同的前缀
    const prefix = t('transfer.common.transferDetail');
    document.title = `${prefix}_${transferDetail.value.no}`;
  }

  // 添加打印样式
  const style = document.createElement('style');
  style.id = 'print-style';
  style.innerHTML = `
    @media print {
      body * {
        visibility: hidden;
      }
      .transfer-detail, .transfer-detail * {
        visibility: visible;
      }
      .transfer-detail {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
      }
      .q-btn, .print-hide {
        display: none !important;
      }
      .detail-card {
        box-shadow: none !important;
        border: 1px solid #ddd;
        margin-bottom: 15px !important;
        page-break-inside: avoid;
      }
    }
  `;
  document.head.appendChild(style);

  // 调用打印功能
  window.print();

  // 打印完成后移除打印样式并恢复原始标题
  setTimeout(() => {
    const printStyle = document.getElementById('print-style');
    if (printStyle) {
      printStyle.remove();
    }
    // 恢复原始文档标题
    document.title = originalTitle;
    // 恢复滚动位置
    window.scrollTo(0, scrollPosition);
  }, 500);
}

// 删除转运单
function deleteTransfer() {
  if (!transferDetail.value || transferDetail.value.status !== -1) {
    $q.notify({
      type: 'warning',
      message: t('transfer.detail.onlyCancelledCanDelete'),
    });
    return;
  }

  $q.dialog({
    title: t('transfer.detail.confirmDelete'),
    message: t('transfer.detail.confirmDeleteMessage'),
    color: 'negative',
    cancel: true,
    persistent: true,
  }).onOk(async () => {
    try {
      const { code, msg } = await TransferApi.deleteTransfer(transferId);
      if (code === 0) {
        $q.notify({
          type: 'positive',
          message: t('transfer.detail.deleteSuccess'),
        });
        // 删除成功后返回列表页
        router.push('/account/transfer');
      } else {
        $q.notify({
          type: 'negative',
          message: msg || t('transfer.detail.deleteFailed'),
        });
      }
    } catch (error) {
      console.error('删除转运单出错:', error);
      $q.notify({
        type: 'negative',
        message: t('transfer.errors.deleteError'),
      });
    }
  });
}
</script>

<style lang="scss" scoped>
.transfer-detail {
  .detail-container {
    max-width: 1200px;
    margin: 0 auto;
  }

  .detail-card {
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
    }

    .card-header {
      font-size: 0.95rem;
    }
  }

  .info-item {
    .text-caption {
      font-size: 0.8rem;
      margin-bottom: 2px;
    }

    .info-value {
      font-size: 0.95rem;
    }
  }

  .status-badge {
    font-size: 0.95rem;
    font-weight: 500;

    @media (max-width: 599px) {
      font-size: 0.85rem;
      padding: 2px 6px;
    }
  }

  .status-timeline {
    padding: 0;
    font-size: 0.9rem;

    :deep(.q-timeline__entry) {
      padding-left: 40px;
    }

    :deep(.q-timeline__entry-icon) {
      font-size: 1.2rem;
    }

    :deep(.q-timeline__entry-title) {
      font-weight: 500;
      font-size: 0.9rem;
    }

    :deep(.q-timeline__entry-subtitle) {
      font-size: 0.8rem;
    }
  }

  .items-table {
    :deep(th) {
      font-weight: 500;
      font-size: 0.9rem;
      padding: 8px 12px;
    }

    :deep(td) {
      font-size: 0.9rem;
      padding: 8px 12px;
    }
  }

  // 响应式样式
  .desktop-view {
    @media (max-width: 599px) {
      display: none;
    }
  }

  .mobile-view {
    @media (min-width: 600px) {
      display: none;
    }
  }

  .product-card {
    background-color: rgba(0, 0, 0, 0.02);
    border-radius: 4px;
    padding: 12px;

    .text-caption {
      font-size: 0.8rem;
      margin-bottom: 2px;
    }

    .info-value {
      font-size: 0.95rem;
      margin-bottom: 8px;
    }
  }

  .back-btn {
    @media (max-width: 599px) {
      padding: 4px 8px;

      .q-icon {
        font-size: 1.2em;
      }
    }
  }

  .track-btn {
    @media (max-width: 599px) {
      width: 100%;
    }
  }

  .ellipsis {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
  }
}

// 打印样式
@media print {
  .print-hide {
    display: none !important;
  }

  .transfer-detail {
    padding: 0 !important;

    .detail-card {
      margin-bottom: 20px;
      page-break-inside: avoid;
      box-shadow: none !important;
      border: 1px solid #ddd;
    }

    .q-pa-md {
      padding: 10px !important;
    }

    .card-header {
      background-color: #1976d2 !important;
      color: white !important;
      -webkit-print-color-adjust: exact !important;
      print-color-adjust: exact !important;
    }

    .bg-grey-2 {
      background-color: #f5f5f5 !important;
      -webkit-print-color-adjust: exact !important;
      print-color-adjust: exact !important;
    }

    .status-badge {
      -webkit-print-color-adjust: exact !important;
      print-color-adjust: exact !important;
    }

    .items-table {
      border: 1px solid #ddd;

      th {
        background-color: #f5f5f5 !important;
        -webkit-print-color-adjust: exact !important;
        print-color-adjust: exact !important;
      }
    }

    .product-card {
      border: 1px solid #ddd;
    }
  }
}
</style>
