<template>
  <div class="row items-center">
    <q-btn outline color="primary" class="q-px-sm">
      {{ modelValue && modelValue.from && modelValue.to ? formatDateRange(modelValue.from, modelValue.to) : '选择日期' }}
      <q-icon name="arrow_drop_down" class="q-ml-xs" />
      <q-menu anchor="bottom right" self="top right">
        <q-date v-model="innerValue" range minimal :options="dateOptions" mask="YYYY-MM-DD" @update:model-value="handleDateUpdate">
          <div class="row items-center justify-end q-pa-sm">
            <q-btn label="取消" color="primary" flat v-close-popup />
            <q-btn label="确定" color="primary" flat @click="onConfirmAndSearch" v-close-popup />
          </div>
        </q-date>
      </q-menu>
    </q-btn>
    <q-btn color="primary" icon="search" class="q-ml-sm" @click="onSearch" />
  </div>
</template>

<script>
import { ref, watch } from 'vue';
import { date } from 'quasar';
import { formatDateRange } from '~/utils/dateUtil';

export default {
  name: 'DateRangeSelector',
  props: {
    modelValue: {
      type: Object,
      required: true,
      validator: (value) => {
        return value && typeof value === 'object' && 'from' in value && 'to' in value;
      },
    },
  },
  emits: ['update:modelValue', 'search'],
  setup(props, { emit }) {
    // 内部日期值，用于绑定到q-date组件
    const innerValue = ref(props.modelValue ? { ...props.modelValue } : { from: '', to: '' });

    // 监听props.modelValue的变化，更新innerValue
    watch(
      () => props.modelValue,
      (newVal) => {
        if (newVal) {
          innerValue.value = { ...newVal };
        }
      },
      { deep: true }
    );

    // 监听innerValue的变化，处理边缘情况
    watch(
      innerValue,
      (newVal) => {
        // 首先检查newVal是否为null或undefined
        if (!newVal) {
          // 如果newVal为null或undefined，设置为当前日期
          const today = date.formatDate(new Date(), 'YYYY-MM-DD');
          innerValue.value = {
            from: today,
            to: today,
          };
          emit('update:modelValue', { ...innerValue.value });
          return;
        }

        // 确保from和to都有值
        if (newVal.from === undefined && newVal.to === undefined) {
          // 如果两个值都是undefined，设置为当前日期
          const today = date.formatDate(new Date(), 'YYYY-MM-DD');
          innerValue.value = {
            from: today,
            to: today,
          };
          emit('update:modelValue', { ...innerValue.value });
        } else if (newVal.from && newVal.to === undefined) {
          // 如果from有值但to没有，将to设置为与from相同
          innerValue.value.to = newVal.from;
          emit('update:modelValue', { ...innerValue.value });
        } else if (newVal.from === undefined && newVal.to) {
          // 如果to有值但from没有，将from设置为与to相同
          innerValue.value.from = newVal.to;
          emit('update:modelValue', { ...innerValue.value });
        } else if (newVal.from && newVal.to && newVal.from === newVal.to) {
          // 如果是单天选择，自动触发搜索
          setTimeout(() => {
            emit('search');
          }, 100);
        }
      },
      { deep: true }
    );

    // 日期选择器选项 限制只能选择今天及之前的日期
    const dateOptions = (date) => {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const selectedDate = new Date(date);
      selectedDate.setHours(0, 0, 0, 0);
      return selectedDate <= today;
    };

    // 日期更新处理
    function handleDateUpdate(val) {
      console.log('handleDateUpdate', val);
      // 首先检查val是否为null或undefined
      if (val === null || val === undefined) {
        // 如果val为null或undefined，设置为当前日期
        const today = date.formatDate(new Date(), 'YYYY-MM-DD');
        innerValue.value = {
          from: today,
          to: today,
        };
        emit('update:modelValue', { ...innerValue.value });
        return;
      }

      // 如果是单天选择，将to设置为与from相同的值
      if (typeof val === 'string') {
        // 单天选择
        innerValue.value = {
          from: val,
          to: val,
        };
        emit('update:modelValue', { ...innerValue.value });
        // 单天选择后自动触发搜索
        setTimeout(() => {
          emit('search');
        }, 100);
      }
    }

    // 确认按钮点击事件
    function onConfirm() {
      emit('update:modelValue', { ...innerValue.value });
    }

    // 确认并搜索事件
    function onConfirmAndSearch() {
      emit('update:modelValue', { ...innerValue.value });
      // 使用setTimeout确保在更新模型值后再触发搜索
      setTimeout(() => {
        emit('search');
      }, 100);
    }

    // 搜索按钮点击事件
    function onSearch() {
      emit('search');
    }

    return {
      innerValue,
      dateOptions,
      handleDateUpdate,
      onConfirm,
      onConfirmAndSearch,
      onSearch,
      formatDateRange,
    };
  },
};
</script>
