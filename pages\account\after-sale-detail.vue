<template>
  <div class="after-sale-detail-page">
    <!-- 标题区域 -->
    <div class="bg-grey-2 q-px-md q-py-sm rounded-borders">
      <div class="row justify-between items-center">
        <div class="row items-center">
          <q-btn flat dense color="primary" icon="arrow_back" @click="router.push('/account/after-sale')" class="q-mr-sm" />
          <span class="text-subtitle1 text-weight-medium">售后详情</span>
        </div>
        <q-btn flat dense color="primary" to="/account/after-sale" label="返回列表" class="q-px-sm" />
      </div>
    </div>

    <div v-if="loading" class="text-center q-py-xl">
      <q-spinner color="primary" size="40px" />
      <div class="text-grey-6 q-mt-md">加载中...</div>
    </div>

    <div v-else-if="!afterSaleDetail.id" class="text-center q-py-xl">
      <q-icon name="receipt_long" size="64px" color="grey-4" />
      <div class="text-grey-6 q-mt-md">暂无该订单售后详情</div>
    </div>

    <div v-else class="q-pa-md">
      <!-- 步骤条 -->
      <q-card class="q-mb-md">
        <q-card-section class="q-pa-md">
          <div class="text-subtitle2 text-weight-medium q-mb-md">处理进度</div>
          <q-stepper v-model="stepperModel" color="primary" :flat="true" :bordered="false" class="after-sale-stepper">
            <q-step
              v-for="(step, index) in steps.steps"
              :key="index"
              :name="index + 1"
              :title="step.title"
              :icon="getStepIcon(index)"
              :done="steps.active > index"
              :active="steps.active === index"
              :error="isFailedStatus && steps.active === index" />
          </q-stepper>
        </q-card-section>
      </q-card>

      <!-- 服务状态 -->
      <q-card class="q-mb-md cursor-pointer" @click="viewLogs">
        <q-card-section class="q-pa-md">
          <div class="row justify-between items-center">
            <div class="column">
              <div class="text-subtitle2 text-weight-medium">
                {{ formatAfterSaleStatusDescription(afterSaleDetail) }}
              </div>
              <div class="text-caption text-grey-7 q-mt-xs">
                {{ formatDateTime(afterSaleDetail.updateTime) }}
              </div>
            </div>
            <q-icon name="arrow_forward" color="grey-6" />
          </div>
        </q-card-section>
      </q-card>

      <!-- 退款金额 -->
      <q-card class="q-mb-md">
        <q-card-section class="q-pa-md">
          <div class="row justify-between items-center">
            <div class="text-subtitle2 text-weight-medium">退款总额</div>
            <div class="text-h6 text-primary text-weight-medium">
              {{ formatAmount(afterSaleDetail.refundPrice) }}
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- 服务商品 -->
      <q-card class="q-mb-md">
        <q-card-section class="q-pa-md">
          <div class="text-subtitle2 text-weight-medium q-mb-md">售后商品</div>
          <div class="row no-wrap items-start">
            <!-- 商品图片 -->
            <div class="product-image q-mr-md">
              <q-img :src="afterSaleDetail.picUrl || '/images/placeholder.png'" style="width: 80px; height: 80px" class="rounded-borders" fit="cover" spinner-color="primary" />
            </div>
            <!-- 商品信息 -->
            <div class="column justify-between flex-1">
              <div class="text-body1 text-weight-medium q-mb-xs">{{ afterSaleDetail.spuName }}</div>
              <div class="text-caption text-grey-7 q-mb-sm">
                {{ afterSaleDetail.properties?.map((property) => property.valueName).join(' ') }}
              </div>
              <div class="row justify-between items-center">
                <div class="text-body2 text-weight-medium text-primary">
                  {{ formatAmount(afterSaleDetail.refundPrice) }}
                </div>
                <div class="text-caption text-grey-7">数量: {{ afterSaleDetail.count }}</div>
              </div>
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- 服务内容 -->
      <q-card class="q-mb-md">
        <q-card-section class="q-pa-md">
          <div class="text-subtitle2 text-weight-medium q-mb-md">服务信息</div>

          <div class="column q-gutter-sm">
            <div class="row justify-between items-center">
              <div class="text-body2 text-grey-8">服务单号</div>
              <div class="row items-center">
                <span class="text-body2 text-weight-medium q-mr-sm">{{ afterSaleDetail.no }}</span>
                <q-btn flat dense size="sm" color="primary" icon="content_copy" @click="copyOrderNo" />
              </div>
            </div>

            <q-separator />

            <div class="row justify-between items-center">
              <div class="text-body2 text-grey-8">申请时间</div>
              <div class="text-body2 text-weight-medium">
                {{ formatDateTime(afterSaleDetail.createTime) }}
              </div>
            </div>

            <q-separator />

            <div class="row justify-between items-center">
              <div class="text-body2 text-grey-8">售后类型</div>
              <div class="text-body2 text-weight-medium">
                {{ formatAfterSaleWay(afterSaleDetail.way) }}
              </div>
            </div>

            <q-separator />

            <div class="row justify-between items-start">
              <div class="text-body2 text-grey-8">申请原因</div>
              <div class="text-body2 text-weight-medium text-right">
                {{ afterSaleDetail.applyReason }}
              </div>
            </div>

            <q-separator />

            <div class="row justify-between items-start">
              <div class="text-body2 text-grey-8">相关描述</div>
              <div class="text-body2 text-weight-medium text-right">
                {{ afterSaleDetail.applyDescription || '无' }}
              </div>
            </div>

            <!-- 申请图片 -->
            <div v-if="afterSaleDetail.applyPicUrls && afterSaleDetail.applyPicUrls.length > 0">
              <q-separator />
              <div class="row justify-between items-start">
                <div class="text-body2 text-grey-8">申请图片</div>
                <div class="row q-gutter-xs">
                  <q-img
                    v-for="(image, index) in afterSaleDetail.applyPicUrls"
                    :key="index"
                    :src="image"
                    style="width: 60px; height: 60px"
                    class="rounded-borders cursor-pointer"
                    fit="cover"
                    @click="previewImage(image)" />
                </div>
              </div>
            </div>
          </div>
        </q-card-section>
      </q-card>
    </div>

    <!-- 底部操作按钮 -->
    <div v-if="afterSaleDetail.id" class="fixed-bottom bg-white q-pa-md">
      <div class="row q-gutter-md">
        <q-btn v-if="afterSaleDetail.buttons?.includes('cancel')" outline color="negative" label="取消申请" class="col" @click="onCancel" />
        <q-btn v-if="afterSaleDetail.buttons?.includes('delivery')" outline color="primary" label="填写退货" class="col" @click="onDelivery" />
        <q-btn outline color="primary" label="联系客服" class="col" @click="contactService" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useQuasar } from 'quasar';
import AfterSaleApi from '~/composables/afterSaleApi';
import { useCurrency } from '~/composables/useCurrency';
import { formatAfterSaleStatusDescription, formatAfterSaleWay, handleAfterSaleButtons, getAfterSaleSteps, isAfterSaleFailedStatus } from '~/utils/afterSaleUtils';
import { formatDateTime } from '~/utils/dateUtil';

definePageMeta({
  middleware: 'auth', // 引入身份验证中间件
});

const router = useRouter();
const route = useRoute();
const $q = useQuasar();
const { formatAmount } = useCurrency();

const loading = ref(true);
const afterSaleDetail = ref({});

// 获取步骤信息
const steps = computed(() => getAfterSaleSteps(afterSaleDetail.value));

// 步骤器模型
const stepperModel = computed(() => steps.value.active + 1);

// 是否为失败状态
const isFailedStatus = computed(() => isAfterSaleFailedStatus(afterSaleDetail.value.status));

// 获取步骤图标
function getStepIcon(index) {
  if (steps.value.active > index) {
    return 'check_circle';
  } else if (steps.value.active === index) {
    if (isFailedStatus.value) {
      return 'cancel';
    }
    return 'radio_button_checked';
  } else {
    return 'radio_button_unchecked';
  }
}

// 获取售后详情
async function getDetail() {
  loading.value = true;

  try {
    const afterSaleId = route.query.id;
    if (!afterSaleId) {
      $q.notify({
        color: 'negative',
        message: '缺少售后信息，请检查',
        icon: 'error',
      });
      router.push('/account/after-sale');
      return;
    }

    const { code, data } = await AfterSaleApi.getAfterSale(afterSaleId);
    if (code !== 0) {
      afterSaleDetail.value = {};
      $q.notify({
        color: 'negative',
        message: '获取售后详情失败',
        icon: 'error',
      });
      return;
    }

    afterSaleDetail.value = data;
    handleAfterSaleButtons(afterSaleDetail.value);
  } catch (error) {
    console.error('获取售后详情失败:', error);
    afterSaleDetail.value = {};
    $q.notify({
      color: 'negative',
      message: '获取售后详情失败',
      icon: 'error',
    });
  } finally {
    loading.value = false;
  }
}

// 取消售后申请
function onCancel() {
  $q.dialog({
    title: '确认取消',
    message: '确定要取消此售后申请吗？',
    cancel: true,
    persistent: true,
  }).onOk(async () => {
    try {
      const { code } = await AfterSaleApi.cancelAfterSale(afterSaleDetail.value.id);
      if (code === 0) {
        $q.notify({
          color: 'positive',
          message: '取消成功',
          icon: 'check_circle',
        });
        // 重新获取详情
        await getDetail();
      }
    } catch (error) {
      console.error('取消售后申请失败:', error);
      $q.notify({
        color: 'negative',
        message: '取消失败，请重试',
        icon: 'error',
      });
    }
  });
}

// 填写退货信息
function onDelivery() {
  // TODO: 实现填写退货信息功能
  $q.notify({
    color: 'info',
    message: '填写退货信息功能开发中',
    icon: 'info',
  });
}

// 联系客服
function contactService() {
  router.push('/chat');
}

// 复制订单号
function copyOrderNo() {
  if (navigator.clipboard) {
    navigator.clipboard
      .writeText(afterSaleDetail.value.no)
      .then(() => {
        $q.notify({
          color: 'positive',
          message: '复制成功',
          icon: 'content_copy',
        });
      })
      .catch(() => {
        $q.notify({
          color: 'negative',
          message: '复制失败',
          icon: 'error',
        });
      });
  } else {
    // 降级方案
    const textArea = document.createElement('textarea');
    textArea.value = afterSaleDetail.value.no;
    document.body.appendChild(textArea);
    textArea.select();
    try {
      document.execCommand('copy');
      $q.notify({
        color: 'positive',
        message: '复制成功',
        icon: 'content_copy',
      });
    } catch (_err) {
      console.error('复制失败', _err);
      $q.notify({
        color: 'negative',
        message: '复制失败',
        icon: 'error',
      });
    }
    document.body.removeChild(textArea);
  }
}

// 预览图片
function previewImage(image) {
  $q.dialog({
    title: '图片预览',
    message: `<img src="${image}" style="max-width: 100%; max-height: 400px;" />`,
    html: true,
  });
}

// 查看日志
function viewLogs() {
  // TODO: 实现查看售后日志功能
  $q.notify({
    color: 'info',
    message: '查看日志功能开发中',
    icon: 'info',
  });
}

onMounted(() => {
  getDetail();
});
</script>

<style lang="scss" scoped>
.after-sale-detail-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 80px; // 为底部按钮留出空间
}

.product-image {
  flex-shrink: 0;
}

.after-sale-stepper {
  :deep(.q-stepper__step-inner) {
    padding: 8px 0;
  }

  :deep(.q-stepper__dot) {
    margin: 0 8px 0 0;
  }
}

.fixed-bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}

// 移动端适配
@media (max-width: 599px) {
  .after-sale-detail-page {
    padding-bottom: 100px;
  }

  .fixed-bottom {
    padding: 16px;
  }

  .after-sale-stepper {
    :deep(.q-stepper__step-inner) {
      padding: 4px 0;
    }

    :deep(.q-stepper__title) {
      font-size: 0.875rem;
    }
  }
}
</style>
0
