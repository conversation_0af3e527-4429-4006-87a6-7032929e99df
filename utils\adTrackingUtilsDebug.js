/**
 * 广告追踪调试工具
 * 用于开发环境下测试和验证广告追踪功能
 */

import { AdTrackingUtil } from '~/utils/adTrackingUtils';
import AdTrackingApi from '~/composables/adTrackingApi';

export const AdTrackingDebug = {
  /**
   * 打印当前广告跟踪状态
   */
  logCurrentState() {
    const sessionId = AdTrackingUtil.getSessionId();
    console.group('🎯 广告跟踪调试信息');
    console.log('📍 当前URL:', window.location.href);
    console.log('🔗 URL参数:', AdTrackingUtil.extractParamsFromUrl());
    console.log('🍪 Cookie参数:', AdTrackingUtil.getParams());
    console.log('🆔 会话ID:', sessionId);
    console.log('✅ 是否有跟踪参数:', AdTrackingUtil.hasTrackingParams());
    console.log('🏷️ 访客记录已创建:', AdTrackingUtil.isVisitorRecordCreated(sessionId));
    console.log('📄 页面信息:', AdTrackingUtil.getPageInfo());
    console.groupEnd();
  },

  /**
   * 模拟广告参数
   * @param {Object} params - 广告参数
   */
  simulateAdParams(params = {}) {
    const defaultParams = {
      utm_source: 'google',
      utm_medium: 'cpc',
      utm_campaign: 'test_campaign',
      utm_term: 'test_keyword',
      utm_content: 'test_content',
    };

    const finalParams = { ...defaultParams, ...params };
    AdTrackingUtil.saveParams(finalParams);
    console.log('🎭 已模拟广告参数:', finalParams);
    this.logCurrentState();
  },

  /**
   * 模拟Google Ads参数
   */
  simulateGoogleAds() {
    const params = {
      utm_source: 'google',
      utm_medium: 'cpc',
      utm_campaign: 'google_ads_test',
      gclid: 'Cj0KCQjw_test_google_click_id',
    };
    this.simulateAdParams(params);
  },

  /**
   * 模拟Facebook Ads参数
   */
  simulateFacebookAds() {
    const params = {
      utm_source: 'facebook',
      utm_medium: 'social',
      utm_campaign: 'facebook_ads_test',
      fbclid: 'IwAR0_test_facebook_click_id',
    };
    this.simulateAdParams(params);
  },

  /**
   * 清除所有跟踪数据
   */
  clearAll() {
    AdTrackingUtil.clearParams();
    console.log('🧹 已清除所有广告跟踪数据');
    this.logCurrentState();
  },

  /**
   * 测试访客记录创建
   */
  async testCreateVisitorRecord() {
    try {
      console.log('🧪 测试创建访客记录...');

      // 确保有测试参数
      if (!AdTrackingUtil.hasTrackingParams()) {
        this.simulateAdParams();
      }

      const trackingData = AdTrackingUtil.formatForApi();
      const sessionId = trackingData.sessionId;

      console.log('📤 发送数据:', trackingData);
      console.log('🏷️ 访客记录已创建:', AdTrackingUtil.isVisitorRecordCreated(sessionId));

      if (AdTrackingUtil.isVisitorRecordCreated(sessionId)) {
        console.log('⚠️ 访客记录已存在，跳过创建');
        return;
      }

      const response = await AdTrackingApi.createVisitorAdTracking(trackingData);
      console.log('📥 响应结果:', response);

      if (response?.code === 0) {
        AdTrackingUtil.markVisitorRecordCreated(sessionId);
        console.log('✅ 访客记录创建成功，已标记');
      } else {
        console.error('❌ 访客记录创建失败:', response?.msg);
      }
    } catch (error) {
      console.error('❌ 测试访客记录创建失败:', error);
    }
  },

  /**
   * 测试重复请求防护
   */
  async testDuplicateRequestPrevention() {
    console.log('🧪 测试重复请求防护...');

    // 清除现有标记
    AdTrackingUtil.clearVisitorRecordMark();

    // 模拟广告参数
    this.simulateAdParams();

    console.log('第一次请求:');
    await this.testCreateVisitorRecord();

    console.log('\n第二次请求（应该被跳过）:');
    await this.testCreateVisitorRecord();

    console.log('\n第三次请求（应该被跳过）:');
    await this.testCreateVisitorRecord();

    console.log('✅ 重复请求防护测试完成');
  },

  /**
   * 测试用户记录创建
   * @param {number} userId - 用户ID
   */
  async testCreateUserRecord(userId = 1) {
    try {
      console.log('🧪 测试创建用户记录...');

      // 确保有测试参数
      if (!AdTrackingUtil.hasTrackingParams()) {
        this.simulateAdParams();
      }

      const trackingData = AdTrackingUtil.formatForApi();
      trackingData.userId = userId;
      console.log('📤 发送数据:', trackingData);

      const response = await AdTrackingApi.createAdTracking(trackingData);
      console.log('📥 响应结果:', response);

      if (response?.code === 0) {
        console.log('✅ 用户记录创建成功');
      } else {
        console.error('❌ 用户记录创建失败:', response?.msg);
      }
    } catch (error) {
      console.error('❌ 测试用户记录创建失败:', error);
    }
  },

  /**
   * 测试访客记录关联
   */
  async testLinkVisitorToUser() {
    try {
      console.log('🧪 测试访客记录关联...');

      const sessionId = AdTrackingUtil.getSessionId();
      console.log('🆔 会话ID:', sessionId);

      const response = await AdTrackingApi.linkVisitorToUser(sessionId);
      console.log('📥 响应结果:', response);

      if (response?.code === 0) {
        console.log('✅ 访客记录关联成功');
      } else {
        console.error('❌ 访客记录关联失败:', response?.msg);
      }
    } catch (error) {
      console.error('❌ 测试访客记录关联失败:', error);
    }
  },

  /**
   * 测试注册转化记录
   */
  async testRegisterConversion() {
    try {
      console.log('🧪 测试注册转化记录...');

      const sessionId = AdTrackingUtil.getSessionId();
      console.log('🆔 会话ID:', sessionId);

      const response = await AdTrackingApi.recordRegisterConversion(sessionId);
      console.log('📥 响应结果:', response);

      if (response?.code === 0) {
        console.log('✅ 注册转化记录成功');
      } else {
        console.error('❌ 注册转化记录失败:', response?.msg);
      }
    } catch (error) {
      console.error('❌ 测试注册转化记录失败:', error);
    }
  },

  /**
   * 运行完整测试流程
   */
  async runFullTest() {
    console.log('🚀 开始完整测试流程...');

    // 1. 清除现有数据
    this.clearAll();

    // 2. 模拟广告参数
    this.simulateAdParams();

    // 3. 创建访客记录
    await this.testCreateVisitorRecord();

    // 4. 测试访客记录关联
    await this.testLinkVisitorToUser();

    // 5. 测试注册转化
    await this.testRegisterConversion();

    console.log('✅ 完整测试流程结束');
  },

  /**
   * 生成测试URL
   * @param {string} baseUrl - 基础URL
   * @param {Object} params - 广告参数
   * @returns {string} 测试URL
   */
  generateTestUrl(baseUrl = window.location.origin, params = {}) {
    const defaultParams = {
      utm_source: 'google',
      utm_medium: 'cpc',
      utm_campaign: 'test_campaign',
      utm_term: 'test_keyword',
      utm_content: 'test_content',
    };

    const finalParams = { ...defaultParams, ...params };
    const url = new URL(baseUrl);

    Object.entries(finalParams).forEach(([key, value]) => {
      if (value) {
        url.searchParams.set(key, value);
      }
    });

    console.log('🔗 生成的测试URL:', url.toString());
    return url.toString();
  },

  /**
   * 显示帮助信息
   */
  showHelp() {
    console.log(`
🎯 广告追踪调试工具使用指南

基础功能：
• AdTrackingDebug.logCurrentState() - 查看当前状态
• AdTrackingDebug.clearAll() - 清除所有数据
• AdTrackingDebug.simulateAdParams() - 模拟广告参数
• AdTrackingDebug.simulateGoogleAds() - 模拟Google Ads
• AdTrackingDebug.simulateFacebookAds() - 模拟Facebook Ads

测试功能：
• AdTrackingDebug.testCreateVisitorRecord() - 测试创建访客记录
• AdTrackingDebug.testDuplicateRequestPrevention() - 测试重复请求防护
• AdTrackingDebug.testCreateUserRecord(userId) - 测试创建用户记录
• AdTrackingDebug.testLinkVisitorToUser() - 测试访客记录关联
• AdTrackingDebug.testRegisterConversion() - 测试注册转化


高级功能：
• AdTrackingDebug.runFullTest() - 运行完整测试流程
• AdTrackingDebug.generateTestUrl(baseUrl, params) - 生成测试URL
• AdTrackingDebug.showHelp() - 显示此帮助信息

使用示例：
AdTrackingDebug.simulateGoogleAds();
AdTrackingDebug.testCreateVisitorRecord();
AdTrackingDebug.runFullTest();
    `);
  },
};

// 开发环境下暴露到全局
if (process.dev && process.client) {
  window.AdTrackingDebug = AdTrackingDebug;
  console.log('🎯 广告追踪调试工具已加载，使用 AdTrackingDebug.showHelp() 查看帮助');
}
