<template>
  <div class="menu-container">
    <div class="head-menu" :class="{ 'search-expanded': searchExpanded }">
      <!-- 移动端菜单按钮 -->
      <q-btn flat dense round icon="menu" color="secondary" class="lt-md mobile-menu-btn" @click="mobileMenuOpen = true" />

      <!-- Logo -->
      <NuxtLink to="/" class="logo-container">
        <img class="logo" src="/images/logo.svg" alt="" />
      </NuxtLink>
      <!-- 中间内容区域：菜单或搜索框 -->
      <div class="center-content">
        <!-- 桌面端菜单（默认显示） -->
        <div v-if="!$q.screen.lt.md && !searchExpanded" class="menu">
          <ul>
            <li>
              <nuxt-link to="/" class="menu-link" active-class="active">{{ $t('home') }}</nuxt-link>
            </li>
            <li>
              <nuxt-link to="/diy-order" class="menu-link" active-class="active">DIY订单</nuxt-link>
            </li>
            <li>
              <nuxt-link to="/transfer" class="menu-link" active-class="active">{{ $t('transport') }}</nuxt-link>
            </li>
            <li>
              <nuxt-link to="/bulk" class="menu-link" active-class="active">{{ $t('bulkPurchase') }}</nuxt-link>
            </li>
            <li>
              <nuxt-link to="/notice" class="menu-link" active-class="active">{{ $t('notes') }}</nuxt-link>
            </li>
            <li>
              <nuxt-link to="/help" class="menu-link" active-class="active">{{ $t('helpCenter') }}</nuxt-link>
            </li>
          </ul>
        </div>

        <!-- 桌面端搜索框（展开时显示） -->
        <div v-if="searchExpanded" class="search-expanded-full">
          <!-- 关闭按钮（向右箭头） -->
          <!-- <q-btn flat dense round icon="arrow_forward" color="secondary" class="search-close-btn" @click="collapseSearch" /> -->

          <!-- 搜索输入框 -->
          <div class="search-input-wrapper">
            <input ref="searchInput" v-model="searchText" type="search" placeholder="搜索商品或链接" class="search-input" @keyup.enter="performSearch" @blur="handleSearchBlur" />
            <q-btn flat dense round icon="search" color="primary" class="search-submit-btn" @click="performSearch" />
          </div>

          <!-- 搜索文字 -->
          <!-- <span class="search-text">搜索</span> -->
        </div>
      </div>

      <!-- 移动端搜索按钮 -->
      <q-btn flat dense round icon="search" color="secondary" class="lt-md mobile-search-btn" @click="mobileSearchOpen = true" />

      <!-- 右侧功能区 -->
      <div class="menu-right">
        <!-- 桌面端搜索图标 -->
        <div v-if="!$q.screen.lt.md && !searchExpanded && !isSearchPage" class="desktop-search-trigger">
          <q-btn flat dense round icon="search" color="secondary" class="search-icon-btn" @click="expandSearch" />
        </div>

        <!-- 用户登录区域 -->
        <div class="login" @mouseenter="menuVisible = true" @mouseleave="menuVisible = false">
          <ClientOnly>
            <q-icon class="q-mb-xs" name="account_circle" size="22px" color="secondary" />
            <template v-if="!isLoggedIn">
              <div class="inline-block q-mx-sm gt-xs">
                <nuxt-link to="/login">{{ $t('login') }}</nuxt-link>
                /
                <nuxt-link to="/register">{{ $t('register') }}</nuxt-link>
              </div>
            </template>
            <template v-else>
              <div class="username text-primary q-ml-sm gt-xs">
                {{ userInfo.name }}
                <q-icon name="arrow_drop_down" size="28px" />
              </div>

              <div class="user-dropdown" v-show="menuVisible">
                <div @click="goToCenter">{{ $t('button.myCenter') }}</div>
                <div @click="goToOrders">{{ $t('button.myOrders') }}</div>
                <div @click="goToProfile">{{ $t('button.myMessage') }}</div>
                <div @click="logout">{{ $t('button.logout') }}</div>
              </div>
            </template>
          </ClientOnly>
        </div>

        <!-- 购物车 -->
        <div class="cart">
          <nuxt-link to="/cart">
            <q-icon class="q-mb-xs" name="shopping_cart" size="20px" color="secondary" />
            <span class="gt-xs">{{ $t('cart') }}</span>
          </nuxt-link>
        </div>
      </div>

      <!-- 移动端菜单对话框 -->
      <q-dialog v-model="mobileMenuOpen" position="left" full-height>
        <q-card class="mobile-menu-dialog" style="width: 280px; max-width: 90vw">
          <q-card-section class="q-pa-md q-pb-none">
            <div class="row items-center justify-between">
              <div class="text-h6">{{ $t('menu') }}</div>
              <q-btn icon="close" flat round dense v-close-popup />
            </div>
          </q-card-section>
          <q-card-section class="q-pa-none">
            <q-list padding>
              <q-item clickable v-ripple tag="a" href="/" @click="mobileMenuOpen = false">
                <q-item-section avatar>
                  <q-icon name="home" />
                </q-item-section>
                <q-item-section>{{ $t('home') }}</q-item-section>
              </q-item>

              <q-item clickable v-ripple tag="a" href="/diy-order" @click="mobileMenuOpen = false">
                <q-item-section avatar>
                  <q-icon name="build" />
                </q-item-section>
                <q-item-section>DIY Order</q-item-section>
              </q-item>

              <q-item clickable v-ripple tag="a" href="/transfer" @click="mobileMenuOpen = false">
                <q-item-section avatar>
                  <q-icon name="local_shipping" />
                </q-item-section>
                <q-item-section>{{ $t('transport') }}</q-item-section>
              </q-item>

              <q-item clickable v-ripple tag="a" href="/bulk" @click="mobileMenuOpen = false">
                <q-item-section avatar>
                  <q-icon name="shopping_bag" />
                </q-item-section>
                <q-item-section>{{ $t('bulkPurchase') }}</q-item-section>
              </q-item>

              <q-item clickable v-ripple tag="a" href="/notice" @click="mobileMenuOpen = false">
                <q-item-section avatar>
                  <q-icon name="article" />
                </q-item-section>
                <q-item-section>{{ $t('notes') }}</q-item-section>
              </q-item>

              <q-item clickable v-ripple tag="a" href="/help" @click="mobileMenuOpen = false">
                <q-item-section avatar>
                  <q-icon name="support" />
                </q-item-section>
                <q-item-section>{{ $t('helpCenter') }}</q-item-section>
              </q-item>

              <q-item
                clickable
                v-ripple
                @click="
                  openChatInNewWindow();
                  mobileMenuOpen = false;
                ">
                <q-item-section avatar>
                  <q-icon name="headset_mic" />
                </q-item-section>
                <q-item-section>{{ $t('help.contactSupport.button') }}</q-item-section>
              </q-item>

              <q-separator class="q-my-md" />

              <template v-if="isLoggedIn">
                <q-item
                  clickable
                  v-ripple
                  @click="
                    goToCenter();
                    mobileMenuOpen = false;
                  ">
                  <q-item-section avatar>
                    <q-icon name="person" />
                  </q-item-section>
                  <q-item-section>{{ $t('button.myCenter') }}</q-item-section>
                </q-item>

                <q-item
                  clickable
                  v-ripple
                  @click="
                    goToOrders();
                    mobileMenuOpen = false;
                  ">
                  <q-item-section avatar>
                    <q-icon name="receipt_long" />
                  </q-item-section>
                  <q-item-section>{{ $t('button.myOrders') }}</q-item-section>
                </q-item>

                <q-item
                  clickable
                  v-ripple
                  @click="
                    goToProfile();
                    mobileMenuOpen = false;
                  ">
                  <q-item-section avatar>
                    <q-icon name="mail" />
                  </q-item-section>
                  <q-item-section>{{ $t('button.myMessage') }}</q-item-section>
                </q-item>

                <q-item
                  clickable
                  v-ripple
                  @click="
                    logout();
                    mobileMenuOpen = false;
                  ">
                  <q-item-section avatar>
                    <q-icon name="logout" />
                  </q-item-section>
                  <q-item-section>{{ $t('button.logout') }}</q-item-section>
                </q-item>
              </template>
              <template v-else>
                <q-item clickable v-ripple tag="a" href="/login" @click="mobileMenuOpen = false">
                  <q-item-section avatar>
                    <q-icon name="login" />
                  </q-item-section>
                  <q-item-section>{{ $t('login') }}</q-item-section>
                </q-item>

                <q-item clickable v-ripple tag="a" href="/register" @click="mobileMenuOpen = false">
                  <q-item-section avatar>
                    <q-icon name="person_add" />
                  </q-item-section>
                  <q-item-section>{{ $t('register') }}</q-item-section>
                </q-item>
              </template>
            </q-list>
          </q-card-section>
        </q-card>
      </q-dialog>

      <!-- 移动端搜索对话框 -->
      <q-dialog v-model="mobileSearchOpen" position="top" class="mobile-search-dialog-wrapper">
        <q-card class="mobile-search-dialog">
          <q-card-section class="search-header q-pb-none">
            <div class="row items-center justify-between">
              <div class="search-title row items-center q-gutter-sm">
                <q-icon name="search" size="20px" color="primary" />
                <span>搜索商品</span>
              </div>
              <q-btn icon="close" flat round dense size="sm" @click="mobileSearchOpen = false" />
            </div>
          </q-card-section>

          <q-card-section class="search-body">
            <q-input v-model="mobileSearchText" outlined placeholder="搜索商品或链接..." autofocus @keyup.enter="doMobileSearch" class="search-input-field">
              <template #prepend>
                <q-icon name="search" color="grey-6" />
              </template>
              <template #append>
                <q-btn v-if="mobileSearchText" icon="clear" flat round dense size="sm" @click="mobileSearchText = ''" />
              </template>
            </q-input>
          </q-card-section>

          <q-card-actions class="search-actions q-pt-none">
            <q-btn unelevated label="搜索" color="primary" @click="doMobileSearch" :disable="!mobileSearchText.trim()" icon-right="search" class="search-btn full-width" size="md" />
          </q-card-actions>
        </q-card>
      </q-dialog>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue';
import { useAuthStore } from '~/store/auth';
import { useUserStore } from '~/store/user';
import { useRouter, useRoute } from 'vue-router';
import { useQuasar } from 'quasar';

// 响应式变量
const menuVisible = ref(false);
const mobileMenuOpen = ref(false);
const mobileSearchOpen = ref(false);
const searchText = ref('');
const mobileSearchText = ref('');
const searchExpanded = ref(false);
const searchInput = ref(null);
const router = useRouter();
const $q = useQuasar();

// 从 Pinia 获取用户信息和登录状态
const authStore = useAuthStore();
const userStore = useUserStore();
const isLoggedIn = computed(() => authStore.isLogin);
const userInfo = computed(() => userStore.userInfo);

// 获取当前路由对象
const route = useRoute();

// 用户相关功能
const goToCenter = () => {
  router.push('/account');
  menuVisible.value = false;
};

const goToOrders = () => {
  router.push('/account/orders');
  menuVisible.value = false;
};

const goToProfile = () => {
  router.push('/account/message');
  menuVisible.value = false;
};

const logout = () => {
  console.log('logout-----------------------------------');
  authStore.clearToken();

  const tokenCookie = useCookie('token');
  const refreshTokenCookie = useCookie('refreshToken');
  tokenCookie.value = null;
  refreshTokenCookie.value = null;

  router.replace('/');
};

const openChatInNewWindow = () => {
  window.open('/chat', '_blank');
};

// 桌面端搜索功能
const expandSearch = () => {
  console.log('🔍 expandSearch called');
  searchExpanded.value = true;

  nextTick(() => {
    if (searchInput.value) {
      searchInput.value.focus();
      console.log('🔍 Search input focused');
    }
  });
};

const collapseSearch = () => {
  console.log('🔍 collapseSearch called');
  searchExpanded.value = false;
  searchText.value = '';
};

const handleSearchBlur = () => {
  // 延迟收起，允许点击搜索按钮
  setTimeout(() => {
    if (!searchText.value.trim()) {
      collapseSearch();
    }
  }, 200);
};

const performSearch = async () => {
  console.log('🔍 performSearch called with text:', searchText.value);

  if (searchText.value.trim()) {
    try {
      // 分析搜索输入类型并构建相应的URL
      const { analyzeSearchInput, buildSearchUrl } = await import('~/utils/searchUtils');
      const analysis = analyzeSearchInput(searchText.value.trim());
      const searchUrl = buildSearchUrl(analysis);

      // 在新窗口打开搜索页面
      window.open(searchUrl, '_blank');
      collapseSearch();
    } catch (error) {
      console.error('搜索功能出错:', error);
      // 如果搜索工具出错，直接打开搜索页面
      window.open('/search', '_blank');
      collapseSearch();
    }
  } else {
    window.open('/search', '_blank');
    collapseSearch();
  }
};

// 移动端搜索功能
const doMobileSearch = async () => {
  console.log('🔍 doMobileSearch called with text:', mobileSearchText.value);

  if (mobileSearchText.value.trim()) {
    try {
      // 分析搜索输入类型并构建相应的URL
      const { analyzeSearchInput, buildSearchUrl } = await import('~/utils/searchUtils');
      const analysis = analyzeSearchInput(mobileSearchText.value.trim());
      const searchUrl = buildSearchUrl(analysis);

      // 在新窗口打开搜索页面
      window.open(searchUrl, '_blank');
      mobileSearchOpen.value = false;
      mobileSearchText.value = '';
    } catch (error) {
      console.error('移动端搜索功能出错:', error);
      // 如果搜索工具出错，直接打开搜索页面
      window.open('/search', '_blank');
      mobileSearchOpen.value = false;
      mobileSearchText.value = '';
    }
  } else {
    window.open('/search', '_blank');
    mobileSearchOpen.value = false;
    mobileSearchText.value = '';
  }
};

// 点击外部区域关闭搜索框
const handleClickOutside = (event) => {
  console.log('click outside1');
  const searchContainer = event.target.closest('.desktop-search');
  if (!searchContainer && searchExpanded.value) {
    collapseSearch();
  }
};

// 监听屏幕尺寸变化，在大屏幕上自动关闭移动端菜单和搜索
watch(
  () => $q.screen.name,
  (newVal) => {
    if (newVal === 'sm' || newVal === 'md' || newVal === 'lg' || newVal === 'xl') {
      mobileMenuOpen.value = false;
      mobileSearchOpen.value = false;
      searchExpanded.value = false;
    }
  }
);

// 监听搜索框展开状态，添加/移除全局点击监听
watch(searchExpanded, (expanded) => {
  console.log('watchsearchExpanded:', expanded);
  // if (expanded) {
  //   document.addEventListener('click', handleClickOutside);
  // } else {
  //   document.removeEventListener('click', handleClickOutside);
  // }
});

// 计算属性：判断是否为搜索页面
const isSearchPage = computed(() => {
  // 检查路径是否为 /search
  const isSearchPath = route.path === '/search';

  // 可选：进一步检查查询参数
  const hasQuery = route.query.q; // 是否有搜索关键词
  const isFromTaobao = route.query.platform === 'taobao';

  // 你可以根据需要组合条件
  // 例如：只要路径是 /search 就认为是搜索页
  return isSearchPath;
});

// 组件挂载时的调试信息
onMounted(() => {
  console.log('🔍 HeaderMenu component mounted');
  console.log('🔍 $q.screen:', $q.screen);
  console.log('🔍 searchExpanded:', searchExpanded.value);
  console.log('🔍 Functions available:', {
    expandSearch: typeof expandSearch,
    collapseSearch: typeof collapseSearch,
    performSearch: typeof performSearch,
  });
});

// 组件卸载时清理事件监听
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});
</script>

<style lang="scss" scoped>
.menu-container {
  width: 100%;
  background-color: #fff;
}

.head-menu {
  width: 100%;
  max-width: 1200px;
  height: 60px;
  margin: 0 auto;
  background-color: #fff;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;

  @media (max-width: 599px) {
    height: 50px;
    padding: 0 10px;
  }

  .mobile-menu-btn {
    margin-right: 10px;
    width: 40px;
    height: 40px;

    @media (max-width: 599px) {
      width: 36px;
      height: 36px;
    }
  }

  .mobile-search-btn {
    margin-left: auto;
    margin-right: 10px;
    width: 40px;
    height: 40px;

    @media (max-width: 599px) {
      width: 36px;
      height: 36px;
    }
  }

  .logo-container {
    display: flex;
    align-items: center;

    @media (max-width: 599px) {
      flex: 1;
      justify-content: center;
    }
  }

  .logo {
    width: 200px;
    //height: 60px;

    @media (max-width: 1023px) {
      width: 200px;
      // height: 46px;
    }

    @media (max-width: 599px) {
      width: 160px;
      // height: 37px;
    }
  }

  .center-content {
    flex: 1;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    .menu {
      height: 100%;
      display: flex;
      align-items: center;

      ul {
        display: flex;
        font-size: 16px;
        margin: 0;
        padding: 0;

        li {
          display: inline-block;
          line-height: 40px;
          margin: 0 15px;
          position: relative;

          .menu-link {
            display: block;
            cursor: pointer;
            color: inherit;
            text-decoration: none;
            position: relative;

            &:hover,
            &.active {
              color: #4f87ff;

              // &:after {
              //   content: '';
              //   position: absolute;
              //   bottom: 0;
              //   left: 0;
              //   width: 100%;
              //   height: 3px;
              //   background-color: #4f87ff;
              // }
            }
          }
        }
      }
    }

    .search-expanded-full {
      height: 100%;
      display: flex;
      align-items: center;
      gap: 12px;
      animation: slideInFromRight 0.3s ease;
      width: 100%;
      justify-content: center;

      .search-close-btn {
        width: 36px;
        height: 36px;
        flex-shrink: 0;
      }

      .search-input-wrapper {
        display: flex;
        align-items: center;
        background: #f5f5f5;
        border-radius: 25px;
        padding: 0 6px 0 20px;
        min-width: 600px;
        max-width: 600px;

        .search-input {
          flex: 1;
          border: none;
          background: transparent;
          outline: none;
          padding: 12px 12px 12px 0;
          font-size: 16px;
          color: #333;

          &::placeholder {
            color: #999;
          }
        }

        .search-submit-btn {
          width: 36px;
          height: 36px;
          flex-shrink: 0;
        }
      }

      .search-text {
        font-size: 16px;
        color: #666;
        white-space: nowrap;
        margin-left: 8px;
      }
    }
  }

  .menu-right {
    height: 100%;
    display: flex;
    align-items: center;
    font-size: 16px;

    @media (max-width: 599px) {
      font-size: 14px;
    }

    // 桌面端搜索触发按钮
    .desktop-search-trigger {
      margin-right: 15px;

      .search-icon-btn {
        width: 40px;
        height: 40px;
      }
    }

    .cart {
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-left: 15px;

      a {
        display: flex;
        align-items: center;
      }
    }

    .login {
      height: 100%;
      margin-right: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;

      .username {
        display: flex;
        align-items: center;
      }

      .user-dropdown {
        position: absolute;
        top: 90%;
        right: 0;
        background-color: #fff;
        border: 1px solid #ddd;
        border-radius: 4px;
        box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.15);
        min-width: 120px;
        z-index: 1000;

        div {
          padding: 0px 12px;
          height: 40px;
          line-height: 40px;
          font-size: 13px;
          color: #333;
          cursor: pointer;
          transition: background 0.3s;

          &:hover {
            background-color: #f0f0f0;
          }

          &.active {
            color: #d9534f;
          }
        }
      }
    }
  }

  @keyframes slideInFromRight {
    from {
      opacity: 0;
      transform: translateX(100px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  // 移动端菜单对话框样式
  .mobile-menu-dialog {
    background-color: #fff;

    .q-item {
      min-height: 48px;
    }
  }

  // 移动端搜索对话框样式
  .mobile-search-dialog-wrapper {
    .mobile-search-dialog {
      width: 95%;
      max-width: 500px;
      margin: 10px auto;
      background: white;
      border-radius: 16px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
      overflow: hidden;

      .search-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px 20px;
        background: #f8f9fa;
        border-bottom: 1px solid #e9ecef;

        .search-title {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 16px;
          font-weight: 600;
          color: #333;
        }
      }

      .search-body {
        padding: 20px;

        .search-input-field {
          .q-field__control {
            border-radius: 12px;
          }
        }
      }

      .search-actions {
        padding: 0 20px 20px;

        .search-btn {
          height: 48px;
          border-radius: 12px;
          font-size: 16px;
          font-weight: 600;
          text-transform: none;

          &[disabled] {
            opacity: 0.5;
          }
        }
      }
    }
  }
}
</style>
