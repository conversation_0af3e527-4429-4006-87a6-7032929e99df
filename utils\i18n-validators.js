import * as validators from '@vuelidate/validators';

import { createI18nMessage } from '@vuelidate/validators';

// 接受 `t` 函数作为参数，允许从外部传入翻译功能
export const createValidators = (t) => {
  const withI18nMessage = createI18nMessage({
    t,
  });

  return {
    required: withI18nMessage(validators.required),
    email: withI18nMessage(validators.email),
    minLength: withI18nMessage(validators.minLength, { withArguments: true }),
    maxLength: withI18nMessage(validators.maxLength, { withArguments: true }),
  };
};
