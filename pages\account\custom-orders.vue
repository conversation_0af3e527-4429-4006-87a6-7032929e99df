<template>
  <Header />
  <Breadcrumbs :breadcrumbs="breadcrumbs" />
  
  <div class="custom-orders-page">
    <div class="page-header">
      <h1 class="page-title">我的自定义订单</h1>
      <q-btn 
        color="primary" 
        icon="add" 
        label="创建新订单" 
        @click="goToCreateOrder"
        class="create-btn"
      />
    </div>

    <!-- 筛选器 -->
    <div class="filters-section">
      <q-card flat bordered class="filters-card">
        <q-card-section>
          <div class="row q-col-gutter-md items-center">
            <div class="col-12 col-sm-6 col-md-3">
              <q-select
                v-model="filters.status"
                :options="statusOptions"
                label="订单状态"
                outlined
                dense
                clearable
                @update:model-value="loadOrders"
              />
            </div>
            <div class="col-12 col-sm-6 col-md-3">
              <q-input
                v-model="filters.keyword"
                label="搜索订单"
                placeholder="商品名称或订单号"
                outlined
                dense
                clearable
                @keyup.enter="loadOrders"
              >
                <template #append>
                  <q-icon name="search" @click="loadOrders" class="cursor-pointer" />
                </template>
              </q-input>
            </div>
            <div class="col-12 col-sm-6 col-md-3">
              <q-input
                v-model="filters.dateFrom"
                label="开始日期"
                type="date"
                outlined
                dense
                @update:model-value="loadOrders"
              />
            </div>
            <div class="col-12 col-sm-6 col-md-3">
              <q-input
                v-model="filters.dateTo"
                label="结束日期"
                type="date"
                outlined
                dense
                @update:model-value="loadOrders"
              />
            </div>
          </div>
        </q-card-section>
      </q-card>
    </div>

    <!-- 订单列表 -->
    <div class="orders-section">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <q-spinner color="primary" size="3em" />
        <p>正在加载订单...</p>
      </div>

      <!-- 空状态 -->
      <div v-else-if="orders.length === 0" class="empty-container">
        <q-icon name="receipt_long" size="60px" color="grey-5" />
        <h3>暂无自定义订单</h3>
        <p>您还没有创建任何自定义订单</p>
        <q-btn color="primary" @click="goToCreateOrder">创建第一个订单</q-btn>
      </div>

      <!-- 订单卡片列表 -->
      <div v-else class="orders-list">
        <q-card 
          v-for="order in orders" 
          :key="order.id" 
          flat 
          bordered 
          class="order-card q-mb-md"
        >
          <q-card-section>
            <div class="order-header">
              <div class="order-info">
                <h4 class="order-title">{{ order.productName }}</h4>
                <p class="order-id">订单号: {{ order.orderNo }}</p>
                <p class="order-date">创建时间: {{ formatDate(order.createTime) }}</p>
              </div>
              <div class="order-status">
                <q-chip 
                  :color="getStatusColor(order.status)" 
                  text-color="white"
                  :label="getStatusText(order.status)"
                />
              </div>
            </div>

            <div class="order-content">
              <div class="row q-col-gutter-md">
                <!-- 商品信息 -->
                <div class="col-12 col-md-8">
                  <div class="product-info">
                    <div class="product-images" v-if="order.images && order.images.length > 0">
                      <q-img
                        v-for="(image, index) in order.images.slice(0, 3)"
                        :key="index"
                        :src="image"
                        class="product-image"
                      />
                      <div v-if="order.images.length > 3" class="more-images">
                        +{{ order.images.length - 3 }}
                      </div>
                    </div>
                    
                    <div class="product-details">
                      <p v-if="order.productLink" class="product-link">
                        <q-icon name="link" size="sm" />
                        <a :href="order.productLink" target="_blank">查看原商品</a>
                      </p>
                      <p v-if="order.specifications" class="specifications">
                        <strong>规格:</strong> {{ order.specifications }}
                      </p>
                      <p v-if="order.productNotes" class="notes">
                        <strong>备注:</strong> {{ order.productNotes }}
                      </p>
                    </div>
                  </div>
                </div>

                <!-- 价格和数量 -->
                <div class="col-12 col-md-4">
                  <div class="price-info">
                    <div class="price-row">
                      <span class="label">单价:</span>
                      <span class="value">¥{{ (order.unitPrice / 100).toFixed(2) }}</span>
                    </div>
                    <div class="price-row">
                      <span class="label">数量:</span>
                      <span class="value">{{ order.quantity }}</span>
                    </div>
                    <div class="price-row">
                      <span class="label">运费:</span>
                      <span class="value">¥{{ (order.domesticShipping / 100).toFixed(2) }}</span>
                    </div>
                    <div class="price-row total">
                      <span class="label">总计:</span>
                      <span class="value">¥{{ ((order.unitPrice * order.quantity + order.domesticShipping) / 100).toFixed(2) }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="order-actions">
              <q-btn 
                flat 
                color="primary" 
                label="查看详情" 
                @click="viewOrderDetail(order.id)"
              />
              <q-btn 
                v-if="order.status === 'draft'"
                flat 
                color="secondary" 
                label="编辑订单" 
                @click="editOrder(order.id)"
              />
              <q-btn 
                v-if="order.status === 'draft'"
                flat 
                color="negative" 
                label="取消订单" 
                @click="cancelOrder(order.id)"
              />
              <q-btn 
                v-if="order.status === 'confirmed'"
                flat 
                color="positive" 
                label="立即支付" 
                @click="payOrder(order.id)"
              />
            </div>
          </q-card-section>
        </q-card>
      </div>

      <!-- 分页 -->
      <div v-if="totalPages > 1" class="pagination-container">
        <q-pagination 
          v-model="currentPage" 
          :max="totalPages" 
          :max-pages="6" 
          boundary-numbers 
          direction-links 
          @update:model-value="loadOrders"
        />
      </div>
    </div>
  </div>

  <Footer />
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import CustomOrderApi from '~/composables/customOrderApi';

const router = useRouter();

// 面包屑导航
const breadcrumbs = [
  { label: '首页', to: '/' },
  { label: '我的账户', to: '/account' },
  { label: '自定义订单', to: '/account/custom-orders' }
];

// 状态选项
const statusOptions = [
  { label: '全部', value: null },
  { label: '草稿', value: 'draft' },
  { label: '已确认', value: 'confirmed' },
  { label: '已支付', value: 'paid' },
  { label: '处理中', value: 'processing' },
  { label: '已发货', value: 'shipped' },
  { label: '已完成', value: 'completed' },
  { label: '已取消', value: 'cancelled' }
];

// 数据状态
const orders = ref([]);
const loading = ref(false);
const currentPage = ref(1);
const totalPages = ref(1);
const pageSize = ref(10);

// 筛选器
const filters = ref({
  status: null,
  keyword: '',
  dateFrom: '',
  dateTo: ''
});

// 加载订单列表
const loadOrders = async () => {
  loading.value = true;
  
  try {
    const params = {
      pageNo: currentPage.value,
      pageSize: pageSize.value,
      ...filters.value
    };
    
    // 移除空值
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null) {
        delete params[key];
      }
    });
    
    const response = await CustomOrderApi.getCustomOrderList(params);
    
    if (response.code === 0) {
      orders.value = response.data.list || [];
      totalPages.value = Math.ceil((response.data.total || 0) / pageSize.value);
    } else {
      console.error('加载订单失败:', response.msg);
      orders.value = [];
    }
  } catch (error) {
    console.error('加载订单出错:', error);
    orders.value = [];
  } finally {
    loading.value = false;
  }
};

// 工具函数
const formatDate = (timestamp) => {
  return new Date(timestamp).toLocaleString('zh-CN');
};

const getStatusColor = (status) => {
  const colors = {
    draft: 'grey',
    confirmed: 'blue',
    paid: 'green',
    processing: 'orange',
    shipped: 'purple',
    completed: 'positive',
    cancelled: 'negative'
  };
  return colors[status] || 'grey';
};

const getStatusText = (status) => {
  const texts = {
    draft: '草稿',
    confirmed: '已确认',
    paid: '已支付',
    processing: '处理中',
    shipped: '已发货',
    completed: '已完成',
    cancelled: '已取消'
  };
  return texts[status] || status;
};

// 操作方法
const goToCreateOrder = () => {
  router.push('/diy-order');
};

const viewOrderDetail = (orderId) => {
  router.push(`/account/custom-orders/${orderId}`);
};

const editOrder = (orderId) => {
  router.push(`/diy-order?edit=${orderId}`);
};

const cancelOrder = async (orderId) => {
  try {
    const confirmed = await new Promise((resolve) => {
      useNuxtApp().$q.dialog({
        title: '确认取消',
        message: '确定要取消这个订单吗？',
        cancel: true,
        persistent: true
      }).onOk(() => resolve(true)).onCancel(() => resolve(false));
    });
    
    if (confirmed) {
      const response = await CustomOrderApi.cancelCustomOrder(orderId, '用户主动取消');
      if (response.code === 0) {
        useNuxtApp().$showNotify({ 
          msg: '订单已取消', 
          type: 'positive' 
        });
        loadOrders();
      }
    }
  } catch (error) {
    console.error('取消订单失败:', error);
    useNuxtApp().$showNotify({ 
      msg: '取消订单失败', 
      type: 'negative' 
    });
  }
};

const payOrder = (orderId) => {
  router.push(`/checkout?orderId=${orderId}`);
};

// 页面初始化
onMounted(() => {
  loadOrders();
});
</script>

<style lang="scss" scoped>
.custom-orders-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px 16px;
  background-color: #fafafa;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .page-title {
    font-size: 28px;
    font-weight: bold;
    color: #333;
    margin: 0;
    position: relative;
    padding-left: 12px;

    &:before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 24px;
      background-color: #0073e6;
      border-radius: 2px;
    }
  }

  .create-btn {
    background-color: #0073e6;

    &:hover {
      background-color: #005bb5;
    }
  }

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;

    .create-btn {
      width: 100%;
    }
  }
}

.filters-section {
  margin-bottom: 30px;

  .filters-card {
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    :deep(.q-field__control) {
      border-radius: 8px;
    }
  }
}

.loading-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
  text-align: center;

  h3 {
    margin: 20px 0 10px;
    color: #333;
  }

  p {
    margin-bottom: 20px;
    color: #666;
  }
}

.orders-list {
  .order-card {
    border-radius: 12px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e0e0e0;

    &:hover {
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
      transform: translateY(-2px);
    }

    .order-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 20px;

      .order-info {
        flex: 1;

        .order-title {
          font-size: 18px;
          font-weight: 600;
          color: #333;
          margin: 0 0 8px;
        }

        .order-id,
        .order-date {
          font-size: 14px;
          color: #666;
          margin: 4px 0;
        }
      }

      .order-status {
        margin-left: 20px;
      }
    }

    .order-content {
      margin-bottom: 20px;

      .product-info {
        .product-images {
          display: flex;
          gap: 8px;
          margin-bottom: 15px;

          .product-image {
            width: 60px;
            height: 60px;
            border-radius: 6px;
            object-fit: cover;
          }

          .more-images {
            width: 60px;
            height: 60px;
            border-radius: 6px;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: #666;
          }
        }

        .product-details {
          .product-link {
            margin: 8px 0;

            a {
              color: #0073e6;
              text-decoration: none;
              margin-left: 5px;

              &:hover {
                text-decoration: underline;
              }
            }
          }

          .specifications,
          .notes {
            font-size: 14px;
            color: #666;
            margin: 8px 0;
            line-height: 1.4;
          }
        }
      }

      .price-info {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 15px;

        .price-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;

          &.total {
            border-top: 1px solid #e9ecef;
            padding-top: 8px;
            margin-top: 8px;
            font-weight: 600;

            .value {
              color: #ff4757;
              font-size: 16px;
            }
          }

          .label {
            color: #666;
            font-size: 14px;
          }

          .value {
            color: #333;
            font-size: 14px;
            font-weight: 500;
          }
        }
      }
    }

    .order-actions {
      display: flex;
      gap: 10px;
      flex-wrap: wrap;
      border-top: 1px solid #f0f0f0;
      padding-top: 15px;
    }
  }
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin: 30px 0;
}

@media (max-width: 768px) {
  .custom-orders-page {
    padding: 15px 10px;
  }

  .order-card {
    .order-header {
      flex-direction: column;
      gap: 15px;

      .order-status {
        margin-left: 0;
        align-self: flex-start;
      }
    }

    .order-content {
      .product-info {
        .product-images {
          .product-image,
          .more-images {
            width: 50px;
            height: 50px;
          }
        }
      }
    }

    .order-actions {
      justify-content: center;
    }
  }
}
</style>
