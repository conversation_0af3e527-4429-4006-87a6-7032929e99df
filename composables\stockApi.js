const StockApi = {
  // 获取库存列表
  getStockPage: (params) => {
    return useClientGet('/agent/stock/page', {
      params,
    });
  },

  // 获取库存详情
  getStockDetail: (id) => {
    return useClientGet('/agent/stock/get-detail', {
      params: { id },
      custom: {
        showLoading: false,
      },
    });
  },

  // 获取通知列表（可能不需要，保留以防万一）
  getList: (params) => {
    return useServerGet('/system/notice/list', {
      params,
    });
  },
};

export default StockApi;
