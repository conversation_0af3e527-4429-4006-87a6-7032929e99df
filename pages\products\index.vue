<template>
  <Header />

  <div class="products-page">
    <!-- 顶部横幅 -->
    <div class="products-banner">
      <div class="products-banner-content">
        <div class="banner-top">
          <h1 class="products-title">精选商品列表</h1>
          <!-- <Breadcrumbs :breadcrumbs="breadcrumbs" class="products-breadcrumbs" /> -->
        </div>
        <div class="self-operated-notice">
          <q-icon name="verified" color="white" size="18px" class="q-mr-xs" />
          <span>根据广大代购用户常买商品精心筛选。</span>
        </div>
      </div>
    </div>

    <div class="products-container">
      <div class="products-content">
        <!-- 移动端分类抽屉 -->
        <q-drawer v-model="showCategoryDrawer" side="left" bordered :width="280" :breakpoint="1023" class="category-drawer" v-if="$q.screen.lt.md">
          <div class="drawer-header">
            <div class="drawer-title">
              <q-icon name="apps" size="22px" class="q-mr-sm" color="primary" />
              {{ $t('products.categories') }}
            </div>
            <q-btn flat round dense icon="close" @click="showCategoryDrawer = false" />
          </div>
          <q-separator />
          <CategorySidebar :categories="categories" :selected-category="selectedCategory" @select-category="handleCategorySelect" />
        </q-drawer>

        <!-- 主内容区 -->
        <div class="products-main-content">
          <!-- 桌面端左侧分类 -->
          <div class="category-sidebar gt-sm">
            <div class="sidebar-title">
              <q-icon name="apps" size="22px" class="q-mr-sm" color="primary" />
              {{ $t('products.categories') }}
            </div>
            <CategorySidebar :categories="categories" :selected-category="selectedCategory" @select-category="handleCategorySelect" />

            <!-- 价格筛选 -->
            <div class="price-filter">
              <div class="sidebar-title">
                <q-icon name="attach_money" size="22px" class="q-mr-sm" color="primary" />
                {{ $t('products.price') }}
              </div>
              <div class="price-slider q-px-md q-py-md">
                <q-range v-model="priceRange" :min="0" :max="5000" label label-always color="primary" :label-value="(v) => `¥${v}`" />
                <div class="price-filter-actions q-mt-md">
                  <q-btn outline color="primary" size="sm" :label="$t('products.apply')" @click="applyPriceFilter" />
                  <q-btn flat color="grey" size="sm" :label="$t('products.reset')" @click="resetPriceFilter" />
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧商品列表 -->
          <div class="products-list-container">
            <!-- 筛选和排序 -->
            <div class="products-filter">
              <div class="filter-left">
                <!-- 移动端分类按钮 -->
                <q-btn outline color="primary" icon="menu" :label="$t('products.showCategories')" class="lt-md category-btn" @click="showCategoryDrawer = true" />

                <!-- 当前分类显示 -->
                <div class="current-category gt-xs">
                  <q-chip color="primary" text-color="white" :icon="getCategoryIcon(selectedCategoryName)">
                    {{ $t(`products.category.${selectedCategoryName}`) || selectedCategoryName }}
                  </q-chip>
                </div>

                <!-- 商品计数 -->
                <!-- <div class="products-count" v-if="!loading && products.length > 0">
                  {{ $t('products.totalItems', { count: totalItems }) }}
                </div> -->
              </div>

              <div class="filter-right">
                <!-- 排序选择 -->
                <q-select v-model="sortOption" :options="sortOptions" outlined dense options-dense emit-value map-options class="sort-select" :label="$t('products.sortBy')" />

                <!-- 视图切换 -->
                <!-- <div class="view-toggle gt-xs">
                  <q-btn-toggle
                    v-model="viewMode"
                    toggle-color="primary"
                    color="grey-3"
                    text-color="grey-8"
                    :options="[
                      { value: 'grid', slot: 'grid' },
                      { value: 'list', slot: 'list' },
                    ]">
                    <template #grid>
                      <q-icon name="grid_view" />
                    </template>
                    <template #list>
                      <q-icon name="view_list" />
                    </template>
                  </q-btn-toggle>
                </div> -->
              </div>
            </div>

            <!-- 商品列表 -->
            <div class="products-list" :class="{ 'list-view': viewMode === 'list' }">
              <template v-if="loading">
                <!-- 加载骨架屏 -->
                <div v-for="i in 12" :key="i" class="product-item-skeleton">
                  <q-card class="product-skeleton-card">
                    <q-skeleton type="rect" height="200px" animation="wave" />
                    <q-card-section>
                      <q-skeleton type="text" class="q-mb-md" animation="wave" />
                      <q-skeleton type="text" width="60%" animation="wave" />
                    </q-card-section>
                  </q-card>
                </div>
              </template>

              <template v-else-if="products.length > 0">
                <!-- 商品列表 -->
                <div v-for="(product, index) in products" :key="product.id" class="product-item">
                  <ProductBox :product="product" :index="index" />
                </div>
              </template>

              <template v-else>
                <!-- 无商品提示 -->
                <div class="no-products">
                  <q-icon name="inventory_2" size="80px" color="grey-4" />
                  <p>{{ $t('products.noProducts') }}</p>
                  <q-btn color="primary" :label="$t('products.allCategories')" class="q-mt-md" @click="handleCategorySelect(null)" />
                </div>
              </template>
            </div>

            <!-- 分页 -->
            <div class="pagination-container" v-if="!loading && products.length > 0">
              <!-- <div class="pagination-info">
                {{
                  $t('products.showingItems', {
                    start: (currentPage - 1) * pageSize + 1,
                    end: Math.min(currentPage * pageSize, totalItems),
                    total: totalItems,
                  })
                }}
              </div> -->
              <q-pagination
                v-model="currentPage"
                :max="totalPages"
                :max-pages="5"
                boundary-links
                direction-links
                color="primary"
                active-color="primary"
                active-text-color="white"
                @update:model-value="handlePageChange" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <Footer />
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import ProductApi from '~/composables/productApi';
import CategoryApi from '~/composables/categoryApi';

// 组件
import CategorySidebar from '~/components/products/CategorySidebar.vue';

// 国际化
const { t } = useI18n();
const route = useRoute();
const router = useRouter();

// 面包屑
const breadcrumbs = [
  { label: t('nav.home') || '首页', to: '/' },
  { label: t('products.title') || '商品列表', to: '/products' },
];

// 状态变量
const loading = ref(true);
const products = ref([]);
const categories = ref([]);
const selectedCategory = ref(null);
const showCategoryDrawer = ref(false);
const viewMode = ref('grid'); // 'grid' 或 'list'
const currentPage = ref(1);
const pageSize = ref(24);
const totalItems = ref(0);
const sortOption = ref('newest');
const priceRange = ref({
  min: 0,
  max: 10000,
});

// 排序选项
const sortOptions = [
  { label: t('products.sortOptions.newest') || '最新上架', value: 'newest' },
  { label: t('products.sortOptions.priceAsc') || '价格从低到高', value: 'priceAsc' },
  { label: t('products.sortOptions.priceDesc') || '价格从高到低', value: 'priceDesc' },
  { label: t('products.sortOptions.popular') || '人气优先', value: 'popular' },
];

// 计算属性
const totalPages = computed(() => Math.ceil(totalItems.value / pageSize.value));

const selectedCategoryName = computed(() => {
  if (!selectedCategory.value) return t('products.allCategories') || '全部商品';

  const category = categories.value.find((cat) => cat.id === selectedCategory.value);
  return category ? category.name : t('products.allCategories') || '全部商品';
});

// 获取分类列表
async function fetchCategories() {
  try {
    const { code, data } = await CategoryApi.getProductCategoryList();
    if (code === 0 && data) {
      categories.value = data;

      // 如果URL中有分类参数，则选中对应分类
      const categoryId = route.query.category;
      if (categoryId) {
        selectedCategory.value = parseInt(categoryId);
      }
    }
  } catch (error) {
    console.error('获取分类列表失败:', error);
  }
}

// 获取商品列表
async function fetchProducts() {
  loading.value = true;

  try {
    const params = {
      pageNo: currentPage.value,
      pageSize: pageSize.value,
    };

    // 添加分类筛选
    if (selectedCategory.value) {
      params.categoryId = selectedCategory.value;
    }

    // 添加价格筛选
    if (priceRange.value.min > 0) {
      params.minPrice = priceRange.value.min;
    }

    if (priceRange.value.max < 5000) {
      params.maxPrice = priceRange.value.max;
    }

    // 添加排序
    switch (sortOption.value) {
      case 'priceAsc':
        params.sortField = 'price';
        params.sortAsc = true;
        break;
      case 'priceDesc':
        params.sortField = 'price';
        params.sortAsc = false;
        break;
      case 'popular':
        params.sortField = 'salesCount';
        params.sortAsc = true;
        break;
      case 'newest':
      default:
        params.sortField = 'createTime';
        params.sortAsc = true;
        break;
    }

    const { code, data } = await ProductApi.searchProducts(params);

    if (code === 0 && data) {
      products.value = data.list || [];
      totalItems.value = data.total || 0;
    }
  } catch (error) {
    console.error('获取商品列表失败:', error);
  } finally {
    loading.value = false;
  }
}

// 处理分类选择
function handleCategorySelect(categoryId) {
  selectedCategory.value = categoryId;
  currentPage.value = 1; // 重置页码

  // 更新URL参数
  updateUrlParams();

  // 关闭移动端抽屉
  showCategoryDrawer.value = false;

  // 重新获取商品
  fetchProducts();
}

// 处理页码变化
function handlePageChange(page) {
  currentPage.value = page;

  // 更新URL参数
  updateUrlParams();

  // 重新获取商品
  fetchProducts();

  // 滚动到顶部
  window.scrollTo({ top: 0, behavior: 'smooth' });
}

// 价格筛选
function applyPriceFilter() {
  currentPage.value = 1; // 重置页码
  updateUrlParams();
  fetchProducts();
}

// 重置价格筛选
function resetPriceFilter() {
  priceRange.value = {
    min: 0,
    max: 10000,
  };
  currentPage.value = 1; // 重置页码
  updateUrlParams();
  fetchProducts();
}

// 获取分类图标
function getCategoryIcon(categoryName) {
  // 这里可以根据分类名称或ID返回不同的图标
  // 以下是一些示例图标，您可以根据实际需求修改
  const iconMap = {
    Clothing: 'checkroom',
    Electronics: 'devices',
    Home: 'chair',
    Beauty: 'spa',
    Health: 'health_and_safety',
    Toys: 'child_care',
    Sports: 'sports_soccer',
    Pet: 'pets',
    Handicrafts: 'palette',
  };

  // 尝试匹配分类名称
  for (const key in iconMap) {
    if (categoryName.includes(key)) {
      return iconMap[key];
    }
  }
  // 默认图标
  return 'sell';
}

// 更新URL参数
function updateUrlParams() {
  const query = { ...route.query };

  // 更新分类参数
  if (selectedCategory.value) {
    query.category = selectedCategory.value.toString();
  } else {
    delete query.category;
  }

  // 更新页码参数
  if (currentPage.value > 1) {
    query.page = currentPage.value.toString();
  } else {
    delete query.page;
  }

  // 更新排序参数
  if (sortOption.value !== 'newest') {
    query.sort = sortOption.value;
  } else {
    delete query.sort;
  }

  // 更新价格范围参数
  if (priceRange.value.min > 0 || priceRange.value.max < 10000) {
    query.minPrice = priceRange.value.min.toString();
    query.maxPrice = priceRange.value.max.toString();
  } else {
    delete query.minPrice;
    delete query.maxPrice;
  }

  // 更新URL，不重新加载页面
  router.replace({ query });
}

// 监听排序变化
watch(sortOption, () => {
  currentPage.value = 1; // 重置页码
  updateUrlParams();
  fetchProducts();
});

// 初始化
onMounted(async () => {
  // 从URL获取参数
  const { category, page, sort, minPrice, maxPrice } = route.query;

  if (category) {
    selectedCategory.value = parseInt(category);
  }

  if (page) {
    currentPage.value = parseInt(page);
  }

  if (sort && sortOptions.some((option) => option.value === sort)) {
    sortOption.value = sort;
  }

  // 处理价格范围参数
  if (minPrice || maxPrice) {
    priceRange.value = {
      min: minPrice ? parseInt(minPrice) : 0,
      max: maxPrice ? parseInt(maxPrice) : 10000,
    };
  }

  // 获取分类和商品
  await fetchCategories();
  await fetchProducts();
});
</script>

<style lang="scss" scoped>
.products-page {
  width: 100%;
  background-color: #f8f9fa;
}

.products-banner {
  background: #2196f3;
  padding: 15px 0;
  color: white;
  margin-bottom: 20px;
  position: relative;
  overflow: hidden;

  @media (max-width: 599px) {
    padding: 12px 0;
    margin-bottom: 15px;
  }
}

.products-banner-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
}

.banner-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;

  @media (max-width: 599px) {
    flex-direction: column;
    align-items: flex-start;
  }
}

.products-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  white-space: nowrap;

  @media (max-width: 599px) {
    font-size: 1.3rem;
    margin-bottom: 5px;
  }
}

.products-breadcrumbs {
  :deep(.q-breadcrumbs__el) {
    color: white;
    font-weight: 400;
    font-size: 0.9rem;
  }

  :deep(a) {
    color: rgba(255, 255, 255, 0.9);

    &:hover {
      color: white;
      text-decoration: underline;
    }
  }
}

.self-operated-notice {
  font-size: 0.9rem;
  margin: 0;
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.15);
  padding: 6px 10px;
  border-radius: 4px;
  line-height: 1.4;

  @media (max-width: 599px) {
    font-size: 0.8rem;
    padding: 5px 8px;
  }
}

.products-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px 40px;

  @media (max-width: 599px) {
    padding: 0 15px 30px;
  }
}

.products-content {
  display: flex;

  @media (max-width: 1023px) {
    flex-direction: column;
  }
}

.products-main-content {
  display: flex;
  width: 100%;
  gap: 20px;

  @media (max-width: 1023px) {
    flex-direction: column;
    gap: 15px;
  }
}

.category-sidebar {
  width: 230px;
  min-width: 230px;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
  overflow: hidden;
  align-self: flex-start;

  @media (max-width: 1023px) {
    margin-bottom: 15px;
    width: 100%;
  }

  .sidebar-title {
    font-size: 1rem;
    font-weight: 500;
    color: #333;
    margin: 0;
    padding: 12px 15px;
    display: flex;
    align-items: center;
    background-color: #f5f5f5;
    border-bottom: 1px solid #e0e0e0;
  }
}

.price-filter {
  margin-top: 15px;
  border-top: 1px solid #e0e0e0;

  .price-slider {
    background-color: #f5f5f5;
    padding-top: 15px;
  }

  .price-filter-actions {
    display: flex;
    justify-content: space-between;
  }
}

.drawer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 15px;
  background-color: #f5f5f5;

  .drawer-title {
    font-size: 1rem;
    font-weight: 500;
    color: #333;
    display: flex;
    align-items: center;
  }
}

.category-drawer {
  :deep(.q-drawer__content) {
    background-color: white;
  }
}

.products-list-container {
  flex: 1;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
  padding: 15px;

  @media (max-width: 599px) {
    padding: 12px;
  }
}

.products-filter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  flex-wrap: wrap;
  padding-bottom: 12px;
  border-bottom: 1px solid #e0e0e0;

  @media (max-width: 599px) {
    margin-bottom: 12px;
  }

  .filter-left {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 8px;

    @media (max-width: 767px) {
      width: 100%;
      margin-bottom: 12px;
    }
  }

  .filter-right {
    display: flex;
    align-items: center;

    @media (max-width: 767px) {
      width: 100%;
      justify-content: space-between;
    }
  }

  .category-btn {
    margin-right: 8px;
  }

  .products-count {
    color: #666;
    font-size: 0.85rem;
    margin-left: 12px;

    @media (max-width: 767px) {
      margin-left: auto;
    }
  }

  .sort-select {
    width: 150px;
    margin-right: 12px;

    @media (max-width: 599px) {
      width: 130px;
      margin-right: 8px;
    }
  }
}

.products-list {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 15px;

  @media (max-width: 1199px) {
    grid-template-columns: repeat(3, 1fr);
  }

  @media (max-width: 767px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  @media (max-width: 479px) {
    grid-template-columns: repeat(1, 1fr);
  }

  &.list-view {
    grid-template-columns: 1fr;
  }
}

.product-item {
  transition: transform 0.2s ease;

  &:hover {
    transform: translateY(-3px);
  }
}

.product-skeleton-card {
  height: 100%;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #e0e0e0;
}

.no-products {
  grid-column: 1 / -1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  color: #999;
  background-color: #f5f5f5;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
  margin: 15px 0;

  p {
    margin: 12px 0;
    font-size: 1rem;
    color: #666;
  }
}

.pagination-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #e0e0e0;

  .pagination-info {
    margin-bottom: 12px;
    color: #666;
    font-size: 0.85rem;
  }
}
</style>
