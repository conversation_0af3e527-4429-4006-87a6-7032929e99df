/**
 * 货币格式化指令
 * 提供在模板中直接使用的货币格式化功能
 */

import { useCurrencyStore } from '~/store/currency';
import { formatCurrency } from '~/utils/currencyUtils';

export default defineNuxtPlugin((nuxtApp) => {
  // 创建货币格式化指令
  nuxtApp.vueApp.directive('currency', {
    // 当绑定元素的值更新时调用
    updated(el, binding) {
      const currencyStore = useCurrencyStore();
      const amount = binding.value;
      
      // 获取指令参数
      const options = binding.arg ? JSON.parse(binding.arg) : {};
      
      // 格式化金额
      el.innerText = formatCurrency(
        amount, 
        currencyStore.selectedCurrency, 
        currencyStore.defaultCurrency,
        options
      );
    },
    // 当指令第一次绑定到元素时调用
    mounted(el, binding) {
      const currencyStore = useCurrencyStore();
      const amount = binding.value;
      
      // 获取指令参数
      const options = binding.arg ? JSON.parse(binding.arg) : {};
      
      // 格式化金额
      el.innerText = formatCurrency(
        amount, 
        currencyStore.selectedCurrency, 
        currencyStore.defaultCurrency,
        options
      );
    }
  });
});
