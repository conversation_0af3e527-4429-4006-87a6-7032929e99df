<template>
  <Header />
  <div class="about-page">
    <div class="about-container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1 class="page-title">关于我们</h1>
        <div class="page-subtitle">您值得信赖的全球购物伙伴</div>
      </div>

      <!-- 公司简介 -->
      <div class="section company-intro q-mb-xl">
        <div class="row q-col-gutter-lg">
          <div class="col-12 col-md-6">
            <h2 class="section-title">公司简介</h2>
            <p class="section-text">我们是一家专注于海外代购和国际转运服务的电商平台，成立于2018年。五年来，我们始终秉持"诚信、专业、高效"的服务理念，为全球消费者提供优质的跨境购物体验。</p>
            <p class="section-text">
              公司总部位于上海，在日本、美国、英国、澳大利亚等多个国家设有采购和仓储中心，拥有专业的国际物流团队和客户服务团队，确保商品从采购到配送的每一个环节都能得到精心处理。
            </p>
            <p class="section-text">截至目前，我们已服务超过100万用户，年交易额突破5亿元人民币，是国内领先的跨境电商平台之一。</p>
          </div>
          <div class="col-12 col-md-6 flex flex-center">
            <q-img src="https://cdn.quasar.dev/img/parallax1.jpg" class="company-image rounded-borders" fit="cover" />
          </div>
        </div>
      </div>

      <!-- 我们的使命和愿景 -->
      <div class="section mission-vision q-mb-xl">
        <div class="row q-col-gutter-lg reverse">
          <div class="col-12 col-md-6">
            <h2 class="section-title">使命与愿景</h2>
            <div class="mission q-mb-lg">
              <h3 class="subsection-title">我们的使命</h3>
              <p class="section-text">打破国界限制，让全球优质商品触手可及。我们致力于为消费者提供安全、便捷、透明的跨境购物服务，让每一位用户都能轻松享受全球购物的乐趣。</p>
            </div>
            <div class="vision">
              <h3 class="subsection-title">我们的愿景</h3>
              <p class="section-text">成为全球领先的跨境电商平台，连接世界各地的优质商品和消费者，创造无国界的购物体验。</p>
            </div>
          </div>
          <div class="col-12 col-md-6 flex flex-center">
            <q-img src="https://cdn.quasar.dev/img/mountains.jpg" class="mission-image rounded-borders" fit="cover" />
          </div>
        </div>
      </div>

      <!-- 我们的优势 -->
      <div class="section advantages q-mb-xl">
        <h2 class="section-title text-center">我们的优势</h2>
        <div class="row q-col-gutter-lg q-mt-md">
          <div class="col-12 col-sm-6 col-md-3">
            <div class="advantage-card">
              <q-icon name="verified" size="3rem" color="primary" class="q-mb-md" />
              <h3 class="advantage-title">正品保障</h3>
              <p class="advantage-text">所有商品均从官方渠道或授权经销商处采购，100%正品保证，假一赔十。</p>
            </div>
          </div>
          <div class="col-12 col-sm-6 col-md-3">
            <div class="advantage-card">
              <q-icon name="local_shipping" size="3rem" color="primary" class="q-mb-md" />
              <h3 class="advantage-title">全球配送</h3>
              <p class="advantage-text">覆盖全球200多个国家和地区，多种物流方式可选，安全快捷。</p>
            </div>
          </div>
          <div class="col-12 col-sm-6 col-md-3">
            <div class="advantage-card">
              <q-icon name="support_agent" size="3rem" color="primary" class="q-mb-md" />
              <h3 class="advantage-title">专业客服</h3>
              <p class="advantage-text">7×24小时在线客服，多语言支持，解决您的任何问题。</p>
            </div>
          </div>
          <div class="col-12 col-sm-6 col-md-3">
            <div class="advantage-card">
              <q-icon name="price_check" size="3rem" color="primary" class="q-mb-md" />
              <h3 class="advantage-title">透明定价</h3>
              <p class="advantage-text">商品价格、服务费、物流费用全部透明展示，无隐藏费用。</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 发展历程 -->
      <div class="section history q-mb-xl">
        <h2 class="section-title text-center">发展历程</h2>
        <div class="timeline-container q-mt-lg">
          <q-timeline color="primary">
            <q-timeline-entry heading>公司发展大事记</q-timeline-entry>

            <q-timeline-entry title="2018年" subtitle="公司成立" icon="business" color="primary">
              <div>公司在上海成立，初期专注于日本商品代购服务，团队仅有5人。</div>
            </q-timeline-entry>

            <q-timeline-entry title="2019年" subtitle="业务扩展" icon="flight_takeoff" color="secondary">
              <div>业务扩展至美国、韩国市场，开设第一个海外仓库，团队扩展至20人。</div>
            </q-timeline-entry>

            <q-timeline-entry title="2020年" subtitle="疫情挑战与转型" icon="coronavirus" color="negative">
              <div>面对全球疫情挑战，公司迅速调整战略，加强线上服务，推出"云购物"服务，实现逆势增长。</div>
            </q-timeline-entry>

            <q-timeline-entry title="2021年" subtitle="获得融资" icon="attach_money" color="positive">
              <div>获得A轮融资1亿元，加速全球仓储网络建设，引入AI客服系统，提升用户体验。</div>
            </q-timeline-entry>

            <q-timeline-entry title="2022年" subtitle="平台升级" icon="upgrade" color="info">
              <div>全新平台上线，引入区块链技术保障商品溯源，用户数突破50万。</div>
            </q-timeline-entry>

            <q-timeline-entry title="2023年" subtitle="全球扩张" icon="public" color="accent">
              <div>业务覆盖全球主要市场，员工超过200人，年交易额突破5亿元。</div>
            </q-timeline-entry>
          </q-timeline>
        </div>
      </div>

      <!-- 团队介绍 -->
      <div class="section team q-mb-xl">
        <h2 class="section-title text-center">核心团队</h2>
        <div class="row q-col-gutter-lg q-mt-md">
          <div class="col-12 col-sm-6 col-md-3">
            <div class="team-card">
              <q-img src="https://cdn.quasar.dev/img/boy-avatar.png" class="team-avatar" />
              <h3 class="team-name">张明</h3>
              <div class="team-position">创始人 & CEO</div>
              <p class="team-bio">前阿里巴巴国际业务负责人，拥有15年跨境电商经验。</p>
            </div>
          </div>
          <div class="col-12 col-sm-6 col-md-3">
            <div class="team-card">
              <q-img src="https://cdn.quasar.dev/img/avatar.png" class="team-avatar" />
              <h3 class="team-name">李华</h3>
              <div class="team-position">COO</div>
              <p class="team-bio">前顺丰国际物流总监，专注于全球供应链优化。</p>
            </div>
          </div>
          <div class="col-12 col-sm-6 col-md-3">
            <div class="team-card">
              <q-img src="https://cdn.quasar.dev/img/avatar3.jpg" class="team-avatar" />
              <h3 class="team-name">王芳</h3>
              <div class="team-position">CTO</div>
              <p class="team-bio">前Google工程师，负责平台技术架构和创新。</p>
            </div>
          </div>
          <div class="col-12 col-sm-6 col-md-3">
            <div class="team-card">
              <q-img src="https://cdn.quasar.dev/img/avatar4.jpg" class="team-avatar" />
              <h3 class="team-name">刘强</h3>
              <div class="team-position">CMO</div>
              <p class="team-bio">前京东营销总监，擅长数字营销和用户增长。</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 联系我们和商务合作入口 -->
      <div class="section cta-section">
        <div class="row q-col-gutter-lg">
          <div class="col-12 col-md-6">
            <q-card class="cta-card">
              <q-card-section class="text-center">
                <q-icon name="contact_support" size="4rem" color="primary" />
                <h3 class="cta-title">联系我们</h3>
                <p class="cta-text">有任何问题或建议？我们的客服团队随时为您提供帮助。</p>
                <q-btn color="primary" to="/contact" label="联系我们" class="q-mt-sm" />
              </q-card-section>
            </q-card>
          </div>
          <div class="col-12 col-md-6">
            <q-card class="cta-card">
              <q-card-section class="text-center">
                <q-icon name="handshake" size="4rem" color="secondary" />
                <h3 class="cta-title">商务合作</h3>
                <p class="cta-text">寻找商业合作机会？我们期待与您共创价值。</p>
                <q-btn color="secondary" to="/business" label="商务合作" class="q-mt-sm" />
              </q-card-section>
            </q-card>
          </div>
        </div>
      </div>
    </div>
  </div>
  <Footer />
</template>

<script setup>
// 可以在这里添加任何需要的逻辑
</script>

<style lang="scss" scoped>
.about-page {
  padding: 20px 0 40px;
  background-color: #f8f8f8;
}

.about-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
  padding: 30px 0;
}

.page-title {
  font-size: 32px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

.page-subtitle {
  font-size: 18px;
  color: #666;
}

.section {
  margin-bottom: 60px;
}

.section-title {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 20px;
  position: relative;

  &:after {
    content: '';
    display: block;
    width: 50px;
    height: 3px;
    background-color: #1976d2;
    margin-top: 10px;
  }

  &.text-center:after {
    margin-left: auto;
    margin-right: auto;
  }
}

.subsection-title {
  font-size: 18px;
  font-weight: bold;
  color: #1976d2;
  margin-bottom: 10px;
}

.section-text {
  font-size: 16px;
  line-height: 1.6;
  color: #555;
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }
}

.company-image,
.mission-image {
  width: 100%;
  max-height: 400px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.advantage-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 24px;
  height: 100%;
  text-align: center;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
  }
}

.advantage-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

.advantage-text {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.timeline-container {
  padding: 20px 0;
}

.team-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 24px;
  height: 100%;
  text-align: center;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
  }
}

.team-avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  margin: 0 auto 16px;
  border: 3px solid #f0f0f0;
}

.team-name {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.team-position {
  font-size: 14px;
  color: #1976d2;
  margin-bottom: 10px;
  font-weight: 500;
}

.team-bio {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.cta-card {
  height: 100%;
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
  }
}

.cta-title {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  margin: 16px 0 10px;
}

.cta-text {
  font-size: 15px;
  color: #666;
  margin-bottom: 16px;
}

@media (max-width: 767px) {
  .page-header {
    margin-bottom: 30px;
    padding: 20px 0;
  }

  .page-title {
    font-size: 26px;
  }

  .page-subtitle {
    font-size: 16px;
  }

  .section {
    margin-bottom: 40px;
  }

  .section-title {
    font-size: 22px;
    margin-bottom: 15px;
  }

  .subsection-title {
    font-size: 17px;
  }

  .section-text {
    font-size: 15px;
  }

  .company-image,
  .mission-image {
    margin-top: 20px;
    max-height: 300px;
  }

  .advantage-card,
  .team-card {
    padding: 20px;
    margin-bottom: 20px;
  }

  .team-avatar {
    width: 100px;
    height: 100px;
  }

  .cta-card {
    margin-bottom: 20px;
  }
}
</style>
