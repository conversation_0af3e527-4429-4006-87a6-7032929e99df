<template>
  <div class="points-selector-section">
    <div class="row items-center q-mb-sm">
      <q-icon name="stars" color="primary" size="sm" class="q-mr-sm" />
      <div class="text-h6">积分抵扣</div>
    </div>
    <div class="text-caption text-grey-8 q-mb-md">使用积分可抵扣部分运费</div>

    <q-card flat bordered class="q-pa-md">
      <div class="row items-center justify-between q-mb-md">
        <div>
          <div class="text-body1">
            可用积分: <span class="text-weight-medium">{{ availablePoints }}</span>
          </div>
          <div class="text-caption text-grey-8">当前积分抵扣比例: 100积分 = ¥1.00</div>
        </div>
        <q-toggle v-model="usePoints" color="primary" label="使用积分" :disable="availablePoints <= 0" @update:model-value="updatePointsUsage" />
      </div>

      <div v-if="usePoints" class="q-mt-md">
        <div class="row items-center">
          <div class="col-12 col-sm-8">
            <q-slider v-model="pointsToUse" :min="0" :max="availablePoints" :step="100" label label-always color="primary" @update:model-value="updatePointsUsage" />
          </div>
          <div class="col-12 col-sm-4">
            <q-input v-model.number="pointsToUse" type="number" outlined dense :min="0" :max="availablePoints" :step="100" suffix="积分" @update:model-value="validatePointsInput" />
          </div>
        </div>
        <div class="row justify-end q-mt-sm">
          <div class="text-body1">
            抵扣金额: <span class="text-negative text-weight-medium">-¥{{ pointsDiscountAmount.toFixed(2) }}</span>
          </div>
        </div>
      </div>
    </q-card>
  </div>
</template>

<script setup>
import { ref, watch, computed } from 'vue';

// 定义组件接收的属性
const props = defineProps({
  modelValue: {
    type: Number,
    default: 0,
  },
  availablePoints: {
    type: Number,
    required: true,
    default: 0,
  },
  maxDiscountAmount: {
    type: Number,
    required: true,
    default: 0,
  },
});

// 定义组件向外发出的事件
const emit = defineEmits(['update:modelValue', 'calculate-points-discount']);

// 是否使用积分
const usePoints = ref(props.modelValue > 0);

// 要使用的积分数量
const pointsToUse = ref(props.modelValue);

// 积分抵扣金额
const pointsDiscountAmount = computed(() => {
  return usePoints.value ? pointsToUse.value / 100 : 0;
});

// 更新积分使用情况
function updatePointsUsage() {
  if (!usePoints.value) {
    pointsToUse.value = 0;
  }

  // 确保积分不超过最大可用积分
  if (pointsToUse.value > props.availablePoints) {
    pointsToUse.value = props.availablePoints;
  }

  // 确保积分抵扣金额不超过最大抵扣金额
  const maxPoints = props.maxDiscountAmount * 100;
  if (pointsToUse.value > maxPoints) {
    pointsToUse.value = Math.floor(maxPoints / 100) * 100;
  }

  emit('update:modelValue', pointsToUse.value);
  emit('calculate-points-discount');
}

// 验证积分输入
function validatePointsInput(value) {
  // 确保积分是100的整数倍
  pointsToUse.value = Math.floor(value / 100) * 100;
  updatePointsUsage();
}

// 监听父组件传入的值变化
watch(
  () => props.modelValue,
  (newValue) => {
    pointsToUse.value = newValue;
    usePoints.value = newValue > 0;
  }
);

// 监听可用积分变化
watch(
  () => props.availablePoints,
  (newValue) => {
    if (pointsToUse.value > newValue) {
      pointsToUse.value = newValue;
      updatePointsUsage();
    }
  }
);

// 监听最大抵扣金额变化
watch(
  () => props.maxDiscountAmount,
  () => {
    updatePointsUsage();
  }
);
</script>

<style lang="scss" scoped>
.points-selector-section {
  .q-card {
    border-radius: 8px;
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    }
  }
}
</style>
