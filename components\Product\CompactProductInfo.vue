<template>
  <div class="compact-product-info">
    <!-- 价格行 -->
    <div class="price-row q-px-md">
      <div class="row items-center no-wrap">
        <div class="col-3 text-right q-pr-md">
          <span class="text-grey-7">单价:</span>
        </div>
        <div class="col-9">
          <span class="text-red text-weight-medium">¥{{ unitPrice }}</span>
        </div>
      </div>
    </div>

    <!-- 数量行 -->
    <div class="quantity-row q-px-md q-mt-xs">
      <div class="row items-center no-wrap">
        <div class="col-3 text-right q-pr-md">
          <span class="text-grey-7">数量:</span>
        </div>
        <div class="col-9 vertical-center">
          <q-btn flat round dense size="sm" icon="remove" @click="decreaseQuantity" :disable="quantity <= 1" class="q-mr-xs" />
          <input
            v-model.number="localQuantity"
            type="text"
            maxlength="3"
            class="quantity-input"
            @change="updateQuantity"
            @input="validateInput"
          />
          <q-btn flat round dense size="sm" icon="add" @click="increaseQuantity" class="q-ml-xs" />
        </div>
      </div>
    </div>

    <!-- 备注行 -->
    <div class="memo-row q-px-md q-mt-xs">
      <div class="row items-center no-wrap">
        <div class="col-3 text-right q-pr-md">
          <span class="text-grey-7">备注:</span>
        </div>
        <div class="col-9 vertical-center">
          <span v-if="memo" class="text-grey-7">{{ memo }}</span>
          <span v-else class="text-grey-6">请使用优惠券 10.1购买</span>
          <q-btn flat round dense size="xs" icon="edit" color="primary" class="q-ml-xs" @click="$emit('edit-memo')" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue';

const props = defineProps({
  unitPrice: {
    type: String,
    required: true
  },
  quantity: {
    type: Number,
    default: 1
  },
  memo: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['update:quantity', 'edit-memo']);

const localQuantity = ref(props.quantity);

watch(() => props.quantity, (newVal) => {
  localQuantity.value = newVal;
});

const decreaseQuantity = () => {
  if (localQuantity.value > 1) {
    localQuantity.value--;
    emit('update:quantity', localQuantity.value);
  }
};

const increaseQuantity = () => {
  localQuantity.value++;
  emit('update:quantity', localQuantity.value);
};

const updateQuantity = () => {
  // 确保数量是有效的数字且不小于1
  const newQuantity = Math.max(1, parseInt(localQuantity.value) || 1);
  localQuantity.value = newQuantity;
  emit('update:quantity', newQuantity);
};

const validateInput = (e) => {
  // 只允许输入数字
  e.target.value = e.target.value.replace(/[^0-9]/g, '');
};
</script>

<style scoped>
.compact-product-info {
  margin-top: 8px;
}

.price-row, .quantity-row, .memo-row {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 6px 0;
}

.quantity-row, .memo-row {
  margin-top: 4px;
}

.quantity-input {
  width: 36px;
  height: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  text-align: center;
  font-size: 12px;
  line-height: 1;
  padding: 0;
  outline: none;
}

.quantity-input:focus {
  border-color: #1976d2;
}

.vertical-center {
  display: flex;
  align-items: center;
}
</style>
