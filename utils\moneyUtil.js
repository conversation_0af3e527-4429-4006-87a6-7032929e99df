/**
 * 计算汇率转换后的金额，单位为分
 *
 * @param {number} amountInCents - 金额，单位：分（例如 100 分 = 1 元）
 * @param {number} exchangeRate - 汇率，源币种转换到目标币种的比率
 * @returns {number} - 转换后的金额，单位：分
 * @throws {Error} - 输入无效时抛出异常
 */
export function exchangeRateConvert(amount, exchangeRate) {
  // 验证输入是否有效
  if (exchangeRate <= 0) {
    throw new Error('汇率必须大于零');
  }

  // 保存金额的符号
  const sign = amount < 0 ? -1 : 1;
  // 使用绝对值进行计算
  const absAmount = Math.abs(amount);

  // 转换金额：直接在分为单位上进行计算，四舍五入到整数
  let convertedAmount = absAmount * exchangeRate;

  // 四舍五入到最接近的整数（单位：分）
  convertedAmount = Math.round(convertedAmount);

  // 返回转换后的金额（单位：分），恢复原始符号
  return convertedAmount * sign;
}
