<template>
  <div class="warehouse-detail q-pa-md">
    <!-- 标题区域 -->
    <div class="bg-grey-2 q-px-md q-py-sm rounded-borders">
      <div class="row justify-between items-center">
        <div class="row items-center">
          <q-btn flat dense color="primary" icon="arrow_back" @click="goBack" class="q-mr-sm" />
          <span class="text-subtitle1 text-weight-medium">{{ $t('warehouseDetail.title') }}</span>
        </div>
        <div class="row items-center">
          <q-btn flat dense color="primary" to="/account/stock" :label="$t('warehouseDetail.backToList')" class="q-px-sm" />
        </div>
      </div>
    </div>

    <!-- 加载中状态 -->
    <div v-if="loading" class="column items-center q-py-xl">
      <q-spinner color="primary" size="3em" />
      <div class="q-mt-sm text-grey-7">{{ $t('warehouseDetail.loading') }}</div>
    </div>

    <!-- 商品不存在 -->
    <div v-else-if="!state.stockInfo.id" class="column items-center q-py-xl">
      <q-icon name="error_outline" size="3em" color="grey-5" />
      <div class="q-mt-sm text-grey-7">{{ $t('warehouseDetail.notFound') }}</div>
      <q-btn color="primary" class="q-mt-md" :label="$t('warehouseDetail.backToList')" @click="goBack" />
    </div>

    <!-- 商品详情内容 -->
    <div v-else>
      <!-- 商品头部信息 -->
      <q-card class="q-mt-md product-header-card">
        <q-card-section class="bg-grey-2">
          <div class="text-subtitle2 text-weight-medium">{{ $t('warehouseDetail.sections.basicInfo') }}</div>
        </q-card-section>
        <q-card-section>
          <div class="row q-col-gutter-lg">
            <!-- 商品图片 -->
            <div class="col-12 col-sm-4 col-md-3">
              <q-img
                :src="getThumbnailUrl(state.stockInfo.picUrl, '200x200')"
                spinner-color="primary"
                class="rounded-borders product-main-image"
                @click="state.stockInfo.picUrl && openImageViewer([state.stockInfo.picUrl])">
                <template v-slot:error>
                  <div class="absolute-full flex flex-center bg-grey-3">
                    <q-icon name="image" size="3rem" color="grey-7" />
                  </div>
                </template>
              </q-img>
            </div>

            <!-- 商品主要信息 -->
            <div class="col-12 col-sm-8 col-md-9">
              <div class="product-title text-h6 text-weight-medium q-mb-md">{{ state.stockInfo.spuName }}</div>

              <div class="row q-col-gutter-md">
                <!-- 规格 -->
                <div class="col-12 q-mb-sm">
                  <div class="row">
                    <div class="col-12 col-sm-3 col-md-2 text-grey-7">规格</div>
                    <div class="col-12 col-sm-9 col-md-10 text-weight-medium">{{ formattedProperties(state.stockInfo.properties) }}</div>
                  </div>
                </div>

                <!-- 数量 -->
                <div class="col-12 col-sm-6 col-md-4 q-mb-sm">
                  <div class="row">
                    <div class="col-5 text-grey-7">{{ $t('warehouseDetail.fields.count') }}</div>
                    <div class="col-7 text-weight-medium">{{ state.stockInfo.count }}</div>
                  </div>
                </div>

                <!-- 重量 -->
                <div class="col-12 col-sm-6 col-md-4 q-mb-sm">
                  <div class="row">
                    <div class="col-5 text-grey-7">{{ $t('warehouseDetail.fields.weight') }}</div>
                    <div class="col-7">{{ state.stockInfo.weight }} g</div>
                  </div>
                </div>

                <!-- 体积 -->
                <div class="col-12 col-sm-6 col-md-4 q-mb-sm">
                  <div class="row">
                    <div class="col-5 text-grey-7">{{ $t('warehouseDetail.fields.volume') }}</div>
                    <div class="col-7">{{ state.stockInfo.volume }} cm³</div>
                  </div>
                </div>

                <!-- 入库时间 -->
                <div class="col-12 col-sm-6 col-md-4">
                  <div class="row">
                    <div class="col-5 text-grey-7">{{ $t('warehouseDetail.fields.inTime') }}</div>
                    <div class="col-7">{{ formatTimestampToWesternDate(state.stockInfo.inTime) }}</div>
                  </div>
                </div>

                <!-- 到期时间 -->
                <div class="col-12 col-sm-6 col-md-4">
                  <div class="row">
                    <div class="col-5 text-grey-7">{{ $t('warehouseDetail.fields.expiredTime') }}</div>
                    <div class="col-7">{{ formatTimestampToWesternDate(state.stockInfo.expiredTime) || $t('warehouseDetail.placeholders.none') }}</div>
                  </div>
                </div>

                <!-- 状态 -->
                <div class="col-12 col-sm-6 col-md-4">
                  <div class="row">
                    <div class="col-5 text-grey-7">{{ $t('warehouseDetail.fields.status') }}</div>
                    <div class="col-7">
                      <q-badge :color="getStatusColor(state.stockInfo.status)">
                        {{ getStatusText(state.stockInfo.status) }}
                      </q-badge>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- 质检图片 -->
      <q-card class="q-mt-md" v-if="state.stockInfo.inspectPicUrls && state.stockInfo.inspectPicUrls.length > 0">
        <q-card-section class="bg-grey-2">
          <div class="text-subtitle2 text-weight-medium">{{ $t('warehouseDetail.sections.inspectionImages') }}</div>
        </q-card-section>
        <q-card-section>
          <div class="row q-col-gutter-md">
            <div v-for="(img, index) in state.stockInfo.inspectPicUrls" :key="index" class="col-12 col-sm-4 col-md-3">
              <q-img :src="getThumbnailUrl(img, '200x200')" spinner-color="primary" class="rounded-borders inspection-image" @click="openImageViewer(state.stockInfo.inspectPicUrls, index)" />
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- 质检视频 -->
      <q-card class="q-mt-md" v-if="state.stockInfo.inspectVideoUrls && state.stockInfo.inspectVideoUrls.length > 0">
        <q-card-section class="bg-grey-2">
          <div class="text-subtitle2 text-weight-medium">{{ $t('warehouseDetail.sections.inspectionVideos') }}</div>
        </q-card-section>
        <q-card-section>
          <div class="row q-col-gutter-md">
            <div v-for="(video, index) in state.stockInfo.inspectVideoUrls" :key="index" class="col-12 col-sm-6 col-md-4">
              <div class="video-container">
                <div class="rounded-borders video-thumbnail bg-grey-3 flex flex-center" @click="playVideo(video)">
                  <div class="column items-center">
                    <q-icon name="videocam" size="3rem" color="primary" />
                    <div class="text-caption q-mt-sm">{{ $t('warehouseDetail.videoPlayer.clickToPlay') }}</div>
                  </div>
                </div>
                <div class="row justify-end q-mt-xs">
                  <q-btn flat dense color="primary" icon="download" size="sm" @click="downloadFile(video)" />
                </div>
              </div>
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- 相关文件 -->
      <q-card class="q-mt-md" v-if="state.stockInfo.fileUrl">
        <q-card-section class="bg-grey-2">
          <div class="text-subtitle2 text-weight-medium">{{ $t('warehouseDetail.sections.relatedFiles') }}</div>
        </q-card-section>
        <q-card-section>
          <div class="row items-center">
            <q-icon name="attach_file" size="md" color="primary" class="q-mr-md" />
            <div class="col">
              <div class="text-body1">{{ getFileName(state.stockInfo.fileUrl) }}</div>
            </div>
            <q-btn flat color="primary" icon="download" @click="downloadFile(state.stockInfo.fileUrl)" />
          </div>
        </q-card-section>
      </q-card>
    </div>

    <!-- 图片查看器 -->
    <q-dialog v-model="imageViewer.show" full-width>
      <q-card class="image-viewer-card">
        <q-card-section class="row items-center q-pb-none">
          <div class="text-h6">{{ $t('warehouseDetail.imageViewer.title') }}</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-card-section class="q-pt-none">
          <q-carousel v-model="imageViewer.currentIndex" animated arrows navigation infinite height="70vh">
            <q-carousel-slide v-for="(img, index) in imageViewer.images" :key="index" :name="index" class="column flex-center">
              <q-img :src="img" fit="contain" style="max-height: 70vh; width: 100%" />
            </q-carousel-slide>
          </q-carousel>
        </q-card-section>
      </q-card>
    </q-dialog>

    <!-- 视频播放器 -->
    <q-dialog v-model="videoPlayer.show" full-width>
      <q-card class="video-player-card">
        <q-card-section class="row items-center q-pb-none">
          <div class="text-h6">{{ $t('warehouseDetail.videoPlayer.title') }}</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-card-section class="q-pt-none">
          <video ref="videoRef" controls class="full-width" style="max-height: 70vh" :src="videoPlayer.url"></video>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat color="primary" icon="download" :label="$t('warehouseDetail.buttons.download')" @click="downloadFile(videoPlayer.url)" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
import StockApi from '~/composables/stockApi';
import { useI18n } from 'vue-i18n';
import { formattedProperties } from '~/utils/productUtils';

definePageMeta({
  middleware: 'auth', // 引入身份验证中间件
});

const route = useRoute();
const router = useNuxtApp().$router;
const { t } = useI18n();

const loading = ref(true);
const videoRef = ref(null);

const state = reactive({
  stockInfo: {},
});

// 图片查看器状态
const imageViewer = reactive({
  show: false,
  images: [],
  currentIndex: 0,
});

// 视频播放器状态
const videoPlayer = reactive({
  show: false,
  url: '',
});

onMounted(() => {
  // 从路由参数获取库存ID
  const stockId = route.query.id;
  if (stockId) {
    getStockDetail(stockId);
  } else {
    loading.value = false;
  }
});

// 获取库存详情
async function getStockDetail(id) {
  loading.value = true;
  try {
    const { code, data } = await StockApi.getStockDetail(id);
    if (code === 0 && data) {
      state.stockInfo = data;
    } else {
      showNotify(t('warehouseDetail.errors.fetchFailed'), 'negative');
    }
  } catch (error) {
    console.error(t('warehouseDetail.errors.fetchError') + ':', error);
    showNotify(t('warehouseDetail.errors.fetchError'), 'negative');
  } finally {
    loading.value = false;
  }
}

// 返回列表
function goBack() {
  router.push('/account/stock');
}

// 打开图片查看器
function openImageViewer(images, startIndex = 0) {
  imageViewer.images = images;
  imageViewer.currentIndex = startIndex;
  imageViewer.show = true;
}

// 播放视频
function playVideo(url) {
  videoPlayer.url = url;
  videoPlayer.show = true;
}

// 下载文件
function downloadFile(url) {
  if (!url) return;

  // 创建一个临时链接
  const link = document.createElement('a');
  link.href = url;
  link.target = '_blank';
  link.download = getFileName(url);
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

// 获取文件名
function getFileName(url) {
  if (!url) return '';
  return url.split('/').pop() || t('warehouseDetail.placeholders.file');
}

// 获取状态文本
function getStatusText(status) {
  switch (status) {
    case 0:
      return t('warehouseDetail.status.normal');
    case 2:
      return t('warehouseDetail.status.locked');
    case 3:
      return t('warehouseDetail.status.expired');
    default:
      return t('warehouseDetail.status.unknown');
  }
}

// 获取状态颜色
function getStatusColor(status) {
  switch (status) {
    case 0:
      return 'positive';
    case 2:
      return 'warning';
    case 3:
      return 'negative';
    default:
      return 'grey';
  }
}
</script>

<style lang="scss" scoped>
.warehouse-detail {
  max-width: 1200px;
  margin: 0 auto;

  .product-header-card {
    .product-title {
      color: #1976d2;
      line-height: 1.4;
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
      word-break: break-word;
    }
  }

  .product-main-image {
    width: 100%;
    height: 200px;
    max-height: 300px;
    cursor: pointer;
    object-fit: contain;
    background-color: #f5f5f5;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
      transform: translateY(-2px);
    }
  }

  .inspection-image {
    width: 100%;
    height: 150px;
    cursor: pointer;
    object-fit: cover;
    border-radius: 8px;
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
      transform: translateY(-2px);
    }
  }

  .video-container {
    .video-thumbnail {
      width: 100%;
      height: 150px;
      cursor: pointer;
      transition: all 0.2s ease;
      border-radius: 8px;
      border: 1px solid #e0e0e0;

      &:hover {
        box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
      }
    }
  }

  .image-viewer-card,
  .video-player-card {
    width: 90vw;
    max-width: 1000px;
  }

  // 响应式调整
  @media (max-width: 599px) {
    .product-title {
      font-size: 1.25rem;
      margin-top: 1rem;
    }
  }
}
</style>
