# 尺码助手实现文档

## 概述
重新实现了尺码助手组件 `SizeGuideContent.vue`，解决了原有的"No data available"问题，并添加了完整的数据结构和智能选项管理。

## 主要功能

### 1. 数据结构重组
- 将原有复杂的嵌套数据结构重新组织为更清晰的格式
- 每个服装类型包含：
  - `measurements`: 测量部位定义（CM/IN）
  - `sizes`: 基础尺码列表
  - `data`: 具体测量数据
  - `countries`: 各国尺码对照

### 2. 智能选项管理
- **自动禁用无效组合**: 当某个国家不支持当前服装类型时，该选项会被置灰
- **自动切换有效选项**: 当切换服装类型时，自动切换到有效的单位和国家
- **特殊处理**: 戒指类型的英寸单位被自动禁用（因为数据为N/A）

### 3. 支持的服装类型
- 女士上衣 (women's top)
- 男士上衣 (men's top)  
- 女士裤子 (women's pants)
- 男士裤子 (men's pants)
- 女士鞋子 (women's shoes)
- 男士鞋子 (men's shoes)
- 戒指 (ring)

### 4. 支持的国家/地区
- 美国 (US)、英国 (UK)、欧盟 (EU)
- 日本 (JP)、澳大利亚 (AU)、俄罗斯 (RU)
- 巴西 (BR)、印度 (IN)、加拿大 (CA)
- 韩国 (KR)、新西兰 (NZ)
- 德国 (DE)、法国 (FR)、瑞典 (SW) - 仅戒指

## 技术实现

### 组件特性
- 使用 Vue 3 Composition API
- 响应式数据管理
- 计算属性优化性能
- 国际化支持

### 用户体验优化
- 无效组合时显示友好提示
- 自动切换到有效选项
- 响应式设计，支持移动端
- 表格样式优化

### 数据验证
- 检查单位数据完整性
- 验证国家支持情况
- 自动处理缺失数据

## 使用方法

```vue
<SizeGuideContent :defaultCategory="'women\'s top'" />
```

## 文件位置
- 组件: `components/SizeGuideContent.vue`
- 国际化: `locales/zh.json` (sizeGuide 部分)
- 页面: `pages/help/size-guide.vue`

## 注意事项
1. 戒指类型不支持英寸单位（数据为N/A）
2. 某些国家对特定服装类型可能没有数据
3. 组件会自动处理这些情况并提供用户友好的体验
