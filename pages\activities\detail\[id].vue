<template>
  <Header />
  <div class="activity-detail-page">
    <div class="activity-container">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container text-center q-pa-xl">
        <q-spinner-dots size="50px" color="primary" />
        <div class="q-mt-md">加载中...</div>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="hasError" class="error-container text-center q-pa-xl">
        <q-icon name="error" size="50px" color="negative" />
        <div class="q-mt-md text-negative">加载失败，请稍后重试</div>
      </div>

      <!-- 活动基本信息 -->
      <div v-else-if="activity" class="activity-content-wrapper">
        <!-- 活动标题和元信息 -->
        <div class="activity-header text-center">
          <h1 class="activity-title">{{ activity.title }}</h1>
          <div class="activity-meta">
            <div class="meta-item">
              <q-icon name="event" size="sm" class="q-mr-xs" />
              <span>{{ formatDate(activity.publishTime) }}</span>
            </div>
            <div class="meta-item">
              <q-icon name="person" size="sm" class="q-mr-xs" />
              <span>{{ activity.author }}</span>
            </div>
            <div class="meta-item">
              <q-icon name="visibility" size="sm" class="q-mr-xs" />
              <span>{{ activity.browseCount }} 次浏览</span>
            </div>
          </div>
        </div>

        <!-- 活动封面图 -->
        <div v-if="activity.picUrl" class="activity-cover q-my-md">
          <q-img :src="activity.picUrl" :ratio="16 / 9" class="rounded-borders" />
        </div>

        <!-- 活动内容 -->
        <div class="activity-content q-mt-lg">
          <div v-html="activity.content"></div>
        </div>

        <!-- 返回按钮 -->
        <div class="activity-actions q-mt-lg text-center">
          <q-btn flat color="primary" to="/activities" icon="arrow_back" label="返回列表" />
        </div>
      </div>


    </div>
  </div>
  <Footer />
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { date } from 'quasar';
import ArticleApi from '../../../composables/articleApi';

const route = useRoute();
const activityId = computed(() => parseInt(route.params.id));

// 状态
const loading = ref(true);
const activity = ref(null);
const hasError = ref(false);

// 获取活动详情
onMounted(async () => {
  try {
    loading.value = true;
    hasError.value = false;

    // 优先使用从 useState 传递的数据
    const detailState = useState('activityDetail');
    if (detailState.value && detailState.value.id === activityId.value) {
      activity.value = detailState.value;
      detailState.value = null; // 清除状态
      loading.value = false;

      // 增加浏览次数
      try {
        await ArticleApi.incrementViews({ id: activityId.value });
      } catch (error) {
        console.error('增加浏览次数失败:', error);
      }
      return;
    }

    // 如果没有传递的数据，则调用API获取
    const { code, data } = await ArticleApi.getDetail({ id: activityId.value });
    if (code === 0 && data) {
      activity.value = data;

      // 增加浏览次数
      try {
        await ArticleApi.incrementViews({ id: activityId.value });
      } catch (error) {
        console.error('增加浏览次数失败:', error);
      }
    } else {
      hasError.value = true;
    }
  } catch (error) {
    console.error('获取活动详情失败:', error);
    hasError.value = true;
  } finally {
    loading.value = false;
  }
});

// 格式化日期
const formatDate = (timestamp) => {
  if (!timestamp) return '';
  return date.formatDate(new Date(timestamp), 'YYYY-MM-DD');
};







</script>

<style lang="scss" scoped>
.activity-detail-page {
  padding: 20px 0 40px;
  background-color: #f8f8f8;
}

.activity-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 16px;
}

.activity-content-wrapper {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
  padding: 24px;
}

.activity-header {
  margin-bottom: 20px;
}

.activity-title {
  font-size: 28px;
  font-weight: bold;
  color: #333;
  margin: 0;
  line-height: 1.3;
}

.activity-status-badge {
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: bold;
  color: white;

  &.status-upcoming {
    background-color: #ff9800;
  }

  &.status-ongoing {
    background-color: #4caf50;
  }

  &.status-ended {
    background-color: #9e9e9e;
  }
}

.activity-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-top: 16px;
  color: #666;
  font-size: 14px;

  .meta-item {
    display: flex;
    align-items: center;
  }

  .countdown {
    color: #ff5722;
    font-weight: bold;
  }
}

.activity-cover {
  margin: 24px 0;
  border-radius: 8px;
  overflow: hidden;
}

.section-title {
  font-size: 22px;
  font-weight: bold;
  color: #333;
  margin-bottom: 16px;
}

.subsection-title {
  font-size: 18px;
  font-weight: bold;
  color: #1976d2;
  margin: 24px 0 16px;
}

.activity-products {
  margin-top: 30px;
}

.product-card {
  height: 100%;
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
  }
}

.product-title {
  font-size: 14px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.product-price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.product-price {
  .current-price {
    font-size: 16px;
    font-weight: bold;
    color: #ff5722;
  }

  .original-price {
    font-size: 12px;
    color: #999;
    text-decoration: line-through;
    margin-left: 5px;
  }
}

.discount-tag {
  background-color: #ff5722;
  color: white;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
}

.activity-actions {
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.social-share {
  display: flex;
  align-items: center;
}

.related-activities {
  margin-top: 40px;
}

.activity-card {
  height: 100%;
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
  }
}

.activity-image-container {
  position: relative;
}

.activity-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  line-height: 1.3;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

.activity-description {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  margin-bottom: 0;
}

.loading-state,
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
  color: #666;
}

@media (max-width: 767px) {
  .activity-content-wrapper {
    padding: 16px;
  }

  .activity-title {
    font-size: 22px;
  }

  .activity-meta {
    font-size: 13px;
    gap: 12px;
  }

  .section-title {
    font-size: 20px;
  }

  .subsection-title {
    font-size: 16px;
  }
}
</style>
