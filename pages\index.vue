<template>
  <!-- 公告栏和菜单 -->
  <Header />
  <!-- Step和搜索 -->
  <StepSearch />
  <!-- 轮播活动 -->
  <Promote :items="eventItems" />
  <!-- 轮播推荐商品 -->
  <ProductSlider :display="hotDisplay" />

  <!-- 精选推荐 -->
  <!-- <ProductShow :display="hotDisplay" /> -->

  <!-- Blog精选 -->
  <!-- <BlogShow :items="blogBanners" :moreLink="'/account/balance'" /> -->

  <!-- 平台能力介绍 -->
  <PlatformCapabilities />

  <!-- 轮播图 -->
  <FeaturesShow />

  <Footer />
</template>

<script setup>
import { useBannerStore } from '~/store/banner';
import { useDisplayStore } from '~/store/display';

const bannerStore = useBannerStore();
const displayStore = useDisplayStore();

await Promise.all([bannerStore.fetchBanners(), displayStore.fetchDisplays()]);

const eventItems = computed(() => bannerStore.getBannersByTag(BannerTag.EVENT));
const blogBanners = computed(() => bannerStore.getBannersByTag(BannerTag.BLOG));

const hotDisplay = computed(() => displayStore.getDisplayByPosition(PromotionPosition.HOT.positionId));
console.log(hotDisplay.value);
</script>

<style lang="scss"></style>
