<template>
  <div class="after-sale-apply-page">
    <!-- 标题区域 -->
    <div class="bg-grey-2 q-px-md q-py-sm rounded-borders">
      <div class="row justify-between items-center">
        <div class="row items-center">
          <q-btn flat dense color="primary" icon="arrow_back" @click="router.push('/account/orders')" class="q-mr-sm" />
          <span class="text-subtitle1 text-weight-medium">申请售后</span>
        </div>
        <q-btn flat dense color="primary" to="/account/orders" label="返回订单" class="q-px-sm" />
      </div>
    </div>

    <div class="q-pa-md">
      <!-- 售后商品 -->
      <q-card class="q-mb-md">
        <q-card-section class="q-pa-md">
          <div class="text-subtitle2 text-weight-medium q-mb-md">售后商品</div>
          <div class="row no-wrap items-start">
            <!-- 商品图片 -->
            <div class="product-image q-mr-md">
              <q-img :src="state.item.picUrl || '/images/placeholder.png'" style="width: 80px; height: 80px" class="rounded-borders" fit="cover" spinner-color="primary" />
            </div>
            <!-- 商品信息 -->
            <div class="column justify-between flex-1">
              <div class="text-body1 text-weight-medium q-mb-xs">{{ state.item.spuName }}</div>
              <div class="text-caption text-grey-7 q-mb-sm">
                {{ formattedProperties(state.item.properties) }}
              </div>
              <div class="row justify-between items-center">
                <div class="text-body2 text-weight-medium text-primary">
                  {{ formatAmount(state.item.price) }}
                </div>
                <div class="text-caption text-grey-7">数量: {{ state.item.count }}</div>
              </div>
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- 表单区域 -->
      <q-form @submit="submit" class="column q-gutter-md">
        <!-- 售后类型 -->
        <q-card>
          <q-card-section class="q-pa-md">
            <div class="text-subtitle2 text-weight-medium q-mb-md">售后类型</div>
            <q-option-group v-model="formData.way" :options="state.wayList" color="primary" @update:model-value="onRefundChange" />
          </q-card-section>
        </q-card>

        <!-- 退款金额 -->
        <q-card>
          <q-card-section class="q-pa-md">
            <div class="row justify-between items-center">
              <div class="text-subtitle2 text-weight-medium">退款金额</div>
              <div class="text-h6 text-primary text-weight-medium">
                {{ formatAmount(state.item.payPrice) }}
              </div>
            </div>
          </q-card-section>
        </q-card>

        <!-- 申请原因 -->
        <q-card>
          <q-card-section class="q-pa-md">
            <div class="text-subtitle2 text-weight-medium q-mb-md">申请原因</div>
            <q-select v-model="formData.applyReason" :options="state.reasonList" outlined dense placeholder="请选择申请原因" :rules="[(val) => !!val || '请选择申请原因']" />
          </q-card-section>
        </q-card>

        <!-- 相关描述 -->
        <q-card>
          <q-card-section class="q-pa-md">
            <div class="text-subtitle2 text-weight-medium q-mb-md">相关描述</div>
            <q-input v-model="formData.applyDescription" type="textarea" outlined dense rows="4" maxlength="120" placeholder="请描述您遇到的问题" counter />

            <!-- 图片上传 订单售后不需要图片 
            <div class="q-mt-md">
              <div class="text-body2 text-weight-medium q-mb-sm">上传图片</div>
              <div class="row q-gutter-sm">
                <div v-for="(image, index) in formData.images" :key="index" class="relative-position">
                  <q-img :src="image" style="width: 80px; height: 80px" class="rounded-borders cursor-pointer" fit="cover" @click="previewImage(image)" />
                  <q-btn round dense size="xs" color="negative" icon="close" class="absolute-top-right" style="margin: 4px" @click="removeImage(index)" />
                </div>
                <div v-if="formData.images.length < 9" class="upload-btn cursor-pointer" @click="selectImages">
                  <q-icon name="add" size="24px" color="grey-6" />
                  <div class="text-caption text-grey-6 q-mt-xs">添加图片</div>
                </div>
              </div>
              <div class="text-caption text-grey-6 q-mt-xs">最多上传9张图片</div>
            </div>
            -->
          </q-card-section>
        </q-card>
      </q-form>
    </div>

    <!-- 底部按钮 -->
    <div class="fixed-bottom bg-white q-pa-md">
      <div class="row q-gutter-md">
        <q-btn outline color="primary" label="联系客服" class="col" @click="contactService" />
        <q-btn color="primary" label="提交申请" class="col" :loading="submitting" @click="submit" />
      </div>
    </div>

    <!-- 隐藏的文件输入 -->
    <!-- <input ref="fileInput" type="file" accept="image/*" multiple style="display: none" @change="handleFileSelect" /> -->
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useQuasar } from 'quasar';
import OrderApi from '~/composables/orderApi';
import AfterSaleApi from '~/composables/afterSaleApi';
import TradeConfigApi from '~/composables/tradeConfigApi';
import { useCurrency } from '~/composables/useCurrency';

definePageMeta({
  middleware: 'auth', // 引入身份验证中间件
});

const router = useRouter();
const route = useRoute();
const $q = useQuasar();
const { formatAmount } = useCurrency();

// const fileInput = ref(null);
const submitting = ref(false);

const state = reactive({
  orderId: 0, // 订单编号
  itemId: 0, // 订单项编号
  order: {}, // 订单
  item: {}, // 订单项
  config: {}, // 交易配置

  // 售后类型
  wayList: [
    {
      label: '仅退款',
      value: 10,
    },
    {
      label: '退货退款',
      value: 20,
    },
  ],
  reasonList: [], // 可选的申请原因数组
});

const formData = reactive({
  way: 10,
  applyReason: '',
  applyDescription: '',
  images: [],
});

// 提交表单
async function submit() {
  // 验证表单
  if (!formData.way) {
    $q.notify({
      color: 'negative',
      message: '请选择售后类型',
      icon: 'error',
    });
    return;
  }

  if (!formData.applyReason) {
    $q.notify({
      color: 'negative',
      message: '请选择申请原因',
      icon: 'error',
    });
    return;
  }

  submitting.value = true;

  try {
    const data = {
      orderItemId: state.itemId,
      way: formData.way,
      applyReason: formData.applyReason,
      applyDescription: formData.applyDescription,
      applyPicUrls: formData.images,
      refundPrice: state.item.payPrice,
    };

    const { code } = await AfterSaleApi.createAfterSale(data);
    if (code === 0) {
      $q.notify({
        color: 'positive',
        message: '申请成功',
        icon: 'check_circle',
      });
      router.push('/account/after-sale');
    }
  } catch (error) {
    console.error('提交售后申请失败:', error);
    $q.notify({
      color: 'negative',
      message: '提交失败，请重试',
      icon: 'error',
    });
  } finally {
    submitting.value = false;
  }
}

// 选择售后类型
function onRefundChange(value) {
  formData.way = value;
  // 清理理由
  state.reasonList = value === 10 ? state.config.afterSaleRefundReasons || [] : state.config.afterSaleReturnReasons || [];
  formData.applyReason = '';
}

// // 处理文件选择
// function handleFileSelect(event) {
//   const files = Array.from(event.target.files);

//   if (files.length + formData.images.length > 9) {
//     $q.notify({
//       color: 'warning',
//       message: '最多只能上传9张图片',
//       icon: 'warning',
//     });
//     return;
//   }

//   files.forEach((file) => {
//     if (file.type.startsWith('image/')) {
//       const reader = new FileReader();
//       reader.onload = (e) => {
//         formData.images.push(e.target.result);
//       };
//       reader.readAsDataURL(file);
//     }
//   });

//   // 清空文件输入
//   event.target.value = '';
// }

// // 移除图片
// function removeImage(index) {
//   formData.images.splice(index, 1);
// }

// // 预览图片
// function previewImage(image) {
//   $q.dialog({
//     title: '图片预览',
//     message: `<img src="${image}" style="max-width: 100%; max-height: 400px;" />`,
//     html: true,
//   });
// }

// 联系客服
function contactService() {
  router.push('/chat');
}

onMounted(async () => {
  // 解析参数
  const orderId = route.query.orderId;
  const itemId = route.query.itemId;

  if (!orderId || !itemId) {
    $q.notify({
      color: 'negative',
      message: '缺少订单信息，请检查',
      icon: 'error',
    });
    router.push('/account/orders');
    return;
  }

  state.orderId = parseInt(orderId);
  state.itemId = parseInt(itemId);

  try {
    // 读取订单信息
    const { code, data } = await OrderApi.getOrderDetail(state.orderId);
    if (code !== 0) {
      $q.notify({
        color: 'negative',
        message: '获取订单信息失败',
        icon: 'error',
      });
      return;
    }

    state.order = data;
    state.item = data.items.find((item) => item.id === state.itemId) || {};

    if (!state.item.id) {
      $q.notify({
        color: 'negative',
        message: '未找到对应的商品信息',
        icon: 'error',
      });
      return;
    }

    // 设置选项 - 如果订单状态为待采购，只能申请退款
    if (state.order.status === 10) {
      state.wayList = state.wayList.filter((item) => item.value === 10);
    }

    // 读取配置
    const configResult = await TradeConfigApi.getTradeConfig();
    console.log('configResult:', configResult);
    if (configResult.code === 0) {
      state.config = configResult.data;
      // 初始化申请原因列表
      state.reasonList = formData.way === 10 ? state.config.afterSaleRefundReasons || [] : state.config.afterSaleReturnReasons || [];
    }
  } catch (error) {
    console.error('初始化页面失败:', error);
    $q.notify({
      color: 'negative',
      message: '页面初始化失败',
      icon: 'error',
    });
  }
});
</script>

<style lang="scss" scoped>
.after-sale-apply-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 80px; // 为底部按钮留出空间
}

.product-image {
  flex-shrink: 0;
}

.upload-btn {
  width: 80px;
  height: 80px;
  border: 2px dashed #ccc;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;

  &:hover {
    border-color: #1976d2;
    background-color: #f5f5f5;
  }
}

.fixed-bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}

// 移动端适配
@media (max-width: 599px) {
  .after-sale-apply-page {
    padding-bottom: 100px;
  }

  .fixed-bottom {
    padding: 16px;
  }
}
</style>
