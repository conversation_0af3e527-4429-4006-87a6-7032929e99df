/**
 * 广告追踪调试插件
 * 仅在开发环境下加载调试工具
 */

export default defineNuxtPlugin(() => {
  // 仅在开发环境下加载
  if (process.dev) {
    // 动态导入调试工具，避免在生产环境中包含
    import('~/utils/adTrackingUtilsDebug.js')
      .then(({ AdTrackingDebug }) => {
        // 暴露到全局
        window.AdTrackingDebug = AdTrackingDebug;

        // 在控制台显示欢迎信息
        console.log('%c🎯 广告追踪调试工具已加载', 'color: #4CAF50; font-weight: bold; font-size: 14px;');
        console.log('%c使用 AdTrackingDebug.showHelp() 查看帮助信息', 'color: #2196F3; font-size: 12px;');

        // 自动显示当前状态（如果有广告参数）
        if (window.location.search.includes('utm_') || window.location.search.includes('gclid') || window.location.search.includes('fbclid')) {
          console.log('%c检测到广告参数，自动显示当前状态：', 'color: #FF9800; font-weight: bold;');
          AdTrackingDebug.logCurrentState();
        }
      })
      .catch((error) => {
        console.error('加载广告追踪调试工具失败:', error);
      });
  }
});
