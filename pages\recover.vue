<template>
  <HeaderSimple />
  <Breadcrumbs :breadcrumbs="breadcrumbs" />

  <div class="recover-main">
    <div class="recover-container">
      <div class="recover-form">
        <q-form @submit.prevent="submitResetPassword" novalidate>
          <div class="recover-form-title">{{ $t('title.resetPassword') }}</div>

          <div class="form-description">
            {{ $t('text.resetPasswordDescription') || '请输入您的邮箱地址，我们将发送验证码帮助您重置密码。' }}
          </div>

          <!-- 邮箱输入 -->
          <q-input
            v-model="email"
            :label="$t('label.email')"
            :placeholder="$t('placeholder.email')"
            type="email"
            dense
            outlined
            clearable
            required
            autocomplete="username"
            :error="!!$v.email.$error"
            :error-message="$v.email.$errors[0]?.$message"
            class="q-mb-md">
            <template #prepend>
              <q-icon name="email" />
            </template>
          </q-input>

          <!-- 验证码输入和发送按钮 -->
          <div class="row q-col-gutter-sm q-mb-md">
            <div class="col-xs-12 col-sm-6">
              <q-input
                v-model="code"
                :label="$t('label.verifyCode')"
                :placeholder="$t('placeholder.verifyCode')"
                type="text"
                outlined
                dense
                maxlength="6"
                autocomplete="off"
                oninput="value=value.replace(/[^0-9.]/g,'')"
                :error="!!$v.code.$error"
                :error-message="$v.code.$errors[0]?.$message">
                <template #prepend>
                  <q-icon name="pin" />
                </template>
              </q-input>
            </div>
            <div class="col-xs-12 col-sm-6 send-code-btn-container">
              <q-btn class="send-code-btn" :label="sendButtonLabel" color="primary" :disable="sending || countdown > 0 || !email" @click="sendVerificationCode" :loading="sending" />
            </div>
          </div>

          <!-- 新密码输入 -->
          <q-input
            v-model="newPassword"
            :label="$t('label.newPassword')"
            :placeholder="$t('placeholder.newPassword')"
            :type="isPwd ? 'password' : 'text'"
            dense
            outlined
            required
            autocomplete="new-password"
            :error="!!$v.newPassword.$error"
            :error-message="$v.newPassword.$errors[0]?.$message"
            class="q-mb-lg">
            <template #prepend>
              <q-icon name="lock" />
            </template>
            <template #append>
              <q-icon :name="isPwd ? 'visibility_off' : 'visibility'" class="cursor-pointer" @click="isPwd = !isPwd" />
            </template>
          </q-input>

          <!-- 提交按钮 -->
          <q-btn class="submit-btn" :label="$t('button.resetPassword')" color="primary" type="submit" :disable="submitting" :loading="submitting" />

          <!-- 返回登录链接 -->
          <div class="back-to-login">
            <span>{{ $t('text.rememberPassword') || '记起密码了？' }}</span>
            <a href="/login" class="text-primary q-ml-sm">{{ $t('label.loginNow') }}</a>
          </div>
        </q-form>
      </div>
    </div>
  </div>

  <Footer />
</template>

<script setup>
import AuthApi from '~/composables/authApi';
import UserApi from '~/composables/userApi';
import useVuelidate from '@vuelidate/core';
import { createValidators } from '~/utils/i18n-validators';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const breadcrumbs = [{ label: t('title.resetPassword'), to: '/recover' }];

const email = ref('');
const code = ref('');
const newPassword = ref('');
const isPwd = ref(true);

const sending = ref(false);
const countdown = ref(0);
const sendButtonLabel = computed(() => (countdown.value > 0 ? `${t('button.resend')} (${countdown.value}s)` : t('button.sendCode')));
const submitting = ref(false);
const sent = ref(false);

const validators = createValidators(useNuxtApp().$t); // 动态生成验证规则
const rules = ref({
  email: { required: validators.required, email: validators.email }, // 邮箱规则
  code: {}, // 默认验证码和密码为空，避免验证
  newPassword: {},
});

const $v = useVuelidate(rules, { email, code, newPassword });

const router = useRouter();
const resultState = useState('resultState', () => ({})); // 定义状态

// 发送验证码
const sendVerificationCode = async () => {
  rules.value = {
    email: { required: validators.required, email: validators.email },
    code: {},
    newPassword: {},
  };
  $v.value.$touch(); // 触发email框验证
  if ($v.value.$invalid) {
    console.log('Form validation failed');
    return;
  }

  try {
    sending.value = true;
    const response = await AuthApi.sendMailCode({ email: email.value, scene: 4 });
    if (response.code === 0) {
      sent.value = true;
      useNuxtApp().$showNotify({ msg: t('notify.verifyCodeSent') });
      startCountdown();
    } else {
      useNuxtApp().$showNotify({ msg: response.msg || t('error.verifyCodeError'), type: 'warning' });
    }
  } catch (error) {
    useNuxtApp().$showNotify({ msg: t('error.verifyCodeError'), type: 'warning' });
  } finally {
    sending.value = false;
  }
};

let countdownInterval = null; // 保存倒计时引用
// 开始倒计时
const startCountdown = () => {
  countdown.value = 60; // 60 秒倒计时
  countdownInterval = setInterval(() => {
    countdown.value -= 1;
    if (countdown.value <= 0) {
      clearInterval(countdownInterval);
      countdownInterval = null;
    }
  }, 1000);
};

// 重置密码
const submitResetPassword = async () => {
  rules.value = {
    email: { required: validators.required, email: validators.email },
    code: { required: validators.required },
    newPassword: { required: validators.required, minLength: validators.minLength(6), maxLength: validators.maxLength(20) },
  };
  $v.value.$touch(); // 触发验证
  if ($v.value.$invalid) {
    console.log('Form validation failed');
    return;
  }

  if (!sent.value) {
    useNuxtApp().$showNotify({ msg: '请先获取验证码!' });
    return;
  }

  try {
    submitting.value = true;
    const response = await UserApi.resetUserPassword({
      email: email.value,
      code: code.value,
      password: newPassword.value,
    });
    if (response.code === 0) {
      useNuxtApp().$showNotify({ msg: 'Password reset successfully!' });
      // 清理倒计时任务
      if (countdownInterval) {
        clearInterval(countdownInterval);
        countdownInterval = null;
      }
      // 设置成功状态数据
      resultState.value = {
        title: 'Password Reset Successful',
        message: 'Your password has been successfully reset. Please log in with your new password.',
        status: 'success',
      };
      // 跳转到结果页面
      router.push('/result');
    } else {
      useNuxtApp().$showNotify({ msg: response.msg || 'Failed to reset password.', type: 'warning' });
    }
  } catch (error) {
    useNuxtApp().$showNotify({ msg: 'Error while resetting password. Please try again.', type: 'warning' });
  } finally {
    submitting.value = false;
  }
};

onUnmounted(() => {
  if (countdownInterval) {
    clearInterval(countdownInterval);
    countdownInterval = null;
  }
});
</script>

<style lang="scss" scoped>
.recover-main {
  max-width: 1200px;
  width: 100%;
  min-height: 500px;
  margin: 50px auto;
  background-image: url('/public/images/recover-bg.webp');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.recover-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30px;
  background-color: rgba(255, 255, 255, 0.9);
}

.recover-form {
  width: 420px;
  max-width: 100%;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  padding: 30px;
  transition: all 0.3s ease;

  .recover-form-title {
    font-size: 1.8rem;
    font-weight: 500;
    text-align: center;
    margin-bottom: 20px;
    color: #1976d2;
    position: relative;

    &:after {
      content: '';
      position: absolute;
      bottom: -8px;
      left: 50%;
      transform: translateX(-50%);
      width: 50px;
      height: 3px;
      background-color: #1976d2;
      border-radius: 2px;
    }
  }

  .form-description {
    text-align: center;
    color: #666;
    margin-bottom: 25px;
    font-size: 14px;
    line-height: 1.5;
  }

  .send-code-btn-container {
    display: flex;
    align-items: flex-start;

    .send-code-btn {
      width: 100%;
      height: 40px;
    }
  }

  .submit-btn {
    width: 100%;
    height: 44px;
    font-weight: 500;
    font-size: 16px;
    margin-bottom: 20px;
  }

  .back-to-login {
    text-align: center;
    margin-top: 15px;
    font-size: 14px;
    color: #666;
  }
}

/* 平板电脑样式 */
@media (max-width: 1023px) {
  .recover-main {
    max-width: 90%;
    margin: 30px auto;
  }
}

/* 手机样式 */
@media (max-width: 599px) {
  .recover-main {
    max-width: 95%;
    margin: 20px auto;
    background-image: none;
    background-color: #f5f5f5;
    min-height: auto;
    box-shadow: none;
  }

  .recover-container {
    padding: 15px;
    background-color: transparent;
  }

  .recover-form {
    width: 100%;
    padding: 20px;
    box-shadow: none;

    .recover-form-title {
      font-size: 1.5rem;
      margin-bottom: 15px;
    }

    .form-description {
      font-size: 13px;
      margin-bottom: 20px;
    }

    .send-code-btn-container {
      margin-top: 10px;
    }

    .submit-btn {
      height: 42px;
      font-size: 15px;
    }
  }

  .row.q-col-gutter-sm {
    margin-left: 0;
    margin-right: 0;

    > div {
      padding-left: 0;
      padding-right: 0;
    }
  }
}
</style>
