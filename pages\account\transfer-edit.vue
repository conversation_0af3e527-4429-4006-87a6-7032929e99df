<template>
  <div class="transfer-edit">
    <!-- 标题区域 - 响应式优化 -->
    <div class="bg-grey-2 q-px-md q-py-sm">
      <div class="row justify-between items-center">
        <span class="text-subtitle1 text-weight-medium">{{ $t('transfer.common.editTransfer') }}</span>
        <q-btn flat color="primary" icon-right="arrow_back" :label="$t('transfer.common.backToDetail')" @click="goBack" class="back-btn" />
      </div>
    </div>

    <div class="q-pa-md">
      <!-- 加载中 -->
      <div v-if="loading" class="column items-center q-py-lg">
        <q-spinner color="primary" size="3em" />
        <div class="q-mt-sm text-grey-7">{{ $t('transfer.common.loading') }}</div>
      </div>

      <!-- 无数据 -->
      <div v-else-if="!state.transferDetail" class="column items-center q-py-lg">
        <q-icon name="error_outline" size="3em" color="grey-5" />
        <div class="text-grey-7 q-mt-sm">{{ $t('transfer.common.noDataOrCannotEdit') }}</div>
        <q-btn color="primary" :label="$t('transfer.common.returnToList')" class="q-mt-md" to="/account/transfer" />
      </div>

      <!-- 编辑表单 -->
      <div v-else>
        <q-form @submit="onSubmit" class="q-gutter-md form-container">
          <!-- 快递信息卡片（不可修改） -->
          <div class="bg-white q-pa-md rounded-borders shadow-1">
            <div class="section-header">
              <q-icon name="local_shipping" color="primary" />
              <span>{{ $t('transfer.edit.expressInfoUnchangeable') }}</span>
            </div>
            <div class="text-caption text-grey-7 q-mb-md">{{ $t('transfer.edit.expressInfoNote') }}</div>

            <div class="row q-col-gutter-md">
              <!-- 快递公司 -->
              <div class="col-12 col-sm-6 col-md-4">
                <q-input v-model="state.logisticsName" outlined dense :label="$t('transfer.common.expressCompany')" stack-label class="field-label" readonly disable />
              </div>

              <!-- 快递单号 -->
              <div class="col-12 col-sm-6 col-md-4">
                <q-input v-model="state.logisticsNo" outlined dense :label="$t('transfer.common.expressNumber')" stack-label class="field-label" readonly disable />
              </div>

              <!-- 快递备注 -->
              <div v-if="state.logisticsRemark" class="col-12 col-sm-12 col-md-4">
                <q-input v-model="state.logisticsRemark" outlined dense :label="$t('transfer.detail.expressRemark')" stack-label class="field-label" readonly disable />
              </div>
            </div>
          </div>

          <!-- 仓库信息（不可修改） -->
          <div class="bg-white q-pa-md rounded-borders shadow-1">
            <div class="section-header">
              <q-icon name="location_on" color="primary" />
              <span>{{ $t('transfer.edit.warehouseInfoUnchangeable') }}</span>
            </div>
            <div class="text-caption text-grey-7 q-mb-md">{{ $t('transfer.edit.warehouseInfoNote') }}</div>

            <div class="row">
              <div class="col-12 col-sm-6 col-md-4">
                <q-input v-model="state.warehouseName" outlined dense :label="$t('transfer.edit.targetWarehouse')" stack-label class="field-label" readonly disable />
              </div>
            </div>
          </div>

          <!-- 填写需转运的商品信息（可修改） -->
          <div class="bg-white q-pa-md rounded-borders shadow-1">
            <div class="section-header">
              <q-icon name="inventory_2" color="primary" />
              <span>{{ $t('transfer.edit.productInfoChangeable') }}</span>
            </div>
            <div class="text-caption text-grey-7 q-mb-md">{{ $t('transfer.edit.productInfoNote') }}</div>

            <div v-for="(item, index) in state.items" :key="index" class="q-mb-md product-item">
              <div class="row justify-between items-center q-mb-sm">
                <div class="text-subtitle2 q-ml-sm">{{ $t('transfer.create.product') }} #{{ index + 1 }}</div>
                <q-btn v-if="index > 0" flat round dense color="negative" icon="delete" @click="removeItem(index)">
                  <q-tooltip>{{ $t('transfer.create.deleteProduct') }}</q-tooltip>
                </q-btn>
              </div>

              <!-- 商品分类和名称 - 响应式优化 -->
              <div class="row q-col-gutter-md">
                <div class="col-12 col-sm-12 col-md-4">
                  <q-select
                    v-model="item.categoryId"
                    :options="state.categoryList"
                    option-value="id"
                    option-label="name"
                    outlined
                    dense
                    map-options
                    emit-value
                    :label="$t('transfer.create.categoryLabel')"
                    stack-label
                    class="field-label"
                    :rules="[(val) => !!val || $t('transfer.create.categoryRequired')]" />
                </div>
                <div class="col-12 col-sm-12 col-md-8">
                  <q-input
                    v-model="item.productName"
                    outlined
                    dense
                    :label="$t('transfer.create.productNameLabel')"
                    stack-label
                    class="field-label"
                    :rules="[(val) => !!val || $t('transfer.create.productNameRequired')]" />
                </div>
              </div>

              <!-- 数量、重量和价值 - 响应式优化 -->
              <div class="row q-col-gutter-md q-mt-sm">
                <div class="col-4 col-sm-4 col-md-4">
                  <q-input
                    v-model.number="item.count"
                    type="number"
                    outlined
                    dense
                    :label="$t('transfer.create.quantityLabel')"
                    stack-label
                    class="field-label"
                    min="1"
                    :rules="[(val) => !!val || $t('transfer.create.quantityRequired'), (val) => val > 0 || $t('transfer.create.quantityPositive')]" />
                </div>
                <div class="col-4 col-sm-4 col-md-4">
                  <q-input
                    v-model.number="item.weight"
                    type="number"
                    outlined
                    dense
                    :label="$t('transfer.create.weightLabel')"
                    stack-label
                    class="field-label"
                    min="0.01"
                    step="0.01"
                    :rules="[(val) => val > 0 || $t('transfer.create.weightPositive')]" />
                </div>
                <div class="col-4 col-sm-4 col-md-4">
                  <q-input v-model.number="item.price" type="number" outlined dense :label="$t('transfer.create.priceLabel')" stack-label class="field-label" min="0.01" step="0.01" />
                </div>
              </div>

              <q-separator class="q-my-md" v-if="index < state.items.length - 1" />
            </div>

            <div class="row justify-center q-mt-md">
              <q-btn outline color="primary" icon="add" :label="$t('transfer.create.addProduct')" @click="addItem" />
            </div>
          </div>

          <!-- 备注信息（可修改） -->
          <div class="bg-white q-pa-md rounded-borders shadow-1">
            <div class="section-header">
              <q-icon name="notes" color="primary" />
              <span>{{ $t('transfer.edit.remarkChangeable') }}</span>
            </div>
            <div class="text-caption text-grey-7 q-mb-md">{{ $t('transfer.create.remarkNote') }}</div>

            <div class="row q-col-gutter-md">
              <div class="col-12">
                <q-input
                  v-model="state.remark"
                  outlined
                  type="textarea"
                  rows="3"
                  stack-label
                  class="field-label"
                  :placeholder="$t('transfer.create.remarkPlaceholder')"
                  maxlength="300"
                  counter
                  :rules="[(val) => val === null || val === '' || val.length <= 300 || $t('transfer.create.remarkMaxLength')]" />
              </div>
            </div>
          </div>

          <!-- 修改说明 -->
          <div class="bg-white q-pa-md q-pb-xl rounded-borders shadow-1 q-mt-md">
            <div class="section-header">
              <q-icon name="info" color="primary" />
              <span>{{ $t('transfer.edit.editNotice') }}</span>
            </div>

            <div class="text-caption notice-box">
              <ol>
                <li v-for="(item, index) in $tm('transfer.edit.editNoticeItems')" :key="index">{{ item }}</li>
              </ol>
            </div>
          </div>

          <!-- 提交区域 - 响应式优化 -->
          <div class="row justify-end q-mt-xl q-mb-xl">
            <div class="col-12 col-md-auto text-right text-center-mobile">
              <q-btn type="submit" color="primary" icon-left="save" :label="$t('transfer.common.save')" class="submit-btn q-mr-md" :loading="submitting" />
              <q-btn outline color="grey-7" icon-left="cancel" :label="$t('transfer.common.cancel')" class="cancel-btn" @click="goBack" />
            </div>
          </div>
        </q-form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useQuasar } from 'quasar';
import { useI18n } from 'vue-i18n';
import TransferApi from '~/composables/transferApi';
import CategoryApi from '../../composables/categoryApi';
import { yuan2fen, fen2yuan } from '~/utils/utils';

definePageMeta({
  middleware: 'auth', // 引入身份验证中间件
});

const $q = useQuasar();
const route = useRoute();
const router = useRouter();
const { t, tm } = useI18n();
const transferId = route.query.id;

// 数据状态
const loading = ref(true);
const submitting = ref(false);

const state = reactive({
  transferDetail: null,
  categoryList: [], // 商品分类选项
  warehouseId: null,
  warehouseName: '',
  logisticsId: null,
  logisticsName: '',
  logisticsNo: '',
  logisticsRemark: '',
  remark: '',
  items: [],
});

// 页面加载时获取数据
onMounted(async () => {
  await loadData();
});

// 加载转运单数据和分类列表
async function loadData() {
  loading.value = true;

  try {
    // 获取转运单详情
    const { code: code1, data: data1 } = await TransferApi.getTransferDetail(transferId);

    if (code1 === 0 && data1) {
      // 检查转运单状态是否为待入库
      if (data1.status !== 0) {
        $q.notify({
          type: 'warning',
          message: t('transfer.detail.onlyPendingCanEdit'),
        });
        state.transferDetail = null;
        return;
      }

      state.transferDetail = data1;

      // 填充表单数据
      state.warehouseId = data1.warehouseId;
      state.warehouseName = data1.warehouseName;
      state.logisticsId = data1.logisticsId;
      state.logisticsName = data1.logisticsName || data1.logisticsId; // 使用名称或ID
      state.logisticsNo = data1.logisticsNo;
      state.logisticsRemark = data1.logisticsRemark;
      state.remark = data1.remark;

      // 处理商品数据
      if (data1.items && data1.items.length > 0) {
        state.items = data1.items.map((item) => ({
          ...item,
          // 将价格从分转换为元
          price: item.price ? fen2yuan(item.price) : null,
        }));
      } else {
        // 如果没有商品，添加一个空商品
        addItem();
      }
    } else {
      state.transferDetail = null;
    }

    // 获取商品分类列表
    const { code: code2, data: data2 } = await CategoryApi.getList();
    if (code2 === 0) {
      state.categoryList = data2;
    }
  } catch (error) {
    console.error('获取数据出错:', error);
    $q.notify({
      type: 'negative',
      message: t('transfer.errors.getDetailError'),
    });
    state.transferDetail = null;
  } finally {
    loading.value = false;
  }
}

// 添加商品项
function addItem() {
  state.items.push({
    categoryId: null,
    productName: '',
    count: 1,
    weight: null,
    price: null,
  });
}

// 移除商品项
function removeItem(index) {
  state.items.splice(index, 1);
}

// 返回详情页
function goBack() {
  router.push({
    path: '/account/transfer-detail',
    query: { id: transferId },
  });
}

// 提交表单
async function onSubmit() {
  submitting.value = true;

  // 准备提交数据
  const submitData = {
    id: transferId,
    remark: state.remark,
    items: state.items.map((item) => ({
      ...item,
      // 使用工具函数将价格从元转换为分
      price: yuan2fen(item.price),
    })),
  };

  try {
    const { code, msg } = await TransferApi.updateTransfer(submitData);

    if (code === 0) {
      $q.notify({
        type: 'positive',
        message: t('transfer.edit.updateSuccess'),
      });

      // 跳转回详情页
      router.push({
        path: '/account/transfer-detail',
        query: { id: transferId },
      });
    } else {
      $q.notify({
        type: 'negative',
        message: msg || t('transfer.edit.updateFailed'),
      });
    }
  } catch (error) {
    console.error('修改转运单出错:', error);
    $q.notify({
      type: 'negative',
      message: t('transfer.errors.updateError'),
    });
  } finally {
    submitting.value = false;
  }
}
</script>

<style lang="scss" scoped>
.transfer-edit {
  .form-container {
    max-width: 900px;
    margin: 0 auto;
  }

  .section-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-size: 1rem;
    font-weight: 500;

    .q-icon {
      margin-right: 8px;
      font-size: 1.2em;
      color: var(--q-primary);
    }
  }

  .product-item {
    background-color: rgba(0, 0, 0, 0.02);
    border-radius: 4px;
    padding: 12px;
  }

  .notice-box {
    background-color: rgba(0, 0, 0, 0.02);
    border-radius: 4px;
    padding: 12px 16px;
    margin: 0 auto;
    max-width: 90%;

    ol {
      margin-top: 8px;
      margin-bottom: 0;
      padding-left: 24px;
    }

    li {
      margin-bottom: 4px;
    }

    p {
      margin-top: 0;
      margin-bottom: 4px;
    }
  }

  // 调整输入框标签样式
  .field-label {
    :deep(.q-field__label) {
      font-size: 0.95rem;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.7);
    }

    :deep(.q-field__marginal) {
      height: 40px;
    }
  }

  // 响应式样式
  .back-btn {
    @media (max-width: 599px) {
      padding: 4px 8px;

      .q-icon {
        font-size: 1.2em;
      }
    }
  }

  .submit-btn,
  .cancel-btn {
    @media (max-width: 599px) {
      width: 45%;
      margin: 0 5px;
    }
  }

  .wrap-on-mobile {
    @media (max-width: 767px) {
      flex-wrap: wrap;
    }
  }

  .text-center-mobile {
    @media (max-width: 767px) {
      text-align: center;
    }
  }

  .q-mt-sm-xs {
    @media (max-width: 599px) {
      margin-top: 8px;
    }
  }

  .q-mb-sm-md {
    @media (max-width: 767px) {
      margin-bottom: 16px;
    }
  }
}
</style>
