<template>
  <div class="size-guide-examples">
    <!-- 示例1: 基础弹窗调用 -->
    <div class="example-section">
      <h3>示例1: 基础弹窗调用</h3>
      <q-btn color="primary" @click="showBasicDialog = true">
        打开尺码助手
      </q-btn>
      
      <SizeGuideDialog v-model="showBasicDialog" />
    </div>

    <!-- 示例2: 指定默认服装类型 -->
    <div class="example-section">
      <h3>示例2: 指定默认服装类型（男士上衣）</h3>
      <q-btn color="secondary" @click="showMensTopDialog = true">
        男士上衣尺码助手
      </q-btn>
      
      <SizeGuideDialog 
        v-model="showMensTopDialog" 
        default-category="men's top"
      />
    </div>

    <!-- 示例3: 商品详情页集成 -->
    <div class="example-section">
      <h3>示例3: 商品详情页集成</h3>
      <q-card class="product-card">
        <q-card-section>
          <div class="product-name">女士连衣裙</div>
          <div class="product-specs">
            <div class="spec-row">
              <span>尺码：</span>
              <q-select 
                v-model="selectedSize" 
                :options="sizeOptions" 
                style="width: 100px"
                dense
                outlined
              />
              <q-btn 
                flat 
                color="primary" 
                size="sm" 
                @click="showProductDialog = true"
                class="q-ml-sm"
              >
                尺码助手
              </q-btn>
            </div>
          </div>
        </q-card-section>
      </q-card>
      
      <SizeGuideDialog 
        v-model="showProductDialog" 
        default-category="women's top"
      />
    </div>

    <!-- 示例4: 购物车页面集成 -->
    <div class="example-section">
      <h3>示例4: 购物车页面集成</h3>
      <q-card class="cart-item">
        <q-card-section>
          <div class="item-info">
            <div class="item-name">运动鞋</div>
            <div class="item-specs">
              尺码：42
              <q-btn 
                flat 
                size="xs" 
                color="primary" 
                @click="showCartDialog = true"
                icon="straighten"
                class="q-ml-sm"
              >
                尺码参考
              </q-btn>
            </div>
          </div>
        </q-card-section>
      </q-card>
      
      <SizeGuideDialog 
        v-model="showCartDialog" 
        default-category="men's shoes"
      />
    </div>

    <!-- 示例5: 内容组件直接嵌入 -->
    <div class="example-section">
      <h3>示例5: 内容组件直接嵌入</h3>
      <q-card>
        <q-card-section>
          <SizeGuideContent default-category="ring" />
        </q-card-section>
      </q-card>
    </div>

    <!-- 示例6: 帮助中心链接 -->
    <div class="example-section">
      <h3>示例6: 帮助中心链接</h3>
      <q-btn 
        color="accent" 
        :to="'/help/size-guide'"
        target="_blank"
      >
        访问帮助中心尺码助手页面
      </q-btn>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import SizeGuideDialog from '~/components/SizeGuideDialog.vue'
import SizeGuideContent from '~/components/SizeGuideContent.vue'

// 响应式数据
const showBasicDialog = ref(false)
const showMensTopDialog = ref(false)
const showProductDialog = ref(false)
const showCartDialog = ref(false)

const selectedSize = ref('M')
const sizeOptions = ref(['XS', 'S', 'M', 'L', 'XL', 'XXL'])
</script>

<style lang="scss" scoped>
.size-guide-examples {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
}

.example-section {
  margin-bottom: 40px;
  
  h3 {
    margin-bottom: 16px;
    color: #1976d2;
  }
}

.product-card {
  max-width: 400px;
  
  .product-name {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 12px;
  }
  
  .spec-row {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}

.cart-item {
  max-width: 300px;
  
  .item-name {
    font-weight: bold;
    margin-bottom: 4px;
  }
  
  .item-specs {
    font-size: 14px;
    color: #666;
    display: flex;
    align-items: center;
  }
}
</style>
