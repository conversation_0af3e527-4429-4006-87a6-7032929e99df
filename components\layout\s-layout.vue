<template>
  <q-layout view="hHh lpR fFf">
    <!-- 头部导航栏 -->
    <q-header v-if="navbar" elevated class="bg-primary text-white">
      <q-toolbar>
        <q-btn v-if="navbar === 'inner'" flat round dense icon="arrow_back" @click="$router.back()" />
        <q-toolbar-title>{{ title }}</q-toolbar-title>
      </q-toolbar>
    </q-header>

    <q-page-container>
      <q-page>
        <slot></slot>
      </q-page>
    </q-page-container>
  </q-layout>
</template>

<script setup>
defineProps({
  title: {
    type: String,
    default: ''
  },
  navbar: {
    type: String,
    default: ''
  }
});
</script>

<style lang="scss" scoped>
.q-layout {
  width: 100%;
  height: 100vh;
}
</style>
