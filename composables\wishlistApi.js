const WishlistApi = {
  addWishlist: (data) => {
    return useClientPost('/trade/wishlist/add', {
      body: data,
      custom: {
        showSuccess: false,
        successMsg: '已加入收藏夹',
      },
    });
  },
  deleteWishlist: (ids) => {
    return useClientDelete('/trade/wishlist/delete', {
      params: { ids },
    });
  },
  getList: () => {
    return useClientGet('/trade/wishlist/list', {
      custom: {
        auth: true,
      },
    });
  },
};

export default WishlistApi;
