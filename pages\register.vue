<template>
  <HeaderSimple />
  <Breadcrumbs :breadcrumbs="breadcrumbs" />

  <div class="register-main">
    <div class="register-container">
      <div class="register-form">
        <div class="q-pa-md">
          <q-form ref="form" @submit="submitForm" class="q-gutter-y-sm column" novalidate>
            <div class="register-form-title">{{ $t('title.register') }}</div>

            <q-input
              dense
              v-model="user.username"
              outlined
              clearable
              autocomplete="username"
              :label="$t('label.username')"
              :placeholder="$t('placeholder.username')"
              :error="!!$v.user.username.$error"
              :error-message="$v.user.username.$errors[0]?.$message">
              <template #prepend>
                <q-icon name="person" />
              </template>
            </q-input>

            <q-input
              dense
              v-model="user.email"
              outlined
              clearable
              type="email"
              autocomplete="email"
              :label="$t('label.email')"
              :placeholder="$t('placeholder.email')"
              :error="!!$v.user.email.$error"
              :error-message="$v.user.email.$errors[0]?.$message">
              <template #prepend>
                <q-icon name="email" />
              </template>
            </q-input>

            <q-input
              dense
              v-model="user.password"
              outlined
              clearable
              :type="isPwd ? 'password' : 'text'"
              autocomplete="new-password"
              :label="$t('label.password')"
              :placeholder="$t('placeholder.password')"
              :error="!!$v.user.password.$error"
              :error-message="$v.user.password.$errors[0]?.$message">
              <template #prepend>
                <q-icon name="lock" />
              </template>
              <template #append>
                <q-icon :name="isPwd ? 'visibility_off' : 'visibility'" class="cursor-pointer" @click="isPwd = !isPwd" />
              </template>
            </q-input>

            <!-- 验证码输入框 -->
            <div class="captcha-input-container">
              <q-input
                dense
                v-model="captcha.code"
                outlined
                clearable
                maxlength="4"
                :label="$t('label.captcha')"
                :placeholder="$t('placeholder.captcha')"
                :error="!!$v.captcha.code.$error"
                :error-message="$v.captcha.code.$errors[0]?.$message"
                class="captcha-input">
                <template #prepend>
                  <q-icon name="security" />
                </template>
              </q-input>

              <div class="captcha-image-container">
                <img v-if="captcha.image" :src="captcha.image" @click="refreshCaptcha" alt="验证码" title="点击刷新验证码" class="captcha-image" />
                <q-btn v-else flat dense icon="refresh" @click="getCaptcha" :loading="captchaLoading" class="captcha-refresh-btn"> 获取验证码 </q-btn>
              </div>
            </div>

            <q-btn class="q-mt-md register-btn" color="primary" type="submit" :label="$t('button.register')" />

            <div class="agreement-section q-mt-sm">
              <q-checkbox size="sm" dense v-model="agree" :label="$t('label.agree')" />
              <div class="agreement-links">
                <NuxtLink to="/agreement" class="text-primary">{{ $t('label.userAgreement') }}</NuxtLink>
                <span class="q-mx-xs">{{ $t('label.and') }}</span>
                <NuxtLink to="/privacy" class="text-primary">{{ $t('label.privatePolicy') }}</NuxtLink>
              </div>
            </div>

            <div class="social-login-section row justify-center q-mt-sm">
              <q-btn flat round color="blue" icon="facebook" />
              <q-btn flat round color="teal" icon="person" />
              <q-btn flat round color="green" icon="wechat" />
            </div>

            <q-separator class="q-my-sm" />

            <div class="login-link text-subtitle2 text-center">
              {{ $t('label.haveAccount') }}
              <a href="/login" class="text-primary q-ml-sm">{{ $t('label.loginNow') }}</a>
            </div>
          </q-form>
        </div>
      </div>
    </div>
  </div>

  <Footer />
</template>

<script setup>
import { useUserStore } from '~/store/user';
import { useNuxtApp } from '#app';
import { ref, onMounted } from 'vue';
import AuthApi from '~/composables/authApi';
import CaptchaApi from '~/composables/captchaApi';
import useVuelidate from '@vuelidate/core';
import { createValidators } from '~/utils/i18n-validators';
import { useI18n } from 'vue-i18n';
import { useAdTracking } from '~/composables/useAdTracking';

const { t } = useI18n();
const breadcrumbs = [{ label: t('title.register'), to: '/register' }];

// 广告追踪功能
const { handleRegisterConversion } = useAdTracking();

const isPwd = ref(true);
const agree = ref(false);

const user = ref({
  username: '',
  email: '',
  password: '',
});

// 验证码相关数据
const captcha = ref({
  key: '',
  code: '',
  image: '',
});

const captchaLoading = ref(false);

// 使用 Vuelidate 定义验证规则
const validators = createValidators(useNuxtApp().$t); // 动态生成验证规则
const rules = {
  user: {
    username: { required: validators.required, minLength: validators.minLength(2), maxLength: validators.maxLength(20) },
    email: { required: validators.required, email: validators.email }, // 必填且为邮箱格式
    password: { required: validators.required, minLength: validators.minLength(6), maxLength: validators.maxLength(20) }, // 必填且至少6位
  },
  captcha: {
    code: { required: validators.required, minLength: validators.minLength(4), maxLength: validators.maxLength(4) },
  },
};
const $v = useVuelidate(rules, { user, captcha });

// 获取验证码图片
const getCaptcha = async () => {
  captchaLoading.value = true;
  try {
    const res = await CaptchaApi.getCaptchaImage();
    if (res.code === 0) {
      captcha.value.key = res.data.key;
      captcha.value.image = res.data.image;
      captcha.value.code = ''; // 清空验证码输入
    } else {
      useNuxtApp().$showNotify({ msg: res.msg || '获取验证码失败', type: 'negative' });
    }
  } catch (error) {
    console.error('获取验证码失败:', error);
    useNuxtApp().$showNotify({ msg: '获取验证码失败', type: 'negative' });
  } finally {
    captchaLoading.value = false;
  }
};

// 刷新验证码
const refreshCaptcha = () => {
  captcha.value.code = '';
  getCaptcha();
};

// 组件挂载时获取验证码
onMounted(() => {
  getCaptcha();
});

const submitForm = async () => {
  console.log('Register clicked');

  // 验证规则
  $v.value.$touch(); // 触发验证
  if ($v.value.$invalid) {
    console.log('Form validation failed');
    return;
  }

  if (!agree.value) {
    useNuxtApp().$showNotify({ msg: t('error.requiredAgreement'), type: 'warning' });
    return;
  }

  // 验证验证码
  if (!captcha.value.code.trim()) {
    useNuxtApp().$showNotify({ msg: '请输入验证码', type: 'warning' });
    return;
  }

  try {
    const res = await AuthApi.registerEmail({
      name: user.value.username,
      email: user.value.email,
      password: user.value.password,
      captchaKey: captcha.value.key,
      captchaCode: captcha.value.code,
    });

    if (res.code === 0) {
      useNuxtApp().$showNotify({ msg: t('notify.registerSuccess') });

      // 获取用户信息
      await useUserStore().getUserInfo();

      // 处理广告追踪注册转化
      try {
        const userInfo = useUserStore().userInfo;
        if (userInfo?.id) {
          await handleRegisterConversion(userInfo.id);
        }
      } catch (error) {
        console.error('处理注册转化失败:', error);
      }

      navigateTo('/mail-verify');
    } else {
      // 注册失败时刷新验证码
      refreshCaptcha();
    }
  } catch (error) {
    console.error('Registration failed:', error);
    // 注册失败时刷新验证码
    refreshCaptcha();
  }
};
</script>

<style lang="scss" scoped>
.register-main {
  max-width: 1200px;
  width: 100%;
  min-height: 520px;
  margin: 50px auto;
  background-image: url('/public/images/register.svg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.register-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 30px;
}

.register-form {
  width: 380px;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  padding: 10px;
  transition: all 0.3s ease;

  .register-form-title {
    font-size: 1.5rem;
    font-weight: 500;
    text-align: center;
    margin-top: 15px;
    margin-bottom: 15px;
    color: #1976d2;
    position: relative;

    &:after {
      content: '';
      position: absolute;
      bottom: -8px;
      left: 50%;
      transform: translateX(-50%);
      width: 40px;
      height: 3px;
      background-color: #1976d2;
      border-radius: 2px;
    }
  }

  .captcha-input-container {
    display: flex;
    gap: 12px;
    align-items: flex-start;

    .captcha-input {
      flex: 1;
    }

    .captcha-image-container {
      display: flex;
      align-items: center;
      min-width: 120px;
      height: 40px;

      .captcha-image {
        width: 120px;
        height: 40px;
        cursor: pointer;
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        object-fit: cover;
        transition: all 0.3s ease;

        &:hover {
          border-color: #1976d2;
          box-shadow: 0 2px 8px rgba(25, 118, 210, 0.2);
        }
      }

      .captcha-refresh-btn {
        width: 120px;
        height: 40px;
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        font-size: 12px;
      }
    }
  }

  .register-btn {
    margin-top: 10px;
    height: 42px;
    font-weight: 500;
  }

  .agreement-section {
    // display: flex;
    // flex-direction: column;
    font-size: 13px;

    .agreement-links {
      display: inline;
      margin-top: 4px;
      margin-left: 28px;
    }
  }

  .social-login-section {
    margin: 10px 0;
  }

  .login-link {
    margin-top: 5px;
  }
}

/* 平板电脑样式 */
@media (max-width: 1023px) {
  .register-main {
    max-width: 90%;
    margin: 30px auto;
    background-position: left center;
  }

  .register-container {
    justify-content: center;
    background-color: rgba(255, 255, 255, 0.8);
  }

  .register-form {
    width: 450px;
    max-width: 100%;
  }
}

/* 手机样式 */
@media (max-width: 599px) {
  .register-main {
    max-width: 95%;
    margin: 20px auto;
    background-image: none;
    background-color: #f5f5f5;
    min-height: auto;
    box-shadow: none;
  }

  .register-container {
    padding: 15px;
  }

  .register-form {
    width: 100%;
    box-shadow: none;

    .register-form-title {
      font-size: 1.3rem;
      margin-top: 10px;
      margin-bottom: 15px;
    }

    .agreement-section {
      font-size: 12px;
    }

    .captcha-input-container {
      // flex-direction: column;
      gap: 8px;

      .captcha-image-container {
        // align-self: center;
        min-width: 100px;

        .captcha-image,
        .captcha-refresh-btn {
          width: 100px;
          height: 35px;
        }
      }
    }
  }
}
</style>
