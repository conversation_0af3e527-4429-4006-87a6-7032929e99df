<template>
  <q-dialog v-model="showConsent" persistent :maximized="$q.screen.lt.sm">
    <q-card class="cookie-consent-card">
      <q-card-section>
        <div class="text-h6">{{ $t('cookiesConsent.consent.title') }}</div>
      </q-card-section>

      <q-card-section class="q-pt-none">
        <p>{{ $t('cookiesConsent.consent.message') }}</p>
        <p class="q-mb-none">
          {{ $t('cookiesConsent.consent.learnMore') }}
          <NuxtLink to="/cookies" class="text-primary">{{ $t('cookiesConsent.consent.cookiesLink') }}</NuxtLink>
          {{ $t('cookiesConsent.consent.andText') }}
          <NuxtLink to="/privacy" class="text-primary">{{ $t('cookiesConsent.consent.privacyLink') }}</NuxtLink>
          {{ $t('cookiesConsent.consent.details') }}
        </p>
      </q-card-section>

      <q-card-actions align="right" class="bg-white text-primary">
        <q-btn flat :label="$t('cookiesConsent.consent.customize')" @click="openCustomizeDialog" />
        <q-btn flat :label="$t('cookiesConsent.consent.reject')" @click="rejectAll" />
        <q-btn unelevated color="primary" :label="$t('cookiesConsent.consent.accept')" @click="acceptAll" />
      </q-card-actions>
    </q-card>
  </q-dialog>

  <q-dialog v-model="showCustomize">
    <q-card class="cookie-customize-card">
      <q-card-section>
        <div class="text-h6">{{ $t('cookiesConsent.customize.title') }}</div>
      </q-card-section>

      <q-card-section>
        <q-item tag="label" v-ripple>
          <q-item-section>
            <q-item-label>{{ $t('cookiesConsent.customize.essential') }}</q-item-label>
            <q-item-label caption>{{ $t('cookiesConsent.customize.essentialDesc') }}</q-item-label>
          </q-item-section>
          <q-item-section side>
            <q-toggle v-model="cookieSettings.essential" disable color="primary" />
          </q-item-section>
        </q-item>

        <q-item tag="label" v-ripple>
          <q-item-section>
            <q-item-label>{{ $t('cookiesConsent.customize.performance') }}</q-item-label>
            <q-item-label caption>{{ $t('cookiesConsent.customize.performanceDesc') }}</q-item-label>
          </q-item-section>
          <q-item-section side>
            <q-toggle v-model="cookieSettings.performance" color="primary" />
          </q-item-section>
        </q-item>

        <q-item tag="label" v-ripple>
          <q-item-section>
            <q-item-label>{{ $t('cookiesConsent.customize.functional') }}</q-item-label>
            <q-item-label caption>{{ $t('cookiesConsent.customize.functionalDesc') }}</q-item-label>
          </q-item-section>
          <q-item-section side>
            <q-toggle v-model="cookieSettings.functional" color="primary" />
          </q-item-section>
        </q-item>

        <q-item tag="label" v-ripple>
          <q-item-section>
            <q-item-label>{{ $t('cookiesConsent.customize.targeting') }}</q-item-label>
            <q-item-label caption>{{ $t('cookiesConsent.customize.targetingDesc') }}</q-item-label>
          </q-item-section>
          <q-item-section side>
            <q-toggle v-model="cookieSettings.targeting" color="primary" />
          </q-item-section>
        </q-item>
      </q-card-section>

      <q-card-actions align="right" class="bg-white">
        <q-btn flat :label="$t('cookiesConsent.customize.cancel')" @click="showCustomize = false" />
        <q-btn unelevated color="primary" :label="$t('cookiesConsent.customize.save')" @click="saveSettings" />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref, onMounted } from 'vue';

const showConsent = ref(false);
const showCustomize = ref(false);

const cookieSettings = ref({
  essential: true,
  performance: true,
  functional: true,
  targeting: true,
});

// 检查用户是否已经设置了Cookie偏好
onMounted(() => {
  const consentGiven = localStorage.getItem('cookieConsent');
  if (!consentGiven) {
    // 如果用户尚未给予同意，显示同意横幅
    showConsent.value = true;
  } else {
    // 如果用户已经给予同意，加载保存的设置
    try {
      const savedSettings = JSON.parse(localStorage.getItem('cookieSettings'));
      if (savedSettings) {
        cookieSettings.value = { ...cookieSettings.value, ...savedSettings };
      }
    } catch (e) {
      console.error('Error parsing cookie settings:', e);
    }
  }
});

// 接受所有Cookie
const acceptAll = () => {
  cookieSettings.value = {
    essential: true,
    performance: true,
    functional: true,
    targeting: true,
  };
  saveConsentToStorage();
  showConsent.value = false;
};

// 拒绝非必要Cookie
const rejectAll = () => {
  cookieSettings.value = {
    essential: true, // 必要Cookie不能禁用
    performance: false,
    functional: false,
    targeting: false,
  };
  saveConsentToStorage();
  showConsent.value = false;
};

// 打开自定义设置对话框
const openCustomizeDialog = () => {
  showCustomize.value = true;
};

// 保存用户设置
const saveSettings = () => {
  saveConsentToStorage();
  showCustomize.value = false;
  showConsent.value = false;
};

// 将同意设置保存到本地存储
const saveConsentToStorage = () => {
  localStorage.setItem('cookieConsent', 'true');
  localStorage.setItem('cookieSettings', JSON.stringify(cookieSettings.value));

  // 这里可以添加代码来应用Cookie设置
  // 例如，如果用户拒绝了分析Cookie，可以禁用分析工具
  applySettings();
};

// 应用Cookie设置
const applySettings = () => {
  // 这里是应用Cookie设置的逻辑
  // 例如，如果用户拒绝了分析Cookie，可以禁用Google Analytics
  if (!cookieSettings.value.performance) {
    // 禁用分析工具的代码
    console.log('Performance cookies disabled');
  }

  if (!cookieSettings.value.targeting) {
    // 禁用广告跟踪的代码
    console.log('Targeting cookies disabled');
  }

  // 触发自定义事件，通知应用程序Cookie设置已更改
  window.dispatchEvent(
    new CustomEvent('cookieSettingsChanged', {
      detail: cookieSettings.value,
    })
  );
};
</script>

<style lang="scss" scoped>
.cookie-consent-card {
  max-width: 600px;
  width: 100%;

  @media (max-width: 599px) {
    max-width: 100%;
    min-height: 100%;
  }
}

.cookie-customize-card {
  max-width: 500px;
  width: 100%;

  @media (max-width: 599px) {
    max-width: 100%;
  }
}
</style>
