<template>
  <!-- 精选推荐 -->
  <div class="jingxuan">
    <div class="module-title">
      <h4>{{ display.title }}</h4>
      <!-- <a :href="moreLink" class="more-link">
        {{ $t('more') }}
        <i class="iconfont icon-gengduo gengduo" />
      </a> -->
    </div>

    <!-- 桌面端网格布局 -->
    <div class="product-grid gt-sm">
      <div v-for="(product, index) in desktopProducts" :key="index" class="product-item">
        <ProductBox :product="product" :index="index" />
      </div>
    </div>

    <!-- 平板端网格布局 -->
    <div class="product-grid-tablet sm lt-md">
      <div v-for="(product, index) in tabletProducts" :key="index" class="product-item">
        <ProductBox :product="product" :index="index" />
      </div>
    </div>

    <!-- 移动端网格布局 -->
    <div class="product-grid-mobile xs">
      <div v-for="(product, index) in mobileProducts" :key="index" class="product-item">
        <ProductBox :product="product" :index="index" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { useResponsive } from '~/composables/useResponsive';

const props = defineProps({
  display: {
    type: Object,
    required: true,
    default: () => ({}), // 确保 prop 的默认值是一个对象
  },
  // title: { type: String, required: false, default: '' },
  // subTitle: { type: String, required: false, default: '' },
  // moreLink: { type: String, required: false, default: '' },
  // products: { type: Array, required: true }, // 商品列表
});

const { isMobile, isTablet, isDesktop } = useResponsive();

// 计算桌面端显示的商品（每行5个，最后一行如果不满5个则舍去）
const desktopProducts = computed(() => {
  if (!props.display.spus || props.display.spus.length === 0) return [];

  const itemsPerRow = 5;
  const completeRows = Math.floor(props.display.spus.length / itemsPerRow);
  return props.display.spus.slice(0, completeRows * itemsPerRow);
});

// 计算平板端显示的商品（每行3个，最后一行如果不满3个则舍去）
const tabletProducts = computed(() => {
  if (!props.display.spus || props.display.spus.length === 0) return [];

  const itemsPerRow = 3;
  const completeRows = Math.floor(props.display.spus.length / itemsPerRow);
  return props.display.spus.slice(0, completeRows * itemsPerRow);
});

// 计算移动端显示的商品（每行2个，最后一行如果不满2个则舍去）
const mobileProducts = computed(() => {
  if (!props.display.spus || props.display.spus.length === 0) return [];

  const itemsPerRow = 2;
  const completeRows = Math.floor(props.display.spus.length / itemsPerRow);
  return props.display.spus.slice(0, completeRows * itemsPerRow);
});
</script>

<style lang="scss" scoped>
.jingxuan,
.trending {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  margin-top: 30px;
  padding: 0;

  .module-title {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 20px;
    position: relative;

    h4 {
      font-size: 24px;
      font-weight: bold;
      margin: 0;
      text-align: center;

      @media (max-width: 599px) {
        font-size: 20px;
      }
    }

    .more-link {
      position: absolute;
      right: 0;
      color: #888;
      font-size: 14px;
      text-decoration: none;
      transition: color 0.3s ease;

      &:hover {
        color: #333;
      }
      .gengduo {
        font-size: 12px;
      }
    }
  }

  .product-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 14px;
  }

  .product-grid-tablet {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 14px;
  }

  .product-grid-mobile {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }

  .product-item {
    width: 100%;
  }
}

@media (min-width: 600px) and (max-width: 1023px) {
  .jingxuan,
  .trending {
    padding: 0 12px;
    margin-top: 20px;

    .product-grid-tablet {
      gap: 12px;
    }
  }
}

@media (max-width: 599px) {
  .jingxuan,
  .trending {
    padding: 0 8px;
    margin-top: 15px;

    .product-grid-mobile {
      gap: 8px;
    }

    .product-item {
      margin-bottom: 10px;
    }
  }
}
</style>
