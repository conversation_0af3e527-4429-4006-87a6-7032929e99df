<template>
  <div v-if="hasCoupons" class="row justify-start items-center q-mt-md q-pa-md wrap-on-mobile coupon-section rounded-borders">
    <span class="col-12 col-sm-1 q-mb-sm-none q-mb-sm text-weight-medium">选择优惠券:</span>
    <q-select
      outlined
      dense
      v-model="selectedCoupon"
      :options="options"
      option-label="name"
      option-value="id"
      emit-value
      map-options
      class="coupon-select q-ml-md-desktop"
      style="background: #ffffff" />
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  modelValue: {
    type: [String, Number, null],
    default: null,
  },
  options: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(['update:modelValue']);

// 判断是否有可用优惠券
const hasCoupons = computed(() => {
  // 检查是否有真正的优惠券（不仅仅是"无可用优惠券"选项）
  return props.options.some((option) => option.id !== null && option.name !== '无可用优惠券');
});

// 使用计算属性实现双向绑定
const selectedCoupon = computed({
  get: () => props.modelValue,
  set: (value) => {
    emit('update:modelValue', value);
  },
});

// 使用计算属性的set方法已经能够处理优惠券变更事件
</script>

<style lang="scss" scoped>
// 优惠券选择区域样式
.coupon-section {
  background-color: #fff8f0;
  border: 1px solid #ffe4cc;
}

// 响应式布局样式
@media (max-width: 600px) {
  .wrap-on-mobile {
    flex-wrap: wrap;
  }

  .coupon-select {
    width: 100%;
    margin-left: 0 !important;
  }

  .q-mb-sm {
    margin-bottom: 8px;
  }
}

@media (min-width: 601px) {
  .q-ml-md-desktop {
    margin-left: 16px;
  }

  .q-mb-sm-none {
    margin-bottom: 0;
  }

  .coupon-select {
    width: 300px;
  }
}
</style>
