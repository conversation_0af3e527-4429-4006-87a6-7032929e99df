# 工单系统使用说明

## 系统概述

工单系统是一个完整的客户服务解决方案，支持用户创建、查看、回复和管理工单。系统包含工单列表、工单详情、文件上传等功能，并提供了可复用的工单弹窗组件。

## 页面说明

### 1. 工单列表页面 (`/account/ticket`)

**功能特性：**
- 工单类型和状态筛选
- 分页显示工单列表
- 响应式设计（PC表格视图 + 移动端卡片视图）
- 新建工单功能
- 快速跳转到工单详情

**操作说明：**
1. 点击"新建工单"按钮创建新工单
2. 使用筛选条件过滤工单
3. 点击工单行或查看按钮进入详情页
4. 点击关闭按钮可关闭未完成的工单

### 2. 工单详情页面 (`/account/ticket-detail/[id]`)

**功能特性：**
- 工单基本信息展示
- 处理记录时间线
- 回复功能（支持文件上传）
- 工单评价功能
- 工单关闭功能
- 文件预览和下载

**操作说明：**
1. 查看工单基本信息和处理历史
2. 在回复区域添加新的回复内容
3. 上传相关文件（图片、文档）
4. 对已解决的工单进行评价
5. 关闭不需要继续处理的工单

## 组件使用

### TicketDialog 工单弹窗组件

**基本用法：**
```vue
<template>
  <div>
    <q-btn @click="showDialog = true">新建工单</q-btn>
    
    <TicketDialog 
      v-model="showDialog"
      @success="onTicketCreated"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue';
import TicketDialog from '~/components/TicketDialog.vue';

const showDialog = ref(false);

const onTicketCreated = (ticket) => {
  console.log('工单创建成功:', ticket);
};
</script>
```

**高级用法（带默认参数）：**
```vue
<template>
  <TicketDialog 
    v-model="showDialog"
    :default-type="TicketApi.TICKET_TYPES.ORDER"
    :default-title="`订单号：${orderId}`"
    :default-description="请描述您遇到的订单问题..."
    @success="onTicketCreated"
  />
</template>
```

**Props 说明：**
- `modelValue` (Boolean): 弹窗显示状态
- `defaultType` (Number): 默认工单类型
- `defaultTitle` (String): 默认工单标题
- `defaultDescription` (String): 默认问题描述

**Events 说明：**
- `update:modelValue`: 弹窗状态变化
- `success`: 工单创建成功，返回工单信息

## 工单类型说明

| 类型值 | 类型名称 | 使用场景 |
|--------|----------|----------|
| 1 | 订单问题 | 订单相关的咨询和问题 |
| 2 | 包裹问题 | 物流、包裹相关问题 |
| 3 | 商品问题 | 商品质量、规格等问题 |
| 4 | 售后服务 | 退换货、维修等售后需求 |
| 5 | 投诉建议 | 服务投诉和改进建议 |
| 6 | 其他问题 | 其他类型的问题 |

## 工单状态说明

| 状态值 | 状态名称 | 说明 |
|--------|----------|------|
| 1 | 待处理 | 新创建的工单，等待客服处理 |
| 2 | 处理中 | 客服正在处理中 |
| 3 | 待回复 | 等待用户回复 |
| 4 | 已解决 | 问题已解决，可进行评价 |
| 5 | 已关闭 | 工单已关闭，不可继续操作 |

## 文件上传说明

**支持的文件类型：**
- 图片：jpg, jpeg, png, gif, bmp, webp
- 文档：pdf, doc, docx, txt

**限制条件：**
- 单个文件最大 10MB
- 每次最多上传 5 个文件
- 支持图片预览功能

## 在其他页面集成工单功能

### 订单页面集成示例

```vue
<template>
  <div class="order-page">
    <!-- 订单信息 -->
    <div class="order-info">
      <h3>订单 #{{ orderId }}</h3>
      <q-btn 
        color="orange" 
        icon="support_agent"
        label="订单问题咨询" 
        @click="showOrderTicket"
      />
    </div>

    <!-- 工单弹窗 -->
    <TicketDialog 
      v-model="ticketDialogVisible"
      :default-type="defaultTicketType"
      :default-title="defaultTicketTitle"
      :default-description="defaultTicketDescription"
      @success="onTicketCreated"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue';
import TicketDialog from '~/components/TicketDialog.vue';
import TicketApi from '~/composables/ticketApi';

const orderId = 'ORD123456';
const ticketDialogVisible = ref(false);
const defaultTicketType = ref(TicketApi.TICKET_TYPES.OTHER);
const defaultTicketTitle = ref('');
const defaultTicketDescription = ref('');

const showOrderTicket = () => {
  defaultTicketType.value = TicketApi.TICKET_TYPES.ORDER;
  defaultTicketTitle.value = `订单号：${orderId}`;
  defaultTicketDescription.value = '请描述您遇到的订单问题...';
  ticketDialogVisible.value = true;
};

const onTicketCreated = (ticket) => {
  // 可以跳转到工单详情页
  navigateTo(`/account/ticket-detail/${ticket.id}`);
};
</script>
```

### 包裹页面集成示例

```vue
<script setup>
const showPackageTicket = (packageId) => {
  defaultTicketType.value = TicketApi.TICKET_TYPES.PACKAGE;
  defaultTicketTitle.value = `包裹号：${packageId}`;
  defaultTicketDescription.value = '请描述您遇到的包裹问题...';
  ticketDialogVisible.value = true;
};
</script>
```

## API 接口说明

所有API接口都在 `composables/ticketApi.js` 中定义：

- `TicketApi.create(data)` - 创建工单
- `TicketApi.getList(params)` - 获取工单列表
- `TicketApi.getDetail(id)` - 获取工单详情
- `TicketApi.reply(id, data)` - 回复工单
- `TicketApi.close(id)` - 关闭工单
- `TicketApi.rate(id, data)` - 评价工单
- `TicketApi.uploadFile(file)` - 上传文件

## 注意事项

1. **权限控制**：确保用户已登录才能访问工单功能
2. **文件安全**：上传的文件会经过后端安全检查
3. **数据验证**：所有表单都有客户端验证，但仍需后端验证
4. **错误处理**：网络错误和业务错误都有相应的提示
5. **响应式设计**：在移动端使用时注意触摸操作的友好性

## 常见问题

**Q: 如何自定义工单类型？**
A: 在 `TicketApi.TICKET_TYPES` 中添加新的类型，并更新相关的显示逻辑。

**Q: 如何修改文件上传限制？**
A: 修改组件中的文件大小和数量限制，同时确保后端也做相应调整。

**Q: 如何添加新的工单状态？**
A: 在 `TicketApi.TICKET_STATUS` 中添加新状态，并更新状态颜色和名称映射。

**Q: 如何自定义工单弹窗样式？**
A: 修改 `TicketDialog.vue` 组件的样式部分，或通过CSS变量进行主题定制。
