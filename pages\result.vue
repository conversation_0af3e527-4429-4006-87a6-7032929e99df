<!-- pages/result.vue -->
<template>
  <HeaderSimple />
  <div v-if="resultState?.title" class="result-page q-mx-auto q-my-xl text-center" style="max-width: 800px">
    <!-- 成功或失败的图标 -->
    <q-icon :name="icon" :color="iconColor" size="80px" class="q-mb-md" />

    <!-- 标题和描述 -->
    <div class="text-h5 q-mb-sm">{{ resultState.title }}</div>
    <div class="text-body1 q-mb-lg">{{ resultState.message }}</div>

    <!-- 返回首页按钮 -->
    <q-btn color="primary" label="Back to Home" @click="goHome" />
  </div>
  <Footer />
</template>

<script setup>
import { useRouter } from 'vue-router';
import { useState } from '#app';

const router = useRouter();
const resultState = useState('resultState');

// 如果状态为空，重定向到首页
if (!resultState.value || !resultState.value.title) {
  router.replace('/');
}

// 根据状态设置图标和颜色
const icon = resultState.value?.status === 'success' ? 'check_circle' : 'error';
const iconColor = resultState.value?.status === 'success' ? 'green' : 'red';

// 返回首页
const goHome = () => {
  resultState.value = null; // 清理状态
  router.push('/');
};
</script>

<style scoped>
.result-page {
  padding: 20px;
  background-color: #fff;
}
</style>
