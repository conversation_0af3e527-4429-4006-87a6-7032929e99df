<template>
  <div class="coupon-code-section">
    <div class="row items-center justify-between q-mb-sm">
      <div class="row items-center">
        <q-icon name="local_offer" color="primary" size="xs" class="q-mr-xs" />
        <div class="text-subtitle2 text-weight-bold">优惠券</div>
      </div>
      <q-btn flat dense color="primary" icon="add" label="输入优惠码" @click="openDialog" />
    </div>

    <q-card flat bordered class="q-pa-sm">
      <div class="row items-center">
        <div v-if="validCoupon" class="col-grow">
          <div class="row items-center justify-between">
            <div>
              <div class="text-caption text-weight-medium">
                优惠码：
                {{ couponCode }}
              </div>
              <div class="text-caption text-grey-8">
                优惠金额:
                <span class="text-negative text-weight-medium">
                  <span v-if="validCoupon.discountType === 1">-¥{{ fen2yuan(validCoupon.discountAmount) }}</span>
                  <span v-else-if="validCoupon.discountType === 2">{{ validCoupon.discountPercent }}折</span>
                </span>
              </div>
            </div>
            <div class="text-right">
              <q-btn flat round color="primary" icon="edit" @click="openDialog" />
              <q-btn flat round color="primary" icon="delete" @click="clearCoupon" />
            </div>
          </div>
        </div>
        <div v-else class="col-grow text-center">
          <!-- <q-btn color="primary" outline label="输入优惠码" @click="openDialog" /> -->
          <div class="text-caption text-grey-8">
            没有使用优惠码
            <!-- <q-btn flat class="q-ml-xs" @click="useExampleCode('SAVE20')">使用示例优惠码</q-btn> -->
          </div>
        </div>
      </div>
    </q-card>

    <!-- 优惠码输入弹窗 -->
    <q-dialog v-model="couponDialog" persistent>
      <q-card style="width: 500px; max-width: 90vw" class="coupon-dialog">
        <q-card-section class="row items-center q-pb-none">
          <div class="text-h6 text-primary">
            <q-icon name="local_offer" class="q-mr-sm" />
            输入优惠码
          </div>
          <q-space />
          <q-btn icon="close" flat round dense @click="closeDialog" />
        </q-card-section>

        <q-card-section class="q-pt-sm">
          <div class="text-body2 text-grey-8 q-mb-md">请输入您的优惠码，享受更多优惠</div>

          <q-input
            v-model="tempCouponCode"
            label="优惠码"
            outlined
            placeholder="请输入优惠码，如：SAVE20"
            :error="!!errorMessage"
            :error-message="errorMessage"
            :loading="verifying"
            @keyup.enter="verifyCoupon"
            @input="clearError"
            class="q-mb-md">
            <template #prepend>
              <q-icon name="confirmation_number" color="primary" />
            </template>
            <template #append>
              <q-btn v-if="tempCouponCode" flat round color="grey" icon="clear" @click="clearInput" />
            </template>
          </q-input>
        </q-card-section>

        <q-card-actions align="right" class="q-pt-none">
          <q-btn flat label="取消" color="grey" @click="closeDialog" />
          <q-btn label="确认" color="primary" @click="verifyCoupon" :loading="verifying" :disable="!tempCouponCode.trim()">
            <template #loading>
              <q-spinner-dots />
            </template>
          </q-btn>
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue';
import { useQuasar } from 'quasar';
import CouponApi from '../../composables/couponApi';
import { fen2yuan } from '../../utils/utils';

const props = defineProps({
  modelValue: {
    type: String,
    default: '',
  },
  validatedCoupon: {
    type: Object,
    default: null,
  },
});

const emit = defineEmits(['update:modelValue', 'coupon-validated', 'coupon-cleared']);

const $q = useQuasar();

const couponCode = ref(props.modelValue);
const validCoupon = ref(props.validatedCoupon);
const verifying = ref(false);
const errorMessage = ref('');
const couponDialog = ref(false);
const tempCouponCode = ref('');

// 打开弹窗
function openDialog() {
  tempCouponCode.value = couponCode.value;
  errorMessage.value = '';
  couponDialog.value = true;
}

// 关闭弹窗
function closeDialog() {
  // 只有在没有验证成功的情况下才能关闭
  if (!verifying.value) {
    couponDialog.value = false;
    tempCouponCode.value = '';
    errorMessage.value = '';
  }
}

// 清除输入
function clearInput() {
  tempCouponCode.value = '';
  errorMessage.value = '';
}

// 验证优惠码
async function verifyCoupon() {
  if (!tempCouponCode.value.trim()) {
    errorMessage.value = '请输入优惠码';
    return;
  }

  try {
    verifying.value = true;
    errorMessage.value = '';

    // 构建请求参数，符合后端接口要求
    const reqVO = {
      couponCode: tempCouponCode.value.trim(),
      businessType: 2,
      totalAmount: 2000,
    };

    const { code, data, msg } = await CouponApi.verifyCouponCode(reqVO);

    if (code === 0 && data) {
      if (data.valid) {
        validCoupon.value = data;
        couponCode.value = tempCouponCode.value.trim();
        emit('update:modelValue', tempCouponCode.value.trim());
        emit('coupon-validated', data);

        // 验证成功，关闭弹窗
        couponDialog.value = false;
        tempCouponCode.value = '';

        $q.notify({
          color: 'positive',
          message: '优惠码验证成功',
          icon: 'check_circle',
        });
      } else {
        // 验证失败，显示错误信息，不关闭弹窗
        errorMessage.value = data.invalidReason || '优惠码无效';
      }
    } else {
      // API调用失败，显示错误信息，不关闭弹窗
      errorMessage.value = msg || '验证失败，请重试';
    }
  } catch (error) {
    console.error('验证优惠码失败:', error);
    errorMessage.value = '网络错误，请重试';
  } finally {
    verifying.value = false;
  }
}

// 清除优惠码
function clearCoupon() {
  couponCode.value = '';
  validCoupon.value = null;
  errorMessage.value = '';
  emit('update:modelValue', '');
  emit('coupon-cleared');
}

// 清除错误信息
function clearError() {
  if (errorMessage.value) {
    errorMessage.value = '';
  }
  // 如果用户修改了优惠码，清除之前的验证结果
  if (validCoupon.value && couponCode.value !== props.modelValue) {
    validCoupon.value = null;
    emit('coupon-cleared');
  }
}

// 监听外部传入的值变化
watch(
  () => props.modelValue,
  (newValue) => {
    couponCode.value = newValue;
  }
);

watch(
  () => props.validatedCoupon,
  (newValue) => {
    validCoupon.value = newValue;
  }
);

// 监听couponCode变化，同步到父组件
watch(couponCode, (newValue) => {
  emit('update:modelValue', newValue);
});
</script>

<style lang="scss" scoped>
.coupon-code-section {
  .q-input {
    :deep(.q-field__control) {
      height: 40px;
    }
  }

  .q-card {
    border-radius: 4px;
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
  }
}

.coupon-dialog {
  border-radius: 12px;
  overflow: hidden;

  .q-card-section {
    &:first-child {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;

      .text-h6 {
        color: white;
      }

      .q-btn {
        color: white;
      }
    }
  }

  .q-input {
    :deep(.q-field__control) {
      border-radius: 8px;
    }

    :deep(.q-field--outlined .q-field__control) {
      &:hover {
        box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
      }
    }

    :deep(.q-field--focused .q-field__control) {
      box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.3);
    }
  }

  .q-btn {
    border-radius: 6px;
    font-weight: 500;

    &:not(.q-btn--flat) {
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
      }
    }
  }
}
</style>
