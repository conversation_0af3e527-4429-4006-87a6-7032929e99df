<template>
  <div class="shipping-results">
    <q-card class="results-card">
      <q-card-section class="q-pb-none">
        <div class="row items-center justify-between">
          <h3 class="text-h6 q-ma-none">
            <q-icon name="local_shipping" class="q-mr-sm" />
            {{ $t('shippingCalculator.shippingOptions', '运输方案') }}
          </h3>
          <q-chip v-if="results.length > 0" color="positive" text-color="white" icon="check_circle" :label="`${results.length} ${$t('common.options', '个方案')}`" />
        </div>
      </q-card-section>

      <q-card-section>
        <!-- 加载状态 -->
        <div v-if="loading" class="text-center q-py-xl">
          <q-spinner-dots size="50px" color="primary" />
          <div class="text-subtitle1 q-mt-md">{{ $t('common.calculating', '计算中...') }}</div>
        </div>

        <!-- 无结果 -->
        <div v-else-if="results.length === 0" class="text-center q-py-xl">
          <q-icon name="search_off" size="60px" color="grey-5" />
          <div class="text-subtitle1 text-grey-7 q-mt-md">
            {{ $t('shippingCalculator.noResults', '暂无可用的运输方案') }}
          </div>
        </div>

        <!-- 结果列表 -->
        <div v-else class="results-list">
          <div v-for="(result, index) in results" :key="result.id" class="shipping-result-item q-mb-md" :class="{ unavailable: !result.available }">
            <q-card flat bordered class="shipping-option-card" :class="{ 'unavailable-card': !result.available }">
              <!-- 基础信息行 -->
              <q-item class="shipping-option-header">
                <q-item-section avatar>
                  <q-avatar :color="getShippingColor(index)" text-color="white" :icon="result.icon || 'local_shipping'" size="50px" />
                </q-item-section>

                <q-item-section>
                  <q-item-label class="text-weight-medium text-h6">
                    {{ result.name }}
                  </q-item-label>
                  <q-item-label caption class="text-grey-7">
                    {{ result.nameEn }}
                  </q-item-label>

                  <!-- 基础运输信息 -->
                  <div class="shipping-basic-info q-mt-sm">
                    <div class="row q-gutter-md">
                      <div class="shipping-info-item">
                        <q-icon name="schedule" size="sm" class="q-mr-xs" />
                        <span class="text-caption"> {{ $t('shippingCalculator.estimatedDays', '预计') }}: {{ result.estimatedDays }} {{ $t('common.days', '天') }} </span>
                      </div>
                      <div class="shipping-info-item" v-if="result.reliability">
                        <q-icon name="verified" size="sm" class="q-mr-xs" />
                        <span class="text-caption"> {{ $t('shippingCalculator.reliability', '可靠性') }}: {{ result.reliability }}% </span>
                      </div>
                      <div class="shipping-info-item" v-if="result.taxInclude">
                        <q-icon name="security" size="sm" class="q-mr-xs" />
                        <span class="text-caption">
                          {{ $t('shippingCalculator.taxIncluded', '包税') }}
                        </span>
                      </div>
                    </div>
                  </div>
                </q-item-section>

                <q-item-section side>
                  <div class="text-right">
                    <!-- 可用方案显示价格 -->
                    <div v-if="result.available" class="price-display">
                      <!-- <span class="currency-symbol">$</span> -->
                      <span class="price-amount">{{ fen2yuan(result.price) }}</span>
                    </div>
                    <!-- 不可用方案显示原因 -->
                    <div v-else class="unavailable-reason">
                      <q-icon name="block" color="negative" size="sm" class="q-mr-xs" />
                      <span class="text-negative text-weight-medium">不可用</span>
                    </div>
                    <!-- <div v-if="result.available" class="text-caption text-grey-7">
                      {{ result.currency || 'USD' }}
                    </div> -->
                    <div v-else class="text-caption text-negative">
                      {{ result.unavailableReason || '该商品分类被禁止运输' }}
                    </div>
                  </div>
                </q-item-section>

                <q-item-section side>
                  <div class="action-buttons">
                    <q-btn v-if="result.available" flat round color="primary" :icon="expandedItems[result.id] ? 'expand_less' : 'expand_more'" size="sm" @click.stop="toggleExpand(result.id)">
                      <q-tooltip>{{ expandedItems[result.id] ? $t('common.collapse', '收起') : $t('common.expand', '展开') }}</q-tooltip>
                    </q-btn>
                    <!-- 不可用方案不显示展开按钮 -->
                    <div v-else class="unavailable-placeholder"></div>
                  </div>
                </q-item-section>
              </q-item>

              <!-- 详细信息展开区域 - 只有可用方案才能展开 -->
              <q-slide-transition>
                <div v-show="result.available && expandedItems[result.id]" class="shipping-details-expanded">
                  <q-separator />
                  <q-card-section class="q-pt-md">
                    <div class="row q-col-gutter-md">
                      <!-- 费用详情 -->
                      <div class="col-12 col-md-6">
                        <h6 class="text-subtitle2 q-mb-sm">
                          <q-icon name="credit_card" class="q-mr-xs" />
                          {{ $t('shippingCalculator.feeDetails', '费用详情') }}
                        </h6>
                        <q-list dense>
                          <q-item v-if="result.feeDetail">
                            <q-item-section>
                              <div class="fee-breakdown">
                                <div class="fee-item">
                                  <span>{{ $t('shippingCalculator.freight', '基础运费') }}:</span>
                                  <span class="fee-value">{{ fen2yuan(result.feeDetail.freight) }}</span>
                                </div>
                                <div class="fee-item" v-if="result.feeDetail.operationFee > 0">
                                  <span>{{ $t('shippingCalculator.operationFee', '操作费') }}:</span>
                                  <span class="fee-value">{{ fen2yuan(result.feeDetail.operationFee) }}</span>
                                </div>
                                <div class="fee-item" v-if="result.feeDetail.serviceFee > 0">
                                  <span>{{ $t('shippingCalculator.serviceFee', '服务费') }}:</span>
                                  <span class="fee-value">{{ fen2yuan(result.feeDetail.serviceFee) }}</span>
                                </div>
                                <div class="fee-item" v-if="result.feeDetail.customsFee > 0">
                                  <span>{{ $t('shippingCalculator.customsFee', '清关费') }}:</span>
                                  <span class="fee-value">{{ fen2yuan(result.feeDetail.customsFee) }}</span>
                                </div>
                                <div class="fee-item" v-if="result.feeDetail.fuelFee > 0">
                                  <span>{{ $t('shippingCalculator.fuelFee', '燃油费') }}:</span>
                                  <span class="fee-value">{{ fen2yuan(result.feeDetail.fuelFee) }}</span>
                                </div>
                                <q-separator class="q-my-sm" />
                                <div class="fee-item total-fee">
                                  <span class="text-weight-bold">{{ $t('shippingCalculator.totalFee', '总费用') }}:</span>
                                  <span class="fee-value text-weight-bold text-primary">{{ fen2yuan(result.price) }}</span>
                                </div>
                              </div>
                            </q-item-section>
                          </q-item>
                        </q-list>
                      </div>

                      <!-- 重量信息 -->
                      <div class="col-12 col-md-6">
                        <h6 class="text-subtitle2 q-mb-sm">
                          <q-icon name="scale" class="q-mr-xs" />
                          {{ $t('shippingCalculator.weightInfo', '重量信息') }}
                        </h6>
                        <q-list dense v-if="result.feeDetail">
                          <q-item>
                            <q-item-section>
                              <div class="weight-info">
                                <div class="weight-item">
                                  <span>{{ $t('shippingCalculator.actualWeight', '实际重量') }}:</span>
                                  <span class="weight-value">{{ result.feeDetail.weight || 0 }}g</span>
                                </div>
                                <div class="weight-item" v-if="result.feeDetail.volumeWeight">
                                  <span>{{ $t('shippingCalculator.volumeWeight', '体积重') }}:</span>
                                  <span class="weight-value">{{ result.feeDetail.volumeWeight || 0 }}g</span>
                                </div>
                                <div class="weight-item">
                                  <span class="text-weight-bold">{{ $t('shippingCalculator.chargeableWeight', '计费重量') }}:</span>
                                  <span class="weight-value text-weight-bold text-primary">{{ result.feeDetail.chargeableWeight || 0 }}g</span>
                                </div>
                              </div>
                            </q-item-section>
                          </q-item>
                        </q-list>
                      </div>
                    </div>

                    <!-- 产品特色和时效分布 - 并排显示 -->
                    <div class="row q-col-gutter-md q-mt-md">
                      <!-- 产品特色 -->
                      <div v-if="result.features" class="col-12 col-md-6">
                        <h6 class="text-subtitle2 q-mb-sm">
                          <q-icon name="star" class="q-mr-xs" />
                          {{ $t('shippingCalculator.features', '产品特色') }}
                        </h6>
                        <div class="features-content">
                          <p class="text-body2">{{ result.features }}</p>
                        </div>
                      </div>

                      <!-- 时效信息 -->
                      <div v-if="result.timelinessInfo && result.timelinessInfo.timelinessInfos" class="col-12 col-md-6">
                        <h6 class="text-subtitle2 q-mb-sm">
                          <q-icon name="timeline" class="q-mr-xs" />
                          {{ $t('shippingCalculator.timelinessInfo', '时效分布') }}
                        </h6>
                        <div class="timeliness-chart">
                          <div class="delivery-rate q-mb-sm">
                            <span class="text-body2">{{ $t('shippingCalculator.deliveryRate', '妥投率') }}: </span>
                            <span class="text-weight-bold text-positive">{{ result.timelinessInfo.deliveryRate }}%</span>
                          </div>
                          <div class="timeliness-bars">
                            <div v-for="info in result.timelinessInfo.timelinessInfos" :key="info.timeInterval" class="timeliness-bar-item">
                              <div class="time-interval">{{ info.timeInterval }}天</div>
                              <div class="progress-container">
                                <q-linear-progress :value="parseFloat(info.rate) / 100" color="primary" size="8px" class="q-my-xs" />
                              </div>
                              <div class="rate-value">{{ info.rate }}%</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- 品类限制信息 -->
                    <div v-if="result.categoryRestrictions && result.categoryRestrictions.length > 0" class="q-mt-md">
                      <h6 class="text-subtitle2 q-mb-sm">
                        <q-icon name="category" class="q-mr-xs" />
                        {{ $t('shippingCalculator.categoryRestrictions', '品类限制') }}
                      </h6>
                      <div class="category-restrictions-table">
                        <q-markup-table flat bordered dense>
                          <thead>
                            <tr>
                              <th class="text-left category-name-col">品类</th>
                              <th class="text-left allow-col">可寄送</th>
                              <th class="text-left block-col">无法寄送</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr v-for="category in result.categoryRestrictions" :key="category.name">
                              <td class="category-name">{{ category.name }}</td>
                              <td class="allow-items">
                                <div v-if="getAllowList(category) && getAllowList(category).length > 0" class="restriction-tags">
                                  <q-chip v-for="item in getAllowList(category)" :key="item" size="xs" color="positive" text-color="white" :label="item" class="q-mr-xs q-mb-xs" />
                                </div>
                                <span v-else class="text-grey-6">-</span>
                              </td>
                              <td class="block-items">
                                <div v-if="getBlockList(category) && getBlockList(category).length > 0" class="restriction-tags">
                                  <q-chip v-for="item in getBlockList(category)" :key="item" size="xs" color="negative" text-color="white" :label="item" class="q-mr-xs q-mb-xs" />
                                </div>
                                <span v-else class="text-grey-6">-</span>
                              </td>
                            </tr>
                          </tbody>
                        </q-markup-table>
                      </div>
                    </div>

                    <!-- 收费标准 -->
                    <div v-if="result.pricingStandard" class="q-mt-md">
                      <h6 class="text-subtitle2 q-mb-sm">
                        <q-icon name="payments" class="q-mr-xs" />
                        {{ $t('shippingCalculator.pricingStandard', '收费标准') }}
                      </h6>
                      <div class="pricing-standard">
                        <q-markup-table flat bordered>
                          <thead>
                            <tr>
                              <th class="text-left">{{ $t('shippingCalculator.weightRange', '重量范围') }}</th>
                              <th class="text-left">{{ $t('shippingCalculator.firstWeight', '首重价格') }}</th>
                              <th class="text-left">{{ $t('shippingCalculator.additionalWeight', '续重价格') }}</th>
                              <th class="text-left">{{ $t('shippingCalculator.maxWeight', '最大重量') }}</th>
                              <th class="text-left">{{ $t('shippingCalculator.fuelSurcharge', '燃油费') }}</th>
                              <th class="text-left">{{ $t('shippingCalculator.serviceFee', '服务费') }}</th>
                              <th class="text-left">{{ $t('shippingCalculator.remoteAreaFee', '远程费') }}</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr>
                              <td>{{ result.pricingStandard.weightRange || '-' }}</td>
                              <td>${{ result.pricingStandard.firstWeight || '0.00' }}/500g</td>
                              <td>${{ result.pricingStandard.additionalWeight || '0.00' }}/200g</td>
                              <td>${{ result.pricingStandard.maxWeight || '0.00' }}</td>
                              <td>${{ result.pricingStandard.fuelSurcharge || '0.00' }}</td>
                              <td>${{ result.pricingStandard.serviceFee || '0.00' }}</td>
                              <td>${{ result.pricingStandard.remoteAreaFee || '0.00' }}</td>
                            </tr>
                          </tbody>
                        </q-markup-table>
                      </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="action-section q-mt-md q-pt-md">
                      <q-separator class="q-mb-md" />
                      <div class="row justify-end">
                        <q-btn flat color="grey-7" :label="$t('common.collapse', '收起')" @click="toggleExpand(result.id)" />
                      </div>
                    </div>
                  </q-card-section>
                </div>
              </q-slide-transition>
            </q-card>
          </div>
        </div>

        <!-- 提示信息 -->
        <div v-if="results.length > 0" class="shipping-notice q-mt-md">
          <q-banner inline-actions class="text-white bg-info">
            <template #avatar>
              <q-icon name="info" />
            </template>
            {{ $t('shippingCalculator.priceNotice', '以上价格仅供参考，实际运费可能因包裹具体情况而有所调整。') }}
          </q-banner>
        </div>
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { fen2yuan } from '../../utils/utils';

const props = defineProps({
  results: {
    type: Array,
    default: () => [],
  },
  loading: {
    type: Boolean,
    default: false,
  },
});

// 状态管理
const expandedItems = ref({});

// 获取运输方式颜色
const getShippingColor = (index) => {
  const colors = ['primary', 'secondary', 'accent', 'positive', 'negative', 'info', 'warning'];
  return colors[index % colors.length];
};

// 切换展开/收起状态
const toggleExpand = (resultId) => {
  expandedItems.value[resultId] = !expandedItems.value[resultId];
};

// 获取允许列表（兼容不同的字段名）
const getAllowList = (category) => {
  return category.allowList || category.allowlist || [];
};

// 获取禁止列表（兼容不同的字段名）
const getBlockList = (category) => {
  return category.blockList || category.blocklist || [];
};
</script>

<style lang="scss" scoped>
.shipping-results {
  .results-card {
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .shipping-result-item {
    .shipping-option-card {
      border-radius: 12px;
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      }
    }

    .shipping-option-header {
      padding: 16px;

      &:hover {
        background-color: #f8f9fa;
      }
    }

    // 不可用方案的样式
    &.unavailable {
      .shipping-option-card {
        background-color: #f5f5f5;
        border: 1px solid #e0e0e0;
        opacity: 0.7;

        &:hover {
          box-shadow: none;
        }
      }

      .shipping-option-header {
        &:hover {
          background-color: #f5f5f5;
        }

        .q-avatar {
          background-color: #bdbdbd !important;
        }

        .q-item-label {
          color: #999;
        }

        .q-item-label.caption {
          color: #bbb;
        }

        .shipping-basic-info {
          .shipping-info-item {
            color: #999;
          }
        }
      }
    }
  }

  .shipping-basic-info {
    .shipping-info-item {
      display: flex;
      align-items: center;
      color: #666;
    }
  }

  .price-display {
    .currency-symbol {
      font-size: 0.9rem;
      color: #666;
    }

    .price-amount {
      font-size: 1.5rem;
      font-weight: 600;
      color: #1976d2;
    }
  }

  .unavailable-reason {
    display: flex;
    align-items: center;
    font-size: 1.1rem;
    font-weight: 600;
  }

  .action-buttons {
    display: flex;
    gap: 8px;
  }

  .shipping-details-expanded {
    background-color: #fafafa;

    .fee-breakdown {
      .fee-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 4px 0;

        &.total-fee {
          padding-top: 8px;
          font-size: 1.1rem;
        }
      }

      .fee-value {
        font-weight: 500;
        color: #1976d2;
      }
    }

    .weight-info {
      .weight-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 4px 0;
      }

      .weight-value {
        font-weight: 500;
        color: #1976d2;
      }
    }

    .features-content {
      background-color: #fff;
      padding: 12px;
      border-radius: 8px;
      border-left: 4px solid #1976d2;
    }

    .timeliness-chart {
      .delivery-rate {
        padding: 8px 12px;
        background-color: #e8f5e8;
        border-radius: 6px;
      }

      .timeliness-bars {
        .timeliness-bar-item {
          display: flex;
          align-items: center;
          gap: 12px;
          padding: 6px 0;

          .time-interval {
            min-width: 60px;
            font-size: 0.875rem;
            color: #666;
          }

          .progress-container {
            flex: 1;
          }

          .rate-value {
            min-width: 40px;
            text-align: right;
            font-weight: 500;
            color: #1976d2;
          }
        }
      }
    }

    .category-restrictions-table {
      background-color: #fff;
      border-radius: 8px;
      overflow: hidden;

      .q-markup-table {
        th {
          background-color: #f5f5f5;
          font-weight: 600;
          color: #333;
          padding: 8px 6px;
          font-size: 0.75rem;

          &.category-name-col {
            width: 15%;
            min-width: 80px;
          }

          &.allow-col,
          &.block-col {
            width: 42.5%;
          }
        }

        td {
          padding: 6px;
          font-size: 0.75rem;
          border-bottom: 1px solid #e0e0e0;
          vertical-align: top;

          &.category-name {
            font-weight: 500;
            background-color: #fafafa;
            white-space: nowrap;
          }

          &.allow-items,
          &.block-items {
            .restriction-tags {
              display: flex;
              flex-wrap: wrap;
              gap: 2px;

              .q-chip {
                font-size: 0.65rem;
                height: 18px;
                padding: 0 6px;
                margin: 1px;
              }
            }
          }
        }

        tr:last-child td {
          border-bottom: none;
        }
      }
    }

    .pricing-standard {
      background-color: #fff;
      border-radius: 8px;
      overflow: hidden;

      .q-markup-table {
        th {
          background-color: #f5f5f5;
          font-weight: 600;
          color: #333;
          padding: 12px 8px;
          font-size: 0.875rem;
        }

        td {
          padding: 10px 8px;
          font-size: 0.875rem;
          border-bottom: 1px solid #e0e0e0;

          &:first-child {
            font-weight: 500;
          }
        }

        tr:last-child td {
          border-bottom: none;
        }
      }
    }

    .action-section {
      background-color: #fff;
      margin: 0 -16px -16px -16px;
      padding: 16px;
      border-radius: 0 0 12px 12px;
    }
  }

  .shipping-notice {
    .q-banner {
      border-radius: 8px;
    }
  }
}

@media (max-width: 768px) {
  .shipping-results {
    .shipping-result-item {
      .shipping-option-header {
        padding: 12px;

        .q-item-section {
          &:not(.side) {
            padding-right: 8px;
          }
        }
      }
    }

    .price-display {
      .price-amount {
        font-size: 1.2rem;
      }
    }

    .shipping-details-expanded {
      .row {
        .col-md-6 {
          margin-bottom: 16px;
        }
      }

      .timeliness-chart {
        .timeliness-bars {
          .timeliness-bar-item {
            flex-direction: column;
            align-items: stretch;
            gap: 4px;

            .time-interval {
              min-width: auto;
              text-align: center;
            }

            .rate-value {
              text-align: center;
            }
          }
        }
      }

      .pricing-standard {
        .q-markup-table {
          font-size: 0.75rem;

          th,
          td {
            padding: 8px 4px;
          }

          th {
            font-size: 0.75rem;
          }
        }
      }

      .category-restrictions-table {
        .q-markup-table {
          font-size: 0.65rem;

          th {
            padding: 6px 4px;
            font-size: 0.65rem;

            &.category-name-col {
              width: 20%;
            }

            &.allow-col,
            &.block-col {
              width: 40%;
            }
          }

          td {
            padding: 4px;
            font-size: 0.65rem;

            &.allow-items,
            &.block-items {
              .restriction-tags {
                gap: 1px;

                .q-chip {
                  font-size: 0.6rem;
                  height: 16px;
                  padding: 0 4px;
                  margin: 0.5px;
                }
              }
            }
          }
        }
      }

      .action-section {
        .row {
          flex-direction: column;
          gap: 8px;

          .q-btn {
            width: 100%;
          }
        }
      }
    }
  }
}
</style>
