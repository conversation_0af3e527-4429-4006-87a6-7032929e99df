<template>
  <!-- 头部广告 -->
  <transition name="slide-up">
    <div v-if="items?.length > 0 && showTopAd" class="top">
      <!-- 单张图片时直接显示 -->
      <template v-if="items.length === 1">
        <div class="single-ad" :style="{ backgroundColor: items[0].css }">
          <div class="ad-content">
            <img :src="items[0].imagepath" :alt="items[0].title" />
            <i class="iconfont icon-guanbi closeBtn" @click="closeTopAd" />
          </div>
        </div>
      </template>

      <!-- 多张图片时使用轮播 -->
      <template v-else>
        <q-carousel v-model="slide" transition-prev="slide-right" transition-next="slide-left" swipeable animated :autoplay="2000" infinite height="auto" class="top-carousel">
          <q-carousel-slide v-for="(item, index) in items" :key="index" :name="index + 1" class="carousel-slide" :style="{ backgroundColor: item.css }" @click="handleAdClick(item)">
            <div class="ad-content">
              <img :src="item.imagepath" :alt="item.title" />
              <i class="iconfont icon-guanbi closeBtn" @click.stop="closeTopAd" />
            </div>
          </q-carousel-slide>
        </q-carousel>
      </template>
    </div>
  </transition>
</template>

<script setup>
import { useBannerStore } from '~/store/banner';
import { BannerTag } from '~/utils/constants';

const bannerStore = useBannerStore();
const items = bannerStore.getBannersByTag(BannerTag.TOPAD);
const showTopAd = ref(false);
const slide = ref(1);

// 广告关闭状态的localStorage键名
const AD_CLOSE_KEY = 'topAdClosed';
const AD_CLOSE_DURATION = 24 * 60 * 60 * 1000; // 24小时（毫秒）

// 检查广告是否应该显示
const checkAdVisibility = () => {
  if (typeof window !== 'undefined') {
    const closedData = localStorage.getItem(AD_CLOSE_KEY);
    if (closedData) {
      try {
        const { timestamp } = JSON.parse(closedData);
        const now = Date.now();

        // 如果关闭时间未超过24小时，则不显示广告
        if (now - timestamp < AD_CLOSE_DURATION) {
          showTopAd.value = false;
          return;
        } else {
          // 超过24小时，清除过期的关闭状态
          localStorage.removeItem(AD_CLOSE_KEY);
        }
      } catch (error) {
        // 数据格式错误，清除并重新显示广告
        localStorage.removeItem(AD_CLOSE_KEY);
      }
    }
  }
  showTopAd.value = true;
};

const closeTopAd = () => {
  showTopAd.value = false;

  // 保存关闭状态到localStorage
  if (typeof window !== 'undefined') {
    const closeData = {
      timestamp: Date.now(),
    };
    localStorage.setItem(AD_CLOSE_KEY, JSON.stringify(closeData));
  }
};

const handleAdClick = (item) => {
  if (item.link && item.link !== '/') {
    window.open(item.link, '_blank');
  }
};

// 组件挂载时检查广告显示状态
onMounted(() => {
  checkAdVisibility();
});
</script>

<style lang="scss" scoped>
.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.5s ease;
}

.slide-up-enter,
.slide-up-leave-to {
  transform: translateY(0);
  opacity: 1;
}

.slide-up-leave-active {
  opacity: 0;
  transform: translateY(-100%);
}

.top {
  width: 100%;
  position: relative;

  .single-ad {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .ad-content {
      position: relative;
      width: 100%;
      max-width: 1200px;
      display: flex;
      justify-content: center;
      align-items: center;

      img {
        width: 100%;
        height: auto;
        max-height: 100px;
        object-fit: cover;
      }
    }
  }

  .top-carousel {
    width: 100%;
    cursor: pointer;
    .carousel-slide {
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 0;

      .ad-content {
        position: relative;
        width: 100%;
        max-width: 1200px;
        display: flex;
        justify-content: center;
        align-items: center;

        img {
          width: 100%;
          height: auto;
          max-height: 100px;
          object-fit: cover;
          cursor: pointer;
          transition: opacity 0.3s ease;

          &:hover {
            opacity: 0.9;
          }
        }
      }
    }
  }

  .closeBtn {
    position: absolute;
    top: 10px;
    right: 10px;
    cursor: pointer;
    color: #fff;
    padding: 4px;
    font-size: 16px;
    z-index: 10;
  }
}

@media (max-width: 599px) {
  .top {
    .single-ad .ad-content img,
    .top-carousel .carousel-slide .ad-content img {
      max-height: 60px;
    }

    .closeBtn {
      top: 5px;
      right: 5px;
      font-size: 14px;
      padding: 3px;
    }
  }
}

@media (min-width: 600px) and (max-width: 1023px) {
  .top {
    .single-ad .ad-content img,
    .top-carousel .carousel-slide .ad-content img {
      max-height: 80px;
    }
  }
}
</style>
