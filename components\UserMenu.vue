<template>
  <q-list bordered padding class="menu-container rounded-borders">
    <!-- 一级菜单 -->
    <template v-for="(item, index) in items" :key="item.label">
      <!-- 有子菜单的一级菜单 -->
      <q-expansion-item
        v-if="item.children"
        :label="$t(item.label)"
        :icon="item.icon"
        :expand-separator="true"
        :default-opened="item.opened || false"
        :header-class="isActiveParent(item) ? 'bg-grey-2' : ''"
        expand-icon-class="text-grey-7">
        <template v-slot:header>
          <q-item-section avatar>
            <q-icon :name="item.icon" :color="item.iconColor || 'grey'" />
          </q-item-section>
          <q-item-section>
            <q-item-label>{{ $t(item.label) }}</q-item-label>
          </q-item-section>
        </template>

        <!-- 渲染二级菜单 -->
        <q-item
          v-for="child in item.children"
          :key="child.label"
          clickable
          v-ripple
          :active="isActive(child.path)"
          :active-class="'bg-' + (child.iconColor || 'primary') + '-1 text-' + (child.iconColor || 'primary')"
          @click="navigate(child.path)"
          class="q-py-sm custom-second-left">
          <q-item-section avatar v-if="child.icon">
            <q-icon :name="child.icon" :color="child.iconColor || 'grey'" size="xs" />
          </q-item-section>
          <q-item-section>{{ $t(child.label) }}</q-item-section>
        </q-item>
      </q-expansion-item>

      <!-- 无子菜单的一级菜单 -->
      <q-item
        v-else
        clickable
        v-ripple
        :active="isActive(item.path)"
        :active-class="'bg-' + (item.iconColor || 'primary') + '-1 text-' + (item.iconColor || 'primary')"
        @click="navigate(item.path)"
        class="q-py-sm">
        <q-item-section avatar>
          <q-icon :name="item.icon" :color="item.iconColor || 'grey'" />
        </q-item-section>
        <q-item-section>{{ $t(item.label) }}</q-item-section>
      </q-item>
    </template>
  </q-list>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useRoute } from 'vue-router';

const props = defineProps({
  items: { type: Array, required: true },
});

const emit = defineEmits(['navigate']);
const route = useRoute();

// 判断当前激活的菜单项
const isActive = (path) => {
  return route.path === path;
};

// 判断父菜单是否有激活的子菜单
const isActiveParent = (item) => {
  if (!item.children) return false;
  return item.children.some((child) => route.path === child.path);
};

// 导航事件
const navigate = (path) => {
  emit('navigate', path);
};
</script>

<style scoped lang="scss">
.menu-container {
  position: sticky; /* 置顶菜单 */
  top: 0; /* 距离顶部 0px */
  background-color: white;
  z-index: 100;
  height: 100%; /* 占满左侧高度 */
  overflow-y: auto; /* 如果内容过多，滚动显示 */
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
  transition: box-shadow 0.3s;

  &:hover {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  }
}

.q-item {
  border-radius: 4px;
  margin: 2px 0;
  transition: all 0.2s ease;

  &:hover {
    background-color: rgba(0, 0, 0, 0.03);
  }

  &.q-item--active {
    font-weight: 500;
  }
}

.q-expansion-item {
  &__content {
    padding: 4px 0;
    background-color: rgba(0, 0, 0, 0.01);
  }
}

.custom-second-left {
  padding-left: 48px; /* 二级菜单缩进，体现层级关系 */
  min-height: 36px;
}

:deep(.q-item__section--avatar) {
  min-width: 28px !important;
  padding-right: 8px;
}

// 移动端样式优化
@media (max-width: 599px) {
  .menu-container {
    box-shadow: none;
  }

  .q-item {
    min-height: 44px; // 移动端触摸区域更大
  }

  .custom-second-left {
    padding-left: 40px;
  }
}
</style>
