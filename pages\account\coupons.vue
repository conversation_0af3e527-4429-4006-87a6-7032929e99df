<template>
  <div>
    <!-- 标题区域 -->
    <div class="bg-grey-2 q-px-md q-py-sm">
      <div class="row items-center">
        <q-icon name="local_offer" size="xs" color="deep-orange" class="q-mr-xs" />
        <span class="text-subtitle1 text-weight-medium">{{ $t('accountCoupons.title') }}</span>
      </div>
    </div>

    <!-- Tab页和优惠券列表 -->
    <div class="q-pa-md">
      <div class="row justify-between items-center q-mb-md flex-wrap">
        <div class="col-12 col-sm-auto">
          <q-tabs v-model="activeTab" class="text-primary" active-color="primary" indicator-color="primary" align="left" narrow-indicator @update:model-value="onTabChange" :dense="$q.screen.lt.sm">
            <q-tab name="available" :label="$t('accountCoupons.tabs.available')" />
            <q-tab name="used" :label="$t('accountCoupons.tabs.used')" />
            <q-tab name="expired" :label="$t('accountCoupons.tabs.expired')" />
          </q-tabs>
        </div>

        <div class="col-12 col-sm-auto q-mt-sm-none q-mt-md">
          <div class="row justify-end">
            <q-btn v-if="activeTab === 'available'" outline color="primary" :label="$t('accountCoupons.getMore')" icon="add" to="/promotions/coupons" />
          </div>
        </div>
      </div>

      <q-separator />

      <!-- 优惠券卡片列表 -->
      <div class="q-mt-md">
        <!-- 加载中 -->
        <div v-if="state.loadStatus" class="column items-center q-py-lg">
          <q-spinner color="primary" size="3em" />
          <div class="q-mt-sm text-grey-7">{{ $t('accountCoupons.loading') }}</div>
        </div>

        <!-- 无数据 -->
        <div v-else-if="state.pagination.list?.length === 0" class="column items-center q-py-lg">
          <q-icon name="local_offer" size="3em" color="grey-5" />
          <div class="text-grey-7 q-mt-sm">{{ $t('accountCoupons.noCoupons', { status: currentTabText }) }}</div>
          <q-btn v-if="activeTab === 'available'" color="primary" :label="$t('accountCoupons.getCoupons')" class="q-mt-sm" to="/promotions/coupons" />
        </div>

        <!-- 优惠券卡片 -->
        <div v-else class="row q-col-gutter-sm q-col-gutter-md-md">
          <div v-for="coupon in state.pagination.list" :key="coupon.id" class="col-12 col-sm-6 col-md-4 q-mb-sm q-mb-md-md">
            <CouponCard :coupon="coupon" :status="activeTab" :compact="$q.screen.lt.sm" />
          </div>
        </div>

        <!-- 分页控件和记录信息 -->
        <div class="row justify-between items-center q-mt-md flex-wrap" v-if="state.pagination.list?.length > 0">
          <div class="col-12 col-sm-auto q-mb-sm q-mb-sm-none">
            <div class="row justify-end justify-sm-start">
              <div class="text-caption text-grey-8">
                {{
                  $t('accountCoupons.pagination', {
                    total: state.pagination.total,
                    current: state.pagination.pageNo,
                    totalPages: Math.ceil(state.pagination.total / state.pagination.pageSize) || 1,
                  })
                }}
              </div>
            </div>
          </div>
          <div class="col-12 col-sm-auto">
            <div class="row justify-end">
              <q-pagination
                v-model="state.pagination.pageNo"
                :max="Math.ceil(state.pagination.total / state.pagination.pageSize)"
                :max-pages="$q.screen.lt.sm ? 3 : 6"
                boundary-links
                direction-links
                :dense="$q.screen.lt.sm"
                @update:model-value="onPageChange" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import CouponCard from '~/components/account/CouponCard.vue';
import CouponApi from '~/composables/couponApi';
import { useQuasar } from 'quasar';
import { useI18n } from 'vue-i18n';

definePageMeta({
  middleware: 'auth', // 引入身份验证中间件
});

const $q = useQuasar();
const { t } = useI18n();

// 当前激活的Tab
const activeTab = ref('available');

// 数据状态
const state = reactive({
  pagination: {
    list: [],
    total: 0,
    pageNo: 1,
    pageSize: 6, // 每页显示9个优惠券，适合3列布局
  },
  loadStatus: false,
});

// 计算属性：当前Tab的显示文本
const currentTabText = computed(() => {
  switch (activeTab.value) {
    case 'available':
      return t('accountCoupons.tabs.available');
    case 'used':
      return t('accountCoupons.tabs.used');
    case 'expired':
      return t('accountCoupons.tabs.expired');
    default:
      return t('accountCoupons.tabs.default');
  }
});

// 页面加载时获取数据
onMounted(() => {
  fetchCoupons();
});

// 获取优惠券列表
async function fetchCoupons() {
  state.loadStatus = true;

  try {
    // 根据Tab页确定优惠券状态
    let status = '1'; // 未使用
    if (activeTab.value === 'used') {
      status = '2'; // 已使用
    } else if (activeTab.value === 'expired') {
      status = '3'; // 已过期
    }

    const { code, data } = await CouponApi.getCouponPage({
      pageNo: state.pagination.pageNo,
      pageSize: state.pagination.pageSize,
      status,
    });

    if (code === 0) {
      state.pagination.list = data.list || [];
      state.pagination.total = data.total || 0;
    } else {
      $q.notify({
        type: 'negative',
        message: t('accountCoupons.errors.fetchFailed'),
      });
      state.pagination.list = [];
      state.pagination.total = 0;
    }
  } catch (error) {
    console.error(t('accountCoupons.errors.fetchError') + ':', error);
    $q.notify({
      type: 'negative',
      message: t('accountCoupons.errors.fetchError'),
    });
    state.pagination.list = [];
    state.pagination.total = 0;
  } finally {
    state.loadStatus = false;
  }
}

// Tab切换事件
function onTabChange() {
  state.pagination.pageNo = 1;
  fetchCoupons();
}

// 分页变更事件
function onPageChange() {
  fetchCoupons();
}
</script>

<style lang="scss" scoped>
.q-tab {
  font-weight: 500;
  padding: 0 16px;
  min-height: 36px;

  @media (max-width: 599px) {
    padding: 0 12px;
    min-height: 32px;
    font-size: 14px;
  }
}

.q-tabs {
  min-height: 36px;

  @media (max-width: 599px) {
    min-height: 32px;
  }
}

.q-pagination {
  .q-btn {
    font-weight: 500;
    padding: 0 8px;
    min-height: 32px;

    @media (max-width: 599px) {
      padding: 0 6px;
      min-height: 28px;
    }
  }
}

.justify-sm-start {
  @media (min-width: 600px) {
    justify-content: flex-start;
  }
}

.q-mb-sm-none {
  @media (min-width: 600px) {
    margin-bottom: 0;
  }
}

.q-mb-md-md {
  @media (min-width: 600px) {
    margin-bottom: 16px;
  }
}

.q-col-gutter-md-md {
  @media (min-width: 600px) {
    margin: -8px;

    > * {
      padding: 8px;
    }
  }
}

@media (max-width: 599px) {
  .q-col-gutter-sm > .col-12 {
    padding: 4px;
  }

  .q-mb-sm {
    margin-bottom: 8px;
  }
}
</style>
