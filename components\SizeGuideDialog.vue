<template>
  <q-dialog 
    v-model="localModelValue" 
    :maximized="$q.screen.lt.sm"
  >
    <q-card style="min-width: 800px; max-width: 90vw; max-height: 90vh;">
      <q-card-section class="row items-center q-pb-none">
        <div class="text-h6">{{ $t('sizeGuide.title') }}</div>
        <q-space />
        <q-btn 
          icon="close" 
          flat 
          round 
          dense 
          @click="localModelValue = false" 
        />
      </q-card-section>
      
      <q-card-section class="q-pt-none" style="max-height: calc(90vh - 100px); overflow-y: auto;">
        <SizeGuideContent :default-category="defaultCategory" />
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script setup>
import SizeGuideContent from './SizeGuideContent.vue'
import { useI18n } from 'vue-i18n'
import { useQuasar } from 'quasar'
import { computed } from 'vue'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  defaultCategory: {
    type: String,
    default: "women's top"
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

const { t } = useI18n()
const $q = useQuasar()

// 使用 computed 来同步 modelValue
const localModelValue = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emit('update:modelValue', value)
  }
})
</script>

<style lang="scss" scoped>
:deep(.q-dialog__inner) {
  padding: 16px;
}

@media (max-width: 599px) {
  :deep(.q-card) {
    min-width: 100% !important;
    max-width: 100% !important;
    max-height: 100% !important;
    margin: 0 !important;
  }
}
</style>