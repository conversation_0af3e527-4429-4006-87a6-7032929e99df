<template>
  <div class="wishlist-page">
    <!-- 标题区域 -->
    <div class="bg-grey-2 q-px-md q-py-sm">
      <div class="row items-center">
        <q-icon name="favorite" size="xs" color="pink" class="q-mr-xs" />
        <span class="text-subtitle1 text-weight-medium">{{ $t('wishlist.title') }}</span>
      </div>
    </div>

    <!-- 商品列表区域 -->
    <div class="q-pa-md">
      <!-- 加载中 -->
      <div v-if="loading" class="column items-center q-py-xl">
        <q-spinner color="primary" size="3em" />
        <div class="q-mt-sm text-grey-7">{{ $t('wishlist.loading') }}</div>
      </div>

      <!-- 无数据 -->
      <div v-else-if="state.pagination.list.length === 0" class="column items-center q-py-xl">
        <q-icon name="favorite_border" size="4em" color="grey-5" />
        <div class="text-grey-7 q-mt-sm">{{ $t('wishlist.noProducts') }}</div>
        <q-btn color="primary" :label="$t('wishlist.browseProducts')" class="q-mt-md" to="/products" />
      </div>

      <!-- 商品卡片列表 -->
      <div v-else>
        <div class="row q-col-gutter-md">
          <div v-for="product in state.pagination.list" :key="product.id" class="col-6 col-sm-6 col-md-4 col-lg-3 q-mb-md">
            <ProductCard :product="product" @remove="confirmRemove" />
          </div>
        </div>

        <!-- 分页控件 -->
        <div class="row justify-between items-center q-mt-lg flex-wrap">
          <div class="col-12 col-sm-auto q-mb-sm-none q-mb-sm">
            <div class="row justify-end justify-sm-start">
              <div class="text-caption text-grey-8">
                {{
                  $t('wishlist.pagination', {
                    total: total,
                    current: state.pagination.pageNo,
                    pages: Math.ceil(total / state.pagination.pageSize) || 1,
                  })
                }}
              </div>
            </div>
          </div>
          <div class="col-12 col-sm-auto">
            <div class="row justify-center">
              <q-pagination
                v-model="state.pagination.pageNo"
                :max="Math.ceil(state.pagination.total / state.pagination.pageSize)"
                :max-pages="$q.screen.lt.sm ? 3 : 6"
                boundary-links
                direction-links
                @update:model-value="onPageChange"
                class="pagination-control" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 删除确认对话框 -->
    <q-dialog v-model="confirmDialog" persistent>
      <q-card class="confirm-dialog">
        <q-card-section class="row items-center">
          <q-avatar icon="help_outline" color="pink-6" text-color="white" />
          <span class="q-ml-sm text-body1">{{ $t('wishlist.confirmRemove.title') }}</span>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat :label="$t('wishlist.confirmRemove.cancel')" color="grey-7" v-close-popup />
          <q-btn flat :label="$t('wishlist.confirmRemove.confirm')" color="negative" @click="removeProduct()" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useQuasar } from 'quasar';
import { useI18n } from 'vue-i18n';
import ProductCard from '~/components/account/ProductCard.vue';
import FavoriteApi from '~/composables/favoriteApi';
import { useFavoriteStore } from '../../store/favorite';
import { useNuxtApp } from '#app';

definePageMeta({
  middleware: 'auth', // 引入身份验证中间件
});
const nuxtApp = useNuxtApp();
const favoriteStore = useFavoriteStore();

const $q = useQuasar();
const { t } = useI18n();

// 商品列表
const products = ref([]);
const total = ref(0);
const loading = ref(false);



const state = reactive({
  pagination: {
    list: [],
    total: 0,
    pageNo: 1,
    pageSize: 3,
  },
  loadStatus: '',
});

// 删除确认对话框
const confirmDialog = ref(false);
const productToRemove = ref(null);

// 页面加载时获取数据
onMounted(() => {
  fetchWishlist();
});

// 获取收藏列表
async function fetchWishlist() {
  loading.value = true;
  try {

    const { code, data } = await FavoriteApi.getFavoritePage({
      pageNo: state.pagination.pageNo,
      pageSize: state.pagination.pageSize,
    });

    if (code === 0) {
      state.pagination.list = data.list || [];
      state.pagination.total = data.total || 0;
    } else {
      $q.notify({
        type: 'negative',
        message: t('wishlist.fetchFailed'),
      });
      state.pagination.list = [];
      totstate.pagination.total = 0;
    }
    loading.value = false;
  } catch (error) {
    console.error('获取收藏列表失败', error);
    $q.notify({
      color: 'negative',
      message: '获取收藏列表失败',
      icon: 'error',
    });
    loading.value = false;
  }
}

// 分页变更事件
function onPageChange() {
  fetchWishlist();
}

// 确认移除商品
function confirmRemove(productId) {
  productToRemove.value = productId;
  confirmDialog.value = true;
}

// 移除商品
async function removeProduct() {
  console.log('移除商品', productToRemove.value);
  if (!productToRemove.value) return;
  try {
    await favoriteStore.removeFromWishlist(productToRemove.value);
    nuxtApp.$showNotify({ msg: '移除成功', type: 'positive' });
    //刷新
    fetchWishlist();
  } catch (error) {
    console.error('从收藏夹移除失败:', error);
  }

  productToRemove.value = null;
}
</script>

<style lang="scss" scoped>
.wishlist-page {
  .q-pagination {
    .q-btn {
      font-weight: 500;
      padding: 0 8px;
      min-height: 32px;
    }
  }

  .confirm-dialog {
    border-radius: 8px;
    max-width: 350px;
  }

  // 响应式调整
  @media (max-width: 599px) {
    .q-col-gutter-md > .col-6 {
      padding: 4px 8px;
    }

    .pagination-control {
      .q-btn {
        padding: 0 6px;
        min-height: 28px;
        font-size: 12px;
      }
    }
  }
}
</style>
