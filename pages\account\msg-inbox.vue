<template>
  <div class="message-inbox">
    <!-- 标题区域 -->
    <div class="bg-grey-2 q-px-md q-py-sm">
      <div class="row items-center">
        <q-icon name="mail" size="xs" color="orange-8" class="q-mr-xs" />
        <span class="text-subtitle1 text-weight-medium">{{ $t('messageCenter.inbox.pageTitle') }}</span>
      </div>
    </div>

    <!-- 筛选区域 -->
    <div class="filter-section q-pa-md">
      <div class="row q-col-gutter-md items-center">
        <div class="col-12 col-sm-auto">
          <q-select
            v-model="filter.templateType"
            :options="messageTypeOptions"
            label="消息类型"
            outlined
            dense
            emit-value
            map-options
            options-dense
            class="message-filter"
            @update:model-value="onTypeChange" />
        </div>

        <div class="col-12 col-sm-auto q-ml-auto">
          <div class="row q-gutter-x-sm">
            <q-btn color="primary" outline label="全部已读" @click="markAllAsRead" :disable="!hasUnread" />
            <q-btn color="negative" outline label="批量删除" @click="confirmDelete" :disable="selected.length === 0" />
          </div>
        </div>
      </div>
    </div>

    <!-- 消息列表 -->
    <div class="message-list q-pa-md">
      <!-- PC端表格视图 -->
      <div v-if="!$q.screen.lt.sm" class="desktop-view">
        <q-table
          :rows="state.pagination.list"
          :columns="columns"
          row-key="id"
          :loading="state.loadStatus"
          selection="multiple"
          v-model:selected="selected"
          hide-pagination
          flat
          bordered
          table-class="message-table"
          dense
          wrap-cells>
          <template #body="props">
            <q-tr :props="props" :class="{ 'bg-blue-1': !props.row.readStatus }">
              <q-td auto-width>
                <q-checkbox v-model="props.selected" size="xs" />
              </q-td>
              <q-td key="type" :props="props">
                <div class="row no-wrap items-center">
                  <q-icon :name="getMessageIcon(props.row.templateType)" :color="getMessageColor(props.row.templateType)" size="sm" />
                  <span class="q-ml-sm">{{ getMessageTypeName(props.row.templateType) }}</span>
                  <q-badge v-if="!props.row.readStatus" color="red" floating>未读</q-badge>
                </div>
              </q-td>
              <q-td key="content" :props="props" class="content-cell">
                <div class="ellipsis" :class="{ 'text-weight-bold': !props.row.readStatus }">
                  {{ truncateText(props.row.templateContent, 50) }}
                </div>
              </q-td>
              <q-td key="createTime" :props="props">
                {{ formatDateTime(props.row.createTime) }}
              </q-td>
              <q-td key="actions" :props="props" auto-width>
                <div class="row q-gutter-xs">
                  <q-btn flat dense size="md" color="primary" label="查看" @click="openMessage(props.row)" />
                  <q-btn flat dense size="sm" color="negative" icon="delete" @click="confirmDeleteSingle(props.row)" />
                </div>
              </q-td>
            </q-tr>
          </template>

          <template #no-data>
            <div class="full-width row flex-center q-py-lg">
              <q-icon name="mail" size="2em" color="grey-5" />
              <div class="q-ml-sm text-grey-7">暂无消息</div>
            </div>
          </template>
        </q-table>

        <!-- 分页 -->
        <div v-if="state.pagination.list.length > 0" class="pagination-container q-mt-lg">
          <q-pagination
            v-model="state.pagination.pageNo"
            :max="Math.ceil(state.pagination.total / state.pagination.pageSize)"
            :max-pages="6"
            boundary-links
            direction-links
            @update:model-value="onPageChange" />
        </div>
      </div>

      <!-- 移动端卡片视图 -->
      <div v-else class="mobile-view">
        <div v-if="state.pagination.list.length === 0" class="text-center q-py-xl">
          <q-icon name="mail" size="3em" color="grey-5" />
          <div class="q-mt-sm text-grey-7">暂无消息</div>
        </div>

        <q-list separator>
          <q-item v-for="message in state.pagination.list" :key="message.id" clickable v-ripple :class="{ 'bg-blue-1': !message.readStatus }">
            <q-item-section avatar>
              <q-avatar :color="getMessageColor(message.templateType)" text-color="white">
                <q-icon :name="getMessageIcon(message.templateType)" />
              </q-avatar>
            </q-item-section>

            <q-item-section>
              <div class="row no-wrap items-center justify-between q-mb-xs type-status-row">
                <div class="text-caption text-grey-7 type-label">{{ getMessageTypeName(message.templateType) }}</div>
                <div class="text-caption status-label">
                  <q-badge v-if="!message.readStatus" color="red" label="未读" />
                  <span v-else class="text-grey-7">已读</span>
                </div>
              </div>
              <q-item-label caption :class="{ 'text-weight-bold': !message.readStatus }">
                {{ truncateText(message.templateContent, 40) }}
              </q-item-label>
              <div class="row no-wrap items-center justify-between q-mt-xs">
                <div class="text-caption text-grey-7">{{ formatDate(message.createTime) }}</div>
                <div class="row q-gutter-xs">
                  <q-btn flat dense size="sm" color="primary" label="查看" @click="openMessage(message)" />
                  <q-btn flat dense size="sm" color="negative" icon="delete" @click.stop="confirmDeleteSingle(message)" />
                </div>
              </div>
            </q-item-section>
          </q-item>
        </q-list>

        <!-- 移动端分页 -->
        <div v-if="state.pagination.list.length > 0" class="row justify-end q-py-md q-px-sm">
          <div class="col-auto q-mr-md text-caption text-grey-7">
            共 {{ state.pagination.total }} 条，第 {{ state.pagination.pageNo }} 页
          </div>
          <div class="col-auto">
            <q-pagination
              v-model="state.pagination.pageNo"
              :max="Math.ceil(state.pagination.total / state.pagination.pageSize)"
              :max-pages="5"
              :boundary-links="false"
              :direction-links="true"
              @update:model-value="onPageChange" />
          </div>
        </div>
      </div>
    </div>

    <!-- 消息详情弹窗 -->
    <q-dialog v-model="messageDialog" persistent>
      <q-card style="min-width: 400px; max-width: 600px; width: 90vw">
        <q-card-section class="row items-center bg-primary text-white">
          <q-icon :name="getMessageIcon(currentMessage.templateType)" size="md" class="q-mr-sm" />
          <div class="text-h6">消息详情</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup color="white" />
        </q-card-section>

        <q-separator />

        <q-card-section class="q-pa-lg">
          <div class="row items-center q-mb-md">
            <q-badge :color="getMessageColor(currentMessage.templateType)" :label="getMessageTypeName(currentMessage.templateType)" />
            <q-space />
            <span class="text-caption text-grey-7">{{ formatDateTime(currentMessage.createTime) }}</span>
          </div>

          <div class="message-content">
            <div class="text-body1" style="line-height: 1.6; white-space: pre-wrap;">
              {{ currentMessage.templateContent }}
            </div>
          </div>
        </q-card-section>

        <q-card-actions align="right" class="q-pa-md">
          <q-btn flat label="关闭" color="primary" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- 删除确认弹窗 -->
    <q-dialog v-model="deleteDialog" persistent>
      <q-card>
        <q-card-section class="row items-center">
          <q-avatar icon="warning" color="orange" text-color="white" />
          <span class="q-ml-sm">{{ deleteMultiple ? '确定要删除选中的消息吗？' : '确定要删除这条消息吗？' }}</span>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="取消" color="primary" v-close-popup />
          <q-btn flat label="确定" color="negative" @click="deleteMessages" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { useQuasar, date } from 'quasar';
import NotifyApi from '~/composables/notifyApi';

const $q = useQuasar();

// 状态管理
const state = reactive({
  pagination: {
    list: [],
    total: 0,
    pageNo: 1,
    pageSize: 10,
  },
  loadStatus: false,
});

// 筛选条件
const filter = reactive({
  templateType: 'all',
});

// 消息类型选项
const messageTypeOptions = [
  { label: '全部消息', value: 'all' },
  { label: '通知消息', value: 1 },
  { label: '系统消息', value: 2 },
];

// 表格列定义
const columns = [
  { name: 'type', align: 'left', label: '类型', field: 'templateType', sortable: true, style: 'width: 120px; white-space: nowrap' },
  { name: 'content', align: 'left', label: '内容', field: 'templateContent', style: 'max-width: 300px' },
  { name: 'createTime', align: 'center', label: '时间', field: 'createTime', sortable: true, format: (val) => formatDateTime(val), style: 'width: 150px' },
  { name: 'actions', align: 'center', label: '操作', style: 'width: 120px' },
];

// 其他状态
const selected = ref([]);

// 消息详情弹窗
const messageDialog = ref(false);
const currentMessage = ref({});

// 删除确认弹窗
const deleteDialog = ref(false);
const deleteMultiple = ref(false);
const messageToDelete = ref(null);

// 计算是否有未读消息
const hasUnread = computed(() => {
  return state.pagination.list.some((msg) => !msg.readStatus);
});

// 加载消息列表
const loadMessages = async () => {
  try {
    state.loadStatus = true;
    const params = {
      pageNo: state.pagination.pageNo,
      pageSize: state.pagination.pageSize,
    };

    // 如果不是全部消息，添加类型筛选
    if (filter.templateType !== 'all') {
      params.templateType = filter.templateType;
    }

    const { code, data } = await NotifyApi.getPage(params);
    if (code === 0 && data) {
      state.pagination.list = data.list || [];
      state.pagination.total = data.total || 0;
    }
  } catch (error) {
    console.error('加载消息失败', error);
    $q.notify({
      color: 'negative',
      message: '加载消息失败',
      icon: 'error',
    });
  } finally {
    state.loadStatus = false;
  }
};

// 分页变化处理
const onPageChange = (newPage) => {
  state.pagination.pageNo = newPage;
  loadMessages();
};

// 类型筛选变化处理
const onTypeChange = () => {
  state.pagination.pageNo = 1; // 重置到第一页
  loadMessages();
};

// 打开消息详情
const openMessage = (message) => {
  currentMessage.value = { ...message };
  messageDialog.value = true;

  // 如果消息未读，标记为已读
  if (!message.readStatus) {
    markAsRead(message.id);
  }
};

// 标记消息为已读
const markAsRead = async (messageId) => {
  try {
    await NotifyApi.updateRead({ ids: [messageId] });

    // 更新本地状态
    const index = state.pagination.list.findIndex((msg) => msg.id === messageId);
    if (index !== -1) {
      state.pagination.list[index].readStatus = true;
    }
  } catch (error) {
    console.error('标记消息已读失败', error);
  }
};

// 标记所有消息为已读
const markAllAsRead = async () => {
  try {
    await NotifyApi.updateAllRead();

    // 更新本地状态
    state.pagination.list.forEach((msg) => {
      msg.readStatus = true;
    });

    $q.notify({
      color: 'positive',
      message: '全部标记为已读成功',
      icon: 'check',
    });
  } catch (error) {
    console.error('标记所有消息已读失败', error);
    $q.notify({
      color: 'negative',
      message: '标记所有消息已读失败',
      icon: 'error',
    });
  }
};

// 确认删除单条消息
const confirmDeleteSingle = (message) => {
  messageToDelete.value = message;
  deleteMultiple.value = false;
  deleteDialog.value = true;
};

// 确认批量删除
const confirmDelete = () => {
  deleteMultiple.value = true;
  deleteDialog.value = true;
};


// 删除消息
const deleteMessages = async () => {
  try {
    let ids = [];
    if (deleteMultiple.value) {
      // 批量删除
      ids = selected.value.map((msg) => msg.id);
    } else {
      // 单条删除
      ids = [messageToDelete.value.id];
    }

    await NotifyApi.batchDelete({ ids });

    // 更新本地状态
    state.pagination.list = state.pagination.list.filter((msg) => !ids.includes(msg.id));
    selected.value = [];

    $q.notify({
      color: 'positive',
      message: '删除成功',
      icon: 'check',
    });

    // 重新加载数据
    loadMessages();
  } catch (error) {
    console.error('删除消息失败', error);
    $q.notify({
      color: 'negative',
      message: '删除失败',
      icon: 'error',
    });
  }
};

// 获取消息类型图标
const getMessageIcon = (templateType) => {
  const icons = {
    1: 'notifications', // 通知消息
    2: 'info', // 系统消息
  };
  return icons[templateType] || 'mail';
};

// 获取消息类型颜色
const getMessageColor = (templateType) => {
  const colors = {
    1: 'green', // 通知消息
    2: 'blue', // 系统消息
  };
  return colors[templateType] || 'grey';
};

// 获取消息类型名称
const getMessageTypeName = (templateType) => {
  const names = {
    1: '通知消息',
    2: '系统消息',
  };
  return names[templateType] || '未知类型';
};

// 文本截断
const truncateText = (text, length) => {
  if (!text) return '';
  return text.length > length ? text.substring(0, length) + '...' : text;
};

// 格式化日期时间
const formatDateTime = (timestamp) => {
  if (!timestamp) return '';
  return date.formatDate(new Date(timestamp), 'YYYY-MM-DD HH:mm');
};

// 格式化日期
const formatDate = (timestamp) => {
  if (!timestamp) return '';
  return date.formatDate(new Date(timestamp), 'YYYY-MM-DD');
};


// 页面加载时获取消息列表
onMounted(() => {
  loadMessages();
});
</script>

<style lang="scss" scoped>
.message-inbox {
  .filter-section {
    background-color: #fff;
    border-radius: 4px;
    margin-bottom: 16px;
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
  }

  .message-filter {
    min-width: 150px;
  }

  .message-search {
    min-width: 200px;
  }

  .message-list {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);

    // 表格样式优化
    .q-table {
      table {
        table-layout: fixed; // 固定表格布局，防止内容挤压列宽
      }

      th,
      td {
        padding: 8px 12px; // 减小单元格内边距
      }
    }

    .message-table {
      width: 100%;
    }

    // 确保类型和状态在移动端显示在同一行
    .type-status-row {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      width: 100%;

      .type-label,
      .status-label {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .type-label {
        margin-right: 8px;
      }
    }

    td {
      font-size: 13px;
    }

    .q-checkbox {
      font-size: 0.8em;
    }

    .content-cell {
      .ellipsis {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 100%;
      }
    }
  }

  .message-content {
    white-space: pre-line;
    line-height: 1.6;
  }

  // 移动端样式
  @media (max-width: 599px) {
    .filter-section {
      .q-select,
      .q-input {
        margin-bottom: 8px;
      }
    }

    .mobile-view {
      .q-item {
        padding: 12px;
      }

      .q-checkbox {
        font-size: 0.8em;
      }

      // 强化移动端类型和状态在同一行显示
      .type-status-row {
        display: flex !important;
        flex-direction: row !important;
        justify-content: space-between !important;

        .type-label,
        .status-label {
          display: inline-block;
          flex-shrink: 0;
        }
      }
    }
  }
}
</style>
