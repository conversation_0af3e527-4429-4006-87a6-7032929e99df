<template>
  <div class="breadcrumbs-wrapper">
    <q-breadcrumbs class="q-px-md q-my-md breadcrumbs-container" separator="/">
      <!-- 固定首页 -->
      <q-breadcrumbs-el icon="home" :label="$t('menu.home')" to="/" clickable class="text-primary" @click="goToRoute('/')" />

      <!-- 动态导航 -->
      <q-breadcrumbs-el v-for="(breadcrumb, index) in breadcrumbs" :key="index" :label="breadcrumb.label" :to="breadcrumb.to" clickable class="text-primary" @click="goToRoute(breadcrumb.to)" />
    </q-breadcrumbs>

    <!-- 移动端菜单按钮 - 只在小屏幕上显示 -->
    <q-btn v-if="showMenuButton" flat round dense icon="menu" color="primary" class="mobile-menu-btn lt-md" @click="toggleMenu" />
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';

// 接收动态导航的参数
const props = defineProps({
  breadcrumbs: {
    type: Array,
    default: () => [],
  },
  showMenuButton: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['toggle-menu']);

const router = useRouter();

// 跳转到指定路由
const goToRoute = (to) => {
  if (to) {
    router.push(to);
  }
};

// 切换菜单显示状态
const toggleMenu = () => {
  emit('toggle-menu');
};
</script>

<style lang="scss" scoped>
.breadcrumbs-wrapper {
  max-width: 1200px;
  width: 100%;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;

  @media (max-width: 599px) {
    padding: 0 10px;
  }
}

.breadcrumbs-container {
  flex: 1;

  @media (max-width: 767px) {
    font-size: 14px;
  }
}

.mobile-menu-btn {
  margin-right: 10px;
  z-index: 5;

  @media (max-width: 599px) {
    margin-right: 5px;
  }
}
</style>
