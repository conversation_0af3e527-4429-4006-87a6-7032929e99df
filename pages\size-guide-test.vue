<template>
  <div class="size-guide-test q-pa-md">
    <h2>尺码助手功能测试</h2>
    
    <!-- 弹窗测试 -->
    <q-card class="q-mb-lg">
      <q-card-section class="bg-primary text-white">
        <div class="text-h6">弹窗测试</div>
      </q-card-section>
      <q-card-section>
        <div class="q-gutter-md">
          <q-btn color="primary" @click="showDialog1 = true">
            女士上衣尺码助手
          </q-btn>
          <q-btn color="secondary" @click="showDialog2 = true">
            男士鞋子尺码助手
          </q-btn>
          <q-btn color="accent" @click="showDialog3 = true">
            戒指尺码助手
          </q-btn>
        </div>
      </q-card-section>
    </q-card>

    <!-- 内容组件测试 -->
    <q-card class="q-mb-lg">
      <q-card-section class="bg-grey-2">
        <div class="text-h6">内容组件测试 - 女士裤子</div>
      </q-card-section>
      <q-card-section>
        <SizeGuideContent default-category="women's pants" />
      </q-card-section>
    </q-card>

    <!-- 页面链接测试 -->
    <q-card>
      <q-card-section class="bg-grey-2">
        <div class="text-h6">页面链接测试</div>
      </q-card-section>
      <q-card-section>
        <q-btn 
          color="orange" 
          :to="'/help/size-guide'"
          target="_blank"
          icon="open_in_new"
        >
          打开帮助中心尺码助手页面
        </q-btn>
      </q-card-section>
    </q-card>

    <!-- 弹窗组件 -->
    <SizeGuideDialog 
      v-model="showDialog1" 
      default-category="women's top"
    />
    
    <SizeGuideDialog 
      v-model="showDialog2" 
      default-category="men's shoes"
    />
    
    <SizeGuideDialog 
      v-model="showDialog3" 
      default-category="ring"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import SizeGuideDialog from '~/components/SizeGuideDialog.vue'
import SizeGuideContent from '~/components/SizeGuideContent.vue'

const showDialog1 = ref(false)
const showDialog2 = ref(false)
const showDialog3 = ref(false)

// 页面元数据
useHead({
  title: '尺码助手功能测试'
})
</script>

<style lang="scss" scoped>
.size-guide-test {
  max-width: 1200px;
  margin: 0 auto;
}
</style>
