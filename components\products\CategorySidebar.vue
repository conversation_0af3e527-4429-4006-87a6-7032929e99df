<template>
  <div class="category-sidebar-component">
    <!-- 全部分类选项 -->
    <div class="category-item" :class="{ active: !selectedCategory }" @click="selectCategory(null)">
      <q-icon name="category" size="20px" class="q-mr-sm" />
      <span>{{ $t('products.allCategories') || '全部商品' }}</span>
    </div>

    <!-- 分类列表 -->
    <template v-if="categories && categories.length > 0">
      <div v-for="category in categories" :key="category.id" class="category-item" :class="{ active: selectedCategory === category.id }" @click="selectCategory(category.id)">
        <q-icon :name="getCategoryIcon(category)" size="20px" class="q-mr-sm" />
        <span>{{ $t(`products.category.${category.name}`) }}</span>
        <span class="category-count" v-if="category.count">({{ category.count }})</span>
      </div>
    </template>

    <!-- 加载中或无分类 -->
    <template v-else>
      <div v-for="i in 5" :key="i" class="category-item-skeleton">
        <q-skeleton type="text" width="80%" />
      </div>
    </template>
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n';

// Props
const props = defineProps({
  categories: {
    type: Array,
    default: () => [],
  },
  selectedCategory: {
    type: Number,
    default: null,
  },
});

// Emits
const emit = defineEmits(['select-category']);

// 选择分类
function selectCategory(categoryId) {
  emit('select-category', categoryId);
}

// 获取分类图标
function getCategoryIcon(category) {
  // 这里可以根据分类名称或ID返回不同的图标
  // 以下是一些示例图标，您可以根据实际需求修改
  const iconMap = {
    Clothing: 'checkroom',
    Electronics: 'devices',
    Home: 'chair',
    Beauty: 'spa',
    Health: 'health_and_safety',
    Toys: 'child_care',
    Sports: 'sports_soccer',
    Pet: 'pets',
    Handicrafts: 'palette',
  };

  // 尝试匹配分类名称
  for (const key in iconMap) {
    if (category.name.includes(key)) {
      return iconMap[key];
    }
  }
  // 默认图标
  return 'sell';
}
</script>

<style lang="scss" scoped>
.category-sidebar-component {
  width: 100%;
}

.category-item {
  display: flex;
  align-items: center;
  padding: 10px 12px;
  border-radius: 6px;
  margin-bottom: 5px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #555;

  &:hover {
    background-color: #f5f5f5;
    color: #1976d2;
  }

  &.active {
    background-color: #e3f2fd;
    color: #1976d2;
    font-weight: 500;
  }

  .category-count {
    margin-left: 5px;
    font-size: 0.85rem;
    color: #999;
  }
}

.category-item-skeleton {
  padding: 10px 12px;
  margin-bottom: 5px;
}
</style>
