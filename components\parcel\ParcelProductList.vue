<template>
  <div class="product-list-section">
    <div class="text-caption text-grey-8 q-mb-md">您选择的仓库商品将被打包发送</div>

    <q-card flat bordered>
      <!-- 桌面视图表格 -->
      <div v-if="!$q.screen.lt.md" class="q-pa-none">
        <q-table :rows="products" :columns="columns" row-key="id" :pagination="{ rowsPerPage: 0 }" hide-pagination flat class="product-table">
          <template #body="props">
            <q-tr :props="props">
              <!-- 商品信息 -->
              <q-td key="product" :props="props" class="product-cell">
                <div class="row items-start q-ml-sm">
                  <div class="row items-start no-wrap" style="width: 100%">
                    <div class="product-image-container q-mr-sm">
                      <q-img :src="getThumbnailUrl(props.row.picUrl, '80x80')" alt="商品图片" spinner-color="primary" />
                    </div>
                    <div class="column no-wrap" style="max-width: 250px; overflow: hidden">
                      <div class="text-weight-medium text-dark q-mb-xs ellipsis-2-lines product-name">
                        {{ props.row.spuName }}
                      </div>
                      <div class="text-caption text-grey-8">{{ formattedProperties(props.row.properties) }}</div>
                    </div>
                  </div>
                </div>
              </q-td>

              <!-- 数量 -->
              <q-td key="count" :props="props" class="text-center">
                <div class="row items-center justify-center no-wrap">
                  <q-btn flat dense round icon="remove" size="sm" color="primary" @click.stop.prevent="updateProductCount(props.row, -1)" :disable="props.row.count <= 1" />
                  <span class="q-mx-xs" style="min-width: 20px; text-align: center">{{ props.row.count }}</span>
                  <q-btn flat dense round icon="add" size="sm" color="primary" @click.stop.prevent="updateProductCount(props.row, 1)" :disable="props.row.count >= props.row.maxCount" />
                </div>
              </q-td>

              <!-- 重量 -->
              <q-td key="weight" :props="props" class="text-center"> {{ props.row.weight }} g </q-td>

              <!-- 体积 -->
              <q-td key="volume" :props="props" class="text-center"> {{ props.row.volume }} cm³ </q-td>

              <!-- 删除按钮 -->
              <q-td key="actions" :props="props" class="text-center">
                <q-btn flat round dense color="grey-7" icon="delete" size="sm" @click.stop="removeProduct(props.row)" />
              </q-td>
            </q-tr>
          </template>
        </q-table>
      </div>

      <!-- 移动端卡片视图 -->
      <div v-else class="q-pa-md">
        <q-list separator>
          <q-item v-for="product in products" :key="product.id" class="q-py-md">
            <q-item-section avatar>
              <div class="product-image-container">
                <q-img :src="getThumbnailUrl(product.picUrl, '80x80')" spinner-color="primary" class="rounded-borders" />
              </div>
            </q-item-section>

            <q-item-section>
              <div class="row justify-between items-start">
                <div class="col">
                  <q-item-label class="ellipsis-2-lines text-weight-medium product-name">{{ product.spuName }}</q-item-label>
                  <q-item-label caption>{{ formattedProperties(product.properties) }}</q-item-label>
                </div>
                <q-btn flat round dense color="grey-7" icon="delete" size="sm" @click.stop="removeProduct(product)" class="q-ml-xs" />
              </div>

              <div class="row q-mt-sm q-gutter-x-md text-caption">
                <div class="row items-center">
                  <q-icon name="inventory" size="xs" class="q-mr-xs" />
                  <span>数量:</span>
                  <div class="row items-center q-ml-xs no-wrap">
                    <q-btn flat dense round icon="remove" size="xs" color="primary" @click.stop.prevent="updateProductCount(product, -1)" :disable="product.count <= 1" />
                    <span class="q-mx-xs text-weight-medium" style="min-width: 20px; text-align: center">{{ product.count }}</span>
                    <q-btn flat dense round icon="add" size="xs" color="primary" @click.stop.prevent="updateProductCount(product, 1)" :disable="product.count >= product.maxCount" />
                  </div>
                </div>
                <div>
                  <q-icon name="scale" size="xs" /> 重量: <span class="text-weight-medium">{{ product.weight }} g</span>
                </div>
              </div>

              <div class="row q-mt-xs q-gutter-x-md text-caption">
                <div>
                  <q-icon name="view_in_ar" size="xs" /> 体积: <span class="text-weight-medium">{{ product.volume }} cm³</span>
                </div>
                <div>
                  <q-icon name="event" size="xs" /> 入库: <span class="text-weight-medium">{{ formatDate(product.inTime) }}</span>
                </div>
              </div>
            </q-item-section>
          </q-item>
        </q-list>
      </div>

      <!-- 重量汇总 -->
      <q-separator />
      <div class="q-pa-md bg-grey-1">
        <div class="row justify-end items-center">
          <div class="text-body2">
            <span class="q-mr-md"
              >商品总数: <span class="text-weight-medium text-primary">{{ props.info.count || 0 }}</span> 件</span
            >
            <span class="q-mr-md"
              >商品总重量: <span class="text-weight-medium text-primary">{{ props.info.weight || 0 }}</span> g</span
            >
            <span
              >预估包装重量: <span class="text-weight-medium text-primary">{{ props.info.packageWeight || 0 }}</span> g</span
            >
          </div>
        </div>
      </div>
    </q-card>
  </div>
</template>

<script setup>
import { useQuasar } from 'quasar';
import { formattedProperties } from '~/utils/productUtils';

// 定义组件接收的属性
const props = defineProps({
  products: {
    type: Array,
    required: true,
    default: () => [],
  },
  info: {
    type: Object,
    required: true,
    default: () => {},
  },
});

// 定义组件向外发出的事件
const emit = defineEmits(['remove-product', 'update-count']);

const $q = useQuasar();

// 表格列定义
const columns = [
  { name: 'product', label: '商品信息', align: 'left', field: 'spuName', sortable: false },
  { name: 'count', label: '数量', align: 'center', field: 'count', sortable: false, style: 'width: 120px' },
  { name: 'weight', label: '重量 (g)', align: 'center', field: 'weight', sortable: false, style: 'width: 100px' },
  { name: 'volume', label: '体积 (m³)', align: 'center', field: 'volume', sortable: false, style: 'width: 100px' },
  { name: 'actions', label: '', align: 'center', sortable: false, style: 'width: 60px' },
];

// 删除商品
function removeProduct(product) {
  $q.dialog({
    title: '确认删除',
    message: '确定要从包裹中移除此商品吗？',
    cancel: true,
    persistent: true,
  }).onOk(() => {
    emit('remove-product', product);
  });
}

// 更新商品数量
function updateProductCount(product, change) {
  const newCount = product.count + change;

  // 确保数量不小于1且不超过最大库存数量
  if (newCount < 1 || (product.maxCount && newCount > product.maxCount)) {
    return;
  }

  // 发送更新事件
  emit('update-count', {
    product,
    newCount,
  });
}
</script>

<style lang="scss" scoped>
.product-list-section {
  .q-card {
    border-radius: 8px;
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    }
  }

  .product-table {
    :deep(.q-table__top),
    :deep(.q-table__bottom),
    :deep(thead tr:first-child th) {
      background-color: #f5f5f5;
    }

    :deep(thead tr th) {
      position: sticky;
      z-index: 1;
    }

    :deep(thead tr:first-child th) {
      top: 0;
    }
  }

  .product-cell {
    max-width: 350px;
    min-width: 200px;
  }

  .product-image-container {
    width: 70px;
    height: 70px;
    min-width: 70px;
    max-width: 70px;
    overflow: hidden;
    border-radius: 4px;

    .q-img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .product-name {
    font-size: 14px;
    line-height: 1.3;
    width: 100%;
    white-space: normal;
  }

  .ellipsis-2-lines {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    max-height: 2.6em;
    min-height: 2em;
    line-height: 1.3;
    word-break: break-word;
  }
}
</style>
