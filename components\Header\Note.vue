<template>
  <!-- 公告 -->
  <div class="head-note">
    <div class="head-content">
      <!-- 时间显示 - 在小屏幕上隐藏 -->
      <div class="current-time gt-xs">{{ $t('chinaTime') }} {{ currentTime }}</div>

      <!-- 公告 -->
      <div class="note">
        <i class="iconfont icon-gonggao guangbo" />
        <div class="note-wrapper" @click="navigateToNotice">
          <transition name="fade" mode="out-in">
            <div class="note-item" :key="currentNoteIndex">
              {{ noteMessages[currentNoteIndex]?.title }}
            </div>
          </transition>
        </div>
      </div>

      <div class="right-options">
        <!-- 币别选择 -->
        <div class="language-switcher q-mr-md" @mouseenter="currencyShowDropdown = true" @mouseleave="currencyShowDropdown = false">
          <div class="current-language">
            {{ currentCurrency.symbol }} {{ currentCurrency.currency }}
            <q-icon name="arrow_drop_down" color="secondary" size="18px" class="gt-xs" />
          </div>
          <div class="dropdown" v-show="currencyShowDropdown">
            <div class="language-option" v-for="curr in currencyList" :key="curr.currency" @click="setCurrency(curr)">
              {{ curr.symbol + ' ' + curr.currency }}
            </div>
          </div>
        </div>

        <!-- 语言选择 -->
        <div class="language-switcher" @mouseenter="showDropdown = true" @mouseleave="showDropdown = false">
          <div class="current-language">
            <i class="iconfont icon-yuyan yuyan"></i>
            <span class="gt-xs">{{ currentLanguage }}</span>
            <q-icon name="arrow_drop_down" color="secondary" size="18px" class="gt-xs" />
          </div>
          <div class="dropdown" v-show="showDropdown">
            <div class="language-option" v-for="loc in lang" :key="loc.code" @click="setlang(loc.code)">
              {{ loc.name }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n';
import { useCurrencyStore } from '~/store/currency';
import { useNoticeStore } from '~/store/notice';
import { useResponsive } from '~/composables/useResponsive';

const { locale } = useI18n(); // 获取i18n中的locale
const currencyStore = useCurrencyStore();
const noticeStore = useNoticeStore();
const { isMobile } = useResponsive();

const showDropdown = ref(false); // 控制下拉菜单显示
const currencyShowDropdown = ref(false);

const lang = ref([
  { code: 'en', name: 'English' },
  { code: 'fr', name: 'Français' },
  { code: 'de', name: 'Deutsch' },
  { code: 'es', name: 'Español' },
  { code: 'ar', name: 'العربية' },
  { code: 'zh', name: '简体中文' },
]);

const currencyList = computed(() => {
  return currencyStore.getCurrencyList;
});

// 获取当前语言显示名称
const currentLanguage = computed(() => {
  const langCode = locale.value.split('-')[0]; // 截取语言代码的前两位
  return lang.value.find((item) => item.code === langCode)?.name || 'English';
});

const currentCurrency = computed(() => {
  return currencyStore.getSelectedCurrency;
});

const currentTime = ref('');
// 定义更新时间函数，包含日期和时间（12 小时制）
const updateTime = () => {
  const now = new Date();
  const date = now.toLocaleDateString('en-CA'); // 格式化为 YYYY/MM/DD
  const time = now.toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: true,
  });
  currentTime.value = `${date} ${time}`;
};

// 公告内容数组
const noteMessages = computed(() => {
  return noticeStore.getList;
});

const currentNoteIndex = ref(0); // 当前显示的公告索引
const intervalNoteId = ref(null); // 保存定时器ID
const startCarousel = () => {
  intervalNoteId.value = setInterval(() => {
    currentNoteIndex.value = (currentNoteIndex.value + 1) % noteMessages.value.length;
  }, 5000);
};

// 设置定时器，定期更新
let intervalTimeId;
onMounted(() => {
  updateTime(); // 初次加载时更新一次
  intervalTimeId = setInterval(updateTime, 1000); // 每秒更新一次
  startCarousel();

  const savedLang = localStorage.getItem('language') || getCookie('language');
  if (savedLang) {
    locale.value = savedLang;
  }
});

// 组件卸载时清除定时器
onUnmounted(() => {
  clearInterval(intervalTimeId);
  clearInterval(intervalNoteId.value);
});

function setlang(name) {
  locale.value = name;
  // Save language to localStorage and cookie
  localStorage.setItem('language', name);
  setCookie('language', name, 365);
  showDropdown.value = false;
}

function setCurrency(currency) {
  currencyStore.setCurrentCurrency(currency);
  // Save currency to localStorage and cookie as JSON string
  localStorage.setItem('currency', JSON.stringify(currency));
  setCookie('currency', JSON.stringify(currency), 365);
  currencyShowDropdown.value = false;
}

// Utility function to set a cookie
function setCookie(name, value, days) {
  const date = new Date();
  date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000);
  document.cookie = `${name}=${value};expires=${date.toUTCString()};path=/`;
}

// Utility function to get a cookie value
function getCookie(name) {
  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) return parts.pop().split(';').shift();
  return null;
}

// 导航到公告页面
function navigateToNotice() {
  navigateTo('/news');
}
</script>

<style lang="scss" scoped>
.head-note {
  width: 100%;
  height: 30px;
  background-color: #f2f2f2;

  @media (max-width: 599px) {
    height: 26px;
  }

  .head-content {
    max-width: 1200px;
    width: 100%;
    margin: 0 auto;
    line-height: 30px;
    display: flex;
    justify-content: space-between;
    padding: 0 15px;

    @media (max-width: 599px) {
      line-height: 26px;
      padding: 0 10px;
    }

    .current-time {
      font-size: 13px;
      white-space: nowrap;

      @media (max-width: 767px) {
        font-size: 12px;
      }
    }

    .note {
      flex: 1;
      display: flex;
      align-items: center;
      position: relative;
      margin: 0 15px;

      @media (max-width: 767px) {
        margin: 0 5px;
      }

      .guangbo {
        font-size: 14px;
        color: #666;

        @media (max-width: 599px) {
          font-size: 12px;
        }
      }

      .note-wrapper {
        height: 30px;
        margin-left: 10px;
        overflow: hidden;
        cursor: pointer;
        flex: 1;

        @media (max-width: 599px) {
          height: 26px;
          margin-left: 5px;
        }
      }

      .note-item {
        font-size: 13px;
        color: #333;
        position: absolute;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 100%;

        @media (max-width: 599px) {
          font-size: 12px;
        }
      }

      /* 淡入淡出动画 */
      .fade-enter-active,
      .fade-leave-active {
        transition: opacity 0.5s;
      }

      .fade-enter,
      .fade-leave-to {
        opacity: 0;
      }
    }

    .right-options {
      display: flex;
      align-items: center;
    }

    .language-switcher {
      position: relative;
      cursor: pointer;
      color: #333;
      margin-left: 10px;

      .current-language {
        display: flex;
        align-items: center;
        font-size: 13px;
        color: #333;

        @media (max-width: 599px) {
          font-size: 12px;
        }
      }

      .yuyan {
        margin-right: 5px;
        color: #8a8a8a;
      }

      .dropdown {
        position: absolute;
        top: 100%;
        right: 0;
        background-color: #fff;
        border: 1px solid #ddd;
        border-radius: 4px;
        box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.15);
        min-width: 100px;
        z-index: 10;
      }

      .language-option {
        padding: 3px 12px;
        font-size: 13px;
        color: #333;
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background-color: #f0f0f0;
        }

        &.active {
          color: #d9534f;
        }
      }
    }
  }
}
</style>
