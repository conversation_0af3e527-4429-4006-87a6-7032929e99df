<template>
  <Header />
  <div class="business-page">
    <div class="business-container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1 class="page-title">商务合作</h1>
        <div class="page-subtitle">携手共创，共赢未来</div>
      </div>
      
      <!-- 合作理念 -->
      <div class="section philosophy-section">
        <div class="row q-col-gutter-lg">
          <div class="col-12 col-md-6">
            <h2 class="section-title">合作理念</h2>
            <p class="section-text">
              我们相信，开放合作是企业发展的核心驱动力。通过与各行业优秀伙伴的深度合作，我们不仅能够为用户提供更加丰富、优质的产品和服务，还能共同探索跨境电商的无限可能。
            </p>
            <p class="section-text">
              我们秉持"互利共赢、长期发展、诚信透明"的合作理念，致力于与合作伙伴建立稳定、可持续的战略合作关系，共同为全球消费者创造价值。
            </p>
          </div>
          <div class="col-12 col-md-6 flex flex-center">
            <q-img
              src="https://cdn.quasar.dev/img/parallax2.jpg"
              class="philosophy-image rounded-borders"
              fit="cover"
            />
          </div>
        </div>
      </div>
      
      <!-- 合作模式 -->
      <div class="section cooperation-models q-mt-xl">
        <h2 class="section-title text-center">合作模式</h2>
        <div class="row q-col-gutter-lg q-mt-md">
          <div class="col-12 col-sm-6 col-md-3">
            <q-card class="model-card">
              <q-card-section class="text-center">
                <q-icon name="store" size="3rem" color="primary" class="q-mb-md" />
                <h3 class="model-title">品牌入驻</h3>
                <p class="model-text">
                  为全球优质品牌提供入驻服务，帮助品牌快速打开国际市场，提升品牌影响力。
                </p>
              </q-card-section>
            </q-card>
          </div>
          <div class="col-12 col-sm-6 col-md-3">
            <q-card class="model-card">
              <q-card-section class="text-center">
                <q-icon name="inventory_2" size="3rem" color="primary" class="q-mb-md" />
                <h3 class="model-title">供应链合作</h3>
                <p class="model-text">
                  与全球供应商、物流服务商合作，优化供应链管理，提高运营效率。
                </p>
              </q-card-section>
            </q-card>
          </div>
          <div class="col-12 col-sm-6 col-md-3">
            <q-card class="model-card">
              <q-card-section class="text-center">
                <q-icon name="handshake" size="3rem" color="primary" class="q-mb-md" />
                <h3 class="model-title">渠道分销</h3>
                <p class="model-text">
                  为分销商提供优质的产品资源和完善的分销支持，共同开拓市场。
                </p>
              </q-card-section>
            </q-card>
          </div>
          <div class="col-12 col-sm-6 col-md-3">
            <q-card class="model-card">
              <q-card-section class="text-center">
                <q-icon name="campaign" size="3rem" color="primary" class="q-mb-md" />
                <h3 class="model-title">营销推广</h3>
                <p class="model-text">
                  与媒体、KOL、营销机构合作，开展多样化的营销活动，提升品牌曝光度。
                </p>
              </q-card-section>
            </q-card>
          </div>
        </div>
      </div>
      
      <!-- 合作优势 -->
      <div class="section advantages q-mt-xl">
        <h2 class="section-title text-center">合作优势</h2>
        <div class="row q-col-gutter-lg q-mt-md">
          <div class="col-12 col-md-6">
            <q-card class="advantage-card">
              <q-card-section>
                <div class="row items-center no-wrap">
                  <q-icon name="trending_up" size="2.5rem" color="primary" class="q-mr-md" />
                  <div>
                    <h3 class="advantage-title">庞大的用户基础</h3>
                    <p class="advantage-text">
                      平台拥有超过100万活跃用户，覆盖全球主要市场，为合作伙伴提供广阔的客户资源。
                    </p>
                  </div>
                </div>
              </q-card-section>
            </q-card>
          </div>
          <div class="col-12 col-md-6">
            <q-card class="advantage-card">
              <q-card-section>
                <div class="row items-center no-wrap">
                  <q-icon name="public" size="2.5rem" color="primary" class="q-mr-md" />
                  <div>
                    <h3 class="advantage-title">全球化运营能力</h3>
                    <p class="advantage-text">
                      在多个国家设有运营中心，拥有丰富的跨境电商运营经验和本地化服务能力。
                    </p>
                  </div>
                </div>
              </q-card-section>
            </q-card>
          </div>
          <div class="col-12 col-md-6">
            <q-card class="advantage-card">
              <q-card-section>
                <div class="row items-center no-wrap">
                  <q-icon name="inventory" size="2.5rem" color="primary" class="q-mr-md" />
                  <div>
                    <h3 class="advantage-title">完善的仓储物流</h3>
                    <p class="advantage-text">
                      在全球主要市场建立了仓储中心，与多家国际物流公司合作，确保高效的配送服务。
                    </p>
                  </div>
                </div>
              </q-card-section>
            </q-card>
          </div>
          <div class="col-12 col-md-6">
            <q-card class="advantage-card">
              <q-card-section>
                <div class="row items-center no-wrap">
                  <q-icon name="analytics" size="2.5rem" color="primary" class="q-mr-md" />
                  <div>
                    <h3 class="advantage-title">数据驱动决策</h3>
                    <p class="advantage-text">
                      利用大数据分析和AI技术，为合作伙伴提供市场洞察和精准营销支持。
                    </p>
                  </div>
                </div>
              </q-card-section>
            </q-card>
          </div>
        </div>
      </div>
      
      <!-- 合作流程 -->
      <div class="section process q-mt-xl">
        <h2 class="section-title text-center">合作流程</h2>
        <div class="process-timeline q-mt-lg">
          <q-timeline color="primary">
            <q-timeline-entry
              title="需求沟通"
              subtitle="了解您的合作需求和期望"
              icon="question_answer"
              color="primary"
            >
              <div>
                通过电话、邮件或面谈的方式，详细了解您的业务情况、合作需求和期望，为后续合作奠定基础。
              </div>
            </q-timeline-entry>
            
            <q-timeline-entry
              title="方案制定"
              subtitle="根据需求定制合作方案"
              icon="assignment"
              color="secondary"
            >
              <div>
                我们的商务团队会根据前期沟通的需求，结合平台资源和能力，为您量身定制合作方案，包括合作模式、资源投入、收益分配等内容。
              </div>
            </q-timeline-entry>
            
            <q-timeline-entry
              title="协议签署"
              subtitle="确认合作细节并签署协议"
              icon="description"
              color="accent"
            >
              <div>
                双方确认合作方案后，我们将准备正式的合作协议，明确双方的权利和义务，为长期合作提供法律保障。
              </div>
            </q-timeline-entry>
            
            <q-timeline-entry
              title="项目实施"
              subtitle="按计划推进合作项目"
              icon="engineering"
              color="positive"
            >
              <div>
                签署协议后，我们将指派专业的项目团队，按照既定计划推进合作项目，确保项目顺利实施。
              </div>
            </q-timeline-entry>
            
            <q-timeline-entry
              title="效果评估"
              subtitle="定期评估合作效果"
              icon="assessment"
              color="info"
            >
              <div>
                我们会定期与您沟通，评估合作效果，分析数据表现，及时调整优化合作策略，确保合作目标的达成。
              </div>
            </q-timeline-entry>
            
            <q-timeline-entry
              title="深化合作"
              subtitle="探索更多合作可能"
              icon="auto_graph"
              color="deep-purple"
            >
              <div>
                基于前期合作的成果和经验，我们将与您探讨更深入、更广泛的合作可能，实现互利共赢的长期合作关系。
              </div>
            </q-timeline-entry>
          </q-timeline>
        </div>
      </div>
      
      <!-- 合作案例 -->
      <div class="section cases q-mt-xl">
        <h2 class="section-title text-center">合作案例</h2>
        <div class="row q-col-gutter-lg q-mt-md">
          <div class="col-12 col-md-4">
            <q-card class="case-card">
              <q-img
                src="https://cdn.quasar.dev/img/mountains.jpg"
                :ratio="16/9"
              />
              <q-card-section>
                <div class="text-h6">日本化妆品品牌全球化</div>
                <div class="text-subtitle2 q-mt-sm">品牌入驻合作</div>
              </q-card-section>
              <q-card-section>
                <p class="case-text">
                  帮助日本某知名化妆品品牌进入中国和东南亚市场，通过平台的用户资源和营销能力，在6个月内实现销售额增长300%，成功打开国际市场。
                </p>
              </q-card-section>
            </q-card>
          </div>
          <div class="col-12 col-md-4">
            <q-card class="case-card">
              <q-img
                src="https://cdn.quasar.dev/img/parallax1.jpg"
                :ratio="16/9"
              />
              <q-card-section>
                <div class="text-h6">跨境物流优化</div>
                <div class="text-subtitle2 q-mt-sm">供应链合作</div>
              </q-card-section>
              <q-card-section>
                <p class="case-text">
                  与国际物流巨头合作，优化跨境物流流程，降低物流成本20%，缩短配送时间30%，显著提升用户体验和平台竞争力。
                </p>
              </q-card-section>
            </q-card>
          </div>
          <div class="col-12 col-md-4">
            <q-card class="case-card">
              <q-img
                src="https://cdn.quasar.dev/img/quasar.jpg"
                :ratio="16/9"
              />
              <q-card-section>
                <div class="text-h6">KOL营销活动</div>
                <div class="text-subtitle2 q-mt-sm">营销推广合作</div>
              </q-card-section>
              <q-card-section>
                <p class="case-text">
                  与多位国际知名KOL合作，开展跨平台营销活动，覆盖用户超过1000万，带动平台新增用户10万+，活动期间销售额同比增长150%。
                </p>
              </q-card-section>
            </q-card>
          </div>
        </div>
      </div>
      
      <!-- 合作伙伴 -->
      <div class="section partners q-mt-xl">
        <h2 class="section-title text-center">合作伙伴</h2>
        <div class="partner-logos q-mt-lg">
          <div class="row q-col-gutter-lg justify-center">
            <div v-for="i in 8" :key="i" class="col-6 col-sm-3 col-md-2">
              <div class="partner-logo-container">
                <q-img
                  :src="`https://cdn.quasar.dev/logo/svg/quasar-logo.svg`"
                  class="partner-logo"
                  fit="contain"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 联系我们 -->
      <div class="section contact-section q-mt-xl">
        <div class="row q-col-gutter-lg">
          <div class="col-12 col-md-6">
            <div class="contact-info-card">
              <h2 class="section-title">联系方式</h2>
              <p class="section-text">
                如果您对我们的商务合作感兴趣，欢迎通过以下方式与我们联系，我们的商务团队将尽快回复您。
              </p>
              
              <div class="contact-item">
                <q-icon name="person" size="1.5rem" color="primary" class="q-mr-md" />
                <div class="contact-details">
                  <div class="contact-label">商务联系人</div>
                  <div class="contact-value">张经理</div>
                </div>
              </div>
              
              <div class="contact-item">
                <q-icon name="phone" size="1.5rem" color="primary" class="q-mr-md" />
                <div class="contact-details">
                  <div class="contact-label">商务热线</div>
                  <div class="contact-value">021-88889999</div>
                </div>
              </div>
              
              <div class="contact-item">
                <q-icon name="email" size="1.5rem" color="primary" class="q-mr-md" />
                <div class="contact-details">
                  <div class="contact-label">商务邮箱</div>
                  <div class="contact-value"><EMAIL></div>
                </div>
              </div>
              
              <div class="contact-item">
                <q-icon name="schedule" size="1.5rem" color="primary" class="q-mr-md" />
                <div class="contact-details">
                  <div class="contact-label">工作时间</div>
                  <div class="contact-value">周一至周五 9:00-18:00</div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="col-12 col-md-6">
            <div class="quick-form-card">
              <h2 class="section-title">快速咨询</h2>
              <p class="section-text">
                留下您的联系方式，我们的商务团队将在1个工作日内与您联系。
              </p>
              
              <q-form @submit="onSubmit" class="q-gutter-md">
                <q-input
                  v-model="form.name"
                  label="您的姓名 *"
                  :rules="[val => !!val || '请输入您的姓名']"
                  outlined
                  dense
                />
                
                <q-input
                  v-model="form.company"
                  label="公司名称 *"
                  :rules="[val => !!val || '请输入公司名称']"
                  outlined
                  dense
                />
                
                <q-input
                  v-model="form.position"
                  label="职位"
                  outlined
                  dense
                />
                
                <q-input
                  v-model="form.phone"
                  label="联系电话 *"
                  :rules="[
                    val => !!val || '请输入联系电话',
                    val => /^1[3-9]\d{9}$/.test(val) || '请输入正确的手机号码'
                  ]"
                  outlined
                  dense
                />
                
                <q-input
                  v-model="form.email"
                  label="电子邮箱 *"
                  type="email"
                  :rules="[
                    val => !!val || '请输入电子邮箱',
                    val => /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/.test(val) || '请输入正确的邮箱格式'
                  ]"
                  outlined
                  dense
                />
                
                <q-select
                  v-model="form.type"
                  :options="cooperationTypes"
                  label="合作类型 *"
                  :rules="[val => !!val || '请选择合作类型']"
                  outlined
                  dense
                  emit-value
                  map-options
                />
                
                <q-input
                  v-model="form.message"
                  label="合作意向描述"
                  type="textarea"
                  outlined
                  autogrow
                  rows="3"
                />
                
                <div class="row justify-end">
                  <q-btn
                    type="submit"
                    color="primary"
                    label="提交"
                    :loading="submitting"
                  />
                </div>
              </q-form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <Footer />
</template>

<script setup>
import { ref } from 'vue';
import { useQuasar } from 'quasar';

const $q = useQuasar();

// 表单数据
const form = ref({
  name: '',
  company: '',
  position: '',
  phone: '',
  email: '',
  type: '',
  message: ''
});

// 合作类型选项
const cooperationTypes = [
  { label: '品牌入驻', value: 'brand' },
  { label: '供应链合作', value: 'supply_chain' },
  { label: '渠道分销', value: 'distribution' },
  { label: '营销推广', value: 'marketing' },
  { label: '其他', value: 'other' }
];

// 提交状态
const submitting = ref(false);

// 提交表单
const onSubmit = async () => {
  submitting.value = true;
  
  try {
    // 模拟API请求
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 在实际应用中，这里应该是API调用
    // await fetch('/api/business', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify(form.value)
    // });
    
    // 显示成功消息
    $q.notify({
      color: 'positive',
      message: '您的合作意向已成功提交，我们将尽快与您联系！',
      icon: 'check_circle',
      position: 'top',
      timeout: 3000
    });
    
    // 重置表单
    form.value = {
      name: '',
      company: '',
      position: '',
      phone: '',
      email: '',
      type: '',
      message: ''
    };
  } catch (error) {
    // 显示错误消息
    $q.notify({
      color: 'negative',
      message: '提交失败，请稍后再试或直接联系我们的商务团队。',
      icon: 'error',
      position: 'top',
      timeout: 3000
    });
    console.error('提交表单失败:', error);
  } finally {
    submitting.value = false;
  }
};
</script>

<style lang="scss" scoped>
.business-page {
  padding: 20px 0 40px;
  background-color: #f8f8f8;
}

.business-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
  padding: 30px 0;
}

.page-title {
  font-size: 32px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

.page-subtitle {
  font-size: 18px;
  color: #666;
}

.section {
  margin-bottom: 60px;
}

.section-title {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 20px;
  position: relative;
  
  &:after {
    content: '';
    display: block;
    width: 50px;
    height: 3px;
    background-color: #1976d2;
    margin-top: 10px;
  }
  
  &.text-center:after {
    margin-left: auto;
    margin-right: auto;
  }
}

.section-text {
  font-size: 16px;
  line-height: 1.6;
  color: #555;
  margin-bottom: 16px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.philosophy-image {
  width: 100%;
  max-height: 400px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.model-card, .advantage-card, .case-card, .contact-info-card, .quick-form-card {
  height: 100%;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
  }
}

.model-title, .advantage-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

.model-text, .advantage-text, .case-text {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.process-timeline {
  padding: 20px 0;
}

.partner-logos {
  padding: 20px 0;
}

.partner-logo-container {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
  }
}

.partner-logo {
  max-width: 80%;
  max-height: 80%;
}

.contact-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.contact-details {
  flex: 1;
}

.contact-label {
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.contact-value {
  color: #555;
}

@media (max-width: 767px) {
  .page-header {
    margin-bottom: 30px;
    padding: 20px 0;
  }
  
  .page-title {
    font-size: 26px;
  }
  
  .page-subtitle {
    font-size: 16px;
  }
  
  .section {
    margin-bottom: 40px;
  }
  
  .section-title {
    font-size: 22px;
    margin-bottom: 15px;
  }
  
  .section-text {
    font-size: 15px;
  }
  
  .philosophy-image {
    margin-top: 20px;
    max-height: 300px;
  }
  
  .model-card, .advantage-card, .case-card, .contact-info-card, .quick-form-card {
    margin-bottom: 20px;
  }
  
  .partner-logo-container {
    height: 80px;
    margin-bottom: 16px;
  }
}
</style>
