<template>
  <div class="help-header">
    <div class="page-header">
      <div class="header-background">
        <div class="header-content">
          <h1 class="page-title">{{ $t('help.title') }}</h1>
          <div class="search-box" v-if="showSearch">
            <div class="search-input-container">
              <q-input v-model="localSearchQuery" outlined dense class="search-input" :placeholder="$t('help.searchPlaceholder')" bg-color="white" @keyup.enter="searchHelp">
                <template #prepend>
                  <q-icon name="search" color="primary" size="sm" />
                </template>
                <template #append>
                  <q-btn v-if="localSearchQuery" round flat dense icon="close" size="sm" @click="localSearchQuery = ''" class="clear-btn" />
                </template>
              </q-input>
              <q-btn color="primary" rounded no-caps unelevated :label="$t('help.search.searchButton')" @click="searchHelp" class="search-btn q-ml-sm" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';

const props = defineProps({
  showSearch: {
    type: Boolean,
    default: true,
  },
  modelValue: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['update:modelValue', 'search']);

const router = useRouter();
useI18n(); // 使用 useI18n 但不解构 t，因为我们只在模板中使用 $t

// 本地搜索查询，用于支持v-model
// 如果没有提供modelValue，则使用内部状态
const internalQuery = ref('');
const localSearchQuery = computed({
  get: () => props.modelValue || internalQuery.value,
  set: (value) => {
    internalQuery.value = value;
    emit('update:modelValue', value);
  },
});

// 监听外部modelValue变化
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue !== localSearchQuery.value) {
      localSearchQuery.value = newValue;
    }
  }
);

// 搜索帮助内容
const searchHelp = () => {
  if (!localSearchQuery.value.trim()) return;

  // 发出搜索事件，让父组件处理搜索逻辑
  emit('search');

  // 默认跳转到搜索页面
  router.push({
    path: '/help/search',
    query: { q: localSearchQuery.value },
  });
};
</script>

<style lang="scss" scoped>
.help-header {
  width: 100%;
}

.page-header {
  text-align: center;
  margin-bottom: 20px;
  position: relative;
}

.header-background {
  background: linear-gradient(135deg, #1976d2, #64b5f6, #42a5f5);
  background-size: 400% 400%;
  animation: gradientAnimation 15s ease infinite;
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url("data:image/svg+xml,%3Csvg width='500' height='500' viewBox='0 0 500 500' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.15'%3E%3C!-- 购物车图标1 --%3E%3Cpath d='M40 30c-2.21 0-4 1.79-4 4s1.79 4 4 4h4.39l4.54 22.71c0.61 3.06 3.3 5.29 6.42 5.29h30.31c3.08 0 5.76-2.18 6.4-5.19L96 40h-8.23l-3.54 16H55.35L50.81 34h29.19v-4H40zm14 36c-3.31 0-6 2.69-6 6s2.69 6 6 6 6-2.69 6-6-2.69-6-6-6zm22 0c-3.31 0-6 2.69-6 6s2.69 6 6 6 6-2.69 6-6-2.69-6-6-6z' transform='translate(25, 15) rotate(15) scale(0.4)'/%3E%3C!-- 问号图标1 --%3E%3Cpath d='M120 30c-11.05 0-20 8.95-20 20 0 2.21 1.79 4 4 4s4-1.79 4-4c0-6.63 5.37-12 12-12s12 5.37 12 12c0 4.42-2.95 8.15-7 9.4v1.6c0 2.21-1.79 4-4 4s-4-1.79-4-4v-4c0-2.21 1.79-4 4-4 5.52 0 10-4.48 10-10s-4.48-10-10-10zm0 40c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4z' transform='translate(380, 45) rotate(-10) scale(0.35)'/%3E%3C!-- 书本图标1 --%3E%3Cpath d='M160 30v40c0 2.21-1.79 4-4 4h-32c-2.21 0-4-1.79-4-4V30c0-2.21 1.79-4 4-4h32c2.21 0 4 1.79 4 4zm-8 4h-24v32h24V34zm-4 4v4h-16v-4h16zm0 8v4h-16v-4h16zm0 8v4h-16v-4h16z' transform='translate(210, 80) rotate(5) scale(0.45)'/%3E%3C!-- 邮件图标 --%3E%3Cpath d='M40 120c-2.21 0-4 1.79-4 4v32c0 2.21 1.79 4 4 4h40c2.21 0 4-1.79 4-4v-32c0-2.21-1.79-4-4-4H40zm4 8h32l-16 12-16-12zm-4 4.25L56 144l-16 12v-23.75zm40 0V156l-16-12 16-11.75zm-36 16.5L56 160H40l4-11.25zm32 0L80 160H56l12-11.25z' transform='translate(65, 320) rotate(-8) scale(0.38)'/%3E%3C!-- 搜索图标 --%3E%3Cpath d='M120 120c-11.05 0-20 8.95-20 20s8.95 20 20 20 20-8.95 20-20-8.95-20-20-20zm0 8c6.63 0 12 5.37 12 12s-5.37 12-12 12-12-5.37-12-12 5.37-12 12-12zm15.71 19.29l10 10c1.56 1.56 1.56 4.1 0 5.66-1.56 1.56-4.1 1.56-5.66 0l-10-10c-1.56-1.56-1.56-4.1 0-5.66 1.56-1.56 4.1-1.56 5.66 0z' transform='translate(300, 350) rotate(12) scale(0.42)'/%3E%3C!-- 标签图标 --%3E%3Cpath d='M160 120c-2.21 0-4 1.79-4 4v4h-4c-2.21 0-4 1.79-4 4s1.79 4 4 4h4v4c0 2.21 1.79 4 4 4s4-1.79 4-4v-4h4c2.21 0 4-1.79 4-4s-1.79-4-4-4h-4v-4c0-2.21-1.79-4-4-4z' transform='translate(420, 180) rotate(-15) scale(0.3)'/%3E%3C!-- 购物车图标2 --%3E%3Cpath d='M40 30c-2.21 0-4 1.79-4 4s1.79 4 4 4h4.39l4.54 22.71c0.61 3.06 3.3 5.29 6.42 5.29h30.31c3.08 0 5.76-2.18 6.4-5.19L96 40h-8.23l-3.54 16H55.35L50.81 34h29.19v-4H40zm14 36c-3.31 0-6 2.69-6 6s2.69 6 6 6 6-2.69 6-6-2.69-6-6-6zm22 0c-3.31 0-6 2.69-6 6s2.69 6 6 6 6-2.69 6-6-2.69-6-6-6z' transform='translate(150, 250) rotate(-20) scale(0.25)'/%3E%3C!-- 问号图标2 --%3E%3Cpath d='M120 30c-11.05 0-20 8.95-20 20 0 2.21 1.79 4 4 4s4-1.79 4-4c0-6.63 5.37-12 12-12s12 5.37 12 12c0 4.42-2.95 8.15-7 9.4v1.6c0 2.21-1.79 4-4 4s-4-1.79-4-4v-4c0-2.21 1.79-4 4-4 5.52 0 10-4.48 10-10s-4.48-10-10-10zm0 40c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4z' transform='translate(280, 200) rotate(25) scale(0.28)'/%3E%3C!-- 书本图标2 --%3E%3Cpath d='M160 30v40c0 2.21-1.79 4-4 4h-32c-2.21 0-4-1.79-4-4V30c0-2.21 1.79-4 4-4h32c2.21 0 4 1.79 4 4zm-8 4h-24v32h24V34zm-4 4v4h-16v-4h16zm0 8v4h-16v-4h16zm0 8v4h-16v-4h16z' transform='translate(400, 280) rotate(-8) scale(0.32)'/%3E%3C!-- 星星图标 --%3E%3Cpath d='M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z' transform='translate(120, 150) rotate(10) scale(1.5)'/%3E%3C!-- 礼物图标 --%3E%3Cpath d='M20 6h-2.18c.11-.31.18-.65.18-1 0-1.66-1.34-3-3-3-1.05 0-1.96.54-2.5 1.35l-.5.67-.5-.68C10.96 2.54 10.05 2 9 2 7.34 2 6 3.34 6 5c0 .35.07.69.18 1H4c-1.11 0-1.99.89-1.99 2L2 19c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2zm-5-2c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zM9 4c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm11 15H4v-2h16v2zm0-5H4V8h5.08L7 10.83 8.62 12 11 8.76l1-1.36 1 1.36L15.38 12 17 10.83 14.92 8H20v6z' transform='translate(350, 120) rotate(-5) scale(1.2)'/%3E%3C!-- 聊天图标 --%3E%3Cpath d='M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 14H6l-2 2V4h16v12z' transform='translate(40, 220) rotate(8) scale(1.3)'/%3E%3C/g%3E%3C/svg%3E");
    background-size: 500px 500px;
    opacity: 0.25;
    z-index: 1;
    animation: slowMove 60s linear infinite;
  }

  @keyframes slowMove {
    0% {
      background-position: 0 0;
    }
    100% {
      background-position: 500px 500px;
    }
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='800' height='400' viewBox='0 0 800 400'%3E%3Cg fill='%23ffffff' opacity='0.1'%3E%3C!-- 右上角大问号 --%3E%3Cpath d='M650 60c-22.1 0-40 17.9-40 40 0 4.4 3.6 8 8 8s8-3.6 8-8c0-13.3 10.7-24 24-24s24 10.7 24 24c0 8.8-5.9 16.3-14 18.8v3.2c0 4.4-3.6 8-8 8s-8-3.6-8-8v-8c0-4.4 3.6-8 8-8 11 0 20-9 20-20s-9-20-20-20zm0 80c-4.4 0-8 3.6-8 8s3.6 8 8 8 8-3.6 8-8-3.6-8-8-8z' transform='rotate(15)'/%3E%3C!-- 左下角购物袋 --%3E%3Cpath d='M150 280c-4.4 0-8 3.6-8 8v32c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8v-32c0-4.4-3.6-8-8-8h-48zm4-16h40c2.2 0 4 1.8 4 4v8h8c4.4 0 8 3.6 8 8v48c0 4.4-3.6 8-8 8h-64c-4.4 0-8-3.6-8-8v-48c0-4.4 3.6-8 8-8h8v-8c0-2.2 1.8-4 4-4zm20 8h-16v-4h16v4z' transform='rotate(-10)'/%3E%3C!-- 右下角书本 --%3E%3Cpath d='M600 300c-4.4 0-8 3.6-8 8v40c0 4.4 3.6 8 8 8h40c4.4 0 8-3.6 8-8v-40c0-4.4-3.6-8-8-8h-40zm8 16h24v24h-24v-24zm4 4v4h16v-4h-16zm0 8v4h16v-4h-16z' transform='rotate(5)'/%3E%3C!-- 左上角购物车 --%3E%3Cpath d='M100 80c-2.21 0-4 1.79-4 4s1.79 4 4 4h4.39l4.54 22.71c0.61 3.06 3.3 5.29 6.42 5.29h30.31c3.08 0 5.76-2.18 6.4-5.19L156 90h-8.23l-3.54 16h-28.88l-4.54-22h29.19v-4h-40zm14 36c-3.31 0-6 2.69-6 6s2.69 6 6 6 6-2.69 6-6-2.69-6-6-6zm22 0c-3.31 0-6 2.69-6 6s2.69 6 6 6 6-2.69 6-6-2.69-6-6-6z' transform='rotate(-8)'/%3E%3C!-- 中间礼物盒 --%3E%3Cpath d='M400 180h-2.18c.11-.31.18-.65.18-1 0-1.66-1.34-3-3-3-1.05 0-1.96.54-2.5 1.35l-.5.67-.5-.68c-.54-.81-1.45-1.34-2.5-1.34-1.66 0-3 1.34-3 3 0 .35.07.69.18 1h-2.18c-1.11 0-1.99.89-1.99 2l-.01 11c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2v-11c0-1.11-.89-2-2-2zm-5-2c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm-6 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm11 15h-16v-2h16v2zm0-5h-16v-6h5.08l-2.08 2.83 1.62 1.17 2.38-3.24 1-1.36 1 1.36 2.38 3.24 1.62-1.17-2.08-2.83h5.08v6z' transform='scale(2) rotate(8)'/%3E%3C!-- 随机分布的小问号 --%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17h-2v-2h2v2zm2.07-7.75l-.9.92C13.45 12.9 13 13.5 13 15h-2v-.5c0-1.1.45-2.1 1.17-2.83l1.24-1.26c.37-.36.59-.86.59-1.41 0-1.1-.9-2-2-2s-2 .9-2 2H8c0-2.21 1.79-4 4-4s4 1.79 4 4c0 .88-.36 1.68-.93 2.25z' transform='translate(250, 100) scale(1.2) rotate(15)'/%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17h-2v-2h2v2zm2.07-7.75l-.9.92C13.45 12.9 13 13.5 13 15h-2v-.5c0-1.1.45-2.1 1.17-2.83l1.24-1.26c.37-.36.59-.86.59-1.41 0-1.1-.9-2-2-2s-2 .9-2 2H8c0-2.21 1.79-4 4-4s4 1.79 4 4c0 .88-.36 1.68-.93 2.25z' transform='translate(500, 250) scale(0.8) rotate(-10)'/%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17h-2v-2h2v2zm2.07-7.75l-.9.92C13.45 12.9 13 13.5 13 15h-2v-.5c0-1.1.45-2.1 1.17-2.83l1.24-1.26c.37-.36.59-.86.59-1.41 0-1.1-.9-2-2-2s-2 .9-2 2H8c0-2.21 1.79-4 4-4s4 1.79 4 4c0 .88-.36 1.68-.93 2.25z' transform='translate(350, 320) scale(1.5) rotate(20)'/%3E%3C/g%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    opacity: 0.12;
    z-index: 1;
    animation: floatBackground 80s ease-in-out infinite alternate;
  }

  @keyframes floatBackground {
    0% {
      background-position: 0% 0%;
    }
    25% {
      background-position: 2% 3%;
    }
    50% {
      background-position: 0% 5%;
    }
    75% {
      background-position: 3% 2%;
    }
    100% {
      background-position: 5% 0%;
    }
  }
}

@keyframes gradientAnimation {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.header-content {
  position: relative;
  z-index: 2;
  padding: 50px 20px 40px;

  &::before {
    content: '';
    position: absolute;
    top: 15px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 80px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100' fill='%23ffffff' opacity='0.2'%3E%3Cg%3E%3Cpath d='M50 5C25.1 5 5 25.1 5 50s20.1 45 45 45 45-20.1 45-45S74.9 5 50 5zm0 80c-19.3 0-35-15.7-35-35s15.7-35 35-35 35 15.7 35 35-15.7 35-35 35z'/%3E%3Cpath d='M55 45h15v10H45V25h10v20z'/%3E%3Ccircle cx='30' cy='70' r='5'/%3E%3Ccircle cx='50' cy='70' r='5'/%3E%3Ccircle cx='70' cy='70' r='5'/%3E%3C/g%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    opacity: 0.3;
    animation: floatIcon 6s ease-in-out infinite;
  }

  @keyframes floatIcon {
    0% {
      transform: translateX(-50%) translateY(0px);
    }
    50% {
      transform: translateX(-50%) translateY(-10px);
    }
    100% {
      transform: translateX(-50%) translateY(0px);
    }
  }

  &::after {
    content: '';
    position: absolute;
    bottom: 15px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 2px;
    background-color: #fff;
    border-radius: 2px;
  }
}

.page-title {
  font-size: 28px;
  font-weight: bold;
  color: #fff;
  margin-bottom: 20px;
  letter-spacing: 0.5px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.search-box {
  max-width: 700px;
  margin: 0 auto;

  .search-input-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
  }

  .search-input {
    font-size: 14px;
    border-radius: 24px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    flex: 1;
    max-width: 550px;

    :deep(.q-field__control) {
      height: 48px;
      border-radius: 24px;
    }

    :deep(.q-field__marginal) {
      height: 48px;
    }

    :deep(.q-field__native) {
      padding-left: 4px;
    }
  }

  .search-btn {
    height: 48px;
    font-weight: 500;
    font-size: 14px;
    padding: 0 24px;
    border-radius: 24px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
    }
  }

  .clear-btn {
    opacity: 0.7;
    &:hover {
      opacity: 1;
    }
  }
}

@media (max-width: 767px) {
  .page-title {
    font-size: 22px;
    margin-bottom: 12px;
  }

  .page-header {
    margin-bottom: 15px;
  }

  .header-content {
    padding: 30px 15px;
  }

  .search-box {
    .search-input-container {
      flex-direction: column;
      gap: 8px;
    }

    .search-input {
      font-size: 13px;
      max-width: 100%;

      :deep(.q-field__control) {
        height: 42px;
      }

      :deep(.q-field__marginal) {
        height: 42px;
      }
    }

    .search-btn {
      height: 42px;
      font-size: 13px;
      width: 100%;
      margin-top: 4px;
    }
  }
}
</style>
