<template>
  <div class="insurance-selector-section" v-if="insuranceOptions && insuranceOptions.length > 0">
    <div class="row items-center q-mb-xs">
      <q-icon name="security" color="primary" size="xs" class="q-mr-xs" />
      <div class="text-subtitle2 text-weight-medium">保险服务</div>
    </div>
    <div class="text-caption text-grey-8 q-mb-xs">为您的包裹选择合适的保险服务，保障物品安全</div>

    <q-card flat bordered class="q-pa-sm">
      <div class="row items-center q-mb-xs">
        <div class="text-caption">
          包裹申报价值: <span class="text-weight-medium">¥{{ parcelValue.toFixed(2) }}</span>
        </div>
      </div>

      <div class="column q-gutter-y-xs">
        <!-- 保险选项 -->
        <div
          v-for="insurance in insuranceOptions"
          :key="insurance.id"
          :class="{ 'selected-service': isServiceSelected(insurance.id) }"
          class="insurance-option cursor-pointer q-mb-xs"
          @click="toggleService(insurance.id)">
          <div class="row items-center no-wrap q-py-xs">
            <q-checkbox v-model="selectedServiceIds" :val="insurance.id" color="primary" class="q-ml-xs q-mr-sm" @click.stop />
            <div class="col">
              <div class="row items-center">
                <div class="text-body2 text-weight-medium">{{ insurance.name }}</div>
              </div>
              <div class="text-caption">{{ insurance.description }}</div>
            </div>
            <div class="text-body2 text-primary text-weight-medium q-mr-sm">¥{{ fen2yuan(insurance.price) }}</div>
          </div>
        </div>
      </div>
    </q-card>
  </div>
</template>

<script setup>
import { ref, watch, nextTick } from 'vue';

// 定义组件接收的属性
const props = defineProps({
  modelValue: {
    type: Array,
    default: () => [],
  },
  insuranceOptions: {
    type: Array,
    required: true,
    default: () => [],
  },
  parcelValue: {
    type: Number,
    required: true,
    default: 0,
  },
});

// 定义组件向外发出的事件
const emit = defineEmits(['update:modelValue']);

// 选中的服务ID列表
const selectedServiceIds = ref([...props.modelValue]);

// 检查服务是否被选中
function isServiceSelected(serviceId) {
  return selectedServiceIds.value.includes(serviceId);
}

// 切换服务选择状态
function toggleService(serviceId) {
  if (isServiceSelected(serviceId)) {
    selectedServiceIds.value = selectedServiceIds.value.filter((id) => id !== serviceId);
  } else {
    selectedServiceIds.value.push(serviceId);
  }
  // 使用nextTick确保DOM更新后再发出事件
  nextTick(() => {
    emit('update:modelValue', [...selectedServiceIds.value]);
  });
}

// 计算保险费用
function calculateInsuranceFee(insurance) {
  if (!insurance) return '0.00';

  const calculatedFee = props.parcelValue * insurance.rate;
  const finalFee = Math.max(calculatedFee, insurance.minFee);

  return finalFee.toFixed(2);
}

// 监听父组件传入的值变化
watch(
  () => props.modelValue,
  (newValue) => {
    // 避免无限循环，只有当值真正变化时才更新
    if (JSON.stringify(newValue) !== JSON.stringify(selectedServiceIds.value)) {
      selectedServiceIds.value = [...newValue];
    }
  },
  { deep: true }
);
</script>

<style lang="scss" scoped>
.insurance-selector-section {
  .q-card {
    border-radius: 4px;
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
    }
  }

  .insurance-option {
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;

    &:hover {
      border-color: #c0c0c0;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
      transform: translateY(-1px);
    }

    &.selected-service {
      border: 1px solid #1976d2;
      background-color: rgba(25, 118, 210, 0.05);
    }
  }

  .payment-method {
    margin-top: -4px;
  }
}
</style>
