<template>
  <Header />
  <Breadcrumbs :breadcrumbs="breadcrumbs" />
  <div class="pay-result-page">
    <!-- 购物流程组件 - 更紧凑的设计 -->
    <div class="shopping-progress bg-white rounded-borders q-pa-sm">
      <div class="row justify-between items-center no-wrap">
        <div class="page-title text-primary q-ml-xs">{{ t('payment.result.title') }}</div>
        <!-- PC端直接显示流程 -->
        <div class="col-auto desktop-progress" v-if="!$q.screen.lt.sm">
          <ModernStepProgress currentStep="1" class="compact-progress" />
        </div>
        <!-- 移动端使用折叠按钮 -->
        <q-btn v-else flat dense round size="sm" :icon="showFullProgress ? 'expand_less' : 'expand_more'" @click="showFullProgress = !showFullProgress" color="primary" />
      </div>

      <!-- 移动端可折叠的流程指示器 -->
      <q-slide-transition>
        <div v-if="$q.screen.lt.sm && showFullProgress" class="q-mt-xs mobile-progress-container">
          <ModernStepProgress currentStep="1" class="compact-progress" />
        </div>
      </q-slide-transition>
    </div>

    <!-- 显示结果 - 美化设计 -->
    <div class="order-result-container text-center">
      <q-card class="result-card">
        <q-card-section class="result-card-content">
          <!-- 成功状态 -->
          <div v-if="payResult === 'success'" class="order-result-content">
            <div class="result-icon-container success">
              <q-icon name="check_circle" size="64px" color="positive" />
            </div>
            <div class="result-status success">{{ t('payment.result.success') }}</div>
            <div class="result-amount">
              <span class="amount-column currency-text" v-html="formatAmount(state.orderInfo.orderPrice, { allowWrap: false })"></span>
            </div>
          </div>

          <!-- 失败状态 -->
          <div v-if="payResult === 'failed'" class="order-result-content">
            <div class="result-icon-container failed">
              <q-icon name="cancel" size="64px" color="negative" />
            </div>
            <div class="result-status failed">{{ t('payment.result.failed') }}</div>
          </div>

          <!-- 关闭状态 -->
          <div v-if="payResult === 'closed'" class="order-result-content">
            <div class="result-icon-container closed">
              <q-icon name="block" size="64px" color="grey-7" />
            </div>
            <div class="result-status closed">{{ t('payment.result.closed') }}</div>
          </div>

          <!-- 等待状态 -->
          <div v-if="payResult === 'waiting'" class="order-result-content">
            <div class="result-icon-container waiting">
              <q-spinner-dots size="64px" color="warning" />
            </div>
            <div class="result-status waiting">{{ t('payment.result.waiting') }}</div>
          </div>

          <!-- 操作按钮 -->
          <div class="action-buttons">
            <q-btn outline rounded color="primary" class="action-btn" @click="goToHome">
              <q-icon name="home" class="q-mr-xs" />
              {{ t('payment.result.backToHome') }}
            </q-btn>
            <q-btn v-if="payResult === 'success'" rounded color="primary" class="action-btn" @click="onOrder">
              <q-icon name="receipt_long" class="q-mr-xs" />
              {{ t('payment.result.viewOrder') }}
            </q-btn>
            <q-btn v-if="payResult === 'failed'" rounded color="primary" class="action-btn" @click="rePay">
              <q-icon name="replay" class="q-mr-xs" />
              {{ t('payment.result.repay') }}
            </q-btn>
          </div>
        </q-card-section>
      </q-card>
    </div>
  </div>

  <Footer />
</template>

<script setup>
import ModernStepProgress from '~/components/widgets/ModernStepProgress';
import OrderApi from '~/composables/orderApi';
import PayApi from '~/composables/payApi';
import { useCurrency } from '~/composables/useCurrency';
import { useI18n } from 'vue-i18n';

const route = useRoute();
const { t } = useI18n();
const breadcrumbs = [{ label: t('payment.result.title'), to: '/cart' }];
const resultParams = JSON.parse(sessionStorage.getItem('resultParams') || '{}');
const { formatAmount } = useCurrency();
const showFullProgress = ref(false); // 控制移动端流程显示

const state = reactive({
  id: 0, // 支付单号
  orderType: 'goods', // 订单类型
  result: 'unpaid', // 支付状态
  orderInfo: {}, // 支付订单信息
  tradeOrder: {}, // 商品订单信息，只有在 orderType 为 goods 才会请求。目的：【我的拼团】按钮的展示
  counter: 0, // 获取结果次数
});

onMounted(async () => {
  // 支付订单号
  if (resultParams && resultParams.id) {
    state.id = resultParams.id;
  } else {
    //从paypal返回的returnurl
    state.id = route.query.id;
  }
  // 订单类型
  if (resultParams && resultParams.orderType) {
    state.orderType = resultParams.orderType;
  }

  // 支付结果传值过来是失败，则直接显示失败界面
  if (resultParams && resultParams.payState === 'fail') {
    state.result = 'failed';
  } else {
    // 轮询三次检测订单支付结果
    await getOrderInfo(state.id);
  }
});

// 支付结果 result => payResult
const payResult = computed(() => {
  if (state.result === 'unpaid') {
    return 'waiting';
  }
  if (state.result === 'paid') {
    return 'success';
  }
  if (state.result === 'failed') {
    return 'failed';
  }
  if (state.result === 'closed') {
    return 'closed';
  }
  return 'unknown';
});

// 获得订单信息
async function getOrderInfo(id) {
  state.counter++;
  // 1. 加载订单信息
  const { data, code } = await PayApi.getOrder(id);
  if (code === 0) {
    state.orderInfo = data;
    if (!state.orderInfo || state.orderInfo.status === 30) {
      // 支付关闭
      state.result = 'closed';
      return;
    }
    if (state.orderInfo.status !== 0) {
      // 非待支付，可能是已支付，可能是已退款
      state.result = 'paid';

      // 特殊：获得商品订单信息
      if (state.orderType === 'goods') {
        const { data, code } = await OrderApi.getOrderDetail(state.orderInfo.merchantOrderId, true);
        if (code === 0) {
          state.tradeOrder = data;
        }
      }
      return;
    }
  }
  // 2.1 情况三一：未支付，且轮询次数小于三次，则继续轮询
  if (state.counter < 3 && state.result === 'unpaid') {
    setTimeout(() => {
      getOrderInfo(id);
    }, 2000);
  }
  // 2.2 情况二：超过三次检测才判断为支付失败
  if (state.counter >= 3) {
    state.result = 'failed';
  }
}

function onOrder() {
  if (state.orderType === 'recharge') {
    // sheep.$router.redirect('/pages/pay/recharge-log');
    navigateTo('/account/balance');
  } else {
    // sheep.$router.redirect('/pages/order/list');
    navigateTo('/account/orders');
  }
}

function rePay() {
  // 重新支付
  navigateTo('/order/pay', {
    params: {
      id: state.id,
      orderType: state.orderType,
    },
  });
}

function goToHome() {
  // 跳转到首页
  navigateTo('/');
}
</script>

<style lang="scss" scoped>
.pay-result-page {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;

  @media (max-width: 599px) {
    padding: 0 8px;
  }
}

// 页面标题样式
.page-title {
  font-size: 22px;
  font-weight: 600;

  @media (max-width: 599px) {
    font-size: 18px;
  }
}

// 购物流程组件样式
.shopping-progress {
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  transition: all 0.3s ease;
  border-radius: 8px;

  &:hover {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }

  .desktop-progress {
    max-width: 70%;
    overflow: hidden;
  }

  .mobile-progress-container {
    display: flex;
    justify-content: center;
    width: 100%;
  }

  .compact-progress {
    transform: scale(0.9);
    transform-origin: center center;
    margin: 0 auto;

    @media (max-width: 599px) {
      transform: scale(0.85);
      width: 100%;
      max-width: 360px;
    }
  }
}

// 订单结果容器样式
.order-result-container {
  margin: 20px 0 30px;
}

// 结果卡片样式
.result-card {
  max-width: 600px;
  margin: 0 auto;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
  }
}

.result-card-content {
  padding: 30px 20px;
}

// 结果图标容器
.result-icon-container {
  margin: 0 auto 20px;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;

  &.success {
    background-color: rgba(76, 175, 80, 0.1);
  }

  &.failed {
    background-color: rgba(244, 67, 54, 0.1);
  }

  &.closed {
    background-color: rgba(158, 158, 158, 0.1);
  }

  &.waiting {
    background-color: rgba(255, 152, 0, 0.1);
  }
}

// 结果状态文字样式
.result-status {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 16px;

  &.success {
    color: var(--q-positive);
  }

  &.failed {
    color: var(--q-negative);
  }

  &.closed {
    color: var(--q-grey-8);
  }

  &.waiting {
    color: var(--q-warning);
  }

  @media (max-width: 599px) {
    font-size: 20px;
  }
}

// 结果金额样式
.result-amount {
  font-size: 22px;
  font-weight: 500;
  margin: 20px 0;
  padding: 12px;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 8px;

  @media (max-width: 599px) {
    font-size: 18px;
    margin: 16px 0;
  }
}

// 金额列样式
.amount-column {
  display: inline-block;
  min-width: 140px;
  text-align: center;
  font-size: 1.3rem;

  @media (max-width: 599px) {
    min-width: 100px;
    font-size: 1.1rem;
  }

  .primary-currency {
    white-space: nowrap;
    display: inline-block;

    :deep(.default-currency) {
      font-size: 0.8em;
      color: #666;
      opacity: 0.85;
      font-weight: normal;
      display: inline-block;
      margin-left: 4px;
      white-space: normal;
    }
  }
}

// 操作按钮区域
.action-buttons {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 30px;
  gap: 16px;
}

// 操作按钮样式
.action-btn {
  min-width: 140px;
  padding: 8px 20px;
  font-weight: 500;

  @media (max-width: 599px) {
    min-width: 120px;
    padding: 6px 16px;
    font-size: 14px;
  }
}
</style>
