/* 商品详情页面的紧凑布局样式 */

/* 价格、数量和备注行的紧凑布局 */
.compact-product-info {
  margin-top: 0 !important;
}

/* 价格行样式 */
.price-row {
  margin-bottom: 4px !important;
  padding: 6px 0 !important;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
}

/* 数量行样式 */
.quantity-row {
  margin-bottom: 4px !important;
  padding: 6px 0 !important;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
}

/* 备注行样式 */
.memo-row {
  padding: 6px 0 !important;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
}

/* 左侧图标垂直居中对齐 */
.checkbox-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

/* 减少行间距 */
.row-spacing-reduced {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}

/* 确保输入框和按钮垂直居中 */
.vertical-center {
  display: flex;
  align-items: center;
}
