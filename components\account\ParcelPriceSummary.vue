<template>
  <div class="q-mb-lg">
    <div class="text-h6 q-mb-sm">费用明细</div>
    <div class="text-caption text-grey-8 q-mb-md">包裹运输的费用明细</div>
    
    <div class="bg-blue-1 rounded-borders q-pa-md">
      <div class="row justify-between q-mb-sm">
        <div class="text-body1">包裹重量:</div>
        <div class="text-body1">{{ parcelWeight }}kg</div>
      </div>
      <div class="row justify-between q-mb-sm">
        <div class="text-body1">基础运费:</div>
        <div class="text-body1">¥{{ baseShippingFee }}</div>
      </div>
      <div class="row justify-between q-mb-sm">
        <div class="text-body1">优惠金额:</div>
        <div class="text-body1 text-negative">-¥{{ discountAmount }}</div>
      </div>
      <q-separator class="q-my-md" />
      <div class="row justify-between">
        <div class="text-body1 text-weight-bold">总计:</div>
        <div class="text-body1 text-weight-bold">¥{{ totalFee }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

// 定义组件接收的属性
const props = defineProps({
  parcelWeight: {
    type: [Number, String],
    required: true,
    default: '0.00'
  },
  baseShippingFee: {
    type: [Number, String],
    required: true,
    default: '0.00'
  },
  discountAmount: {
    type: [Number, String],
    required: true,
    default: '0.00'
  }
});

// 计算总费用
const totalFee = computed(() => {
  const baseShippingFee = parseFloat(props.baseShippingFee);
  const discountAmount = parseFloat(props.discountAmount);
  
  const totalFee = Math.max(0, baseShippingFee - discountAmount);
  
  return totalFee.toFixed(2);
});
</script>

<style lang="scss" scoped>
/* 可以添加特定于此组件的样式 */
</style>
