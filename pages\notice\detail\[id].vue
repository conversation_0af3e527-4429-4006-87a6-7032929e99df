<template>
  <Header />
  <div class="news-detail-page">
    <div class="news-container">
      <!-- 新闻内容 -->
      <div v-if="news" class="news-content-wrapper">
        <!-- 新闻标题和元信息 -->
        <div class="news-header text-center">
          <h1 class="news-title">{{ news.title }}</h1>
          <div class="news-meta">
            <div class="meta-item">
              <q-icon name="event" size="sm" class="q-mr-xs" />
              <span>{{ formatDate(news.publishTime) }}</span>
            </div>
            <div class="meta-item">
              <q-badge :color="getCategoryColor(news.type)">
                {{ getCategoryText(news.type) }}
              </q-badge>
            </div>
          </div>
        </div>

        <!-- 新闻内容 -->
        <div class="news-content q-mt-lg">
          <div v-html="news.content"></div>
        </div>


        <!-- 返回按钮 -->
        <div class="news-actions q-mt-lg">
          <div class="row justify-end">
            <q-btn flat color="primary" to="/notice" class="back-btn">
              <q-icon name="arrow_back" class="q-mr-xs" />
              返回列表
            </q-btn>
          </div>
        </div>
      </div>

      <!-- 加载中状态 -->
      <div v-else-if="loading" class="loading-state">
        <q-spinner color="primary" size="3em" />
        <div class="q-mt-md">加载中...</div>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="hasError" class="error-state">
        <q-icon name="error_outline" color="negative" size="3em" />
        <div class="q-mt-md">内容不存在或已被删除</div>
        <q-btn color="primary" to="/notice" class="q-mt-md"> 返回公告列表 </q-btn>
      </div>
    </div>
  </div>
  <Footer />
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { date } from 'quasar';
import NoticeApi from '~/composables/noticeApi';

const route = useRoute()
const newsId = computed(() => {
  const id = route.params.id
  return id ? parseInt(id, 10) : null
})

// 状态
const loading = ref(true)
const news = ref(null)
const hasError = ref(false)

// ✅ 使用 useState 读取共享状态
const detailState = useState('noticeDetail')

onMounted(async () => {
  try {
    loading.value = true

    // ✅ 优先使用 useState 传递的数据
    if (detailState.value && detailState.value.id === newsId.value) {
      console.log('✅ 使用 useState 传递的数据:', detailState.value)
      news.value = detailState.value

      // 可选：使用后清除，避免污染后续页面
      // detailState.value = null

      loading.value = false
      return
    }

    // 否则调用 API 获取
    if (newsId.value) {
      const { code, data } = await NoticeApi.getDetail(newsId.value)
      if (code === 0) {
        news.value = data
      } else {
        hasError.value = true
      }
    } else {
      hasError.value = true
    }
  } catch (error) {
    console.error('Failed to fetch news details:', error)
    hasError.value = true
  } finally {
    loading.value = false
  }
})
// 格式化日期
const formatDate = (timestamp) => {
  return date.formatDate(new Date(timestamp), 'YYYY-MM-DD');
};


// 获取分类颜色
const getCategoryColor = (type) => {
  switch (type) {
    case 1:
      return 'red';
    case 2:
      return 'orange';
    default:
      return 'grey';
  }
};

// 获取分类文本
const getCategoryText = (type) => {
  switch (type) {
    case 1:
      return '系统通知';
    case 2:
      return '平台公告';
    default:
      return '其他';
  }
};

</script>

<style lang="scss" scoped>
.news-detail-page {
  padding: 20px 0 40px;
  background-color: #f8f8f8;
}

.news-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 16px;
}

.news-content-wrapper {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
  padding: 24px;
}

.news-header {
  margin-bottom: 20px;
  text-align: center;
}

.news-title {
  font-size: 28px;
  font-weight: bold;
  color: #333;
  margin-bottom: 16px;
  line-height: 1.3;
}

.news-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  color: #666;
  font-size: 14px;
  justify-content: center;

  .meta-item {
    display: flex;
    align-items: center;
  }
}

.news-content {
  font-size: 16px;
  line-height: 1.7;
  color: #333;

  :deep(h3) {
    font-size: 20px;
    font-weight: bold;
    margin: 24px 0 16px;
    color: #1976d2;
  }

  :deep(p) {
    margin-bottom: 16px;
  }

  :deep(ul),
  :deep(ol) {
    margin-bottom: 16px;
    padding-left: 24px;

    li {
      margin-bottom: 8px;
    }
  }

  :deep(table) {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 16px;

    th,
    td {
      border: 1px solid #ddd;
      padding: 8px;
      text-align: left;
    }

    th {
      background-color: #f2f2f2;
      font-weight: bold;
    }

    tr:nth-child(even) {
      background-color: #f9f9f9;
    }
  }

  :deep(img) {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
    margin: 16px 0;
  }

  :deep(a) {
    color: #1976d2;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}

.subsection-title {
  font-size: 18px;
  font-weight: bold;
  color: #1976d2;
  margin-bottom: 16px;
}

.section-title {
  font-size: 22px;
  font-weight: bold;
  color: #333;
  margin-bottom: 20px;
  position: relative;

  &:after {
    content: '';
    display: block;
    width: 50px;
    height: 3px;
    background-color: #1976d2;
    margin-top: 10px;
  }
}

.news-actions {
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}



.back-btn {
  font-size: 14px;
}

.related-news {
  margin-top: 40px;
}

.news-item {
  &:hover {
    background-color: #f5f5f5;
  }
}

.news-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  line-height: 1.3;
}

.news-summary {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  margin-top: 4px;
}

.loading-state,
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
  color: #666;
}

@media (max-width: 767px) {
  .news-content-wrapper {
    padding: 16px;
  }

  .news-title {
    font-size: 22px;
    margin-bottom: 12px;
  }

  .news-meta {
    font-size: 13px;
    gap: 12px;
  }

  .news-content {
    font-size: 15px;

    :deep(h3) {
      font-size: 18px;
      margin: 20px 0 12px;
    }
  }

  .section-title {
    font-size: 20px;
    margin-bottom: 16px;
  }

  .subsection-title {
    font-size: 16px;
  }
}
</style>
