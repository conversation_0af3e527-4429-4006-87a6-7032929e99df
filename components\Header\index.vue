<template>
  <!-- 头部公告和菜单 -->
  <div class="header">
    <!-- 顶部广告 -->
    <HeaderTopGgBar />
    <HeaderNote />
    <!-- 菜单 - 可固定 -->
    <div class="menu-wrapper" :class="{ 'menu-fixed': isMenuFixed }">
      <HeaderMenu />
    </div>
    <!-- 占位元素，防止内容跳跃 -->
    <div v-if="isMenuFixed" class="menu-placeholder"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';

const isMenuFixed = ref(false);
const menuOffset = ref(0);

// 监听滚动事件
const handleScroll = () => {
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

  // 当滚动超过菜单原始位置时固定菜单
  if (scrollTop > menuOffset.value) {
    isMenuFixed.value = true;
  } else {
    isMenuFixed.value = false;
  }
};

// 计算菜单的原始位置
const calculateMenuOffset = () => {
  const topGgBar = document.querySelector('.top');
  const noteBar = document.querySelector('.head-note');

  let offset = 0;
  if (topGgBar) {
    offset += topGgBar.offsetHeight;
  }
  if (noteBar) {
    offset += noteBar.offsetHeight;
  }

  menuOffset.value = offset;
};

onMounted(() => {
  // 延迟计算，确保DOM已渲染
  nextTick(() => {
    calculateMenuOffset();
    window.addEventListener('scroll', handleScroll);
    window.addEventListener('resize', calculateMenuOffset);
  });
});

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll);
  window.removeEventListener('resize', calculateMenuOffset);
});
</script>

<style lang="scss" scoped>
.header {
  position: relative;
}

.menu-wrapper {
  transition: all 0.3s ease;

  &.menu-fixed {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.menu-placeholder {
  height: 60px; // HeaderMenu的高度

  @media (max-width: 599px) {
    height: 50px; // 移动端HeaderMenu的高度
  }
}
</style>
