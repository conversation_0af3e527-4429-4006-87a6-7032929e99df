import { defineStore } from 'pinia';
import HelpApi from '~/composables/helpApi';

export const useHelpStore = defineStore('help', {
  state: () => ({
    categories: [],
    faqs: [],
    faqTotal: 0,
    faqCurrentPage: 1,
    faqPageSize: 10,
    loading: false,
    faqLoading: false,
    initialized: false,
  }),

  getters: {
    getCategories: (state) => state.categories,
    getFaqs: (state) => state.faqs,
    getFaqTotal: (state) => state.faqTotal,
    getFaqCurrentPage: (state) => state.faqCurrentPage,
    getFaqPageSize: (state) => state.faqPageSize,
    isLoading: (state) => state.loading,
    isFaqLoading: (state) => state.faqLoading,
    isInitialized: (state) => state.initialized,
  },

  actions: {
    async fetchCategories() {
      // 如果已经加载过数据，不重复加载
      if (this.initialized && this.categories.length > 0) {
        return this.categories;
      }

      try {
        this.loading = true;
        const { code, data } = await HelpApi.getList();

        if (code === 0 && data) {
          this.categories = data;
          this.initialized = true;
        }

        return this.categories;
      } catch (error) {
        console.error('Failed to fetch help categories:', error);
        return [];
      } finally {
        this.loading = false;
      }
    },

    async fetchFaqs(page = 1, pageSize = 10) {
      try {
        this.faqLoading = true;
        this.faqCurrentPage = page;
        this.faqPageSize = pageSize;

        const { code, data } = await HelpApi.getHelpPage(page, pageSize);

        if (code === 0 && data) {
          this.faqs = data.list || [];
          this.faqTotal = data.total || 0;
        }

        return {
          list: this.faqs,
          total: this.faqTotal,
        };
      } catch (error) {
        console.error('Failed to fetch FAQ list:', error);
        return {
          list: [],
          total: 0,
        };
      } finally {
        this.faqLoading = false;
      }
    },

    // 重置状态，强制重新加载数据
    resetState() {
      this.categories = [];
      this.initialized = false;
    },

    // 重置FAQ状态
    resetFaqState() {
      this.faqs = [];
      this.faqTotal = 0;
      this.faqCurrentPage = 1;
    },
  },
});
