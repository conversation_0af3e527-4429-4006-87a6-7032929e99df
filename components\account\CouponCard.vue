<template>
  <q-card :class="['coupon-card', { 'coupon-used': status === 'used', 'coupon-expired': status === 'expired', 'coupon-compact': compact }]">
    <!-- 优惠券头部 -->
    <q-card-section class="coupon-header row items-center no-wrap q-py-sm">
      <div class="coupon-amount text-h5 text-weight-bold">
        <template v-if="coupon.discountType === 1"> <span class="text-subtitle2">￥</span>{{ fen2yuan(coupon.discountPrice) }} </template>
        <template v-else-if="coupon.discountType === 2">
          <span class="text-h6">{{ (coupon.discountPercent / 10).toFixed(1) }}</span>
          <span class="text-subtitle2">折</span>
        </template>
      </div>
      <q-space />
      <div class="coupon-type">
        {{ getCouponTypeName(coupon.discountType) }}
      </div>
    </q-card-section>

    <q-separator />

    <!-- 优惠券内容 -->
    <q-card-section class="coupon-content q-py-xs">
      <div class="text-body1 text-weight-medium ellipsis-2-lines">{{ coupon.name }}</div>
      <div class="text-caption" v-if="coupon.discountType === 2 && coupon.discountLimitPrice > 0">最高优惠 {{ fen2yuan(coupon.discountLimitPrice) }} 元</div>
      <div class="row justify-between items-center q-mt-xs">
        <div class="text-caption">有效期：{{ formatDate(coupon.validStartTime) }} 至 {{ formatDate(coupon.validEndTime) }}</div>
        <!-- 根据状态显示不同的按钮或标签 -->
        <template v-if="status === 'available'">
          <q-btn color="primary" label="去使用" dense flat to="/products" />
        </template>
        <q-badge v-else-if="status === 'used'" color="negative" label="已使用" />
        <q-badge v-else-if="status === 'expired'" color="grey" label="已过期" />
      </div>
    </q-card-section>

    <!-- 使用条件 -->
    <q-card-section class="coupon-condition q-py-xs">
      <div class="text-caption" v-if="coupon.usePrice > 0">满 {{ fen2yuan(coupon.usePrice) }} 元可用</div>
      <div v-else class="text-caption text-grey-6">无使用限制</div>
    </q-card-section>
  </q-card>
</template>

<script setup>
import { date } from 'quasar';
import { fen2yuan } from '~/utils/utils';

const props = defineProps({
  coupon: {
    type: Object,
    required: true,
  },
  status: {
    type: String,
    default: 'available', // 'available', 'used', 'expired'
    validator: (value) => ['available', 'used', 'expired'].includes(value),
  },
  compact: {
    type: Boolean,
    default: false,
    description: '是否使用紧凑模式（适用于移动端）',
  },
});

// 格式化日期
function formatDate(timestamp) {
  return date.formatDate(timestamp, 'YYYY-MM-DD');
}

// 获取优惠券类型名称
function getCouponTypeName(type) {
  switch (type) {
    case 1:
      return '满减券';
    case 2:
      return '折扣券';
    default:
      return '优惠券';
  }
}
</script>

<style lang="scss" scoped>
.coupon-card {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  border-radius: 12px;
  height: 100%;
  display: flex;
  flex-direction: column;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  border-left: 4px solid #1976d2;

  &.coupon-compact {
    border-radius: 8px;
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.05);
    border-left-width: 3px;
  }

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
  }

  &.coupon-used {
    opacity: 0.8;
    border-left-color: #9e9e9e;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: repeating-linear-gradient(45deg, rgba(0, 0, 0, 0.03), rgba(0, 0, 0, 0.03) 10px, rgba(0, 0, 0, 0.06) 10px, rgba(0, 0, 0, 0.06) 20px);
      pointer-events: none;
    }
  }

  &.coupon-expired {
    opacity: 0.7;
    border-left-color: #9e9e9e;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: repeating-linear-gradient(45deg, rgba(0, 0, 0, 0.02), rgba(0, 0, 0, 0.02) 10px, rgba(0, 0, 0, 0.04) 10px, rgba(0, 0, 0, 0.04) 20px);
      pointer-events: none;
    }
  }

  // 添加优惠券边缘的锯齿效果
  &::before,
  &::after {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    height: 8px;
    z-index: 1;
  }

  &::before {
    top: 50px; // 与头部高度一致
    background-image: radial-gradient(circle at 6px 0, transparent 6px, white 6px);
    background-size: 12px 8px;
    background-position: -6px 0;

    .coupon-compact & {
      top: 40px; // 紧凑模式下头部高度
      background-size: 10px 6px;
    }
  }
}

.coupon-header {
  background: linear-gradient(135deg, #1976d2 0%, #64b5f6 100%);
  height: 50px;
  color: white;
  position: relative;

  .coupon-compact & {
    height: 40px;
  }
}

.coupon-amount {
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);

  .coupon-compact & {
    font-size: 1.25rem !important;

    .text-subtitle2 {
      font-size: 0.875rem !important;
    }

    .text-h6 {
      font-size: 1.125rem !important;
    }
  }
}

.coupon-type {
  background-color: rgba(255, 255, 255, 0.2);
  padding: 3px 6px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;

  .coupon-compact & {
    padding: 2px 4px;
    font-size: 0.7rem;
  }
}

.coupon-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 10px 16px;
  min-height: 90px;

  .row {
    margin-top: auto;
  }

  .coupon-compact & {
    padding: 8px 12px;
    min-height: 70px;

    .text-body1 {
      font-size: 0.875rem !important;
    }

    .text-caption {
      font-size: 0.7rem !important;
    }

    .q-btn {
      padding: 4px 8px;
      min-height: 24px;
      font-size: 0.75rem;
    }
  }
}

.coupon-condition {
  border-top: 1px dashed #ddd;
  height: 30px;
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  padding: 0 16px;
  font-size: 0.75rem;
  color: #757575;

  .coupon-compact & {
    height: 24px;
    padding: 0 12px;
    font-size: 0.7rem;
  }
}

.ellipsis-2-lines {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  min-height: 2em;
  line-height: 1.2;
}
</style>
