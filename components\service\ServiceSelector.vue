<template>
  <!-- 服务选择按钮 -->
  <div class="service-selector-container">
    <q-btn outline color="primary" class="service-dropdown" @click="openServiceDialog" size="sm">
      <div class="row items-center no-wrap">
        <q-icon name="local_offer" size="12px" class="q-mr-xxs" />
        <span class="service-btn-text">选服务</span>
        <q-icon name="expand_more" size="12px" class="q-ml-xxs" />
      </div>
    </q-btn>

    <!-- 已选服务展示区域 -->
    <div v-if="modelValue && modelValue.length > 0" class="selected-services-container q-ml-sm">
      <div class="service-chips-wrapper">
        <q-chip
          v-for="(service, serviceIndex) in modelValue"
          :key="serviceIndex"
          dense
          size="sm"
          removable
          @remove="removeService(serviceIndex)"
          :color="service.free ? 'green-7' : 'orange-8'"
          text-color="white"
          class="service-chip"
          style="padding-right: 8px">
          <q-icon :name="service.free ? 'card_giftcard' : 'paid'" size="12px" class="q-mr-xs" />
          {{ service.name }}
          <span v-if="!service.free" class="q-ml-xs">¥{{ fen2yuanSimple(service.price) }}</span>
        </q-chip>
      </div>
    </div>
  </div>

  <!-- 服务选择弹窗 -->
  <q-dialog v-model="serviceDialogVisible" persistent>
    <q-card style="width: 420px; max-width: 90vw" class="service-dialog">
      <q-card-section class="row items-center bg-primary text-white q-py-sm">
        <div class="text-subtitle1 q-ml-sm">选择服务</div>
        <q-space />
        <q-btn icon="close" flat round dense v-close-popup color="white" />
      </q-card-section>

      <q-card-section class="q-pa-sm q-pt-md">
        <!-- 免费服务 -->
        <div class="service-section">
          <div class="service-title row items-center q-mb-sm">
            <q-icon name="card_giftcard" color="green-7" size="18px" class="q-mr-sm" />
            <div class="text-subtitle2 text-weight-medium">免费服务</div>
          </div>
          <div class="service-options">
            <q-item v-for="service in freeServices" :key="service.id" dense class="q-pa-none q-my-none service-item cursor-pointer q-ml-md" @click="toggleServiceSelection(service, true)" clickable>
              <q-item-section avatar class="items-center">
                <q-checkbox
                  v-model="selectedFreeServices"
                  :val="service"
                  :disable="isServiceSelected(service.id) && !selectedFreeServices.includes(service)"
                  color="green-7"
                  size="xs"
                  class="service-checkbox"
                  @click.stop />
              </q-item-section>
              <q-item-section>
                <q-item-label>{{ service.name }}</q-item-label>
                <q-item-label caption>{{ service.description }}</q-item-label>
              </q-item-section>
              <q-item-section side>
                <q-badge color="green-7" text-color="white" label="免费" class="service-badge q-mr-md" />
              </q-item-section>
            </q-item>
          </div>
        </div>

        <!-- 收费服务 -->
        <div class="service-section q-mt-md">
          <div class="service-title row items-center q-mb-sm">
            <q-icon name="paid" color="orange-8" size="18px" class="q-mr-sm" />
            <div class="text-subtitle2 text-weight-medium">收费服务</div>
          </div>
          <div class="service-options">
            <q-item v-for="service in chargeServices" :key="service.id" dense class="q-pa-none q-my-none service-item cursor-pointer q-ml-md" @click="toggleServiceSelection(service, false)" clickable>
              <q-item-section avatar class="items-center">
                <q-checkbox
                  v-model="selectedChargeServices"
                  :val="service"
                  :disable="isServiceSelected(service.id) && !selectedChargeServices.includes(service)"
                  color="orange-8"
                  size="xs"
                  class="service-checkbox"
                  @click.stop />
              </q-item-section>
              <q-item-section>
                <q-item-label>{{ service.name }}</q-item-label>
                <q-item-label caption>{{ service.description }}</q-item-label>
              </q-item-section>
              <q-item-section side>
                <q-badge color="orange-8" text-color="white" :label="`¥ ${fen2yuanSimple(service.price)}`" class="service-badge q-mr-md" />
              </q-item-section>
            </q-item>
          </div>
        </div>
      </q-card-section>

      <q-separator spaced="false" />

      <q-card-actions align="right" class="q-py-sm q-px-md">
        <q-btn flat label="取消" color="grey-7" v-close-popup />
        <q-btn unelevated label="确定" color="primary" class="q-px-md" @click="confirmServiceSelection" />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { fen2yuanSimple } from '../../utils/utils';

const props = defineProps({
  // 已选择的服务列表
  modelValue: {
    type: Array,
    default: () => [],
  },
  // 免费服务列表
  freeServices: {
    type: Array,
    default: () => [],
  },
  // 收费服务列表
  chargeServices: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(['update:modelValue']);

// 服务选择相关状态
const serviceDialogVisible = ref(false);
const selectedFreeServices = ref([]);
const selectedChargeServices = ref([]);

// 监听modelValue变化，更新内部状态
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal && !serviceDialogVisible.value) {
      // 当弹窗关闭时，同步外部数据到内部状态
      syncSelectedServices();
    }
  },
  { deep: true }
);

// 打开服务选择弹窗
const openServiceDialog = () => {
  // 清空之前的选择
  selectedFreeServices.value = [];
  selectedChargeServices.value = [];

  // 如果已经有选择的服务，预先选中
  syncSelectedServices();

  serviceDialogVisible.value = true;
};

// 同步已选服务到内部状态
const syncSelectedServices = () => {
  if (props.modelValue && props.modelValue.length > 0) {
    props.modelValue.forEach((service) => {
      if (service.free) {
        if (!selectedFreeServices.value.some((s) => s.id === service.id)) {
          selectedFreeServices.value.push(service);
        }
      } else {
        if (!selectedChargeServices.value.some((s) => s.id === service.id)) {
          selectedChargeServices.value.push(service);
        }
      }
    });
  }
};

// 检查服务是否已被选中
const isServiceSelected = (serviceId) => {
  // 检查当前选择的服务中是否已包含该服务
  const inFreeServices = selectedFreeServices.value.some((service) => service.id === serviceId);
  const inChargeServices = selectedChargeServices.value.some((service) => service.id === serviceId);

  return inFreeServices || inChargeServices;
};

// 点击整行时切换服务选择状态
const toggleServiceSelection = (service, isFree) => {
  // 确定使用哪个服务数组
  const serviceArray = isFree ? selectedFreeServices : selectedChargeServices;

  // 检查服务是否已经在选中列表中
  const index = serviceArray.value.findIndex((s) => s.id === service.id);

  // 如果已选中，则移除；否则添加
  if (index >= 0) {
    serviceArray.value.splice(index, 1);
  } else {
    // 检查是否已在另一个列表中选中（防止同一服务同时出现在两个列表中）
    const otherArray = isFree ? selectedChargeServices : selectedFreeServices;
    const otherIndex = otherArray.value.findIndex((s) => s.id === service.id);

    if (otherIndex < 0) {
      serviceArray.value.push(service);
    }
  }
};

// 确认服务选择
const confirmServiceSelection = () => {
  // 合并免费和收费服务
  const selectedServices = [...selectedFreeServices.value, ...selectedChargeServices.value];

  // 更新父组件的值
  emit('update:modelValue', selectedServices);

  // 关闭弹窗
  serviceDialogVisible.value = false;
};

// 移除服务
const removeService = (serviceIndex) => {
  const newServices = [...props.modelValue];
  newServices.splice(serviceIndex, 1);
  emit('update:modelValue', newServices);
};
</script>

<style lang="scss" scoped>
.service-selector-container {
  display: flex;
  align-items: center; /* 改为居中对齐 */
}

.selected-services-container {
  flex: 1;
  overflow: hidden;
  display: flex;
  align-items: center; /* 确保内容也是居中对齐 */
}

.service-chips-wrapper {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin: -2px; /* 负边距，用于抵消芯片的外边距，使布局更紧凑 */

  /* 确保芯片有一致的边距 */
  .q-chip {
    margin: 2px;
  }
}

.service-dropdown {
  height: 24px;
  min-height: 24px;
  padding: 0 4px; /* 减小左右内边距，使按钮更紧凑 */
  min-width: 80px; /* 减小最小宽度 */
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  margin: 2px 0; /* 添加垂直边距，与芯片保持一致 */
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background-color: #f0f8ff;
    border-color: #1976d2;
  }

  .service-btn-text {
    font-size: 12px;
    font-weight: normal;
    line-height: 1;
  }
}

.service-chip {
  font-size: 11px;
  height: 24px;
  white-space: nowrap;
  display: inline-flex;
  align-items: center;
  padding: 0 8px;
}

/* 自定义超小间距类 */
.q-mr-xxs {
  margin-right: 2px !important;
}

.q-ml-xxs {
  margin-left: 2px !important;
}

// 服务弹窗样式
.service-dialog {
  border-radius: 8px;
  overflow: hidden;

  .service-section {
    margin-bottom: 8px;

    .service-title {
      font-weight: 500;
      padding-bottom: 6px;
      border-bottom: 1px dashed #e0e0e0;
      font-size: 14px;
    }

    .service-options {
      padding: 4px 0;

      .q-item {
        border-radius: 4px;
        transition: background-color 0.2s ease;

        &:hover {
          background-color: #f5f5f5;
        }
      }

      .service-item {
        display: flex;
        align-items: center;
        padding: 4px 0;
        min-height: 32px;

        .q-item__section--avatar {
          min-width: 32px;
          padding-right: 0;
          align-self: center;
        }

        .q-item__section--side {
          padding-left: 4px;
        }

        .q-item-label {
          font-size: 13px;
          line-height: 1.2;
        }

        .q-item-label--caption {
          font-size: 11px;
          line-height: 1.1;
        }
      }

      .service-checkbox {
        margin: 0;
      }

      .service-badge {
        font-size: 11px;
        padding: 2px 4px;
      }
    }
  }
}
</style>
