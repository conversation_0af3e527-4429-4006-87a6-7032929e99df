<template>
  <div class="blog-card" @click="navigate(blog.link)">
    <div class="image-container">
      <img :src="blog.image" :alt="blog.title" class="item-image" />
    </div>
    <div class="item-content">
      <div class="content-top">
        <h3 class="item-title">{{ blog.title }}</h3>
        <p class="item-description">{{ blog.description }}</p>
      </div>
      <div class="item-meta">
        <span class="item-author">
          <q-icon name="person" size="16px" class="q-mr-xs" />
          {{ blog.author }}
        </span>
        <span class="item-date">{{ blog.date }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router';
// Props
const props = defineProps({
  blog: {
    type: Object,
    required: true,
  },
  index: {
    type: Number,
    required: true,
  },
});
const router = useRouter();

const navigate = (path) => {
  router.push(path);
};
</script>

<style lang="scss" scoped>
.blog-card {
  display: flex;
  height: 120px;
  background-color: #f9f9f9;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }

  .image-container {
    width: 160px;
    min-width: 160px;
    height: 120px;
    overflow: hidden;

    .item-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.5s ease;

      &:hover {
        transform: scale(1.05);
      }
    }
  }

  .item-content {
    padding: 10px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    flex: 1;
    overflow: hidden;

    .content-top {
      overflow: hidden;
    }

    .item-title {
      font-size: 16px;
      font-weight: bold;
      margin: 0 0 6px 0;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .item-description {
      font-size: 14px;
      color: #666;
      margin: 0 0 8px 0;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      line-height: 1.3;
    }

    .item-meta {
      display: flex;
      justify-content: space-between;
      font-size: 12px;
      color: #999;

      .item-author {
        display: flex;
        align-items: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 60%;
      }

      .item-date {
        white-space: nowrap;
      }
    }
  }
}

@media (max-width: 599px) {
  .blog-card {
    height: 110px;

    .image-container {
      width: 120px;
      min-width: 120px;
      height: 110px;
    }

    .item-content {
      padding: 8px;

      .item-title {
        font-size: 15px;
        margin-bottom: 4px;
      }

      .item-description {
        font-size: 13px;
        -webkit-line-clamp: 2;
        margin-bottom: 6px;
      }

      .item-meta {
        font-size: 11px;
      }
    }
  }
}
</style>
