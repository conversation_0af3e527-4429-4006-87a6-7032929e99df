<template>
  <HeaderSimple />

  <div class="mail-verify-page">
    <div class="mail-verify-container">
      <div class="mail-verify-card">
        <div class="mail-verify-content">
          <q-img src="/images/mail-box.svg" alt="Mailbox Verification" class="mail-verify-image" />

          <h1 class="mail-verify-title">{{ $t('verify.mailbox.title') || 'Mailbox Verification' }}</h1>

          <div class="mail-verify-message">
            {{ $t('verify.mailbox.message') || 'A verification email has been sent to your email address:' }}
            <span class="mail-verify-email">{{ email }}</span>
            {{ $t('verify.mailbox.instruction') || 'Please check your email to complete the activation.' }}
          </div>

          <div class="mail-verify-help">
            <div class="mail-verify-help-text">
              {{ $t('verify.mailbox.help') || 'If you do not receive the email, please check your Junk Box or click' }}
            </div>
            <q-btn color="primary" :label="$t('verify.mailbox.resend') || 'Resend Email'" class="mail-verify-resend-btn" @click="resendEmail" :loading="sending" :disable="countdown > 0" />
            <div v-if="countdown > 0" class="mail-verify-countdown">{{ $t('verify.mailbox.resendIn') || 'Resend in' }} {{ countdown }} {{ $t('verify.mailbox.seconds') || 'seconds' }}</div>
          </div>

          <div class="mail-verify-actions">
            <q-btn outline color="primary" :label="$t('verify.mailbox.backToLogin') || 'Back to Login'" class="mail-verify-back-btn" @click="goToLogin" />
            <q-btn color="primary" :label="$t('verify.mailbox.goToHome') || 'Go to Homepage'" class="mail-verify-home-btn" @click="goToHome" />
          </div>
        </div>
      </div>
    </div>
  </div>

  <Footer />
</template>

<script setup>
import { useUserStore } from '~/store/user';
import AuthApi from '~/composables/authApi';
import { useNuxtApp } from '#app';
import { useI18n } from 'vue-i18n';

definePageMeta({
  middleware: 'auth', // 引入身份验证中间件
});

const { t } = useI18n();
const nuxtApp = useNuxtApp();
const router = useRouter();

// 用户邮箱
const userStore = useUserStore();
const email = computed(() => userStore.userInfo.email);

// 状态变量
const sending = ref(false);
const countdown = ref(0);

// 重发邮件
const resendEmail = async () => {
  if (countdown.value > 0) return;

  try {
    sending.value = true;
    console.log('Resend email to:', email.value);
    const res = await AuthApi.resend({ userId: useUserStore().user.id });

    if (res.code === 0) {
      nuxtApp.$showNotify({ msg: t('verify.mailbox.resendSuccess') || 'Verification email has been resent successfully' });
      startCountdown();
    } else {
      nuxtApp.$showNotify({ msg: res.msg || t('verify.mailbox.resendError') || 'Failed to resend email', type: 'negative' });
    }
  } catch (error) {
    console.error('Error resending email:', error);
    nuxtApp.$showNotify({
      msg: t('verify.mailbox.resendError') || 'Failed to resend email',
      type: 'negative',
    });
  } finally {
    sending.value = false;
  }
};

// 倒计时功能
const startCountdown = () => {
  countdown.value = 60; // 60秒倒计时
  const interval = setInterval(() => {
    countdown.value -= 1;
    if (countdown.value <= 0) {
      clearInterval(interval);
    }
  }, 1000);
};

// 返回登录页
const goToLogin = () => {
  router.push('/login');
};

// 返回首页
const goToHome = () => {
  router.push('/');
};
</script>

<style lang="scss" scoped>
.mail-verify-page {
  width: 100%;
  padding: 30px 20px;
  min-height: 70vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mail-verify-container {
  max-width: 700px;
  width: 100%;
  margin: 0 auto;
}

.mail-verify-card {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  padding: 40px;

  @media (max-width: 599px) {
    padding: 25px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }
}

.mail-verify-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.mail-verify-image {
  width: 150px;
  height: auto;
  margin-bottom: 30px;

  @media (max-width: 599px) {
    width: 120px;
    margin-bottom: 20px;
  }
}

.mail-verify-title {
  font-size: 2rem;
  font-weight: 600;
  color: #1976d2;
  margin-bottom: 25px;

  @media (max-width: 599px) {
    font-size: 1.5rem;
    margin-bottom: 20px;
  }
}

.mail-verify-message {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #333;
  margin-bottom: 30px;
  max-width: 550px;

  @media (max-width: 599px) {
    font-size: 1rem;
    margin-bottom: 25px;
  }
}

.mail-verify-email {
  font-weight: 600;
  color: #1976d2;
  display: block;
  margin: 8px 0;
}

.mail-verify-help {
  margin-bottom: 30px;

  @media (max-width: 599px) {
    margin-bottom: 25px;
  }
}

.mail-verify-help-text {
  margin-bottom: 15px;
  color: #666;

  @media (max-width: 599px) {
    margin-bottom: 12px;
  }
}

.mail-verify-resend-btn {
  min-width: 150px;
  height: 40px;
}

.mail-verify-countdown {
  margin-top: 10px;
  font-size: 0.9rem;
  color: #666;
}

.mail-verify-actions {
  display: flex;
  gap: 15px;

  @media (max-width: 599px) {
    flex-direction: column;
    width: 100%;
    gap: 10px;
  }

  .mail-verify-back-btn,
  .mail-verify-home-btn {
    min-width: 150px;
    height: 40px;

    @media (max-width: 599px) {
      width: 100%;
    }
  }
}
</style>
