<template>
  <div class="goods-item" @click="$emit('click')">
    <div class="goods-image">
      <q-img :src="goodsData.picUrl || '/images/placeholder.png'" :ratio="1" />
    </div>
    <div class="goods-info">
      <div class="goods-name">{{ goodsData.spuName || goodsData.name || '商品名称' }}</div>
      <div class="goods-desc" v-if="goodsData.introduction">{{ goodsData.introduction }}</div>
      <div class="goods-price">¥{{ formatPrice(goodsData.price) }}</div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  goodsData: {
    type: Object,
    default: () => ({
      id: '',
      spuName: '',
      name: '商品名称',
      price: 0,
      picUrl: '',
      introduction: '',
    }),
  },
});

defineEmits(['click']);

// 格式化价格
function formatPrice(price) {
  if (!price && price !== 0) return '0.00';
  return Number(price).toFixed(2);
}
</script>

<style scoped lang="scss">
.goods-item {
  display: flex;
  flex-direction: column;
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
  background-color: #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  }

  .goods-image {
    width: 100%;
    height: 120px;
    overflow: hidden;
  }

  .goods-info {
    padding: 8px;

    .goods-name {
      font-size: 14px;
      font-weight: 500;
      color: #333;
      margin-bottom: 4px;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    .goods-desc {
      font-size: 12px;
      color: #666;
      margin-bottom: 4px;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
    }

    .goods-price {
      font-size: 16px;
      font-weight: 600;
      color: #ff3000;
    }
  }
}
</style>
