import { useFetch, useRuntimeConfig } from '#app';
import { useAuthStore } from '~/store/auth';
 
interface RequestOptions {
  custom?: {
    showSuccess?: false; // 是否显示成功提示
    showError?: false;   // 是否显示失败提示
    successMsg?: string;   // 自定义成功提示内容
    errorMsg?: string;     // 自定义失败提示内容
    auth?: false;        // 是否需要鉴权
  };
  headers?: Record<string, string>;
  [key: string]: any;
}
 
type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE';

// 自定义方法：显示全局提示
const showToast = ({ message, type = 'info', }: { message: string; type?: string;}) => {
  showNotify(msg,type);
};
 
const useLanguage = () => {
  if (process.server) {
    const cookies = useCookie('language');
    return cookies.value || 'en'; // 默认语言为 'en'
  } else if (process.client) {
    return localStorage.getItem('language') || 'en'; // 默认语言为 'en'    // 客户端从 localStorage 获取语言
  }
};

// 请求拦截器
function handleRequest(options: RequestOptions) {
  const selectedLanguage = useLanguage() || 'en'; 
  const token = useAuthStore().useToken();
  const config = useRuntimeConfig();

  options.headers = {
    ...options.headers,
    'Content-Type': 'application/json',
    ...(token ? { Authorization: `Bearer ${token}` } : {}), // 动态添加 Authorization
    'tenant-id': globalConfig.tenantId,      // 添加租户 ID
    lang: selectedLanguage,                   // 动态语言
  };

  // 如果需要鉴权但用户未登录，显示提示并中断请求  理论上服务端请求不应该涉及token登录等问题
  // if (options.custom?.auth && !useAuthStore().isLogin) {
  //   // showToast({ message: '请先登录后再操作', type: 'warning' });
  //   throw new Error('用户未登录，已拒绝请求。');
  // }

}


 
// 响应拦截器
function handleResponse(response: any, customOptions: RequestOptions['custom'], url: string, options: RequestOptions) {
  const resCode = response.code ?? {}; // 解析返回数据
  const resData = response.data ?? {}; // 解析返回数据
  const resMsg = response.msg ?? {};

  // 自动处理登录相关接口的 Token
  if (url && typeof url === 'string' && url.includes('/member/auth/') && resData.accessToken) {
    useAuthStore().saveToken(resData.accessToken, resData.refreshToken);
  }

  if (resCode !== 0) {
    if (resCode === 401) {
      return refreshToken(url, options); // 刷新 Token 并重试请求
    }

    if (process.client && customOptions?.showError) {
      let errorMessage = '网络请求出错';
      if(customOptions?.errorMsg || resMsg){
        errorMessage = customOptions.errorMsg || resMsg;
      }else{
        switch (resCode) {
          case 400:
            errorMessage = '请求错误';
            break;
          case 403:
            errorMessage = '拒绝访问';
            break;
          case 404:
            errorMessage = '请求出错';
            break;
          case 408:
            errorMessage = '请求超时';
            break;
          case 429:
            errorMessage = '请求频繁, 请稍后再访问';
            break;
          case 500:
            errorMessage = '服务器开小差啦,请稍后再试~';
            break;
          case 501:
            errorMessage = '服务未实现';
            break;
          case 502:
            errorMessage = '网络错误';
            break;
          case 503:
            errorMessage = '服务不可用';
            break;
          case 504:
            errorMessage = '网络超时';
            break;
          case 505:
            errorMessage = 'HTTP 版本不受支持';
            break;
        }
        if (resMsg.includes('timeout')) errorMessage = '请求超时';
        if (resMsg.includes('Network')) errorMessage = '服务器异常';
      }
      showToast({ message: errorMessage , type: 'error' });
    }
  }

  // if (response.error) {
  //   throw new Error(response.error.message || '响应错误');
  // }
  return response;
}
 
/**
 * 创建请求方法
 * @param method
 */
function createUseFetchRequest(method: HttpMethod) {
  return async function (
    url: string,
    options: RequestOptions = {}
  ) {
    if (!url || typeof url !== 'string') {
      throw new Error('Invalid URL provided for the request');
    }
    const baseURL = useRuntimeConfig().public.baseUrl + useRuntimeConfig().public.apiPath;
    const requestUrl = `${baseURL.replace(/\/$/, '')}/${url.replace(/^\//, '')}`;
 
    // console.log('Request URL:', requestUrl);
    try {
      // 处理请求
      handleRequest(options);
      const { custom, body, ...fetchOptions } = options;

      const response = await useFetch(requestUrl, {
          key: requestUrl, // 确保唯一缓存键
          method,
          body,
          ...fetchOptions,
          onRequest: (ctx) => {
            //处理请求
          },
          onResponse: (ctx) => {
            // return handleResponse(response.data.value, custom, requestUrl, { ...options, method });
          },
          onResponseError: (ctx) => {
            console.error('Response Error:', ctx.response);
          },
        });

        // console.log('requestUrl:', requestUrl);
        // console.log('Response1:', response);
        // console.log('Response2:', response.data.value);

      return handleResponse(response.data.value, custom, requestUrl, { ...options, method });
    } catch (error) {
      throw error;
    }
  };
}

async function refreshToken(originalUrl: string, originalOptions: RequestOptions) {
  const authStore = useAuthStore();
  const tenantId = globalConfig.tenantId;
  const refreshToken =  useRefreshToken();

  if (!refreshToken) {
    authStore.clearToken();
    useRouter().push('/login');
    throw new Error('刷新 Token 失败：用户未登录');
  }

  try {
    const { public: { baseUrl,apiPath } } = useRuntimeConfig();
    const refreshResponse: any = await $fetch(`${baseUrl}${apiPath}/member/auth/refresh-token`, {
      method: 'POST',
      params: { refreshToken },
      headers: {
        'Content-Type': 'application/json',
        'tenant-id': tenantId, // 添加租户 ID
      },
    });

    if (refreshResponse.code === 0 && refreshResponse.data?.accessToken) {
      useAuthStore().saveToken(refreshResponse.data.accessToken, refreshResponse.data.refreshToken);

      originalOptions.headers = {
        ...originalOptions.headers,
        Authorization: `Bearer ${refreshResponse.data.accessToken}`,
      };
      // 使用原始请求方法重试请求
      return await $fetch(originalUrl, {
        ...originalOptions,
        method: originalOptions.method || 'GET', // 确保请求方法正确
      });
    } else {
      throw new Error('刷新 Token 失败');
    }
  } catch (error) {
    console.error('刷新 Token 请求失败:', error);
    authStore.clearToken();
    throw error;
  }
}
 
// 提供 useFetch & HTTP 方法 - 统一管理请求 - 再到组件中使用
export const useServerGet = createUseFetchRequest('GET');
export const useServerPost = createUseFetchRequest('POST');
export const useServerPut = createUseFetchRequest('PUT');
export const useServerDelete = createUseFetchRequest('DELETE');