<template>
  <div 
    class="optimized-image-container" 
    :style="containerStyle"
    :class="containerClass"
  >
    <!-- 占位符 -->
    <div 
      v-if="showPlaceholder" 
      class="image-placeholder"
      :style="placeholderStyle"
    >
      <q-skeleton 
        v-if="loading" 
        type="rect" 
        width="100%" 
        height="100%" 
      />
      <div v-else-if="error" class="error-placeholder">
        <q-icon name="broken_image" size="24px" color="grey-5" />
        <span class="error-text">图片加载失败</span>
      </div>
    </div>

    <!-- 实际图片 -->
    <img
      v-show="!showPlaceholder"
      ref="imageRef"
      :src="optimizedSrc"
      :alt="alt"
      :loading="lazy ? 'lazy' : 'eager'"
      :class="imageClass"
      :style="imageStyle"
      @load="onLoad"
      @error="onError"
    />

    <!-- 图片遮罩层（可选） -->
    <div v-if="showOverlay && !showPlaceholder" class="image-overlay">
      <slot name="overlay" />
    </div>
  </div>
</template>

<script setup>
/**
 * 优化的图片组件
 * 
 * 功能：
 * - 懒加载支持
 * - WebP格式优化
 * - 响应式尺寸
 * - 加载状态和错误处理
 * - 占位符显示
 * - 图片压缩和质量控制
 * 
 * 使用示例：
 * <OptimizedImage 
 *   src="/images/product.jpg"
 *   alt="商品图片"
 *   :width="300"
 *   :height="200"
 *   :lazy="true"
 *   :quality="80"
 * />
 */

const props = defineProps({
  // 基础属性
  src: {
    type: String,
    required: true
  },
  alt: {
    type: String,
    default: ''
  },
  
  // 尺寸控制
  width: {
    type: [Number, String],
    default: 'auto'
  },
  height: {
    type: [Number, String],
    default: 'auto'
  },
  aspectRatio: {
    type: String,
    default: null // 例如: '16/9', '1/1'
  },
  
  // 加载控制
  lazy: {
    type: Boolean,
    default: true
  },
  quality: {
    type: Number,
    default: 80,
    validator: (value) => value >= 1 && value <= 100
  },
  
  // 格式控制
  format: {
    type: String,
    default: 'webp', // webp, jpg, png
    validator: (value) => ['webp', 'jpg', 'jpeg', 'png', 'auto'].includes(value)
  },
  
  // 样式控制
  objectFit: {
    type: String,
    default: 'cover', // cover, contain, fill, scale-down, none
    validator: (value) => ['cover', 'contain', 'fill', 'scale-down', 'none'].includes(value)
  },
  rounded: {
    type: [Boolean, String],
    default: false
  },
  
  // 功能控制
  showOverlay: {
    type: Boolean,
    default: false
  },
  enableZoom: {
    type: Boolean,
    default: false
  },
  
  // 占位符控制
  placeholderColor: {
    type: String,
    default: '#f5f5f5'
  }
});

const emit = defineEmits(['load', 'error', 'click']);

// 响应式状态
const loading = ref(true);
const error = ref(false);
const imageRef = ref(null);

// 计算属性
const showPlaceholder = computed(() => loading.value || error.value);

const optimizedSrc = computed(() => {
  if (!props.src) return '';
  
  // 如果是外部链接或base64，直接返回
  if (props.src.startsWith('http') || props.src.startsWith('data:')) {
    return props.src;
  }
  
  // 构建优化参数
  const params = new URLSearchParams();
  
  // 尺寸参数
  if (props.width !== 'auto') {
    params.set('w', String(props.width));
  }
  if (props.height !== 'auto') {
    params.set('h', String(props.height));
  }
  
  // 质量参数
  params.set('q', String(props.quality));
  
  // 格式参数
  if (props.format !== 'auto') {
    params.set('f', props.format);
  }
  
  // 如果没有参数，直接返回原始路径
  if (params.toString() === '') {
    return props.src;
  }
  
  return `${props.src}?${params.toString()}`;
});

const containerStyle = computed(() => {
  const style = {};
  
  if (props.width !== 'auto') {
    style.width = typeof props.width === 'number' ? `${props.width}px` : props.width;
  }
  
  if (props.height !== 'auto') {
    style.height = typeof props.height === 'number' ? `${props.height}px` : props.height;
  }
  
  if (props.aspectRatio) {
    style.aspectRatio = props.aspectRatio;
  }
  
  return style;
});

const containerClass = computed(() => [
  'optimized-image',
  {
    'image-rounded': props.rounded === true,
    'image-zoom': props.enableZoom,
    'image-loading': loading.value,
    'image-error': error.value
  },
  typeof props.rounded === 'string' ? `rounded-${props.rounded}` : ''
]);

const imageStyle = computed(() => ({
  objectFit: props.objectFit,
  width: '100%',
  height: '100%'
}));

const imageClass = computed(() => [
  'responsive-image',
  {
    'lazy-image': props.lazy,
    'fade-in': !loading.value && !error.value
  }
]);

const placeholderStyle = computed(() => ({
  backgroundColor: props.placeholderColor,
  width: '100%',
  height: '100%'
}));

// 事件处理方法
const onLoad = (event) => {
  loading.value = false;
  error.value = false;
  emit('load', event);
};

const onError = (event) => {
  loading.value = false;
  error.value = true;
  console.error('Image load error:', props.src);
  emit('error', event);
};

const onClick = (event) => {
  if (!loading.value && !error.value) {
    emit('click', event);
  }
};

// 生命周期
onMounted(() => {
  // 如果图片已经加载完成（缓存），立即触发load事件
  if (imageRef.value?.complete && imageRef.value?.naturalWidth > 0) {
    onLoad();
  }
});
</script>

<style lang="scss" scoped>
.optimized-image-container {
  position: relative;
  overflow: hidden;
  display: inline-block;
  
  &.image-rounded {
    border-radius: 8px;
  }
  
  &.image-zoom {
    cursor: pointer;
    transition: transform 0.3s ease;
    
    &:hover {
      transform: scale(1.05);
    }
  }
  
  &.image-loading {
    background-color: #f5f5f5;
  }
  
  &.image-error {
    background-color: #fafafa;
  }
}

.image-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .error-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    
    .error-text {
      font-size: 12px;
      color: #999;
    }
  }
}

.responsive-image {
  display: block;
  transition: opacity 0.3s ease;
  
  &.lazy-image {
    opacity: 0;
  }
  
  &.fade-in {
    opacity: 1;
  }
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.5);
  opacity: 0;
  transition: opacity 0.3s ease;
  
  .optimized-image-container:hover & {
    opacity: 1;
  }
}

// 圆角变体
.rounded-sm { border-radius: 4px; }
.rounded-md { border-radius: 8px; }
.rounded-lg { border-radius: 12px; }
.rounded-xl { border-radius: 16px; }
.rounded-full { border-radius: 50%; }

// 响应式设计
@media (max-width: 768px) {
  .optimized-image-container {
    &.image-zoom:hover {
      transform: none; // 移动端禁用缩放效果
    }
  }
}
</style>
