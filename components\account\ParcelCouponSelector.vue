<template>
  <div class="q-mb-lg">
    <div class="text-h6 q-mb-sm">物流优惠券</div>
    <div class="text-caption text-grey-8 q-mb-md">您可以选择使用物流优惠券来减少运费</div>

    <div class="row items-center q-mb-md">
      <div v-if="modelValue" class="col-grow bg-blue-1 rounded-borders q-pa-md">
        <div class="row items-center justify-between">
          <div>
            <div class="text-body1 text-weight-bold">{{ modelValue.name }}</div>
            <div class="text-body2">{{ modelValue.description }} | 有效期至: {{ formatTimestampToWesternDate(modelValue.expireTime) }}</div>
          </div>
          <q-btn flat round color="primary" icon="edit" @click="openDialog" />
        </div>
      </div>
      <div v-else class="col-grow bg-blue-1 rounded-borders q-pa-md text-center">
        <q-btn color="primary" label="选择优惠券" @click="openDialog" />
      </div>
    </div>

    <!-- 优惠券选择弹窗 -->
    <q-dialog v-model="couponDialog">
      <q-card style="width: 700px; max-width: 90vw">
        <q-card-section class="row items-center">
          <div class="text-h6">选择物流优惠券</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-card-section class="q-pt-none">
          <div class="row q-col-gutter-md">
            <div v-for="coupon in availableCoupons" :key="coupon.id" class="col-12">
              <q-card class="cursor-pointer" :class="{ 'bg-blue-1': tempSelectedCoupon && tempSelectedCoupon.id === coupon.id }" @click="selectAndConfirmCoupon(coupon)">
                <q-card-section>
                  <div class="row items-center justify-between">
                    <div>
                      <div class="text-h6 text-primary">{{ coupon.name }}</div>
                      <div class="text-body2">{{ coupon.description }}</div>
                      <div class="text-caption">有效期至: {{ formatTimestampToWesternDate(coupon.expireTime) }}</div>
                    </div>
                    <div class="text-h5 text-weight-bold text-negative">-¥{{ coupon.amount }}</div>
                  </div>
                </q-card-section>
              </q-card>
            </div>
            <div v-if="availableCoupons.length === 0" class="col-12 text-center q-pa-md">
              <div class="text-body1">暂无可用优惠券</div>
            </div>
          </div>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="不使用优惠券" color="primary" @click="clearCoupon" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { formatTimestampToWesternDate } from '~/utils/utils';

// 定义组件接收的属性
const props = defineProps({
  modelValue: {
    type: Object,
    default: null,
  },
  coupons: {
    type: Array,
    required: true,
    default: () => [],
  },
  baseShippingFee: {
    type: [Number, String],
    default: 0,
  },
});

// 定义组件向外发出的事件
const emit = defineEmits(['update:modelValue', 'calculate-discount']);

// 弹窗状态
const couponDialog = ref(false);
const tempSelectedCoupon = ref(null);

// 根据基础运费筛选可用的优惠券
const availableCoupons = computed(() => {
  const baseShippingFee = parseFloat(props.baseShippingFee);
  return props.coupons.filter((coupon) => {
    // 如果优惠券有最低消费限制，检查是否满足
    if (coupon.minAmount > 0) {
      return baseShippingFee >= coupon.minAmount;
    }
    return true;
  });
});

// 打开优惠券选择弹窗
function openDialog() {
  tempSelectedCoupon.value = props.modelValue;
  couponDialog.value = true;
}

// 选择优惠券并确认
function selectAndConfirmCoupon(coupon) {
  tempSelectedCoupon.value = coupon;
  emit('update:modelValue', coupon);
  emit('calculate-discount');
  couponDialog.value = false;
}

// 清除优惠券选择
function clearCoupon() {
  tempSelectedCoupon.value = null;
  emit('update:modelValue', null);
  emit('calculate-discount');
  couponDialog.value = false;
}
</script>

<style lang="scss" scoped>
/* 可以添加特定于此组件的样式 */
</style>
