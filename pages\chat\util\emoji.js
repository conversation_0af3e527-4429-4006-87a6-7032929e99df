// 表情列表
export const emojiList = [
  { name: '😀', file: 'emoji_1.png' },
  { name: '😁', file: 'emoji_2.png' },
  { name: '😂', file: 'emoji_1.png' },
  { name: '🤣', file: 'emoji_2.png' },
  { name: '😃', file: 'emoji_1.png' },
  { name: '😄', file: 'emoji_2.png' },
  { name: '😅', file: 'emoji_1.png' },
  { name: '😆', file: 'emoji_2.png' },
  { name: '😉', file: 'emoji_1.png' },
  { name: '😊', file: 'emoji_2.png' },
  { name: '😋', file: 'emoji_1.png' },
  { name: '😎', file: 'emoji_2.png' },
  { name: '😍', file: 'emoji_1.png' },
  { name: '😘', file: 'emoji_2.png' },
  { name: '😗', file: 'emoji_1.png' },
  { name: '😙', file: 'emoji_2.png' },
  { name: '😚', file: 'emoji_1.png' },
  { name: '🙂', file: 'emoji_2.png' },
  { name: '🤗', file: 'emoji_1.png' },
  { name: '🤩', file: 'emoji_2.png' },
];

// 表情分页
export const emojiPage = [emojiList.slice(0, 10), emojiList.slice(10, 20)];
