<template>
  <div class="logistics-timeline q-pa-md">
    <q-timeline color="primary">
      <q-timeline-entry
        v-for="(item, index) in trackList"
        :key="index"
        :title="formatDateTime(item.logisticsTime)"
        :subtitle="item.logisticsContent"
        :color="index === 0 ? 'primary' : 'grey'"
        :icon="index === 0 ? 'local_shipping' : 'access_time'"
      >
      </q-timeline-entry>
      
      <q-timeline-entry v-if="trackList.length === 0" title="暂无物流信息" color="grey" icon="info">
        <div>暂未查询到物流轨迹信息</div>
      </q-timeline-entry>
    </q-timeline>
  </div>
</template>

<script setup>
const props = defineProps({
  trackList: {
    type: Array,
    default: () => [],
  },
});

// 格式化日期时间
function formatDateTime(timestamp) {
  if (!timestamp) return '';
  const date = new Date(timestamp);
  return date
    .toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    })
    .replace(/\//g, '-');
}
</script>

<style lang="scss" scoped>
.logistics-timeline {
  max-height: 60vh;
  overflow-y: auto;
  
  .q-timeline__title {
    font-weight: 500;
  }
  
  .q-timeline__subtitle {
    white-space: normal;
    line-height: 1.5;
  }
  
  @media (max-width: 599px) {
    max-height: 70vh;
  }
}
</style>
