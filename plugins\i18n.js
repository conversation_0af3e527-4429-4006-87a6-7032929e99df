import { createI18n } from 'vue-i18n';
import en from '@/locales/en.json';
import fr from '@/locales/fr.json';
import zh from '@/locales/zh.json';
import de from '@/locales/de.json';
import es from '@/locales/es.json';
import ar from '@/locales/ar.json';

export default defineNuxtPlugin((nuxtApp) => {
  // 默认语言
  let locale = 'en';

  // 判断客户端还是服务端
  if (process.client) {
    locale = localStorage.getItem('language') || 'en';
  } else if (process.server) {
    const cookie = useCookie('language');
    locale = cookie.value || 'en';
  }
  // console.log('savedLocale', locale);

  const i18n = createI18n({
    legacy: false,
    globalInjection: true,
    locale,
    messages: {
      en: en,
      fr: fr,
      zh: zh,
      de: de,
      es: es,
      ar: ar,
    },
  });
  // vueApp.use(i18n);
  // 注册到 Vue 实例
  nuxtApp.vueApp.use(i18n);

  // 使用 Nuxt 提供的全局注入方式
  nuxtApp.provide('t', i18n.global.t);
});
