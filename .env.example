# 环境变量配置示例
# 复制此文件为 .env 并填入实际值

# ================================
# 服务器配置
# ================================
# API服务器基础URL
SERVER_BASE_URL=https://api.cnfans.com

# API路径前缀
SERVER_API_PATH=/api/v1

# 网站URL
WEB_URL=https://cnfans.com

# ================================
# 应用配置
# ================================
# 商店名称
SHOP_NAME=CNFans

# 租户ID
TENANT_ID=your_tenant_id

# 密钥
SK=your_secret_key

# ================================
# 支付配置
# ================================
# 是否隐藏支付功能
PAY_HIDE=false

# 支付隐藏页面URL
PAY_HIDE_URL=

# 支付返回URL
PAY_RETURN_URL=https://cnfans.com/pay/result

# ================================
# 验证码配置
# ================================
# 是否启用验证码
ENABLE_CAPTCHA=true

# Cloudflare Turnstile站点密钥
CAPTCHA_SITE_KEY=your_turnstile_site_key

# ================================
# 邮箱配置
# ================================
# 客服邮箱
SUPPORT_EMAIL=<EMAIL>

# ================================
# 第三方服务配置
# ================================
# Google Analytics ID
GOOGLE_ANALYTICS_ID=

# Facebook Pixel ID
FACEBOOK_PIXEL_ID=

# ================================
# 数据库配置（如果需要）
# ================================
# 数据库URL
DATABASE_URL=

# Redis URL
REDIS_URL=

# ================================
# 文件存储配置
# ================================
# 文件上传路径
UPLOAD_PATH=/uploads

# 最大文件大小（MB）
MAX_FILE_SIZE=10

# 允许的文件类型
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,webp,pdf,doc,docx

# ================================
# 缓存配置
# ================================
# 缓存过期时间（秒）
CACHE_TTL=3600

# 是否启用缓存
ENABLE_CACHE=true

# ================================
# 日志配置
# ================================
# 日志级别 (error, warn, info, debug)
LOG_LEVEL=info

# 是否启用日志文件
ENABLE_LOG_FILE=true

# ================================
# 安全配置
# ================================
# JWT密钥
JWT_SECRET=your_jwt_secret

# 会话密钥
SESSION_SECRET=your_session_secret

# CORS允许的域名
CORS_ORIGIN=https://cnfans.com

# ================================
# 开发配置
# ================================
# 开发模式
NODE_ENV=development

# 是否启用调试模式
DEBUG=false

# 是否启用热重载
HOT_RELOAD=true

# ================================
# 监控配置
# ================================
# 是否启用性能监控
ENABLE_PERFORMANCE_MONITORING=true

# 是否启用错误监控
ENABLE_ERROR_MONITORING=true

# Sentry DSN（如果使用Sentry）
SENTRY_DSN=

# ================================
# 国际化配置
# ================================
# 默认语言
DEFAULT_LOCALE=zh-CN

# 支持的语言
SUPPORTED_LOCALES=zh-CN,en-US,ja-JP

# ================================
# SEO配置
# ================================
# 网站标题
SITE_TITLE=CNFans - 专业的海外购物代购平台

# 网站描述
SITE_DESCRIPTION=专业的淘宝、1688、京东代购服务，安全可靠的国际物流

# 网站关键词
SITE_KEYWORDS=代购,淘宝代购,1688代购,京东代购,海外购物

# ================================
# 社交媒体配置
# ================================
# Facebook页面URL
FACEBOOK_URL=

# Twitter账号
TWITTER_URL=

# Instagram账号
INSTAGRAM_URL=

# 微信公众号
WECHAT_QR_CODE=

# ================================
# 客服配置
# ================================
# 在线客服URL
CUSTOMER_SERVICE_URL=

# 客服电话
CUSTOMER_SERVICE_PHONE=

# 客服工作时间
CUSTOMER_SERVICE_HOURS=9:00-18:00 (UTC+8)

# ================================
# 物流配置
# ================================
# 默认物流公司
DEFAULT_SHIPPING_COMPANY=

# 物流跟踪API密钥
SHIPPING_TRACKING_API_KEY=

# ================================
# 推送通知配置
# ================================
# Firebase配置
FIREBASE_API_KEY=
FIREBASE_AUTH_DOMAIN=
FIREBASE_PROJECT_ID=

# 推送通知密钥
PUSH_NOTIFICATION_KEY=

# ================================
# 备份配置
# ================================
# 是否启用自动备份
ENABLE_AUTO_BACKUP=false

# 备份存储路径
BACKUP_STORAGE_PATH=

# 备份保留天数
BACKUP_RETENTION_DAYS=30
