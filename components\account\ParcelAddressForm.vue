<template>
  <div>
    <q-form @submit="onSubmit" class="q-gutter-md">
      <div class="row q-col-gutter-md">
        <div class="col-12 col-md-6">
          <q-input
            v-model="form.name"
            label="收件人姓名"
            :rules="[val => !!val || '请输入收件人姓名']"
            outlined
            dense
          />
        </div>
        <div class="col-12 col-md-6">
          <q-input
            v-model="form.phone"
            label="联系电话"
            :rules="[
              val => !!val || '请输入联系电话',
              val => /^1[3-9]\d{9}$/.test(val) || '请输入正确的手机号码'
            ]"
            outlined
            dense
          />
        </div>
      </div>

      <div class="row q-col-gutter-md">
        <div class="col-12 col-md-6">
          <q-input
            v-model="form.province"
            label="省份"
            :rules="[val => !!val || '请输入省份']"
            outlined
            dense
          />
        </div>
        <div class="col-12 col-md-6">
          <q-input
            v-model="form.city"
            label="城市"
            :rules="[val => !!val || '请输入城市']"
            outlined
            dense
          />
        </div>
      </div>

      <q-input
        v-model="form.address"
        label="详细地址"
        :rules="[val => !!val || '请输入详细地址']"
        outlined
        dense
      />

      <div class="row q-col-gutter-md">
        <div class="col-12 col-md-6">
          <q-input
            v-model="form.zipCode"
            label="邮政编码"
            :rules="[
              val => !!val || '请输入邮政编码',
              val => /^\d{6}$/.test(val) || '请输入正确的邮政编码'
            ]"
            outlined
            dense
          />
        </div>
        <div class="col-12 col-md-6 flex items-center">
          <q-checkbox v-model="form.isDefault" label="设为默认地址" />
        </div>
      </div>

      <div class="row justify-end q-mt-md">
        <q-btn label="取消" flat color="grey-7" v-close-popup class="q-mr-sm" />
        <q-btn label="保存" type="submit" color="primary" />
      </div>
    </q-form>
  </div>
</template>

<script setup>
import { ref } from 'vue';

// 定义组件接收的属性
const props = defineProps({
  address: {
    type: Object,
    default: () => ({
      name: '',
      phone: '',
      province: '',
      city: '',
      address: '',
      zipCode: '',
      isDefault: false
    })
  }
});

// 定义组件向外发出的事件
const emit = defineEmits(['submit']);

// 表单数据
const form = ref({
  name: props.address.name || '',
  phone: props.address.phone || '',
  province: props.address.province || '',
  city: props.address.city || '',
  address: props.address.address || '',
  zipCode: props.address.zipCode || '',
  isDefault: props.address.isDefault || false
});

// 提交表单
function onSubmit() {
  emit('submit', { ...form.value, id: props.address.id || Date.now() });
}
</script>
