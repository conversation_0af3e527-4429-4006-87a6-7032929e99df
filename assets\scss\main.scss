/* 清除默认的内外边距 */
body,
input,
img,
div,
ul,
li,
a,
span,
i,
p,
h1,
h2,
h3,
h4,
h5,
h6,
strong,
ins,
em,
button,
del {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
    height: 100%;
    width: 100%;
    font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
    // overflow: hidden;
    margin: 0;
    padding: 0;
    background-color: #fafafa;
}

/* 清除a标签的下划线和字体颜色 */
a {
    text-decoration: none;
    color: #000;
}

/* 清除li标签前的小点点 */
li {
    list-style: none;
}
/* 清除input标签的边框线和外轮廓线 */
input {
    border: none;
    outline: none;
}

/* 导入货币样式 */
@import '../styles/currency.scss';