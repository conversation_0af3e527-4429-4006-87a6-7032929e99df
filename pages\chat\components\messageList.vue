<template>
  <div class="message-list-container">
    <!-- 连接状态提示 -->
    <div v-if="connectionStatus" class="connection-status" :class="connectionStatusClass">
      <q-icon :name="connectionStatusIcon" size="18px" class="q-mr-xs" />
      {{ $t(connectionStatusText) }}
      <div class="connection-status-badge">{{ connectionStatusCount }}</div>
    </div>

    <!-- 聊天消息列表 -->
    <q-scroll-area ref="scrollAreaRef" class="message-scroll-area" :thumb-style="{ right: '2px', borderRadius: '5px', background: '#027be3', width: '5px', opacity: 0.75 }">
      <!-- 加载更多按钮 -->
      <div v-if="hasMoreMessages" class="load-more-container">
        <q-btn flat dense size="sm" color="primary" :label="t('help.chat.loadMore')" @click="loadMoreMessages" />
      </div>

      <!-- 消息列表 -->
      <div class="messages-container">
        <div v-for="(item, index) in messageList" :key="index" class="message-item">
          <MessageListItem :message="item" :message-index="index" :message-list="messageList"></MessageListItem>
        </div>
      </div>

      <!-- 新消息提示 -->
      <q-btn v-if="showNewMessageTip" class="new-message-tip" color="primary" round size="sm" icon="arrow_downward" @click="scrollToBottom">
        <q-tooltip>{{ t('help.chat.newMessageTip') }}</q-tooltip>
      </q-btn>
    </q-scroll-area>

    <!-- 移除底部输入框插槽，由父组件直接控制 -->
  </div>
</template>

<script setup>
import { reactive, ref, onMounted, nextTick, computed } from 'vue';
import MessageListItem from './messageListItem.vue';
import { useQuasar } from 'quasar';
import KefuApi from '~/composables/kefuApi';
import { KeFuMessageContentTypeEnum, UserTypeEnum } from '../util/constants';
import { useI18n } from 'vue-i18n';

const $q = useQuasar();
const { t } = useI18n();
const scrollAreaRef = ref(null);
const messageList = ref([]); // 消息列表
const showNewMessageTip = ref(false); // 显示有新消息提示
const hasMoreMessages = ref(false); // 是否有更多消息

// 连接状态相关
const connectionStatus = ref('connected'); // 'connecting', 'connected', 'reconnecting', 'disconnected'
const connectionStatusCount = ref(2); // 状态计数，用于显示在状态提示上

// 根据连接状态计算样式类
const connectionStatusClass = computed(() => {
  switch (connectionStatus.value) {
    case 'connecting':
      return 'status-connecting';
    case 'connected':
      return 'status-connected';
    case 'reconnecting':
      return 'status-reconnecting';
    case 'disconnected':
      return 'status-disconnected';
    default:
      return '';
  }
});

// 根据连接状态计算图标
const connectionStatusIcon = computed(() => {
  switch (connectionStatus.value) {
    case 'connecting':
      return 'sync';
    case 'connected':
      return 'check_circle';
    case 'reconnecting':
      return 'sync_problem';
    case 'disconnected':
      return 'error_outline';
    default:
      return 'info';
  }
});

// 根据连接状态计算文本
const connectionStatusText = computed(() => {
  switch (connectionStatus.value) {
    case 'connecting':
      return 'help.chat.status.connecting';
    case 'connected':
      return 'help.chat.status.connected';
    case 'reconnecting':
      return 'help.chat.status.reconnecting';
    case 'disconnected':
      return 'help.chat.status.disconnected';
    default:
      return '';
  }
});
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
});

// 获取消息列表
const getMessageList = async () => {
  try {
    // 调用API获取消息列表
    const res = await KefuApi.getKefuMessagePage(queryParams);

    if (res && res.code === 0 && res.data) {
      // 获取消息列表并按时间倒序排列（最新的消息在最后）
      const messages = res.data.list || [];
      messageList.value = messages.sort((a, b) => a.createTime - b.createTime);
      hasMoreMessages.value = messageList.value.length < res.data.total;

      // 滚动到底部
      await nextTick();
      scrollToBottom();

      return res.data;
    } else {
      throw new Error(res?.msg || t('help.chat.getMessageListFailed'));
    }
  } catch (error) {
    console.error(t('help.chat.getMessageListFailed'), error);
    $q.notify({
      message: t('help.chat.getMessageListFailed'),
      color: 'negative',
      position: 'bottom',
    });
    return { list: [], total: 0 };
  }
};

// 加载更多消息
const loadMoreMessages = async () => {
  try {
    queryParams.pageNo++;

    // 调用API获取更多消息
    const res = await KefuApi.getKefuMessagePage(queryParams);

    if (res && res.code === 0 && res.data) {
      // 将新消息添加到列表并保持时间顺序
      if (res.data.list && res.data.list.length > 0) {
        const newMessages = res.data.list;
        // 合并消息并按时间排序
        messageList.value = [...messageList.value, ...newMessages].sort((a, b) => a.createTime - b.createTime);
      }

      // 判断是否还有更多消息
      hasMoreMessages.value = messageList.value.length < res.data.total;

      if (!hasMoreMessages.value) {
        $q.notify({
          message: t('help.chat.noMoreMessages'),
          color: 'info',
          position: 'top',
        });
      }
    } else {
      throw new Error(res?.msg || t('help.chat.loadMoreFailed'));
    }
  } catch (error) {
    console.error(t('help.chat.loadMoreFailed'), error);
    $q.notify({
      message: t('help.chat.loadMoreFailed'),
      color: 'negative',
      position: 'bottom',
    });
  }
};

// 刷新消息列表
const refreshMessageList = async (message = undefined) => {
  console.log('【消息列表】刷新消息列表，消息:', message);

  if (message !== undefined) {
    try {
      console.log('【消息列表】处理单条消息');

      // 确保消息有必要的字段
      const validMessage = {
        id: message.id || Date.now(),
        content: message.content || '',
        contentType: message.contentType || KeFuMessageContentTypeEnum.TEXT,
        userType: message.userType || UserTypeEnum.ADMIN,
        senderType: message.senderType || 2, // 确保senderType字段存在，默认为客服消息
        createTime: message.createTime || new Date().toISOString(),
      };

      console.log('【消息列表】验证后的消息:', validMessage);
      console.log('【消息列表】消息ID:', validMessage.id);
      console.log('【消息列表】消息内容:', validMessage.content);
      console.log('【消息列表】消息类型:', validMessage.contentType);
      console.log('【消息列表】用户类型:', validMessage.userType);
      console.log('【消息列表】发送者类型:', validMessage.senderType, '(1=用户, 2=客服)');
      console.log('【消息列表】创建时间:', validMessage.createTime);

      showNewMessageTip.value = true;

      // 追加新消息
      messageList.value.push(validMessage);
      console.log('【消息列表】消息已添加到列表，当前列表长度:', messageList.value.length);

      // 如果用户正在查看底部，自动滚动到底部
      const scrollArea = scrollAreaRef.value;
      if (scrollArea) {
        console.log('【消息列表】检查滚动位置');
        const { verticalPosition, verticalSize, verticalContainerSize } = scrollArea.getScrollPosition();
        console.log('【消息列表】滚动位置:', verticalPosition, '总高度:', verticalSize, '可见高度:', verticalContainerSize);

        const isAtBottom = verticalPosition + verticalContainerSize >= verticalSize - 100;
        console.log('【消息列表】是否在底部:', isAtBottom);

        if (isAtBottom) {
          console.log('【消息列表】用户在底部，自动滚动到底部');
          await nextTick();
          scrollToBottom();
          showNewMessageTip.value = false;
        } else {
          console.log('【消息列表】用户不在底部，显示新消息提示');
        }
      } else {
        console.warn('【消息列表】滚动区域引用不存在');
      }
    } catch (error) {
      console.error('【消息列表】添加消息到列表失败:', error);
      console.error('【消息列表】错误消息:', error.message);
      console.error('【消息列表】原始消息:', message);

      $q.notify({
        message: t('help.chat.receiveMessageFailed'),
        color: 'negative',
        position: 'bottom',
      });
    }
    return;
  }

  console.log('【消息列表】重新加载所有消息');
  // 重新加载所有消息
  queryParams.pageNo = 1;
  await getMessageList();
};

// 滚动到底部
const scrollToBottom = () => {
  const scrollArea = scrollAreaRef.value;
  if (scrollArea) {
    scrollArea.setScrollPosition('vertical', 1000000);
    showNewMessageTip.value = false;
  }
};

// 组件挂载时加载消息
onMounted(async () => {
  await getMessageList();
});

// 更新连接状态
const updateConnectionStatus = (status, count = null) => {
  connectionStatus.value = status;
  if (count !== null) {
    connectionStatusCount.value = count;
  }

  // 5秒后自动隐藏已连接状态
  if (status === 'connected') {
    setTimeout(() => {
      if (connectionStatus.value === 'connected') {
        connectionStatus.value = '';
      }
    }, 5000);
  }
};

// 暴露方法给父组件
defineExpose({
  getMessageList,
  refreshMessageList,
  scrollToBottom,
  updateConnectionStatus,
});
</script>

<style scoped lang="scss">
.message-list-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  background-color: #f5f7fa;
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23bdbdbd' fill-opacity='0.05' fill-rule='evenodd'/%3E%3C/svg%3E");

  // 连接状态提示样式
  .connection-status {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 10;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;

    // 状态计数徽章
    .connection-status-badge {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background-color: rgba(255, 255, 255, 0.3);
      color: white;
      font-size: 12px;
      font-weight: bold;
      margin-left: 8px;
    }

    // 连接中状态
    &.status-connecting {
      background-color: #ff9800; // 橙色
      color: white;

      .q-icon {
        animation: spin 1.5s linear infinite;
      }
    }

    // 已连接状态
    &.status-connected {
      background-color: #4caf50; // 绿色
      color: white;
    }

    // 重连中状态
    &.status-reconnecting {
      background-color: #ff9800; // 橙色
      color: white;

      .q-icon {
        animation: spin 1.5s linear infinite;
      }
    }

    // 断开连接状态
    &.status-disconnected {
      background-color: #f44336; // 红色
      color: white;
    }
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  .message-scroll-area {
    flex: 1;
    height: calc(100vh - 160px); /* 减去头部和底部输入框的高度 */
    position: relative;
    background-color: #f5f7fa;
    background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23bdbdbd' fill-opacity='0.05' fill-rule='evenodd'/%3E%3C/svg%3E");

    @media (min-width: 768px) {
      height: calc(80vh - 160px);
    }

    .load-more-container {
      display: flex;
      justify-content: center;
      padding: 15px 0;

      .q-btn {
        background-color: rgba(255, 255, 255, 0.8);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.08);
        border-radius: 20px;
        transition: all 0.3s ease;

        &:hover {
          background-color: white;
          box-shadow: 0 3px 8px rgba(0, 0, 0, 0.12);
        }
      }
    }

    .messages-container {
      padding: 15px;

      @media (min-width: 768px) {
        padding: 20px;
      }

      .message-item {
        margin-bottom: 16px;
        animation: fadeIn 0.3s ease-out;
      }
    }

    .new-message-tip {
      position: fixed;
      bottom: 120px;
      right: 20px;
      z-index: 1000;
      box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
      animation: pulse 2s infinite;
    }
  }

  /* 移除input-container样式，由父组件控制 */
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
  }
}
</style>
