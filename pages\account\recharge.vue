<template>
  <div>
    <!-- 标题区域 -->
    <div class="bg-grey-2 row items-center">
      <q-btn flat round icon="arrow_back" color="primary" class="q-ml-xs" to="/account/balance" />
      <span class="q-pa-sm inline-block">账户充值</span>
    </div>

    <div class="q-pa-md">
      <!-- 充值金额选项 -->
      <div class="q-mb-lg">
        <div class="text-subtitle1 q-mb-sm">选择充值金额</div>

        <div class="row q-col-gutter-sm">
          <!-- 预设金额选项 -->
          <div v-for="amount in predefinedAmounts" :key="amount" class="col-4 col-sm-2">
            <q-card flat bordered class="amount-card cursor-pointer text-center" :class="{ 'selected-amount': selectedAmount === amount }" @click="selectAmount(amount)">
              <q-card-section class="q-pa-sm">
                <div class="text-body1">¥{{ amount }}</div>
              </q-card-section>
            </q-card>
          </div>

          <!-- 自定义金额选项 -->
          <div class="col-4 col-sm-2">
            <q-card flat bordered class="amount-card cursor-pointer" :class="{ 'selected-amount': isCustomAmount }" @click="selectCustomAmount">
              <q-card-section class="q-pa-sm text-center">
                <div class="text-body1">自定义金额</div>
              </q-card-section>
            </q-card>
          </div>
        </div>

        <!-- 自定义金额输入框 -->
        <transition name="fade-slide-down">
          <div v-if="isCustomAmount" class="q-pt-md custom-input-container">
            <div class="custom-amount-input-wrapper">
              <q-input
                v-model.number="customAmount"
                type="number"
                label="输入充值金额"
                outlined
                class="custom-amount-input"
                :rules="[(val) => (val !== null && val !== '') || '请输入金额', (val) => val >= 5 || '金额必须大于或等于 ¥5']">
                <template #prepend>
                  <q-icon name="currency_yuan" color="primary" />
                </template>
                <template #append>
                  <q-icon name="check_circle" color="positive" v-if="customAmount && customAmount >= 5" />
                </template>
              </q-input>
              <div class="text-caption text-grey-7 q-mt-xs text-center">请输入您想要充值的金额（最低¥5）</div>
            </div>
          </div>
        </transition>

        <!-- 显示选择的金额 -->
        <div class="selected-amount-display q-mt-lg q-mb-md text-center" v-if="finalAmount > 0">
          <div class="custom-amount-container">
            <span class="text-h5 text-weight-bold custom-amount-display" v-html="formatCustomAmount(finalAmount * 100)"></span>
          </div>
        </div>
      </div>

      <!-- 支付渠道选择 -->
      <PaymentChannelSelector v-model="selectedPaymentChannel" :enableChannels="enablePayMethods" :amount="finalAmount * 100" :orderPrice="finalAmount * 100" />

      <!-- 提交按钮 -->
      <div class="row justify-center q-mt-xl">
        <q-btn color="primary" size="lg" class="payment-submit-btn" :class="{ 'full-width-btn': $q.screen.lt.sm }" :disable="!isFormValid" @click="confirmPayment">
          <q-icon name="payment" class="q-mr-sm" />
          确认支付
        </q-btn>
      </div>

      <!-- 支付状态弹窗组件 -->
      <PaymentStatusDialog
        ref="paymentStatusDialogRef"
        :amount="finalAmount * 100"
        :channel-name="getPaymentChannelName()"
        :format-amount="formatCustomAmount"
        @payment-completed="handlePaymentCompleted"
        @payment-cancelled="handlePaymentCancelled" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useQuasar } from 'quasar';
import PaymentChannelSelector from '~/components/payment/PaymentChannelSelector.vue';
import PaymentStatusDialog from '~/components/payment/PaymentStatusDialog.vue';
import PayApi from '~/composables/payApi';
import { useCurrencyStore } from '~/store/currency';
import { useCurrency } from '~/composables/useCurrency';
import { getPaymentCurrency } from '~/utils/paymentCurrencyUtils';
import { useRouter } from 'vue-router';

definePageMeta({
  middleware: 'auth', // 引入身份验证中间件
});

const router = useRouter();

const returnUrl = globalConfig.payReturnUrl;
let paymentWindow = null; // 支付新窗口

const $q = useQuasar();
const currencyStore = useCurrencyStore();
const { formatAmount } = useCurrency();

// 支付状态弹窗引用
const paymentStatusDialogRef = ref(null);

// 预设金额选项
const predefinedAmounts = [5, 10, 20, 50, 100, 200, 500, 1000];

// 选中的金额
const selectedAmount = ref(null);
const customAmount = ref(null);
const selectedPaymentChannel = ref(null);

// 是否选择了自定义金额
const isCustomAmount = computed(() => {
  return selectedAmount.value === 'custom';
});

// 最终充值金额
const finalAmount = computed(() => {
  if (isCustomAmount.value) {
    return customAmount.value || 0;
  }
  return selectedAmount.value || 0;
});

// 表单是否有效
const isFormValid = computed(() => {
  if (isCustomAmount.value) {
    return customAmount.value !== null && customAmount.value >= 5 && selectedPaymentChannel.value;
  }
  return selectedAmount.value !== null && selectedPaymentChannel.value;
});

const enablePayMethods = ref([]);

onMounted(async () => {
  // 获取可用的支付方式
  enablePayMethods.value = await getEnablePayChannels();

  // 默认选中第一个支付渠道
  if (enablePayMethods.value && enablePayMethods.value.length > 0) {
    selectedPaymentChannel.value = enablePayMethods.value[0].code;
  }

  // 默认选中第一个充值金额选项
  if (predefinedAmounts.length > 0) {
    selectAmount(predefinedAmounts[0]);
  }
});

// 获得支付方式
async function getEnablePayChannels() {
  const { code, data } = await PayApi.getEnablePayChannelList(10); //todo 临时写死  需要对应数据库pay_app的wallet记录的ID
  if (code === 0) {
    return data;
  }
}

// 选择预设金额
function selectAmount(amount) {
  selectedAmount.value = amount;
  customAmount.value = null;
}

// 选择自定义金额
function selectCustomAmount() {
  selectedAmount.value = 'custom';
  if (!customAmount.value) {
    customAmount.value = 5; // 默认最小金额
  }
}

// 确认支付
async function confirmPayment() {
  if (!isFormValid.value) return;

  //创建充值订单
  const { code, data } = await PayApi.createWalletRecharge({
    payPrice: finalAmount.value * 100,
    currency: currencyStore.selectedCurrency.currency,
  });
  if (code !== 0) {
    return;
  }

  const payOrderId = data.payOrderId;

  //获取支付单信息
  const { code: code1 } = await PayApi.getOrder(payOrderId, true);
  if (code1 !== 0) {
    return;
  }

  payByChannel(selectedPaymentChannel.value, payOrderId);
}

//渠道支付
async function payByChannel(channelCode, payOrderId) {
  if (channelCode === 'mock') {
    if (submietOrderPay(payOrderId, channelCode)) {
      // router.push('/account/balance');
      $q.notify({
        color: 'positive',
        message: '支付成功',
        icon: 'check_circle',
      });
    }
  } else {
    // 提前打开一个空白页面，避免浏览器拦截
    paymentWindow = window.open('/loading', '_blank');
    if (!paymentWindow) {
      $q.notify({
        color: 'negative',
        message: '无法打开支付页面，请检查浏览器弹窗设置',
        icon: 'error',
      });
      return;
    }

    // 显示支付状态弹窗
    if (paymentStatusDialogRef.value) {
      paymentStatusDialogRef.value.showDialog();
    }

    try {
      if (submietOrderPay(payOrderId, channelCode)) {
        // 添加事件监听器
        window.addEventListener('message', handleMessage);
        // 在新窗口跳转到支付页面
        paymentWindow.location.href = data.displayContent;
      }
    } catch (_error) {
      if (paymentWindow) {
        paymentWindow.close();
      }
      $q.notify({
        color: 'negative',
        message: '支付请求异常',
        icon: 'error',
      });
    }
  }
}

async function submietOrderPay(payOrderId, channelCode) {
  const { code, data } = await PayApi.submitOrder({
    id: payOrderId,
    channelCode: channelCode,
    returnUrl: returnUrl + '?t=1',
    cancelUrl: returnUrl + '?t=2',
    currency: currencyStore.selectedCurrency.currency,
  });

  if (code === 0) {
    return true;
  }
  return false;
}

// 处理支付完成
function handlePaymentCompleted() {
  // 用户点击"支付完成"
  $q.notify({
    color: 'positive',
    message: '充值成功！正在刷新余额...',
    icon: 'check_circle',
  });

  // 实际应用中，这里应该调用API查询支付状态
  // 成功后跳转到钱包页面
  setTimeout(() => {
    window.location.href = '/account/balance';
  }, 1500);
}

// 处理支付取消
function handlePaymentCancelled() {
  // 用户点击"重新选择支付方式"
  selectedPaymentChannel.value = null;
}

// 获取支付渠道名称
function getPaymentChannelName() {
  const channels = [
    { id: 'credit_card', name: '信用卡' },
    { id: 'paypal', name: 'PayPal' },
    { id: 'stripe', name: 'Stripe' },
    { id: 'wechat', name: '微信支付' },
    { id: 'alipay', name: '支付宝' },
  ];

  const channel = channels.find((c) => c.id === selectedPaymentChannel.value);
  return channel ? channel.name : '未知支付方式';
}

// 定义监听器回调函数
const handleMessage = (event) => {
  // 判断事件类型
  if (event.data === 'payment-completed') {
    // 关闭支付窗口
    if (paymentWindow) {
      paymentWindow.close();
    }

    // 显示支付成功提示
    $q.notify({
      color: 'positive',
      message: '充值成功！正在跳转...',
      icon: 'check_circle',
    });

    // 跳转到余额页面
    setTimeout(() => {
      navigateTo('/account/balance');
    }, 1500);
  } else if (event.data === 'payment-cancelled') {
    // 关闭支付窗口
    if (paymentWindow) {
      paymentWindow.close();
    }
  } else {
    // 如果是未知事件，选择忽略
    return;
  }

  // 移除监听器（防止重复监听）
  window.removeEventListener('message', handleMessage);
};

// 自定义金额格式化函数，美化金额显示
function formatCustomAmount(amountInCents) {
  // 使用formatAmount获取原始格式化结果
  const formattedAmount = formatAmount(amountInCents);

  // 如果是多币种显示（包含默认币种）
  if (formattedAmount.includes('default-currency')) {
    // 提取主币种部分和默认币种部分
    const mainCurrencyMatch = formattedAmount.match(/<span class="primary-currency">(.*?)<span class="default-currency">/);
    const defaultCurrencyMatch = formattedAmount.match(/<span class="default-currency">(.*?)<\/span>/);

    if (mainCurrencyMatch && mainCurrencyMatch[1] && defaultCurrencyMatch && defaultCurrencyMatch[1]) {
      // 保留主币种金额完整显示（包含币种符号）
      const mainAmount = mainCurrencyMatch[1].trim();

      // 提取默认币种金额，增强颜色
      const defaultAmount = defaultCurrencyMatch[1];

      // 返回自定义格式
      return `<span class="primary-currency">${mainAmount}</span> <span class="enhanced-default-currency">${defaultAmount}</span>`;
    }
  }

  // 如果是单币种显示，添加自定义样式
  if (formattedAmount.includes('primary-currency')) {
    return formattedAmount.replace(/<span class="primary-currency">(.*?)<\/span>/, '<span class="primary-currency">$1</span>');
  }

  // 如果是纯文本，添加主币种样式
  return `<span class="primary-currency">${formattedAmount}</span>`;
}

// 在组件销毁时清理资源
onUnmounted(() => {
  window.removeEventListener('message', handleMessage);
  if (paymentWindow) {
    paymentWindow.close();
  }
});
</script>

<style lang="scss" scoped>
.amount-card {
  /* 保留过渡效果用于选中状态变化 */
  transition: background-color 0.2s ease, border-color 0.2s ease, box-shadow 0.2s ease;
  border-radius: 8px;
  height: 100%;
  min-height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;

  .q-card-section {
    padding: 8px;
    width: 100%;
  }

  &:hover {
    background-color: rgba(0, 0, 0, 0.03);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
  }

  &.selected-amount {
    border-color: var(--q-primary) !important;
    background-color: #f0f8ff;
    box-shadow: 0 2px 6px rgba(0, 100, 255, 0.2);
  }
}

.custom-input-container {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 16px;
}

.custom-amount-input-wrapper {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
  padding: 20px;
  border-radius: 12px;
  background: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
  /* 移除过渡效果 */

  /* 移除悬停浮动效果 */

  .custom-amount-input {
    border-radius: 8px;

    :deep(.q-field__control) {
      height: 48px;
    }

    :deep(.q-field__marginal) {
      height: 48px;
    }

    :deep(.q-field__native) {
      font-size: 1.1rem;
      font-weight: 500;
    }
  }
}

.custom-amount-container {
  display: inline-block;
  padding: 12px 24px;
  border-radius: 12px;
  background: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
  /* 移除过渡效果 */

  /* 移除悬停浮动效果 */
}

.custom-amount-display {
  .primary-currency {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1976d2; // 主要蓝色
    margin-right: 4px;
  }

  .enhanced-default-currency {
    font-size: 1.2rem;
    color: #1976d2; // 与主币种保持一致的颜色
    font-weight: 500;
    margin-left: 4px;
    display: inline-block;
  }
}

.payment-submit-btn {
  min-width: 200px;
  padding: 10px 24px;
  border-radius: 8px;
  font-weight: 500;

  &.full-width-btn {
    width: 100%;
    max-width: 400px;
  }
}

@media (max-width: 599px) {
  .q-col-gutter-sm > .col-4 {
    padding: 4px;
  }

  .amount-card {
    min-height: 40px;

    .text-body1 {
      font-size: 14px;
    }
  }
}
</style>

<style>
/* 支付状态弹窗样式已移至组件中 */

/* 自定义金额输入框动画 */
.fade-slide-down-enter-active,
.fade-slide-down-leave-active {
  transition: all 0.3s ease;
}
.fade-slide-down-enter-from,
.fade-slide-down-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}
.fade-slide-down-enter-to,
.fade-slide-down-leave-from {
  opacity: 1;
  transform: translateY(0);
}
</style>
