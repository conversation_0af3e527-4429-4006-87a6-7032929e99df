# 尺码助手使用指南

## 概述

尺码助手是一个帮助用户选择合适尺码的工具，支持多种服装类型、多个国家标准和单位转换。

## 组件结构

```
components/
├── SizeGuideContent.vue     # 核心内容组件（包含所有逻辑和数据）
└── SizeGuideDialog.vue      # 弹窗组件

pages/
└── help/
    └── size-guide.vue       # 帮助中心独立页面
```

## 使用方式

### 1. 弹窗调用（推荐）

#### 基础用法
```vue
<template>
  <div>
    <!-- 触发按钮 -->
    <q-btn @click="showSizeGuide = true">
      尺码助手
    </q-btn>
    
    <!-- 弹窗组件 -->
    <SizeGuideDialog v-model="showSizeGuide" />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import SizeGuideDialog from '~/components/SizeGuideDialog.vue'

const showSizeGuide = ref(false)
</script>
```

#### 指定默认服装类型
```vue
<template>
  <SizeGuideDialog 
    v-model="showSizeGuide" 
    default-category="men's top"
  />
</template>
```

#### 可用的服装类型
- `women's top` - 女士上衣（默认）
- `men's top` - 男士上衣
- `women's pants` - 女士裤子
- `men's pants` - 男士裤子
- `women's shoes` - 女士鞋子
- `men's shoes` - 男士鞋子
- `ring` - 戒指

### 2. 内容组件直接使用

如果需要在页面中直接嵌入尺码助手内容：

```vue
<template>
  <div>
    <SizeGuideContent default-category="women's shoes" />
  </div>
</template>

<script setup>
import SizeGuideContent from '~/components/SizeGuideContent.vue'
</script>
```

### 3. 帮助中心页面

直接访问独立页面：`/help/size-guide`

## 集成示例

### 商品详情页集成

```vue
<template>
  <div class="product-detail">
    <!-- 商品规格选择区域 -->
    <div class="product-specs">
      <div class="spec-item">
        <label>尺码：</label>
        <q-select v-model="selectedSize" :options="sizeOptions" />
        <q-btn 
          flat 
          color="primary" 
          size="sm" 
          @click="showSizeGuide = true"
          class="q-ml-sm"
        >
          尺码助手
        </q-btn>
      </div>
    </div>
    
    <!-- 尺码助手弹窗 -->
    <SizeGuideDialog 
      v-model="showSizeGuide" 
      :default-category="getProductCategory()"
    />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import SizeGuideDialog from '~/components/SizeGuideDialog.vue'

const showSizeGuide = ref(false)
const selectedSize = ref('')
const sizeOptions = ref(['S', 'M', 'L', 'XL'])

// 根据商品信息确定服装类型
function getProductCategory() {
  // 这里可以根据商品的分类信息返回对应的服装类型
  // 例如：根据商品的 categoryId 或 tags 来判断
  return "women's top" // 示例
}
</script>
```

### 购物车页面集成

```vue
<template>
  <div class="cart-item">
    <div class="item-info">
      <div class="item-name">{{ item.name }}</div>
      <div class="item-specs">
        尺码：{{ item.size }}
        <q-btn 
          flat 
          size="xs" 
          color="primary" 
          @click="openSizeGuide(item)"
          icon="straighten"
        >
          尺码参考
        </q-btn>
      </div>
    </div>
    
    <SizeGuideDialog 
      v-model="showSizeGuide" 
      :default-category="currentCategory"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import SizeGuideDialog from '~/components/SizeGuideDialog.vue'

const showSizeGuide = ref(false)
const currentCategory = ref("women's top")

function openSizeGuide(item) {
  // 根据商品信息设置默认类型
  currentCategory.value = item.category || "women's top"
  showSizeGuide.value = true
}
</script>
```

## 国际化支持

组件已支持多语言，需要在国际化文件中添加相应的翻译：

### 必需的国际化键值

```javascript
{
  "sizeGuide": {
    "title": "尺码助手",
    "unit": "单位",
    "country": "国家/地区", 
    "category": "服装类型",
    "notice": "注意事项文本...",
    "categories": {
      "womens_top": "女士上衣",
      "mens_top": "男士上衣",
      // ... 其他类型
    },
    "countries": {
      "US": "美国",
      "UK": "英国",
      // ... 其他国家
    }
  }
}
```

## 样式自定义

### 弹窗样式
```scss
// 自定义弹窗大小
:deep(.q-dialog .q-card) {
  min-width: 900px; // 自定义最小宽度
  max-height: 80vh; // 自定义最大高度
}
```

### 表格样式
```scss
// 自定义表格样式
:deep(.size-table) {
  .q-table__top {
    background-color: #f5f5f5;
  }
  
  th {
    font-weight: bold;
    background-color: #e3f2fd;
  }
}
```

## 注意事项

1. **数据更新**：尺码数据存储在 `SizeGuideContent.vue` 组件中，如需更新数据，直接修改 `SIZE_DATA` 对象
2. **响应式设计**：组件已适配移动端，弹窗在小屏幕设备上会全屏显示
3. **性能优化**：数据为静态数据，无需额外的 API 请求
4. **国际化**：确保在使用前已添加相应的国际化翻译

## 常见问题

### Q: 如何添加新的服装类型？
A: 在 `SizeGuideContent.vue` 的 `SIZE_DATA` 对象中添加新的类型数据，并在国际化文件中添加对应的翻译。

### Q: 如何修改默认选择的国家？
A: 修改 `SizeGuideContent.vue` 中 `selectedCountry` 的初始值。

### Q: 弹窗在移动端显示异常？
A: 检查是否正确引入了 Quasar 的响应式工具，组件使用 `$q.screen.lt.sm` 来判断移动端。
