<template>
  <q-card class="product-card" @click="goToDetail">
    <!-- 商品图片 -->
    <div class="product-image-container">
      <q-img :src="product.mainImage || '/images/placeholder.png'" :ratio="1" class="product-image" fit="cover" spinner-color="primary" spinner-size="40px">
        <!-- 标签 -->
        <div class="product-labels" v-if="product.isNew || product.isHot || product.discount">
          <q-badge v-if="product.isNew" color="green" class="product-label">新品</q-badge>
          <q-badge v-if="product.isHot" color="orange" class="product-label">热卖</q-badge>
          <q-badge v-if="product.discount" color="red" class="product-label"> {{ product.discount }}折 </q-badge>
        </div>

        <!-- 快捷操作按钮 -->
        <div class="product-actions">
          <q-btn round flat color="primary" icon="shopping_cart" class="action-btn" @click.stop="addToCart" v-if="product.stock > 0">
            <q-tooltip>{{ $t('products.addToCart') }}</q-tooltip>
          </q-btn>
          <q-btn round flat color="pink" icon="favorite" class="action-btn" @click.stop="addToWishlist">
            <q-tooltip>{{ $t('products.addToWishlist') }}</q-tooltip>
          </q-btn>
          <q-btn round flat color="teal" icon="visibility" class="action-btn" @click.stop="quickView">
            <q-tooltip>{{ $t('products.quickView') }}</q-tooltip>
          </q-btn>
        </div>
      </q-img>
    </div>

    <!-- 商品信息 -->
    <q-card-section class="product-info">
      <div class="product-category" v-if="product.categoryName">{{ product.categoryName }}</div>
      <div class="product-title">{{ product.name }}</div>
      <div class="product-price-row">
        <div class="product-price">
          <span class="current-price">¥{{ formatPrice(product.price) }}</span>
          <span class="original-price" v-if="product.originalPrice && product.originalPrice > product.price"> ¥{{ formatPrice(product.originalPrice) }} </span>
        </div>
        <div class="product-rating" v-if="product.rating">
          <q-rating v-model="product.rating" size="1em" color="amber" icon="star" readonly />
          <span class="rating-count" v-if="product.ratingCount">({{ product.ratingCount }})</span>
        </div>
      </div>
      <div class="product-stock" :class="{ 'out-of-stock': product.stock <= 0 }">
        {{ product.stock > 0 ? $t('products.inStock') : $t('products.outOfStock') }}
      </div>
    </q-card-section>
  </q-card>
</template>

<script setup>
import { useRouter } from 'vue-router';
import { useQuasar } from 'quasar';

// Props
const props = defineProps({
  product: {
    type: Object,
    required: true,
  },
  index: {
    type: Number,
    default: 0,
  },
});

const router = useRouter();
const $q = useQuasar();

// 格式化价格
const formatPrice = (price) => {
  return parseFloat(price).toFixed(2);
};

// 跳转到商品详情页
const goToDetail = () => {
  router.push(`/products/${props.product.id}`);
};

// 添加到购物车
const addToCart = () => {
  // 这里添加购物车逻辑
  $q.notify({
    color: 'positive',
    message: '已添加到购物车',
    icon: 'shopping_cart',
  });
};

// 添加到收藏
const addToWishlist = () => {
  // 这里添加收藏逻辑
  $q.notify({
    color: 'pink',
    message: '已添加到收藏',
    icon: 'favorite',
  });
};

// 快速查看
const quickView = () => {
  // 这里添加快速查看逻辑
  $q.dialog({
    title: props.product.name,
    message: '快速查看功能开发中...',
  });
};
</script>

<style lang="scss" scoped>
.product-card {
  height: 100%;
  border-radius: 4px;
  overflow: hidden;
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;
  border: 1px solid #e0e0e0;

  &:hover {
    transform: translateY(-3px);
    border-color: #ccc;

    .product-actions {
      opacity: 1;
    }

    .product-image {
      transform: scale(1.03);
    }
  }
}

.product-image-container {
  position: relative;
  overflow: hidden;
}

.product-image {
  transition: transform 0.5s ease;
}

.product-labels {
  position: absolute;
  top: 8px;
  left: 8px;
  display: flex;
  flex-direction: column;
  gap: 4px;
  z-index: 1;

  .product-label {
    font-size: 0.75rem;
    padding: 3px 6px;
  }
}

.product-actions {
  position: absolute;
  bottom: 8px;
  right: 8px;
  display: flex;
  gap: 6px;
  opacity: 0;
  transition: opacity 0.2s ease;
  z-index: 1;

  .action-btn {
    background-color: rgba(255, 255, 255, 0.9);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;

    &:hover {
      transform: scale(1.05);
    }
  }
}

.product-info {
  padding: 12px;
}

.product-category {
  font-size: 0.75rem;
  color: #666;
  margin-bottom: 4px;
}

.product-title {
  font-size: 0.95rem;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
  line-height: 1.3;
  height: 2.6em;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.product-price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.product-price {
  display: flex;
  align-items: center;
  gap: 6px;

  .current-price {
    font-size: 1rem;
    font-weight: 600;
    color: #e53935;
  }

  .original-price {
    font-size: 0.8rem;
    color: #999;
    text-decoration: line-through;
  }
}

.product-rating {
  display: flex;
  align-items: center;
  gap: 4px;

  .rating-count {
    font-size: 0.75rem;
    color: #666;
  }
}

.product-stock {
  font-size: 0.8rem;
  color: #4caf50;
  font-weight: 500;

  &.out-of-stock {
    color: #f44336;
  }
}
</style>
