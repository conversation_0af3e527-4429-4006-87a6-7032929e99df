// 店铺标题样式
.shop-header {
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

// 表格标题样式
.table-header {
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

// 商品项样式
.border {
  border-top: 1px solid #e0e0e0;
  border-left: 1px solid #e0e0e0;
  border-right: 1px solid #e0e0e0;
}

.border:nth-last-child(1) {
  border-bottom: 1px solid #e0e0e0;
}

// 文本样式
.text-bold {
  font-weight: bold;
}

.nowrap {
  white-space: nowrap;
}

// 多行文本省略
.multiline-ellipsis {
  display: -webkit-box; /* 必须搭配 */
  -webkit-box-orient: vertical; /* 垂直方向排列子元素 */
  -webkit-line-clamp: 2; /* 限制显示两行 */
  line-clamp: 2; /* 标准属性 */
  overflow: hidden; /* 超出隐藏 */
  text-overflow: ellipsis; /* 显示省略号 */
  white-space: normal; /* 允许换行 */
  max-width: 100%; /* 设置宽度限制，避免超出 */
  word-break: break-word; /* 防止长单词溢出 */
}

.multiline-ellipsis-2 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
  max-width: 100%;
  word-break: break-word;
}

.multiline-ellipsis-3 {
  display: -webkit-box; /* 必须搭配 */
  -webkit-box-orient: vertical; /* 垂直方向排列子元素 */
  -webkit-line-clamp: 3; /* 限制显示三行 */
  line-clamp: 3; /* 标准属性 */
  overflow: hidden; /* 超出隐藏 */
  text-overflow: ellipsis; /* 显示省略号 */
  white-space: normal; /* 允许换行 */
  max-width: 100%; /* 设置宽度限制，避免超出 */
  word-break: break-word; /* 防止长单词溢出 */
}

// 移动端商品卡片样式
.cart-item-card {
  transition: all 0.2s ease;

  &:hover {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }
}

.mobile-cart-item {
  margin-bottom: 8px;
  border-radius: 8px;
  transition: all 0.2s ease;

  &:hover {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }
}

.checkbox-container {
  display: flex;
  align-items: center;
  height: 80px;
}

.img-container {
  display: flex;
  align-items: center;
  height: 80px;
}

.img-link {
  display: flex;
  align-items: center;
  height: 100%;
}

.mobile-checkbox {
  margin: 0;
  padding: 0;
  transform: scale(0.9);
}

.mobile-product-img {
  width: 80px;
  height: 80px;
  border-radius: 4px;
  transition: transform 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    transform: scale(1.05);
  }
}

.mobile-product-name {
  font-size: 13px;
  line-height: 1.4;
  margin-bottom: 2px;
  max-height: 2.8em;
  font-weight: 500;
  color: #333;

  a {
    color: inherit;
    text-decoration: none;

    &:hover {
      color: var(--q-primary);
      text-decoration: underline;
    }
  }
}

.mobile-product-props {
  font-size: 12px;
  line-height: 1.2;
  margin: 0;
  color: #777;
}

// 价格行样式
.price-row {
  border-top: 1px dashed #e0e0e0;
  padding-top: 8px;

  .row.no-wrap {
    min-height: 24px;
  }

  .price-label {
    font-size: 12px;
    line-height: 1.2;
    font-weight: 500;
    white-space: nowrap;
  }

  .price-value {
    font-size: 13px;
    line-height: 1.4;
    font-weight: 500;
    white-space: nowrap;
  }
}

// 数量控制样式
.quantity-controls {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 20px;
}

.mobile-qty-btn {
  min-height: 20px;
  min-width: 20px;
  height: 20px;
  width: 20px;
  padding: 0;
  margin: 0;

  .q-icon {
    font-size: 14px;
  }

  :deep(.q-btn__wrapper) {
    padding: 0;
    min-height: 20px;
  }
}

.custom-qty-input {
  width: 36px;
  height: 20px;
  margin: 0 2px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  text-align: center;
  font-size: 12px;
  line-height: 1;
  padding: 0;
  outline: none;

  &:focus {
    border-color: #1976d2;
  }
}

// 删除按钮样式
.mobile-delete-btn {
  min-height: 28px;
  padding: 0 8px;
  font-size: 12px;
}

// 备注行样式
.memo-row {
  border-top: 1px dashed #e0e0e0;
  padding-top: 8px;

  .memo-label {
    font-size: 12px;
    line-height: 1.2;
    font-weight: 500;
  }

  .memo-content {
    font-size: 12px;
    line-height: 1.4;
    max-width: 100%;
    word-break: break-word;
  }
}

/* 商品信息容器 */
.product-info-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* 紧凑型商品信息样式 */
.compact-info-container {
  margin-top: 4px;
}

.compact-row {
  padding: 1px 0;
  margin-bottom: 1px;
}

.vertical-center {
  display: flex;
  align-items: center;
}

/* 响应式样式 */
.desktop-view {
  display: block;
}

.mobile-view {
  display: none;
}

/* 平板电脑和移动端样式 */
@media (max-width: 1023px) {
  .desktop-view {
    display: none;
  }

  .mobile-view {
    display: block;
  }
}
