<template>
  <div class="transfer-create">
    <!-- 标题区域 - 响应式优化 -->
    <div class="bg-grey-2 q-px-md q-py-sm">
      <div class="row justify-between items-center">
        <span class="text-subtitle1 text-weight-medium">{{ $t('transfer.common.createTransfer') }}</span>
        <q-btn flat color="primary" icon-right="arrow_back" :label="$t('transfer.common.backToList')" to="/account/transfer" class="back-btn" />
      </div>
    </div>

    <div class="q-pa-md">
      <q-form @submit="onSubmit" class="q-gutter-md form-container">
        <!-- 选择将要寄往的仓库 - 响应式优化 -->
        <div class="bg-white q-pa-md rounded-borders shadow-1">
          <div class="row items-center justify-between q-mb-sm wrap-on-mobile">
            <div class="section-header col-12 col-sm-auto">
              <q-icon name="location_on" color="primary" />
              <span>{{ $t('transfer.create.selectWarehouse') }}</span>
            </div>
            <div class="warehouse-btn-container col-12 col-sm-auto q-mt-sm-xs">
              <WarehouseAddressButton />
            </div>
          </div>
          <div class="text-caption text-grey-7 q-mb-md">{{ $t('transfer.create.warehouseNote') }}</div>

          <div class="row">
            <div class="col-12 col-sm-6 col-md-5 col-lg-4">
              <q-select
                v-model="state.warehouseId"
                :options="state.warehouseList"
                option-value="id"
                option-label="name"
                outlined
                dense
                map-options
                emit-value
                :label="$t('transfer.create.selectWarehouseLabel')"
                stack-label
                class="field-label"
                :rules="[(val) => !!val || $t('transfer.create.warehouseRequired')]" />
            </div>
          </div>
        </div>

        <!-- 填写国内快递信息 - 响应式优化 -->
        <div class="bg-white q-pa-md rounded-borders shadow-1">
          <div class="section-header">
            <q-icon name="local_shipping" color="primary" />
            <span>{{ $t('transfer.create.expressInfo') }}</span>
          </div>
          <div class="text-caption text-grey-7 q-mb-md">{{ $t('transfer.create.expressNote') }}</div>

          <div class="row q-col-gutter-md">
            <div class="col-12 col-sm-6 col-md-4">
              <q-select
                v-model="state.logisticsId"
                :options="state.deliveryExpresList"
                option-value="id"
                option-label="name"
                outlined
                dense
                map-options
                emit-value
                :label="$t('transfer.create.expressCompanyLabel')"
                stack-label
                class="field-label"
                @update:model-value="onLogisticsChange"
                :rules="[(val) => !!val || $t('transfer.create.expressCompanyRequired')]" />
            </div>
            <div class="col-12 col-sm-6 col-md-4">
              <q-input
                v-model="state.logisticsNo"
                outlined
                dense
                :label="$t('transfer.create.expressNumberLabel')"
                stack-label
                class="field-label"
                :rules="[(val) => !!val || $t('transfer.create.expressNumberRequired')]" />
            </div>
            <!-- 其他快递备注 - 响应式布局优化 -->
            <div v-if="showOtherExpressNote" class="col-12 col-sm-12 col-md-4">
              <q-input
                v-model="state.logisticsRemark"
                outlined
                dense
                :label="$t('transfer.create.otherExpressLabel')"
                stack-label
                class="field-label"
                :rules="[(val) => !!val || $t('transfer.create.otherExpressRequired')]" />
            </div>
          </div>
        </div>

        <!-- 填写需转运的商品信息 - 响应式优化 -->
        <div class="bg-white q-pa-md rounded-borders shadow-1">
          <div class="section-header">
            <q-icon name="inventory_2" color="primary" />
            <span>{{ $t('transfer.create.productInfo') }}</span>
          </div>
          <div class="text-caption text-grey-7 q-mb-md">{{ $t('transfer.create.productNote') }}</div>

          <div v-for="(item, index) in state.items" :key="index" class="q-mb-md product-item">
            <div class="row justify-between items-center q-mb-sm">
              <div class="text-subtitle2 q-ml-sm">{{ $t('transfer.create.product') }} #{{ index + 1 }}</div>
              <q-btn v-if="index > 0" flat round dense color="negative" icon="delete" @click="removeItem(index)">
                <q-tooltip>{{ $t('transfer.create.deleteProduct') }}</q-tooltip>
              </q-btn>
            </div>

            <!-- 商品分类和名称 - 响应式优化 -->
            <div class="row q-col-gutter-md">
              <div class="col-12 col-sm-12 col-md-4">
                <q-select
                  v-model="item.categoryId"
                  :options="state.categoryList"
                  option-value="id"
                  option-label="name"
                  outlined
                  dense
                  map-options
                  emit-value
                  :label="$t('transfer.create.categoryLabel')"
                  stack-label
                  class="field-label"
                  :rules="[(val) => !!val || $t('transfer.create.categoryRequired')]" />
              </div>
              <div class="col-12 col-sm-12 col-md-8">
                <q-input
                  v-model="item.productName"
                  outlined
                  dense
                  :label="$t('transfer.create.productNameLabel')"
                  stack-label
                  class="field-label"
                  :rules="[(val) => !!val || $t('transfer.create.productNameRequired')]" />
              </div>
            </div>

            <!-- 数量、重量和价值 - 响应式优化 -->
            <div class="row q-col-gutter-md q-mt-sm">
              <div class="col-4 col-sm-4 col-md-4">
                <q-input
                  v-model.number="item.count"
                  type="number"
                  outlined
                  dense
                  :label="$t('transfer.create.quantityLabel')"
                  stack-label
                  class="field-label"
                  min="1"
                  :rules="[(val) => !!val || $t('transfer.create.quantityRequired'), (val) => val > 0 || $t('transfer.create.quantityPositive')]" />
              </div>
              <div class="col-4 col-sm-4 col-md-4">
                <q-input
                  v-model.number="item.weight"
                  type="number"
                  outlined
                  dense
                  :label="$t('transfer.create.weightLabel')"
                  stack-label
                  class="field-label"
                  min="0.01"
                  step="0.01"
                  :rules="[(val) => val > 0 || $t('transfer.create.weightPositive')]" />
              </div>
              <div class="col-4 col-sm-4 col-md-4">
                <q-input v-model.number="item.price" type="number" outlined dense :label="$t('transfer.create.priceLabel')" stack-label class="field-label" min="0.01" step="0.01" />
              </div>
            </div>

            <q-separator class="q-my-md" v-if="index < state.items.length - 1" />
          </div>

          <div class="row justify-center q-mt-md">
            <q-btn outline color="primary" icon="add" :label="$t('transfer.create.addProduct')" @click="addItem" />
          </div>
        </div>

        <!-- 备注信息 -->
        <div class="bg-white q-pa-md rounded-borders shadow-1">
          <div class="section-header">
            <q-icon name="notes" color="primary" />
            <span>{{ $t('transfer.create.remarkInfo') }}</span>
          </div>
          <div class="text-caption text-grey-7 q-mb-md">{{ $t('transfer.create.remarkNote') }}</div>

          <div class="row q-col-gutter-md">
            <div class="col-12">
              <q-input
                v-model="state.remark"
                outlined
                type="textarea"
                rows="3"
                stack-label
                class="field-label"
                :placeholder="$t('transfer.create.remarkPlaceholder')"
                maxlength="300"
                counter
                :rules="[(val) => val === null || val === '' || val.length <= 300 || $t('transfer.create.remarkMaxLength')]" />
            </div>
          </div>
        </div>

        <!-- 注意事项 -->
        <div class="bg-white q-pa-md q-pb-xl rounded-borders shadow-1 q-mt-md">
          <div class="section-header">
            <q-icon name="info" color="primary" />
            <span>{{ $t('transfer.create.notice') }}</span>
          </div>

          <div class="text-caption notice-box">
            <ol>
              <li v-for="(item, index) in $tm('transfer.create.noticeItems')" :key="index">{{ item }}</li>
            </ol>
          </div>
        </div>

        <!-- 提交区域 - 响应式优化 -->
        <div class="row justify-between items-center q-mt-xl q-mb-xl wrap-on-mobile">
          <div class="col-12 col-md-7">
            <q-checkbox v-model="state.agreement" :label="$t('transfer.create.agreement')" :rules="[(val) => !!val || $t('transfer.create.agreementRequired')]" />
          </div>
          <div class="col-12 col-md-5 text-right text-center-mobile q-mt-md q-mt-md-none">
            <q-btn
              type="submit"
              color="primary"
              icon-left="done"
              :label="$t('transfer.create.submit')"
              size="lg"
              class="submit-btn"
              :loading="submitting"
              :disable="!state.agreement"
              :title="!state.agreement ? $t('transfer.create.agreementRequired') : ''" />
          </div>
        </div>
      </q-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { useQuasar } from 'quasar';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import TransferApi from '~/composables/transferApi';
import WarehouseAddressButton from '~/components/account/WarehouseAddressButton.vue';
import WarehouseApi from '../../composables/warehouseApi';
import CategoryApi from '../../composables/categoryApi';
import DeliveryExpressApi from '../../composables/deliveryExpressApi';
import { yuan2fen } from '~/utils/utils';

definePageMeta({
  middleware: 'auth', // 引入身份验证中间件
});

const $q = useQuasar();
const router = useRouter();
const { t, tm } = useI18n();

const state = reactive({
  loadStatus: false,
  warehouseList: [], // 仓库选项
  deliveryExpresList: [], // 快递公司选项
  categoryList: [], // 商品分类选项
  warehouseId: null,
  logisticsId: null,
  logisticsCode: null,
  logisticsNo: null,
  logisticsRemark: null,
  remark: null,
  items: [],
  agreement: false,
});

// 提交状态
const submitting = ref(false);

// 是否显示其他快递备注
const showOtherExpressNote = computed(() => state.logisticsCode === 'other');

// 处理快递公司选择变化
function onLogisticsChange(selectedLogisticId) {
  console.log('onLogisticsChange', selectedLogisticId);
  if (selectedLogisticId) {
    const selectedLogistics = state.deliveryExpresList.find((item) => item.id === selectedLogisticId);
    state.logisticsCode = selectedLogistics.code;
    // 如果不是其他快递，清空备注
    if (selectedLogistics.code !== 'other') {
      state.logisticsRemark = '';
    }
  }
}

// 页面加载时获取数据
onMounted(async () => {
  try {
    // 获取仓库列表
    const { code: code1, data: data1 } = await WarehouseApi.getList();
    if (code1 === 0) {
      state.warehouseList = data1;
    }
    // 获取快递公司列表
    const { code: code2, data: data2 } = await DeliveryExpressApi.getList();
    if (code2 === 0) {
      state.deliveryExpresList = data2;
    }
    // 获取商品分类列表
    const { code: code3, data: data3 } = await CategoryApi.getList();
    if (code3 === 0) {
      state.categoryList = data3;
    }

    // 默认添加一个商品输入框
    if (state.items.length === 0) {
      addItem();
    }
  } catch (error) {
    console.error('获取初始数据出错:', error);
  }
});

// 添加商品项
function addItem() {
  state.items.push({
    categoryId: null,
    productName: '',
    count: 1,
    weight: null,
    price: null,
  });
}

// 移除商品项
function removeItem(index) {
  state.items.splice(index, 1);
}

// 提交表单
async function onSubmit() {
  if (!state.agreement) {
    $q.notify({
      type: 'warning',
      message: t('transfer.create.agreementRequired'),
    });
    return;
  }

  // 检查其他快递备注
  if (state.logisticsCode === 'other' && !state.logisticsRemark) {
    $q.notify({
      type: 'warning',
      message: t('transfer.create.otherExpressRequired'),
    });
    return;
  }

  submitting.value = true;

  // 准备提交数据
  const submitData = {
    warehouseId: state.warehouseId,
    logisticsId: state.logisticsId,
    logisticsNo: state.logisticsNo,
    logisticsRemark: state.logisticsRemark,
    remark: state.remark,
    items: state.items.map((item) => ({
      ...item,
      // 使用工具函数将价格从元转换为分
      price: yuan2fen(item.price),
    })),
  };

  try {
    const { code, msg } = await TransferApi.createTransfer(submitData);

    if (code === 0) {
      $q.notify({
        type: 'positive',
        message: t('transfer.create.createSuccess'),
      });

      // 跳转到转运单列表页
      router.push('/account/transfer');
    } else {
      $q.notify({
        type: 'negative',
        message: msg || t('transfer.create.createFailed'),
      });
    }
  } catch (error) {
    console.error('创建转运单出错:', error);
    $q.notify({
      type: 'negative',
      message: t('transfer.errors.createError'),
    });
  } finally {
    submitting.value = false;
  }
}
</script>

<style lang="scss" scoped>
.transfer-create {
  .form-container {
    max-width: 900px;
    margin: 0 auto;
  }

  .section-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-size: 1rem;
    font-weight: 500;

    .q-icon {
      margin-right: 8px;
      font-size: 1.2em;
      color: var(--q-primary);
    }
  }

  .select-width {
    width: 100%;
  }

  .product-item {
    background-color: rgba(0, 0, 0, 0.02);
    border-radius: 4px;
    padding: 12px;
  }

  .notice-box {
    background-color: rgba(0, 0, 0, 0.02);
    border-radius: 4px;
    padding: 12px 16px;
    margin: 0 auto;
    max-width: 90%;

    ol {
      margin-top: 8px;
      margin-bottom: 0;
      padding-left: 24px;
    }

    li {
      margin-bottom: 4px;
    }

    p {
      margin-top: 0;
      margin-bottom: 4px;
    }
  }

  // 仓库地址按钮容器
  .warehouse-btn-container {
    margin-right: 8px;

    @media (max-width: 599px) {
      margin-right: 0;
      margin-top: 8px;
      text-align: left;
    }
  }

  // 调整输入框标签样式
  .field-label {
    :deep(.q-field__label) {
      font-size: 0.95rem;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.7);
    }

    :deep(.q-field__marginal) {
      height: 40px;
    }
  }

  // 响应式样式
  .back-btn {
    @media (max-width: 599px) {
      padding: 4px 8px;

      .q-icon {
        font-size: 1.2em;
      }
    }
  }

  .submit-btn {
    @media (max-width: 599px) {
      width: 100%;
      max-width: 300px;
      margin: 0 auto;
    }
  }

  .wrap-on-mobile {
    @media (max-width: 767px) {
      flex-wrap: wrap;
    }
  }

  .q-mt-sm-xs {
    @media (max-width: 599px) {
      margin-top: 8px;
    }
  }

  .text-center-mobile {
    @media (max-width: 767px) {
      text-align: center;
    }
  }

  .q-mb-sm-md {
    @media (max-width: 767px) {
      margin-bottom: 16px;
    }
  }
}
</style>
