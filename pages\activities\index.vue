<template>
  <Header />
  <div class="activities-page">
    <div class="activities-container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1 class="page-title">活动中心</h1>
        <div class="page-subtitle">参与精彩活动，享受独家优惠</div>
      </div>
      
      <!-- 活动列表 -->
      <div class="activities-list">
        <h2 class="section-title">全部活动</h2>
        
        <div v-if="state.pagination.list.length > 0" class="row q-col-gutter-md">
          <div v-for="activity in state.pagination.list" :key="activity.id" class="col-12 col-sm-6 col-md-4">
            <q-card class="activity-card">
              <div class="activity-image-container">
                <q-img :src="activity.picUrl" :ratio="16/9" class="activity-image" />
              </div>
              <q-card-section>
                <div class="text-h6 activity-title">{{ activity.title }}</div>
                
                <div class="activity-time q-mt-sm row justify-between items-center">
                  <div>
                    <q-icon name="event" size="xs" class="q-mr-xs" />
                    {{ formatDate(activity.publishTime) }}
                  </div>
                  <div>
                    <q-icon name="visibility" size="sm" class="q-mr-xs" />
                    <span>{{ activity.browseCount }} 次浏览</span>
                  </div>
                </div>
                
                <p class="activity-description q-mt-sm">{{ activity.introduction }}</p>
              
              </q-card-section>
              
              <q-card-actions align="right">
                <q-btn flat color="primary" @click="goToDetail(activity)">
                  查看详情
                </q-btn>
              </q-card-actions>
            </q-card>
          </div>
        </div>
        
        <div v-else class="no-activities">
          <q-icon name="event_busy" size="4rem" color="grey-5" />
          <p class="text-grey-7 q-mt-md">暂无活动</p>
        </div>
        
        <!-- 分页 -->
        <div v-if="totalPages > 1" class="pagination-container q-mt-lg">
          <q-pagination
                v-model="state.pagination.pageNo"
                :max="Math.ceil(state.pagination.total / state.pagination.pageSize)"
                :max-pages="6"
                boundary-links
                direction-links
                @update:model-value="onPageChange" />
        </div>
      </div>
    </div>
  </div>
  <Footer />
</template>

<script setup>
import { computed, onMounted, reactive } from 'vue';
import { date } from 'quasar';
import ArticleApi from '../../composables/articleApi';

const state = reactive({
  pagination: {
    list: [],
    total: 0,
    pageNo: 1,
    pageSize: 2,
  },
  loadStatus: false,
});

// 计算总页数
const totalPages = computed(() => {
  return Math.ceil(state.pagination.total / state.pagination.pageSize);
});

// 从URL获取页码和筛选条件
onMounted(() => {
  fetchActivities();
});

// 获取活动列表
async function fetchActivities() {
  state.loadStatus = true;

  const { code, data } = await ArticleApi.getPage({
    pageNo: state.pagination.pageNo,
    pageSize: state.pagination.pageSize,
    categoryId: ArticleCategoryTypeEnum.ACTIVITIES,
  });
  state.loadStatus = false;
  if (code === 0) {
    state.pagination.list = data.list || [];
    state.pagination.total = data.total || 0;
  }
}

// 页码变化处理
const onPageChange = (newPage) => {
  state.pagination.pageNo = newPage;
  fetchActivities();
};

// 跳转到详情页面
const goToDetail = (activity) => {
  // 使用 useState 保存数据
  const detailState = useState('activityDetail');
  detailState.value = {
    id: activity.id,
    title: activity.title,
    author: activity.author,
    categoryId: activity.categoryId,
    picUrl: activity.picUrl,
    introduction: activity.introduction,
    content: activity.content,
    createTime: activity.createTime,
    browseCount: activity.browseCount,
    spuId: activity.spuId,
    publishTime: activity.publishTime
  };

  navigateTo(`/activities/detail/${activity.id}`);
};

// 格式化日期
const formatDate = (timestamp) => {
  if (!timestamp) return '';
  return date.formatDate(new Date(timestamp), 'YYYY-MM-DD');
};



</script>

<style lang="scss" scoped>
.activities-page {
  padding: 20px 0 40px;
  background-color: #f8f8f8;
}

.activities-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
  padding: 30px 0;
}

.page-title {
  font-size: 32px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

.page-subtitle {
  font-size: 18px;
  color: #666;
}

.section-title {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 20px;
  position: relative;
  
  &:after {
    content: '';
    display: block;
    width: 50px;
    height: 3px;
    background-color: #1976d2;
    margin-top: 10px;
  }
}

.filter-section {
  margin-bottom: 30px;
}

.hot-activities {
  margin-bottom: 40px;
}

.hot-activity-image-container {
  position: relative;
  height: 100%;
}

.hot-activity-image {
  height: 100%;
  width: 100%;
  object-fit: cover;
}

.hot-activity-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.9);
}

.hot-activity-title {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

.hot-activity-time {
  color: #666;
  font-size: 14px;
}

.hot-activity-description {
  flex-grow: 1;
  margin: 10px 0;
  color: #555;
  font-size: 16px;
  line-height: 1.5;
}

.hot-activity-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}

.activity-card {
  height: 100%;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
  }
}

.activity-image-container {
  position: relative;
}

.activity-status-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  color: white;
  
  &.status-upcoming {
    background-color: #ff9800;
  }
  
  &.status-ongoing {
    background-color: #4caf50;
  }
  
  &.status-ended {
    background-color: #9e9e9e;
  }
}

.activity-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  line-height: 1.3;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

.activity-time {
  color: #666;
  font-size: 13px;
}

.activity-description {
  color: #555;
  font-size: 14px;
  line-height: 1.5;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  margin-bottom: 0;
}

.countdown {
  font-size: 13px;
  color: #ff5722;
  
  .countdown-label {
    font-weight: normal;
  }
  
  .countdown-value {
    font-weight: bold;
  }
}

.no-activities {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin: 40px 0 20px;
}

@media (max-width: 767px) {
  .page-header {
    margin-bottom: 30px;
    padding: 20px 0;
  }
  
  .page-title {
    font-size: 26px;
  }
  
  .page-subtitle {
    font-size: 16px;
  }
  
  .section-title {
    font-size: 22px;
    margin-bottom: 15px;
  }
  
  .hot-activity-title {
    font-size: 20px;
  }
  
  .hot-activity-description {
    font-size: 14px;
  }
  
  .activity-title {
    font-size: 16px;
  }
  
  .activity-description {
    -webkit-line-clamp: 2;
    line-clamp: 2;
  }
  
  .pagination-container {
    margin: 30px 0 15px;
  }
}
</style>
