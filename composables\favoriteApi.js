import { date } from "quasar";

const FavoriteApi = {
  // 获得商品收藏分页
  getFavoritePage: (data) => {
    return useClientGet('/product/favorite/page',{
      params: data,
    });
  },
  // 获得商品收藏spuId列表
  getFavoriteList: () => {
    return useClientGet('/product/favorite/list-id');
  },
  // 检查是否收藏过商品
  isFavoriteExists: (spuId) => {
    return useClientGet('/product/favorite/exits',{
      params: {
        spuId,
      },
    });
  },
  // 添加商品收藏
  createFavorite: (data) => {
    return useClientPost('/product/favorite/create',{
      body: data,
      custom: {
        auth: true,
        showSuccess: false,
        successMsg: '收藏成功',
      },
    });
  },
  // 取消商品收藏
  deleteFavorite: (data) => {
    return useClientDelete('/product/favorite/delete',{
      body: data,
      custom: {
        auth: true,
        showSuccess: false,
        successMsg: '取消成功',
      },
    });
  },
};

export default FavoriteApi;
