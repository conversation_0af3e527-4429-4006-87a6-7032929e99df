<?xml version="1.0" encoding="UTF-8"?>
<svg width="800px" height="600px" viewBox="0 0 800 600" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>批量采购</title>
    <defs>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="linearGradient-1">
            <stop stop-color="#1976D2" offset="0%"></stop>
            <stop stop-color="#64B5F6" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="linearGradient-2">
            <stop stop-color="#E3F2FD" offset="0%"></stop>
            <stop stop-color="#BBDEFB" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="批量采购" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <rect id="背景" fill="#FFFFFF" x="0" y="0" width="800" height="600"></rect>
        
        <!-- 大箱子 -->
        <g id="大箱子" transform="translate(100, 150)">
            <rect id="箱子-底" fill="url(#linearGradient-1)" x="0" y="100" width="300" height="200" rx="10"></rect>
            <rect id="箱子-顶" fill="#1565C0" x="0" y="100" width="300" height="30" rx="5"></rect>
            <rect id="箱子-开口" fill="#0D47A1" x="100" y="100" width="100" height="30"></rect>
            
            <!-- 箱子标签 -->
            <rect id="标签" fill="#FFFFFF" x="20" y="150" width="80" height="60" rx="5"></rect>
            <rect id="标签-条码" fill="#333333" x="30" y="170" width="60" height="5"></rect>
            <rect id="标签-条码2" fill="#333333" x="30" y="180" width="60" height="5"></rect>
            <rect id="标签-条码3" fill="#333333" x="30" y="190" width="40" height="5"></rect>
        </g>
        
        <!-- 小箱子组 -->
        <g id="小箱子组" transform="translate(450, 200)">
            <!-- 小箱子1 -->
            <g id="小箱子1" transform="translate(0, 0)">
                <rect id="小箱子-底" fill="url(#linearGradient-2)" x="0" y="0" width="100" height="80" rx="5"></rect>
                <rect id="小箱子-顶" fill="#90CAF9" x="0" y="0" width="100" height="15" rx="3"></rect>
                <rect id="小箱子-标签" fill="#FFFFFF" x="10" y="25" width="30" height="20" rx="2"></rect>
            </g>
            
            <!-- 小箱子2 -->
            <g id="小箱子2" transform="translate(120, 30)">
                <rect id="小箱子-底" fill="url(#linearGradient-2)" x="0" y="0" width="120" height="90" rx="5"></rect>
                <rect id="小箱子-顶" fill="#90CAF9" x="0" y="0" width="120" height="15" rx="3"></rect>
                <rect id="小箱子-标签" fill="#FFFFFF" x="10" y="25" width="30" height="20" rx="2"></rect>
            </g>
            
            <!-- 小箱子3 -->
            <g id="小箱子3" transform="translate(60, 100)">
                <rect id="小箱子-底" fill="url(#linearGradient-2)" x="0" y="0" width="80" height="70" rx="5"></rect>
                <rect id="小箱子-顶" fill="#90CAF9" x="0" y="0" width="80" height="15" rx="3"></rect>
                <rect id="小箱子-标签" fill="#FFFFFF" x="10" y="25" width="30" height="20" rx="2"></rect>
            </g>
        </g>
        
        <!-- 购物车图标 -->
        <g id="购物车" transform="translate(350, 400)">
            <circle id="购物车-轮子1" fill="#424242" cx="30" cy="170" r="15"></circle>
            <circle id="购物车-轮子2" fill="#424242" cx="120" cy="170" r="15"></circle>
            <path d="M0,0 L40,0 L60,100 L150,100 L150,130 L50,130 L0,0 Z" id="购物车-形状" fill="#1976D2"></path>
            <rect id="购物车-把手" fill="#1565C0" x="140" y="30" width="30" height="10" rx="5"></rect>
        </g>
        
        <!-- 文字元素 -->
        <g id="文字" transform="translate(250, 50)" fill="#1976D2" font-family="Arial, sans-serif" font-weight="bold">
            <text id="批量采购" font-size="48">批量采购</text>
        </g>
        
        <!-- 装饰元素 -->
        <g id="装饰-圆点" transform="translate(50, 50)">
            <circle fill="#E3F2FD" cx="0" cy="0" r="10"></circle>
            <circle fill="#BBDEFB" cx="30" cy="30" r="8"></circle>
            <circle fill="#90CAF9" cx="60" cy="10" r="6"></circle>
            <circle fill="#64B5F6" cx="20" cy="60" r="12"></circle>
        </g>
        
        <g id="装饰-圆点2" transform="translate(700, 100)">
            <circle fill="#E3F2FD" cx="0" cy="0" r="10"></circle>
            <circle fill="#BBDEFB" cx="30" cy="30" r="8"></circle>
            <circle fill="#90CAF9" cx="60" cy="10" r="6"></circle>
            <circle fill="#64B5F6" cx="20" cy="60" r="12"></circle>
        </g>
    </g>
</svg>
