<!-- 
  这是一个示例文件，展示如何在其他页面中使用TicketDialog组件
  可以在订单页面、包裹页面等地方调用
-->
<template>
  <div class="example-page">
    <h2>TicketDialog组件使用示例</h2>
    
    <!-- 基本使用 -->
    <div class="q-mb-md">
      <q-btn 
        color="primary" 
        label="新建工单" 
        @click="showBasicDialog"
      />
    </div>

    <!-- 订单问题工单 -->
    <div class="q-mb-md">
      <q-btn 
        color="orange" 
        label="订单问题工单" 
        @click="showOrderDialog"
      />
    </div>

    <!-- 包裹问题工单 -->
    <div class="q-mb-md">
      <q-btn 
        color="purple" 
        label="包裹问题工单" 
        @click="showPackageDialog"
      />
    </div>

    <!-- 工单弹窗组件 -->
    <TicketDialog 
      v-model="dialogVisible"
      :default-type="defaultType"
      :default-title="defaultTitle"
      :default-description="defaultDescription"
      @success="onTicketCreated"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { useQuasar } from 'quasar';
import TicketDialog from '~/components/TicketDialog.vue';
import TicketApi from '~/composables/ticketApi';

const $q = useQuasar();

// 弹窗状态
const dialogVisible = ref(false);
const defaultType = ref(TicketApi.TICKET_TYPES.OTHER);
const defaultTitle = ref('');
const defaultDescription = ref('');

// 基本使用
const showBasicDialog = () => {
  defaultType.value = TicketApi.TICKET_TYPES.OTHER;
  defaultTitle.value = '';
  defaultDescription.value = '';
  dialogVisible.value = true;
};

// 订单问题工单
const showOrderDialog = () => {
  const orderId = 'ORD123456'; // 实际使用时从当前页面获取
  defaultType.value = TicketApi.TICKET_TYPES.ORDER;
  defaultTitle.value = `订单号：${orderId}`;
  defaultDescription.value = '请描述您遇到的订单问题...';
  dialogVisible.value = true;
};

// 包裹问题工单
const showPackageDialog = () => {
  const packageId = 'PKG789012'; // 实际使用时从当前页面获取
  defaultType.value = TicketApi.TICKET_TYPES.PACKAGE;
  defaultTitle.value = `包裹号：${packageId}`;
  defaultDescription.value = '请描述您遇到的包裹问题...';
  dialogVisible.value = true;
};

// 工单创建成功回调
const onTicketCreated = (ticket) => {
  $q.notify({
    color: 'positive',
    message: `工单 ${ticket.no} 创建成功`,
    icon: 'check',
  });
  
  // 可以在这里执行其他操作，比如跳转到工单详情页
  // router.push(`/account/ticket-detail/${ticket.id}`);
};
</script>

<style lang="scss" scoped>
.example-page {
  padding: 20px;
  
  h2 {
    margin-bottom: 20px;
    color: #333;
  }
}
</style>
