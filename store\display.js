import { defineStore } from 'pinia';
import Promotion<PERSON>pi from '~/composables/promotionApi';

export const useDisplayStore = defineStore('display', {
  state: () => ({
    displays: [],
    // lastUpdateTime: 0,
    // cacheTime: 24 * 60 * 60 * 1000, //缓存时间
  }),
  getters: {
    getDisplayByPosition: (state) => (position) => {
      return state.displays.find((display) => display.position === position) || {};
    },
  },

  actions: {
    async fetchDisplays() {
      // const currentTime = Date.now();
      // if (currentTime - this.lastUpdateTime > this.cacheTime) {
      const { code, data } = await PromotionApi.getDisplayList();
      if (code === 0) {
        this.displays = data;
        // this.lastUpdateTime = currentTime;
      }
      // }
    },
  },
  // persist: process.client && {
  //   storage: localStorage,
  //   key: '_DI',
  // },
});
