# 余额支付弹窗修正说明

## 问题描述

在使用 Quasar 的 `$q.dialog()` 时，`onOk` 回调函数默认会关闭弹窗，即使在回调中处理了支付失败的情况。这导致支付密码错误时弹窗仍然会关闭，用户无法重新输入密码。

## 问题原因

Quasar Dialog 的 `onOk` 回调函数有以下特点：
1. 当 `onOk` 回调被触发时，弹窗会自动关闭
2. 即使在回调中调用了 `walletDialog.hide()` 或其他方法，弹窗的关闭行为已经被触发
3. 无法在 `onOk` 回调中阻止弹窗关闭

## 解决方案

采用自定义回调函数的方式，而不是使用 Quasar Dialog 的 `onOk` 事件：

### 1. 修改 WalletPaymentConfirmDialog 组件

#### 添加自定义属性
```javascript
const props = defineProps({
  // 支付金额，已格式化的HTML
  amount: {
    type: String,
    required: true,
  },
  // 当前余额，已格式化的HTML
  balance: {
    type: String,
    required: true,
  },
  // 确认回调函数
  onConfirm: {
    type: Function,
    default: null,
  },
});
```

#### 修改确认按钮逻辑
```javascript
const onOKClick = () => {
  if (isFormValid.value) {
    const data = {
      payPassword: payPassword.value,
      clearPassword: () => {
        payPassword.value = '';
      },
      closeDialog: () => {
        onDialogOK(); // 只有在需要时才关闭弹窗
      }
    };
    
    // 如果有传入的回调函数，调用它；否则发出事件
    if (props.onConfirm) {
      props.onConfirm(data);
    } else {
      emit('confirm', data);
    }
  }
};
```

### 2. 修改支付页面逻辑

#### 使用 componentProps 传递回调
```javascript
const walletDialog = $q.dialog({
  component: WalletPaymentConfirmDialog,
  componentProps: {
    amount: formatAmount(state.orderInfo.orderPrice, { useHtml: true }),
    balance: formatAmount(userWallet.value.balance, { useHtml: true }),
    onConfirm: async (data) => {
      // 支付逻辑
      try {
        const { code, msg } = await PayApi.submitOrder({
          id: state.orderInfo.id,
          channelCode: 'wallet',
          payPassword: data.payPassword,
        });
        
        if (code === 0) {
          // 支付成功，关闭弹窗并跳转
          data.closeDialog();
          goPayResult(state.orderInfo.id, state.orderType);
        } else {
          // 支付失败，清空密码但不关闭弹窗
          data.clearPassword();
          useNuxtApp().$showNotify({
            msg: msg || '支付失败，请重试',
            type: 'negative',
          });
        }
      } catch (error) {
        // 网络错误，清空密码但不关闭弹窗
        data.clearPassword();
        console.error('钱包支付失败', error);
        useNuxtApp().$showNotify({
          msg: '支付失败，请重试',
          type: 'negative',
        });
      }
    }
  },
});
```

## 关键改进

### 1. 控制弹窗关闭时机
- **支付成功时**：调用 `data.closeDialog()` 主动关闭弹窗
- **支付失败时**：不调用关闭方法，弹窗保持打开状态

### 2. 密码输入框管理
- **支付失败时**：调用 `data.clearPassword()` 清空密码输入框
- **用户可以重新输入**：无需重新打开弹窗

### 3. 错误提示
- 支付失败时显示明确的错误信息
- 网络错误时也提供相应提示

## 测试要点

### 1. 支付成功场景
- [ ] 输入正确密码，支付成功
- [ ] 弹窗自动关闭
- [ ] 正确跳转到结果页面

### 2. 支付失败场景
- [ ] 输入错误密码，支付失败
- [ ] 弹窗保持打开状态
- [ ] 密码输入框自动清空
- [ ] 显示错误提示信息
- [ ] 可以重新输入密码

### 3. 网络错误场景
- [ ] 网络异常时的处理
- [ ] 弹窗保持打开状态
- [ ] 密码输入框自动清空
- [ ] 显示网络错误提示

### 4. 用户取消场景
- [ ] 点击取消按钮
- [ ] 弹窗正常关闭
- [ ] 不执行支付逻辑

## 用户体验改进

### 1. 减少操作中断
- 支付失败时用户无需重新打开弹窗
- 可以直接修改密码重新尝试

### 2. 明确的反馈
- 清晰的错误提示信息
- 自动清空错误密码，避免重复输入

### 3. 流畅的操作流程
- 成功时自动关闭并跳转
- 失败时保持界面状态，便于重试

## 技术要点

### 1. 避免使用 onOk 事件
- Quasar Dialog 的 `onOk` 会自动关闭弹窗
- 使用自定义回调函数可以完全控制弹窗行为

### 2. 组件通信方式
- 通过 `componentProps` 传递回调函数
- 组件内部调用传入的回调函数
- 保持组件的灵活性和可复用性

### 3. 状态管理
- 密码输入框状态由组件内部管理
- 通过回调函数提供清空密码的方法
- 弹窗关闭由外部逻辑控制

## 总结

通过这次修正，余额支付功能现在能够正确处理支付失败的情况：
- 支付成功时正常关闭弹窗并跳转
- 支付失败时保持弹窗打开，允许用户重新输入密码
- 提供清晰的错误反馈和良好的用户体验

这种实现方式完全解决了 Quasar Dialog `onOk` 自动关闭弹窗的问题，给用户提供了更好的支付体验。
