const HelpApi = {
  getList: () => {
    return useServerGet('/promotion/help-category/list');
  },
  getSimpleList: () => {
    return useServerGet('/promotion/help-category/simple-list');
  },
  get: (id) => {
    return useServerGet('/promotion/help-article/get', { params: { id } });
  },
  getDetail: (categoryCode, itemCode) => {
    return useServerGet(`/promotion/help-article/detail/${categoryCode}/${itemCode}`);
  },
  getHelpPage(page = 1, pageSize = 10) {
    return useClientGet('/promotion/help-article/page', {
      params: { page, pageSize },
    });
  },
  submitFeedback: (data) => {
    return useClientPost('/promotion/help-article/feedback', {
      body: data,
    });
  },
  getFaqList: (page = 1, pageSize = 10) => {
    return useServerGet(`/promotion/help-article/faq-list?page=${page}&pageSize=${pageSize}`);
  },
  search: (params) => {
    // 构建查询参数
    const queryParams = new URLSearchParams();
    if (params.keyword) queryParams.append('keyword', params.keyword);
    if (params.page) queryParams.append('page', params.page);
    if (params.pageSize) queryParams.append('pageSize', params.pageSize);
    if (params.language) queryParams.append('language', params.language);
    if (params.categoryCode) queryParams.append('categoryCode', params.categoryCode);

    return useServerGet(`/promotion/help-article/search?${queryParams.toString()}`);
  },
};

export default HelpApi;
