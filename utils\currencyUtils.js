/**
 * 货币工具函数
 * 提供货币格式化和转换的通用方法
 */

import { fen2yuan } from './utils';
import { exchangeRateConvert } from './moneyUtil';

/**
 * 格式化金额显示
 * 根据选择的货币和默认货币进行格式化
 *
 * @param {number} amount - 金额，单位：分
 * @param {Object} selectedCurrency - 用户选择的货币对象，包含currency、symbol和rate属性
 * @param {Object} defaultCurrency - 默认货币对象，包含currency、symbol和rate属性
 * @param {Object} options - 格式化选项
 * @param {boolean} options.showDefault - 是否在不同币种时显示默认币种金额，默认为true
 * @param {boolean} options.spaceBetween - 是否在符号和金额之间添加空格，默认为false
 * @param {boolean} options.useHtml - 是否使用HTML格式输出，以便应用样式，默认为true
 * @param {string} options.defaultCurrencyClass - 默认币种的CSS类名，默认为"default-currency"
 * @param {boolean} options.allowWrap - 是否允许在区域内换行，默认为true
 * @returns {string} 格式化后的金额字符串
 */
export function formatCurrency(amount, selectedCurrency, defaultCurrency, options = {}) {
  // 设置默认选项
  const { showDefault = true, spaceBetween = false, useHtml = true, defaultCurrencyClass = 'default-currency', allowWrap = true } = options;

  // 空格字符，根据spaceBetween选项决定
  const space = spaceBetween ? ' ' : '';

  // 如果选择的货币与默认货币相同，只显示默认货币金额
  if (selectedCurrency.currency === defaultCurrency.currency) {
    return `${selectedCurrency.symbol}${space}${fen2yuan(amount)}`;
  } else {
    // 计算选择货币的金额
    const convertedAmount = exchangeRateConvert(amount, selectedCurrency.rate);

    // 如果需要显示默认货币，则同时显示两种货币
    if (showDefault) {
      // 使用HTML格式输出，应用样式
      if (useHtml) {
        const wrapClass = allowWrap ? 'allow-wrap' : '';
        return `<span class="primary-currency">${selectedCurrency.symbol}${space}${fen2yuan(convertedAmount)} <span class="${defaultCurrencyClass} ${wrapClass}">(${
          defaultCurrency.symbol
        }${space}${fen2yuan(amount)})</span></span>`;
      } else {
        // 不使用HTML，返回纯文本
        return `${selectedCurrency.symbol}${space}${fen2yuan(convertedAmount)} (${defaultCurrency.symbol}${space}${fen2yuan(amount)})`;
      }
    } else {
      // 只显示选择的货币
      return `${selectedCurrency.symbol}${space}${fen2yuan(convertedAmount)}`;
    }
  }
}

/**
 * 创建一个格式化函数，预设货币对象
 *
 * @param {Object} selectedCurrency - 用户选择的货币对象
 * @param {Object} defaultCurrency - 默认货币对象
 * @param {Object} options - 格式化选项
 * @returns {Function} 格式化函数，接受金额参数
 */
export function createCurrencyFormatter(selectedCurrency, defaultCurrency, options = {}) {
  return (amount) => formatCurrency(amount, selectedCurrency, defaultCurrency, options);
}
