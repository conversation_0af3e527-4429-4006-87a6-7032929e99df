<template>
  <div class="address-selector-section">
    <div class="row items-center justify-between q-mb-sm">
      <div class="row items-center">
        <q-icon name="location_on" color="primary" size="xs" class="q-mr-xs" />
        <div class="text-subtitle2 text-weight-bold">收货地址</div>
      </div>
      <q-btn flat dense color="primary" icon="add" label="添加地址" @click="openAddressForm" />
    </div>
    <div class="text-caption text-grey-8 q-mb-md">请选择您的包裹收件地址</div>

    <q-card flat bordered class="address-display-card q-pa-md">
      <div class="row items-center">
        <div v-if="modelValue && modelValue.id" class="col-grow">
          <div class="row items-center justify-between">
            <div>
              <div class="row items-center q-mb-xs">
                <q-icon name="person" size="xs" color="grey-7" class="q-mr-xs" />
                <div class="text-body1 text-weight-medium">{{ modelValue.name }}</div>
                <q-badge v-if="modelValue.isDefault" color="primary" class="q-ml-sm">默认</q-badge>
              </div>
              <div class="row items-center q-mb-xs">
                <q-icon name="phone" size="xs" color="grey-7" class="q-mr-xs" />
                <div class="text-body2">+{{ modelValue.phoneCode }}&nbsp;{{ modelValue.mobile }}</div>
              </div>
              <div class="row items-start">
                <q-icon name="home" size="xs" color="grey-7" class="q-mr-xs q-mt-xs" />
                <div class="text-body2 address-detail">{{ formatAddress(modelValue) }}</div>
              </div>
            </div>
            <q-btn flat round color="primary" icon="edit" @click="openDialog" />
          </div>
        </div>
        <div v-else class="col-grow text-center">
          <q-btn color="primary" label="选择收货地址" @click="openDialog" />
        </div>
      </div>
    </q-card>

    <!-- 地址选择弹窗 -->
    <AddressListDialog
      v-if="addresses && addresses.length > 0"
      v-model="addressDialog"
      :addresses="addresses"
      :initial-selected="tempSelectedAddress"
      @select="handleAddressSelect"
      @add-address="openAddressForm" />

    <!-- 添加/编辑地址弹窗 -->
    <AddressFormDialog ref="addressFormDialog" @confirm="handleAddressConfirm" />
  </div>
</template>

<script setup>
import { ref, watch } from 'vue';
import { useQuasar } from 'quasar';
import AddressFormDialog from '~/components/account/AddressFormDialog.vue';
import AddressListDialog from '~/components/account/AddressListDialog.vue';

// 定义组件接收的属性
const props = defineProps({
  modelValue: {
    type: Object,
    default: null,
  },
  addresses: {
    type: Array,
    required: true,
    default: () => [],
  },
});

// 定义组件向外发出的事件
const emit = defineEmits(['update:modelValue', 'add-address']);

const $q = useQuasar();

// 弹窗状态
const addressDialog = ref(false);
const tempSelectedAddress = ref(props.modelValue);
const addressFormDialog = ref(null);

// 格式化地址
function formatAddress(address) {
  console.log('address', address);
  if (!address) return '';
  return `${address.detailAddress} ${address.areaName} ${address.postCode}`;
}

// 打开地址选择弹窗
function openDialog() {
  tempSelectedAddress.value = props.modelValue;
  addressDialog.value = true;
}

// 处理地址选择
function handleAddressSelect(address) {
  if (address) {
    emit('update:modelValue', address);
    tempSelectedAddress.value = address;
  }
  addressDialog.value = false;
}

// 打开添加地址表单
function openAddressForm() {
  addressFormDialog.value.openModal();
}

// 处理地址确认
function handleAddressConfirm(address) {
  console.log('ParcelAddressSelector 收到新地址:', address);

  // 确保地址有ID
  if (!address.id) {
    console.error('新地址缺少ID:', address);
    $q.notify({
      color: 'negative',
      message: '地址数据异常，请重试',
      icon: 'error',
    });
    return;
  }

  // 发送添加地址事件
  emit('add-address', address);

  // 自动选择新地址
  emit('update:modelValue', address);

  // 显示成功提示
  $q.notify({
    color: 'positive',
    message: '地址添加成功',
    icon: 'check_circle',
  });
}

// 监听父组件传入的值变化
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue) {
      tempSelectedAddress.value = newValue;
    }
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
.address-selector-section {
  .address-display-card {
    border-radius: 8px;
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    }

    .address-detail {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
</style>
