<template>
  <Header />
  <div class="help-center-page">
    <div class="help-center-container">
      <!-- 使用共享的标题和搜索框组件 -->
      <HelpHeader />

      <div class="help-content-wrapper">
        <!-- 左侧导航菜单 -->
        <HelpSidebar :categories="state.list" />

        <!-- 右侧内容区域 -->
        <div class="help-content">
          <!-- 帮助分类卡片 -->
          <div class="help-categories-grid">
            <h2 class="section-title">{{ $t('help.categories.title') }}</h2>
            <div class="row q-col-gutter-md">
              <div v-for="(category, index) in cartCategories" :key="index" class="col-12 col-sm-6 col-md-4">
                <q-card class="help-category-card" :to="`/help/category/${category.id}`" clickable>
                  <q-card-section class="help-card-header">
                    <q-icon :name="category.icon" size="md" color="primary" />
                    <div class="help-card-title">{{ category.title }}</div>
                  </q-card-section>
                  <q-separator />
                  <q-card-section>
                    <p class="help-card-description">{{ category.description }}</p>
                    <q-list dense class="help-card-list">
                      <q-item v-for="(item, itemIndex) in category.items.slice(0, 3)" :key="itemIndex" :to="`/help/${category.code}/${item.code}`" clickable dense class="help-card-list-item">
                        <q-item-section avatar>
                          <q-icon name="article" size="xs" color="primary" />
                        </q-item-section>
                        <q-item-section>
                          <q-item-label>{{ item.title }}</q-item-label>
                        </q-item-section>
                      </q-item>
                    </q-list>
                    <div class="text-right q-mt-sm">
                      <q-btn flat color="primary" :to="`/help/category/${category.code}`" :label="$t('help.categories.viewMore')" style="font-size: 12px" />
                    </div>
                  </q-card-section>
                </q-card>
              </div>
            </div>
          </div>
          <!-- 常见问题解答 (FAQ) -->
          <div id="faq" class="popular-questions">
            <h2 class="section-title">{{ $t('help.faq.title') }}</h2>
            <p class="section-description">{{ $t('help.faq.description') }}</p>
            <q-list bordered separator>
              <q-item v-for="(question, index) in popularQuestions" :key="index" :to="question.link" clickable v-ripple class="popular-question-item">
                <q-item-section avatar>
                  <q-icon name="help_outline" color="primary" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>{{ question.title }}</q-item-label>
                </q-item-section>
                <q-item-section side>
                  <q-icon name="chevron_right" color="grey" />
                </q-item-section>
              </q-item>
            </q-list>
            <div class="text-right q-mt-md">
              <q-btn flat color="primary" icon-right="more_horiz" :label="$t('help.faq.viewMore')" to="/help/faq" />
            </div>
          </div>

          <!-- 视频教程 -->
          <div class="video-tutorials" v-if="videoTutorials.length">
            <h2 class="section-title">{{ $t('help.videoTutorials.title') }}</h2>
            <div class="row q-col-gutter-md">
              <div v-for="(video, index) in videoTutorials" :key="index" class="col-12 col-sm-6 col-md-4">
                <q-card class="video-card">
                  <q-img :src="video.thumbnail" :ratio="16 / 9" class="video-thumbnail" @click="openVideo(video)">
                    <div class="absolute-center">
                      <q-icon name="play_circle" size="3rem" color="white" class="play-icon" />
                    </div>
                  </q-img>
                  <q-card-section>
                    <div class="video-title">{{ video.title }}</div>
                    <div class="video-duration">
                      <q-icon name="schedule" size="xs" class="q-mr-xs" />
                      {{ video.duration }}
                    </div>
                  </q-card-section>
                </q-card>
              </div>
            </div>
          </div>

          <!-- 联系客服 -->
          <!-- <div class="contact-customer-service">
            <h2 class="section-title">{{ $t('help.contactSupport.title') }}</h2>
            <div class="contact-card-container">
              <q-card class="contact-card">
                <q-card-section class="contact-card-content">
                  <div class="contact-icon">
                    <q-icon name="headset_mic" size="4rem" color="primary" />
                  </div>
                  <div class="contact-info">
                    <h3 class="contact-title">{{ $t('customerService.title') }}</h3>
                    <p class="contact-description">{{ $t('customerService.description') }}</p>
                    <p class="contact-hours">{{ $t('customerService.workingHours') }}</p>
                    <q-btn color="primary" :label="$t('help.contactSupport.button')" icon="chat" @click="openChatInNewWindow" class="contact-button" />
                  </div>
                </q-card-section>
              </q-card>
            </div>
          </div> -->
        </div>
      </div>
    </div>
  </div>
  <Footer />
</template>

<script setup>
import { computed, onMounted, reactive } from 'vue';
import HelpSidebar from '~/components/help/HelpSidebar.vue';
import HelpHeader from '~/components/help/HelpHeader.vue';
import { useHelpStore } from '~/store/help';

const state = reactive({
  list: [],
  popularQuestions: [],
  helpCategories: [],
  videoTutorials: [],
  faqs: [],
  showMore: false,
});

const cartCategories = computed(() => {
  return state.list.filter((item) => item.recommend);
});

// 常见问题解答(FAQ) - 从state.list中获取所有faq为true的记录
const popularQuestions = computed(() => {
  const faqItems = [];
  // 遍历所有分类
  state.list.forEach((category) => {
    // 遍历分类下的所有文章
    if (category.items && Array.isArray(category.items)) {
      category.items.forEach((item) => {
        // 如果文章是FAQ，则添加到结果中
        if (item.faq) {
          faqItems.push({
            title: item.title,
            link: `/help/${category.code}/${item.code}`,
          });
        }
      });
    }
  });
  // 最多返回6个FAQ
  return faqItems.slice(0, 6);
});

// 更多FAQ现在通过专门的FAQ页面展示

// 视频教程
const videoTutorials = [
  // {
  //   title: '新手购物指南',
  //   thumbnail: 'https://cdn.quasar.dev/img/parallax1.jpg',
  //   duration: '3:45',
  //   url: 'https://www.example.com/video1',
  // },
  // {
  //   title: '如何选择合适的物流方式',
  //   thumbnail: 'https://cdn.quasar.dev/img/parallax2.jpg',
  //   duration: '5:20',
  //   url: 'https://www.example.com/video2',
  // },
  // {
  //   title: '退换货流程详解',
  //   thumbnail: 'https://cdn.quasar.dev/img/mountains.jpg',
  //   duration: '4:15',
  //   url: 'https://www.example.com/video3',
  // },
];

// 使用Pinia Store
const helpStore = useHelpStore();

// 获取帮助中心数据
onMounted(async () => {
  await helpStore.fetchCategories();
  // 将store中的数据同步到state中
  state.list = helpStore.getCategories;
});

// 打开视频
const openVideo = (video) => {
  // 在实际应用中，这里可以打开视频播放弹窗或跳转到视频页面
  window.open(video.url, '_blank');
};

// 更多常见问题现在通过路由跳转到专门的FAQ页面

// 在新窗口打开客服聊天页面
const openChatInNewWindow = () => {
  window.open('/chat', '_blank');
};
</script>

<style lang="scss" scoped>
.help-center-page {
  background-color: #f5f7fa;
  padding: 20px 0 40px;
}

.help-center-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px 0;
}

.page-title {
  font-size: 32px;
  font-weight: bold;
  color: #333;
  margin-bottom: 20px;
}

.search-box {
  max-width: 600px;
  margin: 0 auto;
}

.search-input {
  font-size: 16px;
}

.help-content-wrapper {
  display: flex;
  gap: 30px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.help-sidebar {
  width: 280px;
  flex-shrink: 0;
  background-color: #f9f9f9;
  border-right: 1px solid #eee;
  padding: 20px 0;
}

.help-menu {
  .help-category-header {
    font-weight: bold;
    color: #333;
  }
}

.help-submenu {
  padding-left: 16px;

  .q-item {
    border-radius: 4px;
    margin: 2px 8px;
    padding: 8px 16px;

    &.active-help-item {
      background-color: rgba(25, 118, 210, 0.1);
      color: #1976d2;
      font-weight: 500;
    }
  }
}

.contact-support {
  padding: 0 16px;

  .contact-title {
    font-size: 14px;
    color: #666;
    margin-bottom: 12px;
    text-align: center;
  }
}

.help-content {
  flex-grow: 1;
  padding: 30px;
  overflow-y: auto;
}

.section-title {
  font-size: 22px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
  position: relative;

  &:after {
    content: '';
    display: block;
    width: 50px;
    height: 3px;
    background-color: #1976d2;
    margin-top: 8px;
  }
}

.section-description {
  font-size: 16px;
  color: #666;
  margin-bottom: 20px;
}

.popular-questions {
  margin-bottom: 40px;
}

.popular-question-item {
  transition: background-color 0.3s;

  &:hover {
    background-color: #f5f5f5;
  }
}

.help-categories-grid {
  margin-bottom: 40px;
}

.help-category-card {
  height: 100%;
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
  }
}

.help-card-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.help-card-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.help-card-description {
  font-size: 14px;
  color: #666;
  margin-bottom: 16px;
}

.help-card-list {
  margin-bottom: 8px;
}

.help-card-list-item {
  padding: 4px 0;
}

.video-tutorials {
  margin-bottom: 20px;
}

.video-card {
  height: 100%;
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);

    .play-icon {
      transform: scale(1.2);
    }
  }
}

.video-thumbnail {
  cursor: pointer;
  position: relative;
}

.play-icon {
  transition: transform 0.3s ease;
  opacity: 0.8;
}

.video-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.video-duration {
  font-size: 12px;
  color: #666;
  display: flex;
  align-items: center;
}

.contact-customer-service {
  margin-bottom: 40px;
}

.contact-card-container {
  display: flex;
  justify-content: center;
}

.contact-card {
  max-width: 600px;
  width: 100%;
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
  }
}

.contact-card-content {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 30px;
}

.contact-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: rgba(25, 118, 210, 0.1);
  flex-shrink: 0;
}

.contact-info {
  flex-grow: 1;
}

.contact-title {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  margin: 0 0 10px 0;
}

.contact-description {
  font-size: 16px;
  color: #666;
  margin-bottom: 8px;
}

.contact-hours {
  font-size: 14px;
  color: #888;
  margin-bottom: 16px;
}

.contact-button {
  padding: 8px 16px;
}

@media (max-width: 1023px) {
  .help-content-wrapper {
    flex-direction: column;
  }

  .help-sidebar {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid #eee;
  }
}

@media (max-width: 767px) {
  .page-title {
    font-size: 26px;
  }

  .help-content {
    padding: 20px 16px;
  }

  .section-title {
    font-size: 20px;
  }

  .section-description {
    font-size: 14px;
  }

  .help-card-title {
    font-size: 16px;
  }
}

/* 弹窗样式 */
:deep(.more-faq-list) {
  margin-top: 16px;
}

:deep(.more-faq-item) {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid #eee;
}

:deep(.more-faq-number) {
  width: 24px;
  height: 24px;
  background-color: #1976d2;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  margin-right: 12px;
  flex-shrink: 0;
}

:deep(.more-faq-title) {
  flex-grow: 1;
}

:deep(.more-faq-link) {
  color: #1976d2;
  text-decoration: none;
  font-size: 14px;

  &:hover {
    text-decoration: underline;
  }
}

:deep(.contact-info) {
  margin-top: 16px;
}

:deep(.contact-item) {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 14px;
}
</style>
