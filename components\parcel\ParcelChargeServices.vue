<template>
  <div class="value-added-services-section" v-if="chargeServices && chargeServices.length > 0">
    <div class="row items-center q-mb-xs">
      <q-icon name="add_circle" color="primary" size="xs" class="q-mr-xs" />
      <div class="text-subtitle2 text-weight-medium">增值服务</div>
    </div>
    <div class="text-caption text-grey-8 q-mb-xs">选择您需要的付费增值服务</div>

    <q-card flat bordered class="q-pa-sm">
      <div class="column q-gutter-y-xs">
        <div v-for="service in chargeServices" :key="service.id" class="col-12">
          <q-card flat bordered :class="{ 'selected-service': isServiceSelected(service.id) }" class="service-card cursor-pointer" @click="toggleService(service.id)">
            <q-card-section class="q-pa-sm">
              <div class="row items-center no-wrap">
                <q-checkbox v-model="selectedServiceIds" :val="service.id" color="primary" class="q-mr-sm" @click.stop />
                <div class="col">
                  <div class="text-body2 text-weight-medium">{{ service.name }}</div>
                  <div class="text-caption text-grey-8">{{ service.description }}</div>
                </div>
                <div class="text-primary text-weight-medium q-ml-sm">¥{{ fen2yuan(service.price) }}</div>
              </div>
            </q-card-section>
          </q-card>
        </div>
      </div>

      <div v-if="selectedServiceIds.length > 0" class="q-mt-sm">
        <q-separator class="q-mb-sm" />
        <div class="row justify-end items-center">
          <div class="text-caption">
            已选择 <span class="text-primary text-weight-medium">{{ selectedServiceIds.length }}</span> 项服务， 费用合计:
            <span class="text-primary text-weight-medium">¥{{ fen2yuan(totalSelectedServicesPrice) }}</span>
          </div>
        </div>
      </div>
    </q-card>
  </div>
</template>

<script setup>
import { ref, watch, computed, nextTick } from 'vue';
import { fen2yuan } from '../../utils/utils';

// 定义组件接收的属性
const props = defineProps({
  modelValue: {
    type: Array,
    default: () => [],
  },
  chargeServices: {
    type: Array,
    required: true,
    default: () => [],
  },
});

// 定义组件向外发出的事件
const emit = defineEmits(['update:modelValue']);

// 选中的服务ID列表
const selectedServiceIds = ref([...props.modelValue]);

// 计算选中服务的总价格
const totalSelectedServicesPrice = computed(() => {
  return selectedServiceIds.value.reduce((total, serviceId) => {
    const service = props.chargeServices.find((s) => s.id === serviceId);
    return total + (service ? service.price : 0);
  }, 0);
});

// 检查服务是否被选中
function isServiceSelected(serviceId) {
  return selectedServiceIds.value.includes(serviceId);
}

// 切换服务选择状态
function toggleService(serviceId) {
  if (isServiceSelected(serviceId)) {
    selectedServiceIds.value = selectedServiceIds.value.filter((id) => id !== serviceId);
  } else {
    selectedServiceIds.value.push(serviceId);
  }

  // 使用nextTick确保DOM更新后再发出事件
  nextTick(() => {
    emit('update:modelValue', [...selectedServiceIds.value]);
  });
}

// 监听父组件传入的值变化
watch(
  () => props.modelValue,
  (newValue) => {
    // 避免无限循环，只有当值真正变化时才更新
    if (JSON.stringify(newValue) !== JSON.stringify(selectedServiceIds.value)) {
      selectedServiceIds.value = [...newValue];
    }
  },
  { deep: true }
);
</script>

<style lang="scss" scoped>
.value-added-services-section {
  .q-card {
    border-radius: 8px;
  }

  .service-card {
    transition: all 0.2s ease;
    height: 100%;

    &:hover {
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
      transform: translateY(-2px);
    }

    &.selected-service {
      border: 2px solid #1976d2;
      background-color: rgba(25, 118, 210, 0.05);
    }
  }

  .flex-grow {
    flex-grow: 1;
    min-width: 0;
  }
}
</style>
