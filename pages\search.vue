<template>
  <!-- 公告栏和菜单 -->
  <Header />

  <div class="search-page">
    <div class="search-container">
      <!-- 搜索框 -->
      <div class="search-header">
        <h1 class="search-title">商品搜索</h1>
        <div class="search-bar">
          <div class="search-input-container">
            <input type="search" v-model="searchQuery" :placeholder="searchStore.searchType === 'image' ? '已通过图片搜索，可输入关键词进一步筛选' : '搜索商品或链接'" @keyup.enter="handleSearch" />
          </div>
          <div class="search-button-container" @click="handleSearch">
            <button class="search-button">
              <i class="iconfont icon-sousuo sousuo"></i>
            </button>
          </div>
        </div>
      </div>

      <!-- 平台切换标签 -->
      <div class="platform-tabs" v-if="!searchStore.isEmptySearch && searchStore.isKeywordSearch">
        <q-tabs v-model="selectedPlatform" align="left" class="text-primary platform-tab-container" dense @update:model-value="handlePlatformChange">
          <q-tab v-for="platform in SearchPlatforms" :key="platform.name" :name="platform.name" :label="$t(platform.labelKey)" />

          <!-- <q-tab name="taobao" label="淘宝" />
          <q-tab name="1688" label="1688" />
          <q-tab name="jd" label="京东" /> -->
        </q-tabs>
        <q-separator />
      </div>

      <!-- 搜索结果 -->
      <div class="search-results">
        <!-- 搜索结果统计 -->
        <div class="search-stats" v-if="!searchStore.isEmptySearch">
          <p>
            <template v-if="searchStore.loading">
              <template v-if="searchStore.searchType === 'image'">正在分析图片并搜索相似商品...</template>
              <template v-else>正在搜索中...</template>
            </template>
            <template v-else-if="searchStore.hasResults">
              <template v-if="searchStore.searchType === 'image'">
                在 <span class="highlight">{{ searchStore.currentPlatformName }}</span> 找到的与您上传图片相似的商品
              </template>
              <template v-else>
                在 <span class="highlight">{{ searchStore.currentPlatformName }}</span> 找到的与 "<span class="highlight">{{ searchStore.searchQuery }}</span
                >" 相关的商品
              </template>
            </template>
            <template v-else>
              <template v-if="searchStore.searchType === 'image'">
                在 <span class="highlight">{{ searchStore.currentPlatformName }}</span> 没有找到与您上传图片相似的商品
              </template>
              <template v-else>
                在 <span class="highlight">{{ searchStore.currentPlatformName }}</span> 没有找到与 "<span class="highlight">{{ searchStore.searchQuery }}</span
                >" 相关的商品
              </template>
            </template>
          </p>
        </div>

        <!-- 加载中 -->
        <div v-if="searchStore.loading" class="loading-container">
          <q-spinner color="primary" size="3em" />
          <p>正在加载搜索结果...</p>
        </div>

        <!-- 空搜索提示 -->
        <div v-else-if="searchStore.isEmptySearch" class="empty-search">
          <i class="iconfont icon-sousuo empty-icon"></i>
          <p>请输入搜索关键词</p>
        </div>

        <!-- 无结果提示 -->
        <div v-else-if="!searchStore.hasResults" class="no-results">
          <!-- <i class="iconfont icon-zanwushuju empty-icon"></i> -->
          <!-- <p>没有找到相关商品</p>
          <p class="suggestion">您可以尝试使用其他关键词搜索</p> -->

          <!-- 自定义订单建议 -->
          <div class="custom-order-suggestion">
            <div class="suggestion-card">
              <q-icon name="build" size="32px" color="primary" class="q-mb-sm" />
              <h4>找不到想要的商品？</h4>
              <p>创建自定义订单，我们帮您代购任何商品！</p>
              <div class="suggestion-actions">
                <q-btn color="primary" @click="goToCustomOrder" class="q-mr-sm"> 创建自定义订单 </q-btn>
                <!-- <q-btn outline color="primary" @click="goToCustomOrderWithLink" v-if="searchStore.isKeywordSearch"> 使用商品链接 </q-btn> -->
              </div>
            </div>
          </div>
        </div>

        <!-- 搜索结果列表 -->
        <div v-else class="product-grid">
          <div v-for="(product, index) in searchStore.searchResults" :key="product.id" class="product-item">
            <ProductSearchBox :product="product" :index="index" />
          </div>
        </div>

        <!-- 分页 -->
        <div v-if="searchStore.hasResults" class="pagination-container">
          <q-pagination v-model="currentPage" :max="searchStore.totalPages" :max-pages="6" boundary-numbers direction-links @update:model-value="handlePageChange" />
        </div>
      </div>
    </div>
  </div>

  <Footer />
</template>

<script setup>
import { useSearchStore } from '~/store/search';
import { useRoute, useRouter } from 'vue-router';
import { analyzeSearchInput } from '~/utils/searchUtils';
import { uploadFile as uploadFileApi } from '~/composables/fileApi';
import SearchApi from '~/composables/searchApi';
import { useQuasar } from 'quasar';

const route = useRoute();
const router = useRouter();
const searchStore = useSearchStore();
const $q = useQuasar();

// 搜索查询参数
const searchQuery = ref('');
const currentPage = ref(1);
const pageSize = ref(10);
const selectedPlatform = ref('taobao');

// 监听路由参数变化
watch(
  () => route.query,
  async (newQuery) => {
    console.log('🔍 Route query changed:', newQuery);

    // 处理以图搜物
    if (newQuery.type === 'image') {
      console.log('🔍 Image search detected:', newQuery);
      searchQuery.value = ''; // 图片搜索时清空输入框
      searchStore.setSearchType('image');

      if (newQuery.imgId) {
        console.log('📷 Using existing image ID:', newQuery.imgId);
        // 已有图片ID，直接搜索
        const imageId = decodeURIComponent(newQuery.imgId);
        searchStore.setImageId(imageId);

        // 等待搜索完成
        try {
          await searchStore.searchByImage();
          console.log('✅ Image search completed, results:', searchStore.searchResults.length);
        } catch (error) {
          console.error('❌ Image search failed:', error);
        }
      } else if (newQuery.status === 'uploading') {
        console.log('📤 Starting image upload and search...');
        // 需要上传图片，稍微延迟一下确保页面完全加载
        setTimeout(() => {
          handleImageUploadAndSearch();
        }, 100);
      }
      return;
    }

    // 处理关键词搜索
    if (newQuery.q !== undefined) {
      searchQuery.value = decodeURIComponent(newQuery.q);

      // 分析搜索输入类型
      const analysis = analyzeSearchInput(searchQuery.value);

      // 如果是链接搜索，跳转到商品详情页
      if (analysis.type === 'url') {
        router.push(`/product?url=${encodeURIComponent(searchQuery.value)}`);
        return;
      }

      // 设置搜索类型为关键词
      searchStore.setSearchType('keyword');
      searchStore.setSearchQuery(searchQuery.value);

      // 设置平台参数
      if (newQuery.platform) {
        selectedPlatform.value = newQuery.platform;
        searchStore.setSelectedPlatform(newQuery.platform);
      } else {
        selectedPlatform.value = 'taobao';
        searchStore.setSelectedPlatform('taobao');
      }

      // 如果有页码参数，设置当前页码
      if (newQuery.page) {
        currentPage.value = parseInt(newQuery.page) || 1;
        searchStore.setCurrentPage(currentPage.value);
      } else {
        currentPage.value = 1;
        searchStore.setCurrentPage(1);
      }

      // 执行搜索
      searchStore.searchProducts(currentPage.value, pageSize.value);
    } else if (newQuery.type !== 'image') {
      // 只有在不是图片搜索的情况下才清空搜索结果
      console.log('🧹 Clearing search results (not image search)');
      searchQuery.value = '';
      searchStore.setSearchQuery('');
      searchStore.setSearchType('keyword');
      searchStore.clearSearchResults();
    }
  },
  { immediate: true }
);

// 处理搜索
const handleSearch = () => {
  if (searchQuery.value.trim()) {
    // 分析搜索输入类型
    const analysis = analyzeSearchInput(searchQuery.value.trim());

    if (analysis.type === 'url') {
      // 链接搜索直接跳转到商品详情页
      router.push(`/product?url=${encodeURIComponent(searchQuery.value.trim())}`);
    } else {
      // 关键字搜索跳转到搜索结果页
      router.push({
        path: '/search',
        query: {
          q: encodeURIComponent(searchQuery.value.trim()),
          platform: selectedPlatform.value,
          page: 1,
        },
      });
    }
  } else {
    // 如果搜索框为空，则清空搜索结果
    router.push('/search');
  }
};

// 处理平台切换
const handlePlatformChange = (platform) => {
  if (searchQuery.value.trim()) {
    router.push({
      path: '/search',
      query: {
        q: encodeURIComponent(searchQuery.value.trim()),
        platform: platform,
        page: 1,
      },
    });
  }
};

// 处理分页变化
const handlePageChange = (page) => {
  if (searchQuery.value.trim()) {
    router.push({
      path: '/search',
      query: {
        q: encodeURIComponent(searchQuery.value.trim()),
        platform: selectedPlatform.value,
        page: page,
      },
    });
  }
};

// 跳转到自定义订单页面
const goToCustomOrder = () => {
  router.push('/diy-order');
};

// 跳转到自定义订单页面并预填充搜索关键词作为商品名称
const goToCustomOrderWithLink = () => {
  const params = new URLSearchParams();
  if (searchQuery.value.trim()) {
    params.set('productName', searchQuery.value.trim());
  }
  router.push(`/diy-order?${params.toString()}`);
};

// 处理图片上传和搜索
const handleImageUploadAndSearch = async () => {
  console.log('🔄 handleImageUploadAndSearch called');

  // 确保在客户端环境中执行
  if (!process.client) {
    console.log('⚠️ Running on server, skipping image upload');
    return;
  }

  try {
    // 从sessionStorage获取文件信息
    const fileInfoStr = sessionStorage.getItem('imageSearchFile');
    const fileUrl = sessionStorage.getItem('imageSearchFileUrl');

    console.log('📦 Retrieved from sessionStorage:', { fileInfoStr: !!fileInfoStr, fileUrl: !!fileUrl });

    if (!fileInfoStr || !fileUrl) {
      console.log('❌ Missing file info or URL in sessionStorage');
      $q.notify({
        color: 'negative',
        message: '未找到上传的图片信息',
        icon: 'error',
      });
      return;
    }

    // 从blob URL重新创建文件对象
    const response = await fetch(fileUrl);
    const blob = await response.blob();
    const fileInfo = JSON.parse(fileInfoStr);
    const file = new File([blob], fileInfo.name, {
      type: fileInfo.type,
      lastModified: fileInfo.lastModified,
    });

    // 设置加载状态
    searchStore.loading = true;

    // 第一步：上传图片
    const uploadResponse = await uploadFileApi(file);

    if (uploadResponse.code !== 0 || !uploadResponse.data) {
      throw new Error(uploadResponse.msg || '图片上传失败');
    }

    const imageUrl = uploadResponse.data.trim();

    // 第二步：调用以图搜物API
    const searchResponse = await SearchApi.searchByImg(imageUrl);

    if (searchResponse.code === 0) {
      console.log('✅ Search API response successful:', searchResponse.data);

      // 更新搜索store状态
      searchStore.searchResults = searchResponse.data.list || [];
      searchStore.total = searchResponse.data.total || 0;
      searchStore.currentPage = 1;
      searchStore.searchType = 'image';
      searchStore.searchQuery = ''; // 图片搜索时清空关键词
      searchStore.loading = false;
      searchStore.setImageId(imageUrl);

      console.log('📊 Updated search store:', {
        resultsCount: searchStore.searchResults.length,
        total: searchStore.total,
        searchType: searchStore.searchType,
        hasResults: searchStore.hasResults,
        isEmptySearch: searchStore.isEmptySearch,
      });

      // 更新URL，移除uploading状态
      router.replace({
        path: '/search',
        query: {
          type: 'image',
          imgId: encodeURIComponent(imageUrl),
        },
      });

      // 清理sessionStorage
      sessionStorage.removeItem('imageSearchFile');
      sessionStorage.removeItem('imageSearchFileUrl');
      URL.revokeObjectURL(fileUrl);

      console.log('🎉 Image search completed successfully');
    } else {
      throw new Error(searchResponse.msg || '图片搜索失败');
    }
  } catch (error) {
    console.error('以图搜物失败:', error);
    $q.notify({
      color: 'negative',
      message: error.message || '以图搜物失败，请重试',
      icon: 'error',
    });

    // 清理sessionStorage
    sessionStorage.removeItem('imageSearchFile');
    sessionStorage.removeItem('imageSearchFileUrl');

    // 跳转回首页
    router.push('/');
  } finally {
    searchStore.loading = false;
  }
};
</script>

<style lang="scss" scoped>
.search-page {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px 16px;

  .search-container {
    width: 100%;

    .platform-tabs {
      margin-bottom: 20px;

      .platform-tab-container {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 4px;

        .q-tab {
          min-height: 40px;
          border-radius: 6px;
          margin: 2px;
          transition: all 0.3s ease;

          &.q-tab--active {
            background: #0073e6;
            color: white;
          }

          &:not(.q-tab--active) {
            color: #666;

            &:hover {
              background: #e9ecef;
            }
          }
        }
      }
    }

    .search-header {
      margin-bottom: 30px;
      text-align: center;

      .search-title {
        font-size: 28px;
        font-weight: bold;
        margin-bottom: 20px;
        color: #333;
      }

      .search-bar {
        position: relative;
        width: 100%;
        max-width: 600px;
        height: 46px;
        margin: 0 auto;
        display: flex;
        justify-content: center;

        .search-input-container {
          flex: 1;
          position: relative;
        }

        input[type='search'] {
          width: 100%;
          height: 46px;
          border-radius: 23px 0 0 23px;
          border: 2px solid #0073e6;
          border-right: none;
          padding: 0 20px;
          font-size: 16px;
          outline: none;
        }

        .search-button-container {
          width: 70px;
          height: 46px;
        }

        .search-button {
          width: 100%;
          height: 100%;
          background-color: #0073e6;
          border-radius: 0 23px 23px 0;
          border: none;
          cursor: pointer;
          position: relative;
          display: flex;
          justify-content: center;
          align-items: center;

          .sousuo {
            font-size: 24px;
            color: #fff;
          }
        }
      }
    }

    .search-results {
      .search-stats {
        margin-bottom: 20px;
        font-size: 16px;
        color: #666;

        .highlight {
          color: #0073e6;
          font-weight: bold;
        }
      }

      .loading-container,
      .empty-search,
      .no-results {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 60px 0;
        color: #666;

        .empty-icon {
          font-size: 60px;
          margin-bottom: 20px;
          color: #ccc;
        }

        p {
          margin: 10px 0;
          font-size: 16px;
        }

        .suggestion {
          font-size: 14px;
          color: #999;
        }

        .custom-order-suggestion {
          //margin-top: 30px;
          width: 100%;
          max-width: 400px;

          .suggestion-card {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border: 2px dashed #0073e6;
            border-radius: 12px;
            padding: 30px 20px;
            text-align: center;

            h4 {
              color: #333;
              margin: 10px 0;
              font-size: 18px;
            }

            p {
              color: #666;
              margin: 10px 0 20px;
              font-size: 14px;
              line-height: 1.5;
            }

            .suggestion-actions {
              display: flex;
              gap: 10px;
              justify-content: center;
              flex-wrap: wrap;
            }
          }
        }
      }

      .product-grid {
        display: grid;
        grid-template-columns: repeat(5, 1fr);
        gap: 16px;
        margin-bottom: 30px;

        @media (max-width: 1023px) and (min-width: 600px) {
          grid-template-columns: repeat(3, 1fr);
        }

        @media (max-width: 599px) {
          grid-template-columns: repeat(2, 1fr);
          gap: 10px;
        }

        .product-item {
          width: 100%;
        }
      }

      .pagination-container {
        display: flex;
        justify-content: center;
        margin: 30px 0;
      }
    }
  }
}

@media (max-width: 599px) {
  .search-page {
    padding: 15px 10px;

    .search-container {
      .search-header {
        margin-bottom: 20px;

        .search-title {
          font-size: 22px;
          margin-bottom: 15px;
        }

        .search-bar {
          height: 40px;

          input[type='search'] {
            height: 40px;
            font-size: 14px;
            padding: 0 15px;
          }

          .search-button-container {
            width: 60px;
            height: 40px;
          }

          .search-button {
            .sousuo {
              font-size: 20px;
            }
          }
        }
      }
    }
  }
}
</style>
