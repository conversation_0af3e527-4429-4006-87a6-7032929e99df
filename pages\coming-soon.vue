<template>
  <div class="coming-soon-page">
    <div class="coming-soon-container">
      <div class="logo-container">
        <img src="/images/logo.png" alt="Logo" class="logo" />
      </div>

      <h1 class="title">即将上线</h1>
      <p class="description">我们正在努力开发这个功能，很快就会与您见面！</p>

      <div class="countdown-container">
        <div class="countdown-item">
          <div class="countdown-value">{{ countdown.days }}</div>
          <div class="countdown-label">天</div>
        </div>
        <div class="countdown-item">
          <div class="countdown-value">{{ countdown.hours }}</div>
          <div class="countdown-label">时</div>
        </div>
        <div class="countdown-item">
          <div class="countdown-value">{{ countdown.minutes }}</div>
          <div class="countdown-label">分</div>
        </div>
        <div class="countdown-item">
          <div class="countdown-value">{{ countdown.seconds }}</div>
          <div class="countdown-label">秒</div>
        </div>
      </div>

      <div class="subscription-form">
        <p class="form-title">订阅我们的通知，获取最新消息</p>
        <div class="form-input-group">
          <q-input v-model="email" type="email" placeholder="请输入您的邮箱" outlined class="subscription-input" :rules="[(val) => validateEmail(val) || '请输入有效的邮箱地址']" />
          <q-btn color="primary" label="订阅" class="subscription-button" :disable="!validateEmail(email)" @click="subscribe" />
        </div>
      </div>

      <div class="social-links">
        <q-btn round flat color="primary" icon="fab fa-weixin" class="social-button">
          <q-tooltip>关注我们的微信公众号</q-tooltip>
        </q-btn>
        <q-btn round flat color="primary" icon="fab fa-weibo" class="social-button">
          <q-tooltip>关注我们的微博</q-tooltip>
        </q-btn>
        <q-btn round flat color="primary" icon="mail" class="social-button">
          <q-tooltip>联系我们</q-tooltip>
        </q-btn>
      </div>

      <div class="back-link">
        <q-btn flat color="primary" to="/" icon="arrow_back" label="返回首页" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue';
import { useQuasar } from 'quasar';

const $q = useQuasar();
const email = ref('');
const countdown = ref({
  days: 0,
  hours: 0,
  minutes: 0,
  seconds: 0,
});

// 设置预计上线日期（这里设置为当前日期后30天）
const targetDate = new Date();
targetDate.setDate(targetDate.getDate() + 30);

// 更新倒计时
const updateCountdown = () => {
  const now = new Date();
  const diff = targetDate - now;

  if (diff <= 0) {
    // 如果已经到期，所有值设为0
    countdown.value = {
      days: 0,
      hours: 0,
      minutes: 0,
      seconds: 0,
    };
    clearInterval(timer);
    return;
  }

  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
  const seconds = Math.floor((diff % (1000 * 60)) / 1000);

  countdown.value = {
    days,
    hours,
    minutes,
    seconds,
  };
};

// 验证邮箱格式
const validateEmail = (email) => {
  const re = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
  return re.test(String(email).toLowerCase());
};

// 订阅功能
const subscribe = () => {
  if (!validateEmail(email.value)) return;

  // 这里应该是实际的订阅API调用
  // await fetch('/api/subscribe', {
  //   method: 'POST',
  //   body: JSON.stringify({ email: email.value }),
  //   headers: { 'Content-Type': 'application/json' }
  // });

  // 显示成功提示
  $q.notify({
    color: 'positive',
    message: '订阅成功！我们会在功能上线时通知您。',
    icon: 'check_circle',
    position: 'top',
    timeout: 3000,
  });

  // 清空输入框
  email.value = '';
};

let timer;

onMounted(() => {
  updateCountdown();
  timer = setInterval(updateCountdown, 1000);
});

onBeforeUnmount(() => {
  clearInterval(timer);
});
</script>

<style lang="scss" scoped>
.coming-soon-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
  padding: 20px;
}

.coming-soon-container {
  max-width: 800px;
  width: 100%;
  text-align: center;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  padding: 40px 20px;
}

.logo-container {
  margin-bottom: 30px;

  .logo {
    max-width: 150px;
    height: auto;
  }
}

.title {
  font-size: 36px;
  font-weight: bold;
  color: #1976d2;
  margin-bottom: 16px;
}

.description {
  font-size: 18px;
  color: #555;
  margin-bottom: 40px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.countdown-container {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 50px;
}

.countdown-item {
  width: 80px;
  height: 100px;
  background-color: #1976d2;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 4px 10px rgba(25, 118, 210, 0.2);
}

.countdown-value {
  font-size: 32px;
  font-weight: bold;
  line-height: 1;
}

.countdown-label {
  font-size: 14px;
  margin-top: 8px;
}

.subscription-form {
  max-width: 500px;
  margin: 0 auto 40px;
}

.form-title {
  font-size: 16px;
  color: #666;
  margin-bottom: 16px;
}

.form-input-group {
  display: flex;
  gap: 10px;
}

.subscription-input {
  flex-grow: 1;
}

.social-links {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 30px;
}

.social-button {
  font-size: 24px;
}

.back-link {
  margin-top: 20px;
}

@media (max-width: 767px) {
  .title {
    font-size: 28px;
  }

  .description {
    font-size: 16px;
  }

  .countdown-container {
    gap: 10px;
  }

  .countdown-item {
    width: 60px;
    height: 80px;
  }

  .countdown-value {
    font-size: 24px;
  }

  .countdown-label {
    font-size: 12px;
  }

  .form-input-group {
    flex-direction: column;
  }

  .subscription-button {
    width: 100%;
  }
}
</style>
