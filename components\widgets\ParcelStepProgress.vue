<template>
  <div class="modern-step-progress" :class="{ 'compact-mode': $q.screen.lt.sm }">
    <div class="steps-container" ref="stepsContainer">
      <div
        v-for="(step, index) in steps"
        :key="index"
        class="step-item"
        :class="{
          active: currentStep >= index + 1,
          completed: currentStep > index + 1,
        }">
        <div class="step-icon">
          <q-icon v-if="currentStep > index + 1" name="check" size="sm" />
          <span v-else>{{ index + 1 }}</span>
        </div>
        <div class="step-label">{{ step.title }}</div>
        <div v-if="index < steps.length - 1" class="step-connector"></div>
      </div>
    </div>

    <!-- 移动端指示器 -->
    <div class="step-indicators" v-if="$q.screen.lt.sm">
      <div
        v-for="(step, index) in steps"
        :key="index"
        class="indicator-dot"
        :class="{
          active: currentStep >= index + 1,
          completed: currentStep > index + 1,
        }"
        @click="scrollToStep(index)"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import { useI18n } from 'vue-i18n';

const props = defineProps({
  currentStep: {
    type: [Number, String],
    default: 1,
  },
});

const stepsContainer = ref(null);
const current = ref(Number(props.currentStep));
const { t } = useI18n();

// 监听currentStep变化
watch(
  () => props.currentStep,
  (newVal) => {
    current.value = Number(newVal);
  }
);

// 定义步骤
const steps = [
  { title: '提交包裹并支付', icon: 'edit' },
  { title: '平台发货', icon: 'shopping_cart' },
  { title: '确认收货', icon: 'inventory_2' },
  { title: '用户评价', icon: 'check_circle' },
];

// 滚动到指定步骤
function scrollToStep(index) {
  if (!stepsContainer.value) return;

  const stepItems = stepsContainer.value.querySelectorAll('.step-item');
  if (stepItems && stepItems[index]) {
    const containerWidth = stepsContainer.value.offsetWidth;
    const stepWidth = stepItems[index].offsetWidth;
    const scrollLeft = stepItems[index].offsetLeft - containerWidth / 2 + stepWidth / 2;

    stepsContainer.value.scrollTo({
      left: scrollLeft,
      behavior: 'smooth',
    });
  }
}

// 初始化时滚动到当前步骤
onMounted(() => {
  // 延迟执行，确保DOM已完全渲染
  setTimeout(() => {
    scrollToStep(current.value - 1);
  }, 300);
});
</script>

<style lang="scss" scoped>
.modern-step-progress {
  width: 100%;
  margin: 20px 0;
  position: relative;

  .steps-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    overflow-x: auto;
    padding: 10px 0;
    scroll-behavior: smooth;

    // 隐藏滚动条但保留功能
    &::-webkit-scrollbar {
      height: 0;
    }
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */

    .step-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;
      min-width: 100px;
      padding: 0 10px;

      .step-icon {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        background-color: #e0e0e0;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #757575;
        font-weight: 500;
        margin-bottom: 8px;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .step-label {
        font-size: 0.9rem;
        color: #757575;
        text-align: center;
        transition: all 0.3s ease;
        white-space: nowrap;
      }

      .step-connector {
        position: absolute;
        top: 18px;
        right: -50%;
        width: 100%;
        height: 2px;
        background-color: #e0e0e0;
        z-index: -1;
        transition: all 0.3s ease;
      }

      &.active {
        .step-icon {
          background-color: #4caf50;
          color: white;
        }

        .step-label {
          color: #4caf50;
          font-weight: 500;
        }
      }

      &.completed {
        .step-icon {
          background-color: #4caf50;
          color: white;
        }

        .step-connector {
          background-color: #4caf50;
        }
      }
    }
  }

  .step-indicators {
    display: flex;
    justify-content: center;
    margin-top: 10px;

    .indicator-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: #e0e0e0;
      margin: 0 4px;
      transition: all 0.3s ease;
      cursor: pointer;

      &.active,
      &.completed {
        background-color: #4caf50;
      }
    }
  }

  &.compact-mode {
    margin: 10px auto;
    max-width: 100%;

    .steps-container {
      padding: 5px 0;
      justify-content: center;

      .step-item {
        min-width: 80px;

        .step-icon {
          width: 28px;
          height: 28px;
          font-size: 0.8rem;
          margin-bottom: 4px;
        }

        .step-label {
          font-size: 0.75rem;
        }

        .step-connector {
          top: 14px;
        }
      }
    }
  }
}
</style>
