const CartApi = {
  addCart: (data) => {
    // const { $t } = useNuxtApp();
    return useClientPost('/trade/cart/add', {
      body: data,
    });
  },
  updateCartCount: (data) => {
    return useClientPut('/trade/cart/update-count', {
      method: 'PUT',
      body: data,
    });
  },
  updateCartMemo: (data) => {
    return useClientPut('/trade/cart/update-memo', {
      method: 'PUT',
      body: data,
    });
  },
  updateCartSelected: (data) => {
    return useClientPut('/trade/cart/update-selected', {
      body: data,
    });
  },
  deleteCart: (ids) => {
    return useClientDelete('/trade/cart/delete', {
      params: {
        ids,
      },
    });
  },
  getCartList: () => {
    return useClientGet('/trade/cart/list', {
      custom: {
        auth: true,
      },
    });
  },
  getCartListByIds: (ids) => {
    return useClientPost('/trade/cart/listByIds', {
      params: {
        ids,
      },
      custom: {
        auth: true,
      },
    });
  },
};

export default CartApi;
