<template>
  <!-- 专题 -->
  <div class="subject-module">
    <div class="module-title">
      <h4>{{ title }}</h4>
      <a :href="moreLink" class="more-link">
        {{ $t('more') }}
        <i class="iconfont icon-gengduo gengduo" />
      </a>
    </div>

    <!-- 桌面端和平板布局 -->
    <div class="items-container gt-xs">
      <div v-for="(item, index) in items" :key="index" class="blog-item">
        <BlogBox :blog="item" :index="index" />
      </div>
    </div>

    <!-- 移动端布局 -->
    <div class="mobile-items-container lt-sm">
      <q-carousel v-model="slide" animated swipeable arrows navigation padding height="180px" class="bg-white shadow-1 rounded-borders">
        <q-carousel-slide v-for="(item, index) in items" :key="index" :name="index" class="column no-wrap">
          <BlogBox :blog="item" :index="index" />
        </q-carousel-slide>
      </q-carousel>
    </div>
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n';

const props = defineProps({
  title: { type: String, required: false, default: () => useI18n().t('featuredTopics') },
  subTitle: { type: String, required: false, default: '' },
  moreLink: { type: String, required: false, default: '' },
  items: {
    type: Array,
    required: false,
    default: () => [],
  },
});

const slide = ref(0);
</script>

<style lang="scss" scoped>
.icon {
  width: 2em;
  height: 2em;
  vertical-align: -0.15em;
  overflow: hidden;
}

//专题
.subject-module {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  margin-top: 30px;
  padding: 0 16px;

  .module-title {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 20px;
    position: relative;

    h4 {
      font-size: 24px;
      font-weight: bold;
    }

    .more-link {
      position: absolute;
      right: 0;
      color: #888;
      font-size: 14px;
      text-decoration: none;
      transition: color 0.3s ease;
      display: flex;
      align-items: center;

      &:hover {
        color: #333;
      }

      .gengduo {
        font-size: 12px;
        margin-left: 4px;
      }
    }
  }

  .items-container {
    display: flex;
    flex-wrap: wrap;
    gap: 14px;
    justify-content: space-between;

    .blog-item {
      width: 32%;

      @media (min-width: 600px) and (max-width: 1023px) {
        width: 48%;
        margin-bottom: 16px;
      }
    }
  }

  .mobile-items-container {
    margin-bottom: 20px;
  }
}

@media (max-width: 599px) {
  .subject-module {
    margin-top: 20px;
    padding: 0 8px;

    .module-title {
      margin-bottom: 16px;

      h4 {
        font-size: 20px;
      }
    }
  }
}

@media (min-width: 600px) and (max-width: 1023px) {
  .subject-module {
    .module-title {
      h4 {
        font-size: 22px;
      }
    }
  }
}
</style>
