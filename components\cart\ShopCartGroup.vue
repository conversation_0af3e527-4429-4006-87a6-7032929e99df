<template>
  <div class="q-mb-md">
    <!-- 店铺栏 -->
    <div v-if="shop.items.length > 0" class="shop-header bg-grey-1 rounded-borders" :class="{ 'q-mt-md': !isFirstGroup }">
      <div class="row items-center">
        <div class="col-8 col-sm-10 text-bold text-subtitle1 q-py-sm">
          <q-icon name="store" color="primary" class="q-ml-lg q-mr-sm" size="20px" />
          <span class="text-primary">{{ shop.shopName }}</span>
        </div>
        <div v-if="shop.freight" class="col-4 col-sm-2 text-right q-pr-md">
          <span class="text-grey-8">运费：</span>
          <span class="text-red text-weight-medium nowrap" v-html="formatAmount(shop.freight)"></span>
        </div>
      </div>
    </div>

    <!-- PC视图商品列表 -->
    <div class="desktop-view">
      <div v-for="(item, itemIndex) in shop.items" :key="item.id" class="row items-center q-py-sm border">
        <!-- 复选框 -->
        <div class="col-1">
          <q-checkbox v-model="item.selected" dense class="q-ml-lg q-pl-md" @update:model-value="onItemSelectChange" />
        </div>

        <!-- 商品名称与图片 -->
        <div class="col-4 row justify-start">
          <div class="">
            <NuxtLink :to="`/product/${item.spu.id}`">
              <q-img :src="getThumbnailUrl(item.sku.picUrl || item.spu.picUrl, '80x80')" style="width: 80px; height: 80px" class="q-mr-md" />
            </NuxtLink>
          </div>

          <div class="column justify-around" style="flex: 1">
            <div class="multiline-ellipsis">
              <NuxtLink :to="`/product/${item.spu.id}`" class="text-dark">
                {{ item.spu.name }}
              </NuxtLink>
              <q-tooltip v-if="item.spu.name.length > 100">
                {{ item.spu.name }}
              </q-tooltip>
            </div>
            <div class="q-mt-sm text-grey-7 text-caption">{{ formattedProperties(item.sku.properties) }}</div>
          </div>
        </div>

        <!-- 备注 - 美化版 -->
        <div class="col-2 text-center">
          <div class="row items-center justify-center">
            <div v-if="item.memo" class="memo-content text-grey-7 multiline-ellipsis-2 q-mr-xs">
              <q-icon name="sticky_note_2" size="16px" color="primary" class="q-mr-xs" />
              {{ item.memo }}
            </div>
            <div v-else class="memo-placeholder text-grey-6 q-mr-xs">
              <q-icon name="note_add" size="16px" color="grey-6" class="q-mr-xs" />
              添加备注
            </div>
            <q-btn flat round dense icon="edit" color="primary" class="memo-edit-btn" @click="openMemoDialog(shopIndex, itemIndex)">
              <q-tooltip>编辑备注</q-tooltip>
            </q-btn>
          </div>
        </div>

        <!-- 单价 -->
        <div class="col-1 text-center text-weight-medium" v-html="formatAmount(item.price || item.sku.price)"></div>

        <!-- 数量修改 -->
        <div class="col-2 text-center">
          <q-btn flat round dense icon="remove" @click="decreaseQuantity(shopIndex, itemIndex)" />
          <q-input
            v-model.number="item.count"
            dense
            outlined
            type="text"
            maxlength="4"
            input-style="text-align: center"
            @change="(newValue) => updateQuantity(shopIndex, itemIndex, newValue)"
            oninput="value=value.replace(/[^0-9.]/g,'')"
            style="width: 60px; display: inline-block; margin: 0 5px" />
          <q-btn flat round dense icon="add" @click="increaseQuantity(shopIndex, itemIndex)" />
        </div>

        <!-- 金额 -->
        <div class="col-1 text-center text-red text-weight-medium" v-html="formatAmount((item.price || item.sku.price) * item.count)"></div>

        <!-- 编辑 -->
        <div class="col-1 text-center">
          <q-btn flat color="red" label="删除" @click="deleteItem(shopIndex, itemIndex)" />
        </div>
      </div>
    </div>

    <!-- 移动端商品卡片 -->
    <div class="mobile-view">
      <div v-for="(item, itemIndex) in shop.items" :key="item.id" class="q-mb-sm">
        <q-card flat bordered class="cart-item-card mobile-cart-item">
          <q-card-section class="q-pa-md q-pt-sm q-pb-sm">
            <div class="row no-wrap items-center">
              <!-- 复选框 -->
              <div class="col-auto checkbox-container">
                <q-checkbox v-model="item.selected" dense class="mobile-checkbox" @update:model-value="onItemSelectChange" />
              </div>

              <!-- 商品图片 -->
              <div class="col-auto q-ml-xs img-container">
                <NuxtLink :to="`/product/${item.spu.id}`" class="img-link">
                  <q-img :src="getThumbnailUrl(item.sku.picUrl || item.spu.picUrl, '80x80')" class="mobile-product-img" />
                </NuxtLink>
              </div>

              <!-- 商品信息 -->
              <div class="col q-ml-sm product-info-container">
                <div class="multiline-ellipsis mobile-product-name">
                  <NuxtLink :to="`/product/${item.spu.id}`" class="text-dark">
                    {{ item.spu.name }}
                  </NuxtLink>
                  <q-tooltip v-if="item.spu.name.length > 50">
                    {{ item.spu.name }}
                  </q-tooltip>
                </div>

                <div class="text-caption text-grey-7 mobile-product-props q-mt-xs">
                  {{ formattedProperties(item.sku.properties) }}
                </div>

                <!-- 使用紧凑型商品信息组件 -->
                <div class="compact-info-container q-mt-xs">
                  <!-- 单价和数量行 -->
                  <div class="compact-row">
                    <div class="row items-center no-wrap">
                      <div class="col-6">
                        <div class="row items-center no-wrap">
                          <div class="col-4 text-left">
                            <span class="text-grey-7 text-caption">单价:</span>
                          </div>
                          <div class="col-8">
                            <span class="text-red text-weight-medium" v-html="formatAmount(item.price || item.sku.price)"></span>
                          </div>
                        </div>
                      </div>
                      <div class="col-6">
                        <div class="row items-center no-wrap">
                          <div class="col-4 text-left">
                            <span class="text-grey-7 text-caption">数量:</span>
                          </div>
                          <div class="col-8 vertical-center">
                            <q-btn flat round dense size="xs" icon="remove" @click="decreaseQuantity(shopIndex, itemIndex)" class="mobile-qty-btn" />
                            <input
                              v-model.number="item.count"
                              type="text"
                              maxlength="3"
                              class="custom-qty-input"
                              @change="(e) => updateQuantity(shopIndex, itemIndex, e.target.value)"
                              @input="(e) => (e.target.value = e.target.value.replace(/[^0-9]/g, ''))" />
                            <q-btn flat round dense size="xs" icon="add" @click="increaseQuantity(shopIndex, itemIndex)" class="mobile-qty-btn" />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 金额和删除按钮行 -->
                  <div class="compact-row">
                    <div class="row items-center no-wrap">
                      <div class="col-6">
                        <div class="row items-center no-wrap">
                          <div class="col-4 text-left">
                            <span class="text-grey-7 text-caption">金额:</span>
                          </div>
                          <div class="col-8">
                            <span class="text-red text-weight-medium" v-html="formatAmount((item.price || item.sku.price) * item.count)"></span>
                          </div>
                        </div>
                      </div>
                      <div class="col-6 text-right">
                        <q-btn flat color="red" no-caps class="mobile-delete-btn" @click="deleteItem(shopIndex, itemIndex)">
                          <q-icon name="delete" size="16px" class="q-mr-xs" />
                          <span>删除</span>
                        </q-btn>
                      </div>
                    </div>
                  </div>

                  <!-- 备注行 - 美化版 -->
                  <div class="compact-row memo-row">
                    <div class="row items-center no-wrap">
                      <div class="col-2 text-left">
                        <span class="text-grey-7 text-caption">备注:</span>
                      </div>
                      <div class="col-9 vertical-center">
                        <div v-if="item.memo" class="memo-content text-grey-7 text-caption">
                          <q-icon name="sticky_note_2" size="14px" color="primary" class="q-mr-xs" />
                          {{ item.memo }}
                        </div>
                        <div v-else class="memo-placeholder text-grey-6 text-caption">
                          <q-icon name="note_add" size="14px" color="grey-6" class="q-mr-xs" />
                          点击添加备注信息
                        </div>
                        <q-btn flat round dense size="xs" icon="edit" color="primary" class="q-ml-xs mobile-memo-edit-btn" @click="openMemoDialog(shopIndex, itemIndex)">
                          <q-tooltip>编辑备注</q-tooltip>
                        </q-btn>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useCurrency } from '~/composables/useCurrency';

const { formatAmount } = useCurrency();

defineProps({
  shop: {
    type: Object,
    required: true,
  },
  shopIndex: {
    type: Number,
    required: true,
  },
  isFirstGroup: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['open-memo-dialog', 'decrease-quantity', 'increase-quantity', 'update-quantity', 'delete-item', 'item-select-change']);

// 打开备注弹窗
const openMemoDialog = (shopIndex, itemIndex) => {
  emit('open-memo-dialog', shopIndex, itemIndex);
};

// 减少数量
const decreaseQuantity = (shopIndex, itemIndex) => {
  emit('decrease-quantity', shopIndex, itemIndex);
};

// 增加数量
const increaseQuantity = (shopIndex, itemIndex) => {
  emit('increase-quantity', shopIndex, itemIndex);
};

// 更新数量
const updateQuantity = (shopIndex, itemIndex, newValue) => {
  emit('update-quantity', shopIndex, itemIndex, newValue);
};

// 删除商品
const deleteItem = (shopIndex, itemIndex) => {
  emit('delete-item', shopIndex, itemIndex);
};

// 商品选择状态变更
const onItemSelectChange = () => {
  emit('item-select-change');
};
</script>

<style lang="scss" scoped>
@import '~/assets/styles/cart.scss';

/* 备注相关样式 */
.memo-content {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
  // border-left: 2px solid #1976d2;
  transition: all 0.2s ease;

  &:hover {
    background-color: #e3f2fd;
  }
}

.memo-placeholder {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  background-color: #f5f5f5;
  border-radius: 4px;
  // border-left: 2px solid #9e9e9e;
  transition: all 0.2s ease;

  &:hover {
    background-color: #eeeeee;
  }
}

.memo-edit-btn {
  opacity: 0.7;
  transition: all 0.2s ease;

  &:hover {
    opacity: 1;
    background-color: rgba(25, 118, 210, 0.1);
    transform: scale(1.1);
  }
}

.mobile-memo-edit-btn {
  opacity: 0.8;
  transition: all 0.2s ease;

  &:hover {
    opacity: 1;
    background-color: rgba(25, 118, 210, 0.1);
  }
}

.memo-row {
  background-color: #fafafa;
  border-radius: 4px;
  margin-top: 8px;
  padding: 4px;
}
</style>
