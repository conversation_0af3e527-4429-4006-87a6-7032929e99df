import { defineStore } from 'pinia';
import ServeApi from '~/composables/serveApi';

export const useServeStore = defineStore('serve', {
  state: () => ({
    list: [],
    loading: false,
    initialized: false,
  }),

  getters: {
    getList: (state) => state.list,
    isLoading: (state) => state.loading,
    isInitialized: (state) => state.initialized,
  },

  actions: {
    async fetchServices() {
      // 如果已经加载过数据，不重复加载
      if (this.initialized && this.list.length > 0) {
        return this.list;
      }
      try {
        this.loading = true;
        const { code, data } = await ServeApi.getList();

        if (code === 0 && data) {
          this.list = data;
          this.initialized = true;
        }

        return this.list;
      } catch (error) {
        console.error('Failed to fetch services:', error);
        return [];
      } finally {
        this.loading = false;
      }
    },

    // 重置状态，强制重新加载数据
    resetState() {
      this.list = [];
      this.initialized = false;
    },
  },
});
