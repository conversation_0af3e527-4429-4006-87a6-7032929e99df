/**
 * 性能监控插件
 * 
 * 功能：
 * - 页面加载性能监控
 * - 用户交互性能监控
 * - 资源加载监控
 * - 错误监控
 * - 用户行为分析
 * 
 * 仅在客户端运行
 */

export default defineNuxtPlugin(() => {
  // 仅在客户端且支持Performance API时运行
  if (!process.client || !window.performance) {
    return;
  }

  // 性能数据收集器
  const performanceCollector = {
    // 页面性能数据
    pageMetrics: {},
    
    // 用户交互数据
    interactionMetrics: [],
    
    // 资源加载数据
    resourceMetrics: [],
    
    // 错误数据
    errorMetrics: [],

    /**
     * 收集页面加载性能数据
     */
    collectPageMetrics() {
      try {
        const navigation = performance.getEntriesByType('navigation')[0];
        const paint = performance.getEntriesByType('paint');
        
        if (navigation) {
          this.pageMetrics = {
            // 页面加载时间
            loadTime: navigation.loadEventEnd - navigation.loadEventStart,
            
            // DOM解析时间
            domParseTime: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
            
            // 首字节时间 (TTFB)
            ttfb: navigation.responseStart - navigation.requestStart,
            
            // DNS查询时间
            dnsTime: navigation.domainLookupEnd - navigation.domainLookupStart,
            
            // TCP连接时间
            tcpTime: navigation.connectEnd - navigation.connectStart,
            
            // 请求响应时间
            responseTime: navigation.responseEnd - navigation.responseStart,
            
            // 页面大小
            transferSize: navigation.transferSize || 0,
            
            // 当前页面URL
            url: window.location.href,
            
            // 用户代理
            userAgent: navigator.userAgent,
            
            // 时间戳
            timestamp: new Date().toISOString()
          };

          // 添加首次绘制时间
          paint.forEach(entry => {
            if (entry.name === 'first-paint') {
              this.pageMetrics.firstPaint = entry.startTime;
            } else if (entry.name === 'first-contentful-paint') {
              this.pageMetrics.firstContentfulPaint = entry.startTime;
            }
          });

          // 发送性能数据
          this.sendMetrics('page', this.pageMetrics);
        }
      } catch (error) {
        console.error('收集页面性能数据失败:', error);
      }
    },

    /**
     * 收集资源加载性能数据
     */
    collectResourceMetrics() {
      try {
        const resources = performance.getEntriesByType('resource');
        
        resources.forEach(resource => {
          // 只收集重要资源的数据
          if (this.isImportantResource(resource.name)) {
            const metric = {
              name: resource.name,
              type: this.getResourceType(resource.name),
              duration: resource.duration,
              size: resource.transferSize || 0,
              startTime: resource.startTime,
              timestamp: new Date().toISOString()
            };

            this.resourceMetrics.push(metric);
          }
        });

        // 批量发送资源数据
        if (this.resourceMetrics.length > 0) {
          this.sendMetrics('resource', this.resourceMetrics);
          this.resourceMetrics = []; // 清空已发送的数据
        }
      } catch (error) {
        console.error('收集资源性能数据失败:', error);
      }
    },

    /**
     * 监控用户交互性能
     */
    monitorInteractions() {
      // 监控点击事件
      document.addEventListener('click', (event) => {
        const startTime = performance.now();
        
        // 使用requestAnimationFrame来测量渲染时间
        requestAnimationFrame(() => {
          const endTime = performance.now();
          const duration = endTime - startTime;

          // 只记录响应时间较长的交互
          if (duration > 16) { // 超过一帧的时间
            this.interactionMetrics.push({
              type: 'click',
              target: this.getElementSelector(event.target),
              duration,
              timestamp: new Date().toISOString()
            });
          }
        });
      });

      // 监控输入事件
      document.addEventListener('input', (event) => {
        const startTime = performance.now();
        
        requestAnimationFrame(() => {
          const endTime = performance.now();
          const duration = endTime - startTime;

          if (duration > 16) {
            this.interactionMetrics.push({
              type: 'input',
              target: this.getElementSelector(event.target),
              duration,
              timestamp: new Date().toISOString()
            });
          }
        });
      });

      // 定期发送交互数据
      setInterval(() => {
        if (this.interactionMetrics.length > 0) {
          this.sendMetrics('interaction', this.interactionMetrics);
          this.interactionMetrics = [];
        }
      }, 30000); // 每30秒发送一次
    },

    /**
     * 监控JavaScript错误
     */
    monitorErrors() {
      // 监控全局错误
      window.addEventListener('error', (event) => {
        const errorMetric = {
          type: 'javascript',
          message: event.error?.message || event.message,
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
          stack: event.error?.stack,
          url: window.location.href,
          userAgent: navigator.userAgent,
          timestamp: new Date().toISOString()
        };

        this.errorMetrics.push(errorMetric);
        this.sendMetrics('error', errorMetric);
      });

      // 监控Promise错误
      window.addEventListener('unhandledrejection', (event) => {
        const errorMetric = {
          type: 'promise',
          message: event.reason?.message || String(event.reason),
          stack: event.reason?.stack,
          url: window.location.href,
          userAgent: navigator.userAgent,
          timestamp: new Date().toISOString()
        };

        this.errorMetrics.push(errorMetric);
        this.sendMetrics('error', errorMetric);
      });
    },

    /**
     * 发送性能数据到服务器
     */
    async sendMetrics(type, data) {
      // 仅在生产环境发送数据
      if (process.env.NODE_ENV !== 'production') {
        console.log(`Performance Metrics (${type}):`, data);
        return;
      }

      try {
        // 使用beacon API发送数据（不阻塞页面）
        if (navigator.sendBeacon) {
          const payload = JSON.stringify({
            type,
            data,
            sessionId: this.getSessionId(),
            userId: this.getUserId()
          });

          navigator.sendBeacon('/api/performance', payload);
        } else {
          // 降级到fetch
          await fetch('/api/performance', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              type,
              data,
              sessionId: this.getSessionId(),
              userId: this.getUserId()
            })
          });
        }
      } catch (error) {
        console.error('发送性能数据失败:', error);
      }
    },

    /**
     * 判断是否为重要资源
     */
    isImportantResource(url) {
      const importantTypes = ['.js', '.css', '.woff', '.woff2', '.jpg', '.png', '.webp'];
      return importantTypes.some(type => url.includes(type)) || url.includes('/api/');
    },

    /**
     * 获取资源类型
     */
    getResourceType(url) {
      if (url.includes('.js')) return 'script';
      if (url.includes('.css')) return 'stylesheet';
      if (url.includes('.woff') || url.includes('.woff2')) return 'font';
      if (url.includes('.jpg') || url.includes('.png') || url.includes('.webp')) return 'image';
      if (url.includes('/api/')) return 'api';
      return 'other';
    },

    /**
     * 获取元素选择器
     */
    getElementSelector(element) {
      if (!element) return 'unknown';
      
      if (element.id) return `#${element.id}`;
      if (element.className) return `.${element.className.split(' ')[0]}`;
      return element.tagName.toLowerCase();
    },

    /**
     * 获取会话ID
     */
    getSessionId() {
      let sessionId = sessionStorage.getItem('performance_session_id');
      if (!sessionId) {
        sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        sessionStorage.setItem('performance_session_id', sessionId);
      }
      return sessionId;
    },

    /**
     * 获取用户ID
     */
    getUserId() {
      // 这里可以从cookie或store中获取用户ID
      const userCookie = useCookie('userId');
      return userCookie.value || 'anonymous';
    }
  };

  // 页面加载完成后收集性能数据
  window.addEventListener('load', () => {
    // 延迟收集，确保所有资源都加载完成
    setTimeout(() => {
      performanceCollector.collectPageMetrics();
      performanceCollector.collectResourceMetrics();
    }, 1000);
  });

  // 开始监控用户交互和错误
  performanceCollector.monitorInteractions();
  performanceCollector.monitorErrors();

  // 页面卸载时发送剩余数据
  window.addEventListener('beforeunload', () => {
    if (performanceCollector.interactionMetrics.length > 0) {
      performanceCollector.sendMetrics('interaction', performanceCollector.interactionMetrics);
    }
  });

  // 将性能收集器暴露给全局，方便调试
  if (process.env.NODE_ENV === 'development') {
    window.performanceCollector = performanceCollector;
  }
});
