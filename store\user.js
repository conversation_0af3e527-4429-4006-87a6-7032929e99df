import { defineStore } from 'pinia';
import { useAuthStore } from './auth';
import User<PERSON><PERSON> from '~/composables/userApi';
import PayApi from '~/composables/payApi';
import OrderApi from '~/composables/orderApi';
import CouponApi from '~/composables/couponApi';

// 默认用户信息
const defaultUserInfo = {
  avatar: '', // 头像
  name: '', // 用户名
  nickname: '', // 昵称
  gender: 0, // 性别
  mobile: '', // 手机号
  point: 0, // 积分
  status: 0, // 邮箱验证状态
  email: '', // 邮箱
};

// 默认钱包信息
const defaultUserWallet = {
  balance: 0, // 余额
};

// 默认订单、优惠券等其他资产信息
const defaultNumData = {
  unusedCouponCount: 0,
  orderCount: {
    allCount: 0,
    unpaidCount: 0,
    undeliveredCount: 0,
    deliveredCount: 0,
    uncommentedCount: 0,
    afterSaleCount: 0,
  },
};

export const useUserStore = defineStore('user', {
  state: () => ({
    userInfo: clone(defaultUserInfo), // 用户信息
    // user: {
    //   userId: '',
    //   email: '',
    // },
    userWallet: clone(defaultUserWallet), // 用户钱包信息
  }),
  actions: {
    setUserId(id) {
      this.user.userId = id;
    },
    setEmail(email) {
      this.user.email = email;
    },
    setUser(user) {
      this.user = user;
    },

    // 获取用户信息
    async getUserInfo() {
      if (useAuthStore().isLogin) {
        const { code, data } = await UserApi.getUserInfo();
        if (code === 0) {
          this.userInfo = data;
        }
      }
    },

    // 获得用户钱包
    async getWallet() {
      const { code, data } = await PayApi.getPayWallet();
      if (code !== 0) {
        return;
      }
      this.userWallet = data;
    },

    // 获取订单、优惠券等其他资产信息
    getNumData() {
      OrderApi.getOrderCount().then((res) => {
        if (res.code === 0) {
          this.numData.orderCount = res.data;
        }
      });
      CouponApi.getUnusedCouponCount().then((res) => {
        if (res.code === 0) {
          this.numData.unusedCouponCount = res.data;
        }
      });
    },
  },
  persist: process.client && {
    storage: localStorage,
    paths: ['user'],
  },
});
