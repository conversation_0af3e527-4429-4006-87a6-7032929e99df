<template>
  <div>
    <!-- 标题区域 -->
    <div class="bg-grey-2 q-px-md q-py-sm">
      <div class="row items-center">
        <q-icon name="stars" size="xs" color="amber" class="q-mr-xs" />
        <span class="text-subtitle1 text-weight-medium">{{ $t('accountPoints.title') }}</span>
      </div>
    </div>

    <!-- 积分展示区域 -->
    <div class="q-pa-md bg-blue-1 rounded-borders">
      <div class="row justify-between items-center">
        <div class="column col-grow">
          <div class="text-caption q-mb-xs text-grey-7">{{ $t('accountPoints.currentPoints') }}</div>
          <div class="text-h5 text-weight-bold text-primary">{{ userInfo?.point }}</div>
        </div>
        <div>
          <q-btn flat round color="primary" icon="help_outline" type="a" href="/help/points-rules" target="_blank">
            <q-tooltip>{{ $t('accountPoints.pointsRules') }}</q-tooltip>
          </q-btn>
        </div>
      </div>
    </div>

    <!-- Tab页和积分记录列表 -->
    <div class="q-pa-md">
      <div class="row justify-between items-center q-mb-md flex-wrap">
        <div class="col-12 col-sm-auto">
          <q-tabs v-model="activeTab" class="text-primary" active-color="primary" indicator-color="primary" align="left" narrow-indicator @update:model-value="onTabChange">
            <q-tab name="all" :label="$t('accountPoints.tabs.all')" />
            <q-tab name="income" :label="$t('accountPoints.tabs.income')" />
            <q-tab name="expense" :label="$t('accountPoints.tabs.expense')" />
          </q-tabs>
        </div>

        <!-- 日期选择器和搜索按钮 -->
        <div class="col-12 col-sm-auto self-end date-selector-container">
          <div class="row justify-end">
            <DateRangeSelector v-model="dateRange" @search="onSearch" />
          </div>
        </div>
      </div>

      <q-separator />

      <!-- 积分记录表格 -->
      <div class="q-mt-md">
        <!-- 加载中 -->
        <div v-if="state.loadStatus" class="column items-center q-py-lg">
          <q-spinner color="primary" size="3em" />
          <div class="q-mt-sm text-grey-7">{{ $t('accountPoints.loading') }}</div>
        </div>

        <!-- 无数据 -->
        <div v-else-if="state.pagination.list?.length === 0" class="column items-center q-py-lg">
          <q-icon name="stars" size="3em" color="grey-5" />
          <div class="text-grey-7 q-mt-sm">{{ $t('accountPoints.noRecords') }}</div>
        </div>

        <!-- 数据列表 -->
        <q-table
          v-else
          :rows="state.pagination.list"
          :columns="columns"
          row-key="id"
          hide-pagination
          flat
          bordered
          :table-header-class="$q.screen.lt.sm ? 'text-body2' : ''"
          :dense="$q.screen.lt.sm"
          class="transaction-table">
          <!-- 积分类型列 -->
          <template #body-cell-title="props">
            <q-td :props="props">
              <div class="row items-center">
                <q-icon :name="getIconByBizType(props.row.point)" :size="$q.screen.lt.sm ? '20px' : '24px'" class="q-mr-sm" :color="getColorByBizType(props.row.point)" />
                <span>{{ props.row.title }}</span>
              </div>
            </q-td>
          </template>

          <!-- 积分数量列 -->
          <template #body-cell-point="props">
            <q-td :props="props">
              <span :class="props.row.point > 0 ? 'text-positive' : 'text-negative'">
                {{ props.row.point > 0 ? '+' + props.row.point : props.row.point }}
              </span>
            </q-td>
          </template>

          <!-- 说明列 -->
          <template #body-cell-description="props">
            <q-td :props="props" :class="{ 'description-cell': true, 'hide-on-mobile': $q.screen.lt.sm }">
              {{ props.row.description }}
            </q-td>
          </template>

          <!-- 时间列 -->
          <template #body-cell-createTime="props">
            <q-td :props="props">
              {{ formatDateTime(props.row.createTime) }}
            </q-td>
          </template>
        </q-table>

        <!-- 分页控件和记录信息 -->
        <div class="row justify-between items-center q-mt-md flex-wrap" v-if="state.pagination.list?.length > 0">
          <div class="col-12 col-sm-auto q-mb-sm q-mb-sm-none">
            <div class="row justify-end justify-sm-start">
              <div class="text-caption text-grey-8">
                {{
                  $t('accountPoints.pagination', {
                    total: state.pagination.total,
                    current: state.pagination.pageNo,
                    totalPages: Math.ceil(state.pagination.total / state.pagination.pageSize) || 1,
                  })
                }}
              </div>
            </div>
          </div>
          <div class="col-12 col-sm-auto">
            <div class="row justify-end">
              <q-pagination
                v-model="state.pagination.pageNo"
                :max="Math.ceil(state.pagination.total / state.pagination.pageSize)"
                :max-pages="$q.screen.lt.sm ? 3 : 6"
                boundary-links
                direction-links
                :dense="$q.screen.lt.sm"
                @update:model-value="onPageChange" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { date, useQuasar } from 'quasar';
import { formatDateTime } from '~/utils/dateUtil';
import DateRangeSelector from '~/components/common/DateRangeSelector.vue';
import { useUserStore } from '~/store/user';
import PointApi from '~/composables/pointApi';
import { useI18n } from 'vue-i18n';

definePageMeta({
  middleware: 'auth', // 引入身份验证中间件
});
const userStore = useUserStore();
const $q = useQuasar();
const { t } = useI18n();
const userInfo = computed(() => userStore.userInfo);

const state = reactive({
  currentTab: 0,
  pagination: {
    list: [], // 修改为空数组而不是数字0
    total: 0,
    pageSize: 6,
    pageNo: 1,
  },
  loadStatus: false,
  date: [],
  today: '',
});

// 当前激活的Tab
const activeTab = ref('all');

// 日期范围
const today = new Date();
const sevenDaysAgo = new Date(today);
sevenDaysAgo.setDate(today.getDate() - 7);

const dateRange = ref({
  from: date.formatDate(sevenDaysAgo, 'YYYY-MM-DD'),
  to: date.formatDate(today, 'YYYY-MM-DD'),
});

// 表格列定义
const columns = computed(() => {
  const baseColumns = [
    { name: 'title', align: 'left', label: t('accountPoints.table.type'), field: 'title' },
    { name: 'point', align: 'right', label: t('accountPoints.table.change'), field: 'point' },
    { name: 'createTime', align: 'center', label: t('accountPoints.table.time'), field: 'createTime' },
  ];

  // 在非移动端显示说明列
  if (!$q.screen.lt.sm) {
    baseColumns.splice(2, 0, {
      name: 'description',
      align: 'left',
      label: t('accountPoints.table.description'),
      field: 'description',
    });
  }

  return baseColumns;
});

// 页面加载时获取数据
onMounted(() => {
  fetchPointRecords();
});

// 获取积分记录
async function fetchPointRecords() {
  state.loadStatus = true;

  // 根据Tab页确定积分交易类型
  let addStatus = undefined;
  if (activeTab.value === 'income') {
    addStatus = true;
  } else if (activeTab.value === 'expense') {
    addStatus = false;
  }

  // 格式化日期范围
  const fromDate = `${dateRange.value.from} 00:00:00`;
  const toDate = `${dateRange.value.to} 23:59:59`;

  const { code, data } = await PointApi.getPointRecordPage({
    pageNo: state.pagination.pageNo,
    pageSize: state.pagination.pageSize,
    addStatus,
    'createTime[0]': fromDate,
    'createTime[1]': toDate,
  });
  state.loadStatus = false;
  if (code === 0) {
    state.pagination.list = data.list;
    state.pagination.total = data.total;
  }
}

// Tab切换事件
function onTabChange() {
  state.pagination.pageNo = 1;
  fetchPointRecords();
}

// 分页变更事件
function onPageChange() {
  fetchPointRecords();
}

// 搜索功能
function onSearch() {
  state.pagination.pageNo = 1;
  fetchPointRecords();
}

// 根据业务类型获取图标
function getIconByBizType(point) {
  if (point > 0) {
    return 'add';
  } else {
    return 'minus';
  }
}

// 根据业务类型获取颜色
function getColorByBizType(point) {
  if (point > 0) {
    return 'positive';
  } else {
    return 'negative';
  }
}
</script>

<style lang="scss" scoped>
.q-tab {
  font-weight: 500;
  padding: 0 16px;
  min-height: 36px;
}

.q-tabs {
  min-height: 36px;
}

.q-pagination {
  .q-btn {
    font-weight: 500;
    padding: 0 8px;
    min-height: 32px;
  }
}

.transaction-table {
  @media (max-width: 599px) {
    font-size: 13px;

    .q-table__top,
    .q-table__bottom,
    thead tr:first-child th {
      font-size: 13px;
    }

    thead tr th {
      padding: 8px 4px;
    }

    tbody td {
      padding: 6px 4px;
    }

    .hide-on-mobile {
      display: none;
    }
  }
}

.date-selector-container {
  @media (max-width: 599px) {
    margin-top: 15px !important;
  }
}

@media (min-width: 600px) {
  .date-selector-container {
    margin-top: 0 !important;
  }

  .justify-sm-start {
    justify-content: flex-start;
  }

  .q-mb-sm-none {
    margin-bottom: 0;
  }
}

@media (max-width: 599px) {
  .q-col-gutter-md > .col-12 {
    padding: 4px 8px;
  }

  .q-mb-sm {
    margin-bottom: 8px;
  }
}
</style>
