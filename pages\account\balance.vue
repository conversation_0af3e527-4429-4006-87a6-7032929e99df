<template>
  <div>
    <!-- 标题区域 -->
    <div class="bg-grey-2 q-px-md q-py-sm">
      <div class="row items-center">
        <q-icon name="account_balance_wallet" size="xs" color="green" class="q-mr-xs" />
        <span class="text-subtitle1 text-weight-medium">{{ $t('accountBalance.title') }}</span>
      </div>
    </div>

    <!-- 余额展示区域 -->
    <div class="balance-card q-mb-md">
      <!-- 顶部区域：余额和充值按钮 -->
      <div class="balance-header q-px-md q-pt-md q-pb-sm">
        <div class="row justify-between items-center">
          <div class="col-auto">
            <div class="text-caption text-grey-7">{{ $t('accountBalance.currentBalance') }}</div>
          </div>
          <div class="col-auto">
            <q-btn color="primary" class="recharge-btn" :label="$t('accountBalance.recharge')" icon="add" to="/account/recharge" />
          </div>
        </div>
        <div class="balance-amount q-mt-sm" v-html="formatAmount(userWallet.balance)"></div>
      </div>

      <!-- 底部区域：收入和支出 -->
      <!-- <div class="balance-footer q-px-md q-py-md">
        <div class="row justify-start q-gutter-x-xl">
          <div class="column">
            <div class="text-caption text-grey-7">{{ $t('accountBalance.totalIncome') }}</div>
            <div class="text-body1 text-positive amount-column q-mt-xs" v-html="formatAmount(userWallet.totalRecharge)"></div>
          </div>
          <div class="column">
            <div class="text-caption text-grey-7">{{ $t('accountBalance.totalExpense') }}</div>
            <div class="text-body1 text-negative amount-column q-mt-xs" v-html="formatAmount(userWallet.totalExpense)"></div>
          </div>
        </div>
      </div> -->
    </div>

    <!-- Tab页和交易记录列表 -->
    <div class="q-pa-md">
      <div class="row justify-between items-center q-mb-md flex-wrap">
        <div class="col-12 col-sm-auto">
          <q-tabs v-model="activeTab" class="text-primary" active-color="primary" indicator-color="primary" align="left" narrow-indicator @update:model-value="onTabChange">
            <q-tab name="all" :label="$t('accountBalance.tabs.all')" />
            <q-tab name="income" :label="$t('accountBalance.tabs.income')" />
            <q-tab name="expense" :label="$t('accountBalance.tabs.expense')" />
          </q-tabs>
        </div>

        <!-- 日期选择器和搜索按钮 -->
        <div class="col-12 col-sm-auto self-end date-selector-container">
          <div class="row justify-end">
            <DateRangeSelector v-model="dateRange" @search="onSearch" />
          </div>
        </div>
      </div>

      <q-separator />

      <!-- 交易记录表格 -->
      <div class="q-mt-md">
        <!-- 加载中 -->
        <div v-if="state.loadStatus" class="column items-center q-py-lg">
          <q-spinner color="primary" size="3em" />
          <div class="q-mt-sm text-grey-7">{{ $t('accountBalance.loading') }}</div>
        </div>

        <!-- 无数据 -->
        <div v-else-if="state.pagination.list?.length === 0" class="column items-center q-py-lg">
          <q-icon name="account_balance_wallet" size="3em" color="grey-5" />
          <div class="text-grey-7 q-mt-sm">{{ $t('accountBalance.noTransactions') }}</div>
        </div>

        <!-- 数据列表 -->
        <q-table
          v-else
          :rows="state.pagination.list"
          :columns="columns"
          row-key="createTime"
          hide-pagination
          flat
          :table-header-class="'transaction-table-header ' + ($q.screen.lt.sm ? 'text-body2' : '')"
          :dense="$q.screen.lt.sm"
          class="transaction-table">
          <!-- 交易类型列 -->
          <template #body-cell-title="props">
            <q-td :props="props" class="transaction-type-cell">
              <div class="row items-center">
                <div class="transaction-icon-wrapper" :class="'bg-' + getColorByBizType(props.row.bizType) + '-1'">
                  <q-icon :name="getIconByBizType(props.row.bizType)" :size="$q.screen.lt.sm ? '18px' : '22px'" :color="getColorByBizType(props.row.bizType)" />
                </div>
                <span class="transaction-title">{{ props.row.title }}</span>
              </div>
            </q-td>
          </template>

          <!-- 交易金额列 -->
          <template #body-cell-price="props">
            <q-td :props="props" class="text-center transaction-amount-cell">
              <div :class="[props.row.price > 0 ? 'text-positive' : 'text-negative', 'amount-column mobile-friendly-amount']" v-html="formatAmount(props.row.price)"></div>
            </q-td>
          </template>

          <!-- 交易时间列 -->
          <template #body-cell-createTime="props">
            <q-td :props="props" class="transaction-time-cell text-center">
              {{ formatDateTime(props.row.createTime) }}
            </q-td>
          </template>
        </q-table>

        <!-- 分页控件和记录信息 -->
        <div class="row justify-between items-center q-mt-md flex-wrap" v-if="state.pagination.list?.length > 0">
          <div class="col-12 col-sm-auto q-mb-sm-none q-mb-sm">
            <div class="row justify-end justify-sm-start">
              <div class="text-caption text-grey-8">
                {{
                  $t('accountBalance.pagination', {
                    total: state.pagination.total,
                    current: state.pagination.pageNo,
                    totalPages: Math.ceil(state.pagination.total / state.pagination.pageSize) || 1,
                  })
                }}
              </div>
            </div>
          </div>
          <div class="col-12 col-sm-auto">
            <div class="row justify-end">
              <q-pagination
                v-model="state.pagination.pageNo"
                :max="Math.ceil(state.pagination.total / state.pagination.pageSize)"
                :max-pages="6"
                boundary-links
                direction-links
                @update:model-value="onPageChange" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { date, useQuasar } from 'quasar';
import { useUserStore } from '~/store/user';
import PayApi from '~/composables/payApi';
import { formatDateTime } from '~/utils/dateUtil';
import DateRangeSelector from '~/components/common/DateRangeSelector.vue';
import { useI18n } from 'vue-i18n';
import { useCurrency } from '~/composables/useCurrency';

definePageMeta({
  middleware: 'auth', // 引入身份验证中间件
});

const userStore = useUserStore();
const $q = useQuasar();
const { formatAmount } = useCurrency();

const userWallet = computed(() => userStore.userWallet);

const state = reactive({
  showMoney: false,
  date: [], // 筛选的时间段
  pagination: {
    list: [],
    total: 0,
    pageNo: 1,
    pageSize: 8,
  },
  summary: {
    totalIncome: 0,
    totalExpense: 0,
  },
  loadStatus: false,
  today: '',
});

// 当前激活的Tab
const activeTab = ref('all');

// 日期范围
const today = new Date();
const sevenDaysAgo = new Date(today);
sevenDaysAgo.setDate(today.getDate() - 7);

const dateRange = ref({
  from: date.formatDate(sevenDaysAgo, 'YYYY-MM-DD'),
  to: date.formatDate(today, 'YYYY-MM-DD'),
});

// 表格列定义
const { t } = useI18n();

const columns = [
  { name: 'title', align: 'left', label: t('accountBalance.table.transactionType'), field: 'title' },
  { name: 'price', align: 'center', label: t('accountBalance.table.amount'), field: 'price' },
  { name: 'createTime', align: 'center', label: t('accountBalance.table.transactionTime'), field: 'createTime' },
];

// 页面加载时获取数据
onMounted(() => {
  userStore.getWallet();
  fetchTransactions();
});

// 获取交易记录
async function fetchTransactions() {
  state.loadStatus = true;

  // 根据Tab页确定交易类型
  let type = '0';
  if (activeTab.value === 'income') {
    type = '1';
  } else if (activeTab.value === 'expense') {
    type = '2';
  }

  // 格式化日期范围
  const fromDate = `${dateRange.value.from} 00:00:00`;
  const toDate = `${dateRange.value.to} 23:59:59`;

  const { code, data } = await PayApi.getWalletTransactionPage({
    pageNo: state.pagination.pageNo,
    pageSize: state.pagination.pageSize,
    type,
    'createTime[0]': fromDate,
    'createTime[1]': toDate,
  });
  state.loadStatus = false;
  if (code === 0) {
    state.pagination.list = data.list;
    state.pagination.total = data.total;
  }
}

// Tab切换事件
function onTabChange() {
  state.pagination.pageNo = 1;
  fetchTransactions();
}

// 分页变更事件
function onPageChange() {
  fetchTransactions();
}

// 搜索功能
function onSearch() {
  state.pagination.pageNo = 1;
  fetchTransactions();
}

// 根据交易类型获取图标
function getIconByBizType(bizType) {
  switch (bizType) {
    case 1:
      return 'add_circle'; // 充值
    case 2:
      return 'savings'; // 提现
    case 3:
      return 'shopping_cart'; // 支付
    case 4:
      return 'assignment_return'; // 退款
    default:
      return 'account_balance_wallet';
  }
}

// 根据交易类型获取颜色
function getColorByBizType(bizType) {
  switch (bizType) {
    case 1:
      return 'positive'; // 充值
    case 2:
      return 'warning'; // 提现
    case 3:
      return 'negative'; // 支付
    case 4:
      return 'info'; // 退款
    default:
      return 'grey';
  }
}
</script>

<style lang="scss" scoped>
// 余额卡片样式
.balance-card {
  background: linear-gradient(135deg, #f5f9ff 0%, #e8f4ff 100%);
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  //  transform: translateY(-2px);
  }

  // 余额卡片顶部区域
  .balance-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.03);
  }

  // 余额卡片底部区域
  .balance-footer {
    background-color: rgba(255, 255, 255, 0.5);
  }
}

// 余额金额样式
.balance-amount {
  font-size: 1.8rem;
  font-weight: 600;
  color: var(--q-primary);
  margin: 8px 0;
  line-height: 1.3;

  .primary-currency {
    white-space: nowrap;
    display: inline-block;

    :deep(.default-currency) {
      font-size: 0.7em;
      color: #666;
      opacity: 0.85;
      font-weight: normal;
      display: inline-block;
      margin-left: 6px;
    }
  }

  @media (max-width: 599px) {
    font-size: 1.5rem;
  }
}

// 充值按钮样式
.recharge-btn {
  border-radius: 8px;
  padding: 8px 16px;
  font-weight: 500;
  min-width: 90px;

  @media (max-width: 599px) {
    padding: 4px 12px;
    font-size: 0.9rem;
    min-width: 80px;
  }
}

// Tab样式
.q-tab {
  font-weight: 500;
  padding: 0 16px;
  min-height: 36px;
  border-radius: 4px 4px 0 0;
  transition: all 0.2s ease;

  &.q-tab--active {
    background-color: rgba(25, 118, 210, 0.05);
  }
}

.q-tabs {
  min-height: 36px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

// 分页按钮样式
.q-pagination {
  .q-btn {
    font-weight: 500;
    padding: 0 8px;
    min-height: 32px;
    border-radius: 4px;
  }
}

// 金额列样式 - 与支付页面保持一致
.amount-column {
  display: inline-block;
  min-width: 140px;
  text-align: center;
  font-size: 1.1rem;

  @media (max-width: 599px) {
    min-width: auto; // 移除最小宽度限制
    font-size: 0.95rem;
    max-width: 100%; // 确保不超出容器
    overflow: hidden; // 防止溢出
  }

  .primary-currency {
    white-space: nowrap;
    display: inline-block;

    @media (max-width: 599px) {
      white-space: normal; // 允许在手机视图下换行
      line-height: 1.2;
    }

    :deep(.default-currency) {
      font-size: 0.85em;
      color: #666;
      opacity: 0.85;
      font-weight: normal;
      display: inline-block;
      margin-left: 4px;
      white-space: normal;

      @media (max-width: 599px) {
        font-size: 0.8em;
        margin-left: 2px;
        display: block; // 在手机视图下换行显示
        margin-top: 2px;
      }
    }
  }
}

// 手机视图下的金额样式优化
.mobile-friendly-amount {
  @media (max-width: 599px) {
    width: 100%;

    :deep(.primary-currency) {
      display: flex;
      flex-direction: column;
      align-items: center;

      :deep(.default-currency) {
        margin-top: 3px;
        font-size: 0.75em;
        opacity: 0.7;
      }
    }
  }
}

// 交易表格样式
.transaction-table {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 8px rgba(0, 0, 0, 0.05);
  table-layout: fixed; // 固定表格布局，确保列宽一致

  .transaction-table-header {
    background-color: #f5f8fa;
    font-weight: 500;
    color: #555;
  }

  // 设置列宽比例
  :deep(th:nth-child(1)),
  :deep(td:nth-child(1)) {
    width: 40%; // 交易类型列宽
  }

  :deep(th:nth-child(2)),
  :deep(td:nth-child(2)) {
    width: 30%; // 金额列宽
  }

  :deep(th:nth-child(3)),
  :deep(td:nth-child(3)) {
    width: 30%; // 时间列宽
  }

  .transaction-type-cell {
    padding: 8px 12px;
  }

  .transaction-amount-cell {
    padding: 8px 12px;

    @media (max-width: 599px) {
      padding: 8px 4px;
      word-break: break-word; // 允许在任何字符处换行
    }
  }

  .transaction-time-cell {
    padding: 8px 12px;
    color: #666;
  }

  .transaction-icon-wrapper {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    flex-shrink: 0; // 防止图标被压缩

    @media (max-width: 599px) {
      width: 30px;
      height: 30px;
      margin-right: 8px;
    }
  }

  .transaction-title {
    font-weight: 500;
    color: #333;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  @media (max-width: 599px) {
    font-size: 13px;

    .q-table__top,
    .q-table__bottom,
    thead tr:first-child th {
      font-size: 13px;
    }

    thead tr th {
      padding: 8px 4px;
    }

    tbody td {
      padding: 6px 4px;
    }

    .transaction-icon-wrapper {
      width: 28px;
      height: 28px;
    }

    // 移动设备上调整列宽比例
    :deep(th:nth-child(1)),
    :deep(td:nth-child(1)) {
      width: 35%; // 交易类型列宽
    }

    :deep(th:nth-child(2)),
    :deep(td:nth-child(2)) {
      width: 35%; // 金额列宽 - 增加宽度
    }

    :deep(th:nth-child(3)),
    :deep(td:nth-child(3)) {
      width: 30%; // 时间列宽
    }
  }
}

/* PC视图下的日期选择器样式 */
@media (min-width: 600px) {
  .date-selector-container {
    margin-top: 0 !important;
  }
}

@media (max-width: 599px) {
  .q-col-gutter-md > .col-12 {
    padding: 4px 8px;
  }

  .justify-sm-start {
    justify-content: flex-start;
  }

  .q-mt-sm-none {
    margin-top: 0;
  }

  .q-mb-sm-none {
    margin-bottom: 0;
  }

  .q-mb-sm {
    margin-bottom: 8px;
  }

  /* 只在手机视图下增加日期选择器与Tab的距离 */
  .date-selector-container {
    margin-top: 15px !important;
  }
}
</style>
