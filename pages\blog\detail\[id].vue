<template>
  <Header />
  <div class="blog-detail-page">
    <div class="blog-container">
      <!-- 博客内容 -->
      <div v-if="blog" class="blog-content-wrapper">
        <!-- 博客标题和元信息 -->
        <div class="blog-header">
          <h1 class="blog-title">{{ blog.title }}</h1>
          <div class="blog-meta">
            <div class="meta-item">
              <q-icon name="event" size="sm" class="q-mr-xs" />
              <span>{{ formatDate(blog.publishTime) }}</span>
            </div>
            <div class="meta-item">
              <q-icon name="person" size="sm" class="q-mr-xs" />
              <span>{{ blog.author }}</span>
            </div>
            <div class="meta-item">
              <q-icon name="visibility" size="sm" class="q-mr-xs" />
              <span>{{ blog.browseCount }} 阅读</span>
            </div>
          </div>
        </div>

        <!-- 博客封面图 -->
        <div v-if="blog.picUrl" class="blog-cover q-my-md">
          <q-img :src="blog.picUrl" :ratio="16 / 9" class="rounded-borders" />
        </div>

        <!-- 博客正文内容 -->
        <div class="blog-content">
          <div v-html="blog.content"></div>
        </div>

        <!-- 返回按钮 -->
        <div class="blog-actions q-mt-lg text-center">
          <q-btn flat color="primary" to="/blog" class="back-btn">
            <q-icon name="arrow_back" class="q-mr-xs" />
            返回列表
          </q-btn>
        </div>
      </div>

      <!-- 加载中状态 -->
      <div v-else-if="loading" class="loading-state">
        <q-spinner color="primary" size="3em" />
        <div class="q-mt-md">加载中...</div>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="hasError" class="error-state">
        <q-icon name="error_outline" color="negative" size="3em" />
        <div class="q-mt-md">文章不存在或已被删除</div>
        <q-btn color="primary" to="/blog" class="q-mt-md"> 返回博客列表 </q-btn>
      </div>

      <!-- 无数据状态 -->
      <div v-else class="error-state">
        <q-icon name="info" color="grey" size="3em" />
        <div class="q-mt-md">暂无数据</div>
        <q-btn color="primary" to="/blog" class="q-mt-md"> 返回博客列表 </q-btn>
      </div>
    </div>
  </div>
  <Footer />
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { date } from 'quasar';
import ArticleApi from '../../../composables/articleApi';

const route = useRoute();
const blogId = computed(() => parseInt(route.params.id));

// 状态
const loading = ref(true);
const blog = ref(null);
const hasError = ref(false);



// 获取博客详情
onMounted(async () => {
  try {
    loading.value = true;
    hasError.value = false;

    // 首先尝试从 useState 获取数据
    const detailState = useState('blogDetail');
    if (detailState.value && detailState.value.id === blogId.value) {
      blog.value = detailState.value;
      loading.value = false;

      // 增加浏览次数
      try {
        await ArticleApi.incrementViews({ id: blogId.value });
      } catch (error) {
        console.error('增加浏览次数失败:', error);
      }
      return;
    }

    // 如果 useState 中没有数据，则调用 API
    const { code, data } = await ArticleApi.getDetail({ id: blogId.value });
    if (code === 0 && data) {
      blog.value = data;

      // 增加浏览次数
      try {
        await ArticleApi.incrementViews({ id: blogId.value });
      } catch (error) {
        console.error('增加浏览次数失败:', error);
      }
    } else {
      hasError.value = true;
    }
  } catch (error) {
    console.error('获取博客详情失败:', error);
    hasError.value = true;
  } finally {
    loading.value = false;
  }
});

// 格式化日期
const formatDate = (timestamp) => {
  if (!timestamp) return '';
  return date.formatDate(new Date(timestamp), 'YYYY-MM-DD');
};
</script>

<style lang="scss" scoped>
.blog-detail-page {
  padding: 20px 0 40px;
}

.blog-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 16px;
}

.blog-content-wrapper {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
  padding: 24px;
}

.blog-header {
  text-align: center;
  margin-bottom: 20px;
}

.blog-title {
  font-size: 28px;
  font-weight: bold;
  color: #333;
  margin-bottom: 16px;
  line-height: 1.3;
}

.blog-meta {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 16px;
  color: #666;
  font-size: 14px;

  .meta-item {
    display: flex;
    align-items: center;
  }
}



.blog-cover {
  margin: 24px 0;
  border-radius: 8px;
  overflow: hidden;
}

.blog-content {
  font-size: 16px;
  line-height: 1.7;
  color: #333;

  :deep(h2) {
    font-size: 22px;
    font-weight: bold;
    margin: 24px 0 16px;
    color: #1976d2;
  }

  :deep(p) {
    margin-bottom: 16px;
  }

  :deep(ul),
  :deep(ol) {
    margin-bottom: 16px;
    padding-left: 24px;

    li {
      margin-bottom: 8px;
    }
  }

  :deep(img) {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
    margin: 16px 0;
  }

  :deep(a) {
    color: #1976d2;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}

.blog-actions {
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.back-btn {
  font-size: 14px;
}

.loading-state,
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
  color: #666;
}

@media (max-width: 767px) {
  .blog-content-wrapper {
    padding: 16px;
  }

  .blog-title {
    font-size: 22px;
    margin-bottom: 12px;
  }

  .blog-meta {
    font-size: 12px;
    gap: 12px;
  }

  .blog-content {
    font-size: 15px;

    :deep(h2) {
      font-size: 18px;
      margin: 20px 0 12px;
    }
  }


}
</style>
