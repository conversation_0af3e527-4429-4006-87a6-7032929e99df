# 工单系统前端开发文档

## 概述

工单系统为用户提供了完整的问题咨询和客服支持功能，支持多种问题类型、附件上传、实时回复和评分功能。

## API 接口

### 基础信息

- **基础路径**: `/app-api/member/ticket`
- **认证方式**: Bear<PERSON>（需要用户登录）
- **内容类型**: `application/json`

### 1. 创建工单

**接口地址**: `POST /create`

**请求参数**:
```json
{
  "title": "订单支付问题",
  "type": 1,
  "priority": 2,
  "description": "订单支付后未到账，请帮忙处理",
  "attachmentUrls": [
    "https://example.com/file1.jpg",
    "https://example.com/file2.pdf"
  ]
}
```

**参数说明**:
- `title`: 工单标题（必填，最大200字符）
- `type`: 工单类型（必填，见工单类型枚举）
- `priority`: 优先级（可选，默认为2-中等）
- `description`: 问题描述（必填）
- `attachmentUrls`: 附件URL列表（可选）

**响应示例**:
```json
{
  "code": 0,
  "data": 1024,
  "msg": ""
}
```

### 2. 获取工单分页列表

**接口地址**: `GET /page`

**请求参数**:
- `pageNo`: 页码（默认1）
- `pageSize`: 每页数量（默认10）
- `type`: 工单类型（可选）
- `status`: 工单状态（可选）
- `keyword`: 关键词搜索（可选）
- `createTime`: 创建时间范围（可选，格式：yyyy-MM-dd HH:mm:ss）

**响应示例**:
```json
{
  "code": 0,
  "data": {
    "total": 50,
    "list": [
      {
        "id": 1024,
        "no": "T20240101001",
        "title": "订单支付问题",
        "type": 1,
        "typeName": "订单问题",
        "priority": 2,
        "priorityName": "中",
        "status": 1,
        "statusName": "待处理",
        "description": "订单支付后未到账，请帮忙处理",
        "attachmentUrls": ["https://example.com/file1.jpg"],
        "adminId": null,
        "adminName": null,
        "rating": null,
        "ratingComment": null,
        "createTime": "2024-01-01 10:00:00",
        "updateTime": "2024-01-01 10:00:00",
        "closedTime": null
      }
    ]
  },
  "msg": ""
}
```

### 3. 获取工单详情

**接口地址**: `GET /get`

**请求参数**:
- `id`: 工单编号（必填）

**响应示例**:
```json
{
  "code": 0,
  "data": {
    "id": 1024,
    "no": "T20240101001",
    "title": "订单支付问题",
    "type": 1,
    "typeName": "订单问题",
    "priority": 2,
    "priorityName": "中",
    "status": 2,
    "statusName": "处理中",
    "description": "订单支付后未到账，请帮忙处理",
    "attachmentUrls": ["https://example.com/file1.jpg"],
    "adminId": 1001,
    "adminName": "客服小王",
    "rating": null,
    "ratingComment": null,
    "createTime": "2024-01-01 10:00:00",
    "updateTime": "2024-01-01 11:00:00",
    "closedTime": null,
    "messages": [
      {
        "id": 2001,
        "ticketId": 1024,
        "messageType": 1,
        "messageTypeName": "用户回复",
        "content": "订单支付后未到账，请帮忙处理",
        "attachmentUrls": ["https://example.com/file1.jpg"],
        "replierId": 1000,
        "replierName": "张三",
        "createTime": "2024-01-01 10:00:00"
      },
      {
        "id": 2002,
        "ticketId": 1024,
        "messageType": 2,
        "messageTypeName": "客服回复",
        "content": "您好，我们已经收到您的问题，正在为您处理中...",
        "attachmentUrls": [],
        "replierId": 1001,
        "replierName": "客服小王",
        "createTime": "2024-01-01 11:00:00"
      }
    ]
  },
  "msg": ""
}
```

### 4. 提交回复

**接口地址**: `POST /reply`

**请求参数**:
```json
{
  "ticketId": 1024,
  "content": "感谢您的回复，问题已解决",
  "attachmentUrls": [
    "https://example.com/screenshot.jpg"
  ]
}
```

**参数说明**:
- `ticketId`: 工单编号（必填）
- `content`: 回复内容（必填）
- `attachmentUrls`: 附件URL列表（可选）

**响应示例**:
```json
{
  "code": 0,
  "data": 2003,
  "msg": ""
}
```

### 5. 关闭工单

**接口地址**: `PUT /close/{id}`

**路径参数**:
- `id`: 工单编号

**响应示例**:
```json
{
  "code": 0,
  "data": true,
  "msg": "操作成功"
}
```

### 6. 评分工单

**接口地址**: `PUT /rate`

**请求参数**:
```json
{
  "ticketId": 1024,
  "rating": 5,
  "ratingComment": "服务很好，问题解决及时"
}
```

**参数说明**:
- `ticketId`: 工单编号（必填）
- `rating`: 评分（必填，1-5分）
- `ratingComment`: 评分备注（可选，最大500字符）

**响应示例**:
```json
{
  "code": 0,
  "data": true,
  "msg": "操作成功"
}
```


## 枚举值说明

### 工单类型 (TicketType)
- `1`: 订单问题
- `2`: 商品问题
- `3`: 包裹问题
- `4`: 物流问题
- `5`: 账户问题
- `6`: 其他问题

### 工单状态 (TicketStatus)
- `1`: 待处理
- `2`: 处理中
- `3`: 待用户回复
- `4`: 已解决
- `5`: 已关闭

### 优先级 (TicketPriority)
- `1`: 高
- `2`: 中
- `3`: 低

### 消息类型 (TicketMessageType)
- `1`: 用户回复
- `2`: 客服回复

## 前端集成示例

### React Hook 示例

```typescript
import { useState, useEffect } from 'react';
import axios from 'axios';

// 工单相关的 Hook
export const useTicket = () => {
  const [tickets, setTickets] = useState([]);
  const [loading, setLoading] = useState(false);

  // 获取工单列表
  const fetchTickets = async (params = {}) => {
    setLoading(true);
    try {
      const response = await axios.get('/app-api/member/ticket/page', { params });
      setTickets(response.data.data.list);
      return response.data.data;
    } catch (error) {
      console.error('获取工单列表失败:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // 创建工单
  const createTicket = async (ticketData) => {
    try {
      const response = await axios.post('/app-api/member/ticket/create', ticketData);
      return response.data.data;
    } catch (error) {
      console.error('创建工单失败:', error);
      throw error;
    }
  };

  // 获取工单详情
  const getTicketDetail = async (id) => {
    try {
      const response = await axios.get(`/app-api/member/ticket/get?id=${id}`);
      return response.data.data;
    } catch (error) {
      console.error('获取工单详情失败:', error);
      throw error;
    }
  };

  // 提交回复
  const replyTicket = async (replyData) => {
    try {
      const response = await axios.post('/app-api/member/ticket/reply', replyData);
      return response.data.data;
    } catch (error) {
      console.error('提交回复失败:', error);
      throw error;
    }
  };

  // 关闭工单
  const closeTicket = async (id) => {
    try {
      const response = await axios.put(`/app-api/member/ticket/close/${id}`);
      return response.data.data;
    } catch (error) {
      console.error('关闭工单失败:', error);
      throw error;
    }
  };

  // 评分工单
  const rateTicket = async (rateData) => {
    try {
      const response = await axios.put('/app-api/member/ticket/rate', rateData);
      return response.data.data;
    } catch (error) {
      console.error('评分工单失败:', error);
      throw error;
    }
  };

  return {
    tickets,
    loading,
    fetchTickets,
    createTicket,
    getTicketDetail,
    replyTicket,
    closeTicket,
    rateTicket
  };
};
```

### Vue 3 Composition API 示例

```typescript
import { ref, reactive } from 'vue';
import axios from 'axios';

export const useTicket = () => {
  const tickets = ref([]);
  const loading = ref(false);

  // 获取工单列表
  const fetchTickets = async (params = {}) => {
    loading.value = true;
    try {
      const response = await axios.get('/app-api/member/ticket/page', { params });
      tickets.value = response.data.data.list;
      return response.data.data;
    } catch (error) {
      console.error('获取工单列表失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  // 创建工单
  const createTicket = async (ticketData) => {
    try {
      const response = await axios.post('/app-api/member/ticket/create', ticketData);
      return response.data.data;
    } catch (error) {
      console.error('创建工单失败:', error);
      throw error;
    }
  };

  // 其他方法类似...

  return {
    tickets,
    loading,
    fetchTickets,
    createTicket,
    // ... 其他方法
  };
};
```

## 注意事项

1. **认证要求**: 所有接口都需要用户登录，请在请求头中携带有效的 Bearer Token
2. **权限控制**: 用户只能操作自己创建的工单
3. **状态限制**: 
   - 已关闭的工单不能回复
   - 只有已解决或已关闭的工单才能评分
4. **附件上传**: 需要先调用文件上传接口获取URL，再传递给工单接口
5. **分页查询**: 支持多种筛选条件，建议合理使用以提升用户体验
6. **错误处理**: 请根据返回的错误码进行相应的错误提示

## 文件上传

工单系统支持附件上传，需要先调用文件上传接口：

**接口地址**: `POST /app-api/infra/file/upload`

**请求方式**: `multipart/form-data`

**请求参数**:
- `file`: 文件对象
- `path`: 文件路径（可选）

**响应示例**:
```json
{
  "code": 0,
  "data": "https://example.com/uploads/file123.jpg",
  "msg": "操作成功"
}
```

获取到文件URL后，可以在创建工单或回复时使用。

## 最佳实践

1. **实时更新**: 建议在工单详情页面实现轮询或WebSocket来实时获取新回复
2. **离线支持**: 考虑实现草稿保存功能，避免用户输入丢失
3. **图片预览**: 对于图片附件，提供预览功能提升用户体验
4. **状态提示**: 根据工单状态显示不同的操作按钮和提示信息
5. **搜索优化**: 在工单列表页面提供便捷的搜索和筛选功能

## 页面设计建议

### 1. 工单列表页面
- **筛选器**: 类型、状态、时间范围筛选
- **搜索框**: 支持标题和描述搜索
- **列表项**: 显示工单号、标题、状态、创建时间
- **状态标识**: 不同颜色标识不同状态
- **分页组件**: 支持页码跳转

### 2. 工单详情页面
- **基本信息**: 工单号、标题、状态、创建时间
- **问题描述**: 完整显示用户问题
- **附件展示**: 图片预览、文件下载
- **对话记录**: 时间线形式展示所有回复
- **操作按钮**: 回复、关闭、评分等

### 3. 创建工单页面
- **表单验证**: 实时验证必填项
- **类型选择**: 下拉或单选形式
- **富文本编辑**: 支持格式化文本输入
- **附件上传**: 拖拽上传、进度显示
- **预览功能**: 提交前预览工单内容

### 4. 回复工单页面
- **回复框**: 支持多行文本输入
- **附件上传**: 补充材料上传
- **历史记录**: 显示之前的对话
- **快捷回复**: 常用回复模板

## 用户体验优化

### 1. 加载状态
```javascript
// 加载状态示例
const [loading, setLoading] = useState(false);

const handleSubmit = async () => {
  setLoading(true);
  try {
    await createTicket(formData);
    // 成功处理
  } catch (error) {
    // 错误处理
  } finally {
    setLoading(false);
  }
};
```



## 移动端适配

### 1. 响应式设计
- 使用弹性布局适配不同屏幕
- 优化触摸操作体验
- 简化移动端界面元素

### 2. 手势支持
- 下拉刷新工单列表
- 左滑显示快捷操作
- 长按显示上下文菜单

### 3. 离线支持
- 缓存工单列表数据
- 支持离线查看详情
- 网络恢复后同步数据

## 安全注意事项

### 1. 数据验证
- 前端验证所有用户输入
- 文件类型和大小限制
- XSS攻击防护

### 2. 权限控制
- 验证用户登录状态
- 检查工单访问权限
- 敏感操作二次确认

### 3. 数据传输
- 使用HTTPS传输数据
- 敏感信息加密存储
- 定期清理本地缓存

## 测试建议

### 1. 功能测试
- 创建不同类型工单
- 测试各种状态流转
- 验证权限控制逻辑

### 2. 兼容性测试
- 不同浏览器兼容性
- 移动设备适配测试
- 网络异常情况测试

### 3. 性能测试
- 大量数据加载测试
- 文件上传性能测试
- 内存泄漏检测

## 部署配置

---

**版本**: v1.0.0
**更新时间**: 2024-01-01
**联系方式**: 如有问题请联系后端开发团队

## 附录

### A. 完整的TypeScript类型定义
```typescript
// 工单相关类型定义
export interface Ticket {
  id: number;
  no: string;
  title: string;
  type: number;
  typeName: string;
  priority: number;
  priorityName: string;
  status: number;
  statusName: string;
  description: string;
  attachmentUrls: string[];
  adminId?: number;
  adminName?: string;
  rating?: number;
  ratingComment?: string;
  createTime: string;
  updateTime: string;
  closedTime?: string;
}

export interface TicketDetail extends Ticket {
  messages: TicketMessage[];
}

export interface TicketMessage {
  id: number;
  ticketId: number;
  messageType: number;
  messageTypeName: string;
  content: string;
  attachmentUrls: string[];
  replierId: number;
  replierName: string;
  createTime: string;
}

export interface CreateTicketRequest {
  title: string;
  type: number;
  priority?: number;
  description: string;
  attachmentUrls?: string[];
}

export interface ReplyTicketRequest {
  ticketId: number;
  content: string;
  attachmentUrls?: string[];
}

export interface RateTicketRequest {
  ticketId: number;
  rating: number;
  ratingComment?: string;
}

export interface TicketPageRequest {
  pageNo?: number;
  pageSize?: number;
  type?: number;
  status?: number;
  keyword?: string;
  createTime?: string[];
}

export interface PageResult<T> {
  total: number;
  list: T[];
}

export interface ApiResponse<T> {
  code: number;
  data: T;
  msg: string;
}
```

### B. 常用工具函数
```typescript
// 状态颜色映射
export const getStatusColor = (status: number): string => {
  const colorMap = {
    1: '#faad14', // 待处理 - 橙色
    2: '#1890ff', // 处理中 - 蓝色
    3: '#722ed1', // 待用户回复 - 紫色
    4: '#52c41a', // 已解决 - 绿色
    5: '#8c8c8c'  // 已关闭 - 灰色
  };
  return colorMap[status] || '#8c8c8c';
};

// 优先级图标映射
export const getPriorityIcon = (priority: number): string => {
  const iconMap = {
    1: '🔴', // 高优先级
    2: '🟡', // 中优先级
    3: '🟢'  // 低优先级
  };
  return iconMap[priority] || '🟡';
};

// 时间格式化
export const formatTime = (timeString: string): string => {
  const date = new Date(timeString);
  const now = new Date();
  const diff = now.getTime() - date.getTime();

  if (diff < 60000) return '刚刚';
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`;
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`;

  return date.toLocaleDateString();
};

// 文件大小格式化
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};
```
