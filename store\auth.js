import { defineStore } from 'pinia';
import { encryptData, decryptData } from '~/utils/encryUtils';

export const useAuthStore = defineStore('auth', {
  state: () => ({
    token: null,
    refreshToken: null,
    isLogin: false,
  }),
  actions: {
    //保存加密后的token
    setToken(token, refreshToken) {
      this.token = token;
      this.refreshToken = refreshToken;
      this.isLogin = !!token;
    },
    clearToken() {
      this.token = null;
      this.refreshToken = null;
      this.isLogin = false;
    },

    useToken() {
      if (import.meta.server) {
        const cookies = useCookie('token');
        return decryptData(cookies.value, globalConfig.sk);
      } else {
        const authStore = useAuthStore();
        return decryptData(authStore.token, globalConfig.sk);
      }
    },

    useRefreshToken() {
      if (import.meta.server) {
        const refreshToken = useCookie('refreshToken');
        return decryptData(refreshToken.value, globalConfig.sk);
      } else {
        const authStore = useAuthStore();
        return decryptData(authStore.refreshToken, globalConfig.sk);
      }
    },

    saveToken(token, refreshToken) {
      const sk = globalConfig.sk;
      const encryptedToken = encryptData(token, sk);
      const encryptedRefreshToken = encryptData(refreshToken, sk);
      if (import.meta.client) {
        // 更新 Pinia Store
        const authStore = useAuthStore();
        authStore.setToken(encryptedToken, encryptedRefreshToken);
      }
      // 更新 Cookie
      const tokenCookie = useCookie('token');
      tokenCookie.value = encryptedToken;
      const refreshTokenCookie = useCookie('refreshToken');
      refreshTokenCookie.value = encryptedRefreshToken;
    },
  },
  persist: import.meta.client && {
    enabled: true,
    key: '_AU',
    storage: localStorage,
  },
});
