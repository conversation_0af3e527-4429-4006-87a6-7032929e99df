<template>
  <div class="my-parcel">
    <!-- 标题区域 -->
    <div class="bg-grey-2 q-px-md q-py-sm">
      <div class="row items-center justify-between">
        <div class="row items-center">
          <q-icon name="inventory_2" size="xs" color="teal" class="q-mr-xs" />
          <span class="text-subtitle1 text-weight-medium">{{ $t('parcel.title') }}</span>
        </div>
        <q-btn color="primary" icon="add" :label="$q.screen.lt.sm ? '' : '创建包裹'" dense @click="createParcel" class="create-parcel-btn" />
      </div>
    </div>

    <div class="q-pa-md">
      <div class="row justify-between items-center q-mb-md flex-wrap">
        <!-- 顶部 Tab -->
        <div class="col-12 col-sm-auto">
          <q-tabs
            v-model="state.currentTab"
            class="text-primary"
            active-color="primary"
            indicator-color="primary"
            align="left"
            narrow-indicator
            :dense="$q.screen.lt.sm"
            @update:model-value="onTabChange">
            <q-tab name="all" :label="$t('parcel.tabs.all')" />
            <q-tab name="0" :label="$t('parcel.tabs.unpaid')" />
            <q-tab name="10" :label="$t('parcel.tabs.unshipped')" />
            <q-tab name="20" :label="$t('parcel.tabs.shipping')" />
            <q-tab name="30" :label="$t('parcel.tabs.completed')" />
          </q-tabs>
        </div>
        <div class="col-12 col-sm-auto q-mt-sm-none q-mt-md">
          <div class="search-container">
            <!-- 搜索框 -->
            <q-input v-model="state.searchNo" outlined dense clearable :placeholder="$t('parcel.search.placeholder')" @keyup.enter="getParcelList" class="search-input">
              <template #append>
                <q-btn flat dense icon="search" @click="getParcelList" />
              </template>
            </q-input>
          </div>
        </div>
      </div>

      <q-separator />

      <!-- 包裹列表 -->
      <div>
        <!-- 表格标题行 - 仅在非移动端显示 -->
        <div v-if="!$q.screen.lt.sm" class="row items-center q-py-xs bg-grey-2 text-grey-8 q-mt-md rounded-borders-top">
          <div class="row col-9 items-center">
            <div class="col-8 text-center text-body2 text-weight-medium">{{ $t('parcel.table.details') }}</div>
            <div class="col-2 text-center text-body2 text-weight-medium">{{ $t('parcel.table.weight') }}</div>
            <div class="col-2 text-center text-body2 text-weight-medium">{{ $t('parcel.table.quantity') }}</div>
          </div>
          <div class="row col-3">
            <div class="col-5 text-center text-body2 text-weight-medium">{{ $t('parcel.table.status') }}</div>
            <div class="col-7 text-center text-body2 text-weight-medium">{{ $t('parcel.table.actions') }}</div>
          </div>
        </div>

        <!-- 有数据时显示包裹列表 -->
        <div v-if="state.pagination.list.length > 0">
          <!-- 桌面视图 -->
          <div v-if="!$q.screen.lt.sm">
            <div v-for="parcel in state.pagination.list" :key="parcel.id" class="bg-white q-mb-md rounded-borders shadow-1">
              <div class="bg-grey-2 q-pa-xs q-px-md">
                <div class="row justify-between items-center">
                  <div>
                    <span class="text-caption text-weight-medium">{{ $t('parcel.parcelInfo.number') }}：</span>
                    <span class="text-primary text-caption">{{ parcel.no }}</span>
                    <span class="q-mx-sm text-grey-7 text-caption">{{ formatDateTime(parcel.createTime) }}</span>
                  </div>
                  <div>
                    <span class="text-caption text-weight-medium">{{ $t('parcel.parcelInfo.totalAmount') }}：</span>
                    <span class="text-red text-weight-bold">{{ fen2yuan(parcel.payPrice) }}</span>
                    <q-btn v-if="parcel.status === 40" flat round dense icon="delete" color="red" class="q-ml-xs" size="sm" @click="deleteParcel(parcel.id)" />
                    <q-btn flat round dense icon="chat" color="orange" class="q-ml-xs" size="sm" />
                  </div>
                </div>
              </div>
              <div>
                <div class="row">
                  <div class="col-9">
                    <!-- 商品栏 -->
                    <div v-for="(item, index) in parcel.items" :key="item.id" class="row q-py-md q-px-sm" :class="{ 'border-top': index > 0 }">
                      <!-- 商品名称与图片 -->
                      <div class="col-8 row justify-start items-center">
                        <div>
                          <NuxtLink :to="`/product/${item.spuId}`">
                            <q-img :src="getThumbnailUrl(item.picUrl, '80x80')" style="width: 80px; height: 80px" class="rounded-borders q-mr-md" />
                          </NuxtLink>
                        </div>

                        <div class="column justify-around" style="flex: 1">
                          <div class="ellipsis-2-lines">
                            <NuxtLink :to="`/product/${item.spuId}`" class="text-weight-medium">
                              {{ item.spuName }}
                            </NuxtLink>
                          </div>
                          <div class="q-mt-sm text-grey-7 text-caption">{{ formattedProperties(item.properties) }}</div>
                        </div>
                      </div>
                      <!-- 重量 -->
                      <div class="col-2 row justify-center items-center">{{ item.weight || '0.00' }}{{ $t('parcel.parcelInfo.weightUnit') }}</div>
                      <!-- 数量 -->
                      <div class="col-2 row justify-center items-center">{{ item.count }}</div>
                    </div>
                  </div>
                  <div class="row col-3">
                    <!-- 包裹状态 -->
                    <div class="col-5 row justify-center items-center">
                      <q-badge :color="getStatusColor(parcel.status)" class="q-py-xs q-px-sm">
                        {{ getParcelStatus(parcel.status) }}
                      </q-badge>
                    </div>
                    <!-- 操作 -->
                    <div class="col-7 row justify-center items-center">
                      <div class="column items-center">
                        <q-btn flat color="primary" dense :label="$t('parcel.actions.details')" class="q-mb-xs" @click="toParcelDetail(parcel.id)" />
                        <q-btn v-if="parcel.buttons.includes('cancel')" flat color="grey" dense :label="$t('parcel.actions.cancel')" class="q-mb-xs" @click="cancelParcel(parcel.id)" />
                        <q-btn
                          v-if="parcel.buttons.includes('pay')"
                          color="primary"
                          dense
                          icon-left="payment"
                          :label="$t('parcel.actions.pay')"
                          class="pay-btn"
                          @click="payParcel(parcel.payOrderId)" />
                        <q-btn v-if="parcel.buttons.includes('track')" flat color="primary" dense :label="$t('parcel.actions.track')" class="q-mb-xs" @click="trackParcel(parcel.id)" />
                        <q-btn v-if="parcel.buttons.includes('delete')" flat color="primary" dense label="删除订单" class="q-mb-xs" @click="deleteParcel(parcel.id)" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 移动端卡片视图 -->
          <div v-else class="mobile-parcel-list">
            <q-card v-for="parcel in state.pagination.list" :key="parcel.id" class="parcel-card q-mb-md">
              <!-- 包裹头部 -->
              <q-card-section class="parcel-header q-py-xs">
                <div class="row justify-between items-center">
                  <div class="parcel-no">
                    <span class="text-caption text-weight-medium">{{ $t('parcel.parcelInfo.number') }}：</span>
                    <span class="text-primary text-caption">{{ parcel.no }}</span>
                  </div>
                  <q-badge :color="getStatusColor(parcel.status)" class="status-badge text-caption">
                    {{ getParcelStatus(parcel.status) }}
                  </q-badge>
                </div>
                <div class="text-caption text-grey-7 q-mt-xs">{{ formatDateTime(parcel.createTime) }}</div>
              </q-card-section>

              <q-separator />

              <!-- 商品列表 -->
              <q-card-section class="q-pa-none">
                <div v-for="(item, index) in parcel.items" :key="item.id" class="product-item q-pa-md" :class="{ 'border-top': index > 0 }">
                  <div class="row no-wrap">
                    <!-- 商品图片 -->
                    <div class="product-img">
                      <NuxtLink :to="`/product/${item.spuId}`">
                        <q-img :src="getThumbnailUrl(item.picUrl, '80x80')" style="width: 70px; height: 70px" class="rounded-borders" />
                      </NuxtLink>
                    </div>

                    <!-- 商品信息 -->
                    <div class="product-info q-ml-md">
                      <div class="ellipsis-2-lines">
                        <NuxtLink :to="`/product/${item.spuId}`" class="text-weight-medium text-body2">
                          {{ item.spuName }}
                        </NuxtLink>
                      </div>
                      <div class="text-grey-7 text-caption q-mt-xs">{{ formattedProperties(item.properties) }}</div>
                      <div class="row justify-between items-center q-mt-xs">
                        <div class="text-grey-7">x{{ item.count }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </q-card-section>

              <!-- 包裹底部 -->
              <q-card-section class="parcel-footer q-py-sm bg-grey-1">
                <div class="row justify-between items-center">
                  <div class="parcel-total">
                    <span>{{ $t('parcel.parcelInfo.itemCount', { count: parcel.items.length }) }}：</span>
                    <span class="text-red text-weight-bold">￥{{ fen2yuan(parcel.payPrice) }}</span>
                  </div>
                </div>

                <!-- 操作按钮 -->
                <div class="row justify-end q-mt-sm">
                  <q-btn v-if="parcel.status === -1" flat dense size="sm" color="red" icon="delete" class="q-mr-sm" @click="deleteParcel(parcel.id)" />
                  <q-btn flat dense size="sm" color="grey" icon="chat" class="q-mr-sm" />
                  <q-btn outline size="sm" color="primary" :label="$t('parcel.actions.details')" class="q-mr-sm" @click="toParcelDetail(parcel.id)" />
                  <q-btn v-if="parcel.buttons.includes('cancel')" outline size="sm" color="grey" :label="$t('parcel.actions.cancel')" class="q-mr-sm" @click="cancelParcel(parcel.id)" />
                  <q-btn v-if="parcel.buttons.includes('pay')" color="primary" size="sm" icon-left="payment" :label="$t('parcel.actions.pay')" class="pay-btn" @click="payParcel(parcel.payOrderId)" />
                  <q-btn v-if="parcel.buttons.includes('track')" outline size="sm" color="primary" :label="$t('parcel.actions.track')" class="q-mr-sm" @click="trackParcel(parcel.id)" />
                  <q-btn v-if="parcel.buttons.includes('delete')" outline size="sm" color="primary" label="删除订单" class="q-mr-sm" @click="deleteParcel(parcel.id)" />
                </div>
              </q-card-section>
            </q-card>
          </div>
        </div>

        <!-- 无数据 -->
        <div v-else class="column items-center q-py-lg">
          <q-icon name="inventory_2" size="3em" color="grey-5" />
          <div class="text-grey-7 q-mt-sm">{{ $t('parcel.noRecords') }}</div>
        </div>

        <!-- 分页控件和记录信息 -->
        <div class="row justify-between items-center q-mt-md flex-wrap" v-if="state.pagination.total > 0">
          <div class="col-12 col-sm-auto q-mb-sm q-mb-sm-none">
            <div class="row justify-end justify-sm-start">
              <div class="text-caption text-grey-8">
                {{
                  $t('parcel.pagination', {
                    total: state.pagination.total,
                    current: state.pagination.pageNo,
                    pages: Math.ceil(state.pagination.total / state.pagination.pageSize) || 1,
                  })
                }}
              </div>
            </div>
          </div>
          <div class="col-12 col-sm-auto">
            <div class="row justify-end">
              <q-pagination
                v-model="state.pagination.pageNo"
                :max="Math.ceil(state.pagination.total / state.pagination.pageSize)"
                :max-pages="$q.screen.lt.sm ? 3 : 6"
                boundary-links
                direction-links
                :dense="$q.screen.lt.sm"
                @update:model-value="onPageChange" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import ParcelApi from '~/composables/parcelApi';
import { useQuasar } from 'quasar';
import { reactive, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { useParcelStore } from '../../store/parcel';

// 初始化国际化
const { t } = useI18n();

definePageMeta({
  middleware: 'auth', // 引入身份验证中间件
});

const $q = useQuasar();
const parcelStore = useParcelStore();

// 显示通知消息
function showNotify(message, type = 'info') {
  $q.notify({
    message,
    color: type,
    position: 'top',
    timeout: 2000,
  });
}

// 数据
const state = reactive({
  currentTab: 'all', // 选中的 tabMaps 下标
  pagination: {
    list: [],
    total: 0,
    pageNo: 1,
    pageSize: 3,
  },
  loadStatus: '',
  searchNo: null,
});

onMounted(() => {
  getParcelList();
});

// 获取包裹列表
async function getParcelList() {
  state.loadStatus = 'loading';
  if (state.searchNo) {
    state.currentTab = 'all';
  }
  const currentStatus = state.currentTab !== 'all' ? parseInt(state.currentTab, 10) : null;

  try {
    // 调用API获取包裹列表
    const { code, data } = await ParcelApi.getParcelPage({
      pageNo: state.pagination.pageNo,
      pageSize: state.pagination.pageSize,
      status: currentStatus,
      no: state.searchNo,
    });

    if (code !== 0) {
      return;
    }

    data.list.forEach((parcel) => handleParcelButtons(parcel));
    state.pagination.list = data.list;
    state.pagination.total = data.total;
    state.loadStatus = state.pagination.list.length < state.pagination.total ? 'more' : 'noMore';
  } catch (error) {
    console.error('获取包裹列表失败:', error);
    showNotify(t('parcel.errors.fetchFailed'), 'negative');
  }
}

function createParcel() {
  //清除创建包裹 中的库存id
  parcelStore.selectProducts = [];
  navigateTo('/account/parcel-create');
}

function onTabChange() {
  state.pagination.pageNo = 1;
  getParcelList();
}

// 处理分页切换
function onPageChange(page) {
  state.pagination.pageNo = page; // 更新当前页码
  getParcelList(); // 刷新当前页数据
}

// 跳转到包裹详情页面
function toParcelDetail(parcelId) {
  if (!parcelId) {
    showNotify(t('parcel.errors.emptyId'), 'negative');
    return;
  }

  // 使用路由导航到包裹详情页面，并传递包裹ID作为查询参数
  navigateTo({
    path: '/account/parcel-detail',
    query: { id: parcelId },
  });
}

// 取消包裹
function cancelParcel(parcelId) {
  if (!parcelId) {
    showNotify(t('parcel.errors.emptyId'), 'negative');
    return;
  }

  // 显示确认对话框
  $q.dialog({
    title: t('parcel.dialog.cancel.title'),
    message: t('parcel.dialog.cancel.message'),
    cancel: true,
    persistent: true,
  }).onOk(() => {
    // 调用取消包裹API
    ParcelApi.cancelParcel(parcelId)
      .then(({ code }) => {
        if (code === 0) {
          showNotify(t('parcel.dialog.cancel.success'), 'positive');
          getParcelList(); // 刷新包裹列表
        } else {
          showNotify(t('parcel.errors.cancelFailed'), 'negative');
        }
      })
      .catch((error) => {
        console.error('取消包裹出错:', error);
        showNotify(t('parcel.errors.cancelError'), 'negative');
      });
  });
}

// 删除包裹
function deleteParcel(parcelId) {
  if (!parcelId) {
    showNotify(t('parcel.errors.emptyId'), 'negative');
    return;
  }

  // 显示确认对话框
  $q.dialog({
    title: t('parcel.dialog.delete.title'),
    message: t('parcel.dialog.delete.message'),
    cancel: true,
    persistent: true,
  }).onOk(() => {
    // 调用删除包裹API
    ParcelApi.deleteParcel(parcelId)
      .then(({ code }) => {
        if (code === 0) {
          showNotify(t('parcel.dialog.delete.success'), 'positive');
          getParcelList(); // 刷新包裹列表
        } else {
          showNotify(t('parcel.errors.deleteFailed'), 'negative');
        }
      })
      .catch((error) => {
        console.error('删除包裹出错:', error);
        showNotify(t('parcel.errors.deleteError'), 'negative');
      });
  });
}

// 支付包裹
function payParcel(pId) {
  if (!pId) {
    showNotify(t('parcel.errors.emptyId'), 'negative');
    return;
  }

  // 跳转到支付页面
  navigateTo({
    path: '/pay',
    query: { pId: pId, ps: PayOrderSources.PARCEL },
  });
}

// 查看物流
function trackParcel(parcelId) {
  if (!parcelId) {
    showNotify(t('parcel.errors.emptyId'), 'negative');
    return;
  }

  // 获取包裹详情并显示物流信息
  const parcel = state.pagination.list.find((p) => p.id === parcelId);
  if (!parcel || !parcel.trackingNumber) {
    showNotify(t('parcel.errors.noTracking'), 'warning');
    return;
  }

  $q.dialog({
    title: t('parcel.dialog.track.title'),
    message: `
      <div>
        <p><strong>${t('parcel.dialog.track.trackingNumber')}:</strong> ${parcel.trackingNumber}</p>
        <p><strong>${t('parcel.dialog.track.shippingMethod')}:</strong> ${parcel.shippingMethod}</p>
        <p><strong>${t('parcel.dialog.track.weight')}:</strong> ${parcel.weight}${t('parcel.parcelInfo.weightUnit')}</p>
        <p class="text-grey-8">${t('parcel.dialog.track.note')}</p>
      </div>
    `,
    html: true,
    cancel: false,
    persistent: false,
  });
}

function getParcelStatus(statusCode) {
  switch (statusCode) {
    case 0:
      return t('parcel.status.unpaid');
    case 10:
      return t('parcel.status.unshipped');
    case 20:
      return t('parcel.status.shipping');
    case 30:
      return t('parcel.status.completed');
    case 40:
      return t('parcel.status.cancelled');
    default:
      return t('parcel.status.unknown');
  }
}

// 获取包裹状态对应的颜色
function getStatusColor(status) {
  switch (status) {
    case 0:
      return 'orange';
    case 10:
      return 'blue';
    case 20:
      return 'teal';
    case 30:
      return 'positive';
    case 40:
      return 'grey';
    default:
      return 'grey';
  }
}

function handleParcelButtons(parcel) {
  parcel.buttons = [];

  if (parcel.status === 0) {
    // 待付款状态：可以支付和取消
    parcel.buttons.push('pay');
    parcel.buttons.push('cancel');
  }

  if (parcel.status === 20 || parcel.status === 30) {
    // 待发货或运输中状态：可以查看物流
    parcel.buttons.push('track');
  }
  if (parcel.status === 40) {
    // 已取消状态：可以删除
    parcel.buttons.push('delete');
  }
}
</script>

<style lang="scss" scoped>
.my-parcel {
  .q-tab {
    font-weight: 500;
    padding: 0 14px;
    min-height: 32px;
    font-size: 14px;

    @media (max-width: 599px) {
      padding: 0 10px;
      min-height: 28px;
      font-size: 13px;
    }
  }

  .q-tabs {
    min-height: 32px;

    @media (max-width: 599px) {
      min-height: 28px;
    }
  }

  .q-pagination {
    .q-btn {
      font-weight: 500;
      padding: 0 6px;
      min-height: 30px;
      font-size: 13px;

      @media (max-width: 599px) {
        padding: 0 4px;
        min-height: 26px;
        font-size: 12px;
      }
    }
  }

  .border-top {
    border-top: 1px solid #e0e0e0;
  }

  .rounded-borders-top {
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
  }

  .ellipsis-2-lines {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    min-height: 2em;
    line-height: 1.2;
  }

  .q-badge {
    font-size: 0.75rem;
  }

  // 卡片标题样式
  .bg-grey-2 {
    .text-subtitle2 {
      font-size: 14px;
    }
  }

  // 移动端卡片样式
  .mobile-parcel-list {
    .parcel-card {
      border-radius: 8px;
      overflow: hidden;

      .parcel-header {
        background-color: #f5f5f5;
        padding: 8px 12px;
      }

      .status-badge {
        font-size: 11px;
        padding: 3px 6px;
      }

      .product-img {
        width: 70px;
        flex-shrink: 0;
      }

      .product-info {
        flex: 1;
        min-width: 0;
      }

      .parcel-total {
        font-size: 13px;
      }

      .parcel-footer {
        padding: 8px 12px;
      }
    }
  }

  // 搜索框和按钮容器
  .search-container {
    display: flex;
    align-items: center;
    width: 100%;

    @media (max-width: 599px) {
      justify-content: space-between;
    }
  }

  // 搜索框样式
  .search-input {
    width: 100%;
    max-width: 280px;
    flex: 1;

    @media (max-width: 599px) {
      max-width: none;
      flex: 1;
    }
  }

  // 支付按钮样式
  .pay-btn {
    border-radius: 4px;
    font-weight: 500;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 3px 5px rgba(0, 0, 0, 0.2);
      transform: translateY(-1px);
    }
  }

  // 响应式辅助类
  .q-mt-sm-none {
    @media (min-width: 600px) {
      margin-top: 0;
    }
  }

  .justify-sm-start {
    @media (min-width: 600px) {
      justify-content: flex-start;
    }
  }

  .q-mb-sm-none {
    @media (min-width: 600px) {
      margin-bottom: 0;
    }
  }

  .create-parcel-btn {
    border-radius: 4px;
    font-weight: 500;
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
      transform: translateY(-1px);
    }
  }
}
</style>
