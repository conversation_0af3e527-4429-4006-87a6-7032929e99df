<template>
  <Header />
  <div class="contact-page">
    <div class="contact-container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1 class="page-title">联系我们</h1>
        <div class="page-subtitle">我们随时为您提供帮助和支持</div>
      </div>

      <!-- 联系方式和表单 -->
      <div class="row q-col-gutter-lg">
        <!-- 联系信息 -->
        <div class="col-12 col-md-5">
          <div class="contact-info-card">
            <h2 class="section-title">联系方式</h2>

            <div class="contact-item">
              <q-icon name="phone" size="1.5rem" color="primary" class="q-mr-md" />
              <div class="contact-details">
                <div class="contact-label">客服热线</div>
                <div class="contact-value">************</div>
                <div class="contact-note">周一至周日 9:00-22:00</div>
              </div>
            </div>

            <div class="contact-item">
              <q-icon name="email" size="1.5rem" color="primary" class="q-mr-md" />
              <div class="contact-details">
                <div class="contact-label">电子邮箱</div>
                <div class="contact-value"><EMAIL></div>
                <div class="contact-note">我们将在24小时内回复您</div>
              </div>
            </div>

            <div class="contact-item">
              <q-icon name="location_on" size="1.5rem" color="primary" class="q-mr-md" />
              <div class="contact-details">
                <div class="contact-label">公司地址</div>
                <div class="contact-value">上海市浦东新区张江高科技园区博云路2号</div>
                <div class="contact-note">邮编: 201203</div>
              </div>
            </div>

            <div class="contact-item">
              <q-icon name="support_agent" size="1.5rem" color="primary" class="q-mr-md" />
              <div class="contact-details">
                <div class="contact-label">在线客服</div>
                <div class="contact-value">
                  <q-btn color="primary" label="立即咨询" icon="chat" class="q-mt-sm" @click="openChatInNewWindow" />
                </div>
              </div>
            </div>

            <div class="social-media q-mt-lg">
              <h3 class="subsection-title">关注我们</h3>
              <div class="row q-gutter-md q-mt-sm">
                <q-btn round flat color="primary" icon="fab fa-weixin" />
                <q-btn round flat color="primary" icon="fab fa-weibo" />
                <q-btn round flat color="primary" icon="fab fa-qq" />
                <q-btn round flat color="primary" icon="fab fa-instagram" />
                <q-btn round flat color="primary" icon="fab fa-facebook" />
              </div>
            </div>
          </div>
        </div>

        <!-- 联系表单 -->
        <div class="col-12 col-md-7">
          <div class="contact-form-card">
            <h2 class="section-title">留言咨询</h2>
            <p class="form-description">如果您有任何问题、建议或合作意向，请填写以下表单，我们将尽快与您联系。</p>

            <q-form @submit="onSubmit" class="contact-form">
              <div class="row q-col-gutter-md">
                <div class="col-12 col-sm-6">
                  <q-input v-model="form.name" label="您的姓名 *" :rules="[(val) => !!val || '请输入您的姓名']" outlined dense class="q-mb-md" />
                </div>
                <div class="col-12 col-sm-6">
                  <q-input
                    v-model="form.phone"
                    label="联系电话 *"
                    :rules="[(val) => !!val || '请输入联系电话', (val) => /^1[3-9]\d{9}$/.test(val) || '请输入正确的手机号码']"
                    outlined
                    dense
                    class="q-mb-md" />
                </div>
              </div>

              <div class="row">
                <div class="col-12">
                  <q-input
                    v-model="form.email"
                    label="电子邮箱 *"
                    type="email"
                    :rules="[(val) => !!val || '请输入电子邮箱', (val) => /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/.test(val) || '请输入正确的邮箱格式']"
                    outlined
                    dense
                    class="q-mb-md" />
                </div>
              </div>

              <div class="row">
                <div class="col-12">
                  <q-select v-model="form.type" :options="contactTypes" label="咨询类型 *" :rules="[(val) => !!val || '请选择咨询类型']" outlined dense emit-value map-options class="q-mb-md" />
                </div>
              </div>

              <div class="row">
                <div class="col-12">
                  <q-input v-model="form.subject" label="主题 *" :rules="[(val) => !!val || '请输入主题']" outlined dense class="q-mb-md" />
                </div>
              </div>

              <div class="row">
                <div class="col-12">
                  <q-input v-model="form.message" label="留言内容 *" type="textarea" :rules="[(val) => !!val || '请输入留言内容']" outlined autogrow rows="5" class="q-mb-md" />
                </div>
              </div>

              <div class="row items-center q-mb-md">
                <q-checkbox v-model="form.agreement" label="我同意" />
                <q-btn flat dense color="primary" label="《隐私政策》" class="q-px-xs" />
              </div>

              <div class="row justify-end">
                <q-btn type="submit" color="primary" label="提交" :loading="submitting" :disable="!form.agreement" />
              </div>
            </q-form>
          </div>
        </div>
      </div>

      <!-- 营业时间和位置信息 -->
      <div class="row q-col-gutter-lg q-mt-xl">
        <!-- 营业时间 -->
        <div class="col-12 col-md-5">
          <div class="business-hours-section">
            <h2 class="section-title">营业时间</h2>
            <q-list bordered separator class="rounded-borders bg-white">
              <q-item>
                <q-item-section>
                  <q-item-label>周一至周五</q-item-label>
                </q-item-section>
                <q-item-section side>
                  <q-item-label>9:00 - 22:00</q-item-label>
                </q-item-section>
              </q-item>
              <q-item>
                <q-item-section>
                  <q-item-label>周六至周日</q-item-label>
                </q-item-section>
                <q-item-section side>
                  <q-item-label>10:00 - 22:00</q-item-label>
                </q-item-section>
              </q-item>
              <q-item>
                <q-item-section>
                  <q-item-label>法定节假日</q-item-label>
                </q-item-section>
                <q-item-section side>
                  <q-item-label>10:00 - 21:00</q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </div>
        </div>

        <!-- 地图 -->
        <div class="col-12 col-md-7">
          <div class="map-section">
            <h2 class="section-title">我们的位置</h2>
            <div class="map-container q-mt-md">
              <q-img src="/images/shanghai-map.jpg" class="rounded-borders" style="height: 400px">
                <div class="absolute-bottom text-subtitle1 text-center bg-black bg-opacity-50 text-white q-pa-sm">上海市浦东新区张江高科技园区博云路2号</div>
              </q-img>
            </div>
          </div>
        </div>
      </div>

      <!-- FAQ部分 -->
      <div class="faq-section q-mt-xl">
        <h2 class="section-title text-center">常见问题</h2>
        <p class="text-center q-mb-lg">以下是我们的客户经常咨询的问题，希望能帮助您快速找到答案</p>

        <div class="row justify-center">
          <div class="col-12 col-md-10">
            <q-list bordered separator class="rounded-borders bg-white">
              <q-expansion-item group="faq" icon="help_outline" label="如何跟踪我的订单？" header-class="text-primary" expand-icon-class="text-primary" expand-icon="keyboard_arrow_down">
                <q-card>
                  <q-card-section>
                    您可以登录您的账户，在"我的订单"页面查看所有订单的状态和物流信息。每个订单都有唯一的追踪号码，您可以使用该号码在对应的物流公司网站上查询详细的配送进度。
                  </q-card-section>
                </q-card>
              </q-expansion-item>

              <q-expansion-item group="faq" icon="help_outline" label="如何申请退款？" header-class="text-primary" expand-icon-class="text-primary" expand-icon="keyboard_arrow_down">
                <q-card>
                  <q-card-section>
                    如果您需要申请退款，请在"我的订单"中找到相应订单，点击"申请退款"按钮，填写退款原因并提交。我们的客服团队会在24小时内处理您的申请。根据退款原因和商品状态，退款可能需要3-15个工作日完成。
                  </q-card-section>
                </q-card>
              </q-expansion-item>

              <q-expansion-item group="faq" icon="help_outline" label="国际运输需要多长时间？" header-class="text-primary" expand-icon-class="text-primary" expand-icon="keyboard_arrow_down">
                <q-card>
                  <q-card-section>
                    国际运输时间取决于目的地国家和选择的物流方式。一般情况下，快递服务（如DHL、FedEx）需要3-7个工作日；普通航空邮件需要7-15个工作日；海运则需要20-45个工作日。特殊情况如海关检查可能会导致延误。
                  </q-card-section>
                </q-card>
              </q-expansion-item>

              <q-expansion-item group="faq" icon="help_outline" label="如何计算关税？" header-class="text-primary" expand-icon-class="text-primary" expand-icon="keyboard_arrow_down">
                <q-card>
                  <q-card-section>
                    关税由目的地国家的海关根据商品类型、价值和原产地决定。在下单时，我们的系统会根据您的收货地址和购买商品提供预估的关税金额。实际关税可能会有所不同，多收的关税我们会退还给您，少收的部分可能需要您在收货时支付。
                  </q-card-section>
                </q-card>
              </q-expansion-item>

              <q-expansion-item group="faq" icon="help_outline" label="你们提供哪些支付方式？" header-class="text-primary" expand-icon-class="text-primary" expand-icon="keyboard_arrow_down">
                <q-card>
                  <q-card-section>
                    我们支持多种支付方式，包括信用卡（Visa、MasterCard、American Express）、PayPal、支付宝、微信支付、银联支付等。所有支付信息都经过加密处理，确保您的资金安全。
                  </q-card-section>
                </q-card>
              </q-expansion-item>
            </q-list>
          </div>
        </div>
      </div>
    </div>
  </div>
  <Footer />
</template>

<script setup>
import { ref } from 'vue';
import { useQuasar } from 'quasar';

const $q = useQuasar();

// 表单数据
const form = ref({
  name: '',
  phone: '',
  email: '',
  type: '',
  subject: '',
  message: '',
  agreement: false,
});

// 咨询类型选项
const contactTypes = [
  { label: '产品咨询', value: 'product' },
  { label: '订单问题', value: 'order' },
  { label: '物流查询', value: 'logistics' },
  { label: '售后服务', value: 'after_sales' },
  { label: '投诉建议', value: 'complaint' },
  { label: '商务合作', value: 'business' },
  { label: '其他', value: 'other' },
];

// 提交状态
const submitting = ref(false);

// 在新窗口打开客服聊天页面
const openChatInNewWindow = () => {
  window.open('/chat', '_blank');
};

// 提交表单
const onSubmit = async () => {
  submitting.value = true;

  try {
    // 模拟API请求
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // 在实际应用中，这里应该是API调用
    // await fetch('/api/contact', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify(form.value)
    // });

    // 显示成功消息
    $q.notify({
      color: 'positive',
      message: '您的留言已成功提交，我们将尽快与您联系！',
      icon: 'check_circle',
      position: 'top',
      timeout: 3000,
    });

    // 重置表单
    form.value = {
      name: '',
      phone: '',
      email: '',
      type: '',
      subject: '',
      message: '',
      agreement: false,
    };
  } catch (error) {
    // 显示错误消息
    $q.notify({
      color: 'negative',
      message: '提交失败，请稍后再试或直接联系我们的客服。',
      icon: 'error',
      position: 'top',
      timeout: 3000,
    });
    console.error('提交表单失败:', error);
  } finally {
    submitting.value = false;
  }
};
</script>

<style lang="scss" scoped>
.contact-page {
  padding: 20px 0 40px;
  background-color: #f8f8f8;
}

.contact-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
  padding: 30px 0;
}

.page-title {
  font-size: 32px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

.page-subtitle {
  font-size: 18px;
  color: #666;
}

.section-title {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 20px;
  position: relative;

  &:after {
    content: '';
    display: block;
    width: 50px;
    height: 3px;
    background-color: #1976d2;
    margin-top: 10px;
  }

  &.text-center:after {
    margin-left: auto;
    margin-right: auto;
  }
}

.subsection-title {
  font-size: 18px;
  font-weight: bold;
  color: #1976d2;
  margin-bottom: 10px;
}

.contact-info-card,
.contact-form-card,
.business-hours-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  height: 100%;
}

.contact-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24px;

  &:last-child {
    margin-bottom: 0;
  }
}

.contact-details {
  flex: 1;
}

.contact-label {
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.contact-value {
  color: #555;
  margin-bottom: 4px;
}

.contact-note {
  font-size: 13px;
  color: #888;
}

.form-description {
  color: #666;
  margin-bottom: 24px;
}

.contact-form {
  .q-field {
    width: 100%;
  }
}

.map-container {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

@media (max-width: 767px) {
  .page-header {
    margin-bottom: 30px;
    padding: 20px 0;
  }

  .page-title {
    font-size: 26px;
  }

  .page-subtitle {
    font-size: 16px;
  }

  .section-title {
    font-size: 22px;
    margin-bottom: 15px;
  }

  .subsection-title {
    font-size: 17px;
  }

  .contact-info-card,
  .contact-form-card,
  .business-hours-card {
    padding: 20px;
    margin-bottom: 20px;
  }

  .contact-item {
    margin-bottom: 20px;
  }

  .map-container {
    height: 300px;
  }
}
</style>
