<template>
  <div class="international-address-form">
    <q-form @submit="onSubmit" class="q-gutter-sm">
      <!-- 地址别名 -->
      <div class="form-section">
        <div class="section-title q-pb-xs">
          <q-icon name="bookmark" size="xs" color="primary" class="q-mr-xs" />
          <span>{{ $t('addresses.form.sections.addressInfo') }}</span>
        </div>
        <div class="row q-col-gutter-sm">
          <div class="col-12">
            <q-input v-model="form.title" :label="$t('addresses.form.fields.alias')" :hint="$t('addresses.form.fields.aliasHint')" outlined class="address-input" bg-color="white">
              <template #prepend>
                <q-icon name="label" color="grey-7" />
              </template>
            </q-input>
          </div>
        </div>
      </div>

      <!-- 收件人信息 -->
      <div class="form-section">
        <div class="section-title q-pb-xs">
          <q-icon name="person" size="xs" color="primary" class="q-mr-xs" />
          <span>{{ $t('addresses.form.sections.recipientInfo') }}</span>
        </div>
        <div class="row q-col-gutter-sm">
          <div class="col-12 col-sm-6">
            <q-input
              v-model="form.name"
              :label="$t('addresses.form.fields.name')"
              :rules="[(val) => !!val || $t('addresses.form.validations.nameRequired')]"
              outlined
              class="address-input"
              bg-color="white">
              <template #prepend>
                <q-icon name="person" color="grey-7" />
              </template>
            </q-input>
          </div>
          <div class="col-12 col-sm-6">
            <div class="row q-col-gutter-sm">
              <div class="col-4">
                <q-select v-model="form.phoneCode" :options="phoneCodeOptions" outlined map-options emit-value class="address-input" bg-color="white">
                  <template #prepend>
                    <q-icon name="flag" color="grey-7" />
                  </template>
                </q-select>
              </div>
              <div class="col-8">
                <q-input
                  v-model="form.mobile"
                  :label="$t('addresses.form.fields.mobile')"
                  :rules="[(val) => !!val || $t('addresses.form.validations.mobileRequired')]"
                  outlined
                  class="address-input"
                  bg-color="white">
                  <template #prepend>
                    <q-icon name="phone" color="grey-7" />
                  </template>
                </q-input>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 国家和地区 -->
      <div class="form-section">
        <div class="section-title q-pb-xs">
          <q-icon name="public" size="xs" color="primary" class="q-mr-xs" />
          <span>{{ $t('addresses.form.sections.regionInfo') }}</span>
        </div>
        <div class="row q-col-gutter-sm">
          <div class="col-12 col-sm-6">
            <q-select
              v-model="form.countryId"
              :options="countryOptions"
              :label="$t('addresses.form.fields.country')"
              :rules="[(val) => !!val || $t('addresses.form.validations.countryRequired')]"
              outlined
              map-options
              emit-value
              @update:model-value="onCountryChange"
              class="address-input"
              bg-color="white">
              <template #prepend>
                <q-icon name="public" color="grey-7" />
              </template>
              <template #option="{ opt, selected }">
                <q-item :clickable="false" v-ripple>
                  <q-item-section avatar>
                    <div class="country-flag">{{ opt.flag }}</div>
                  </q-item-section>
                  <q-item-section>
                    <q-item-label>{{ opt.label }}</q-item-label>
                    <q-item-label caption>{{ opt.code }}</q-item-label>
                  </q-item-section>
                  <q-item-section side v-if="selected">
                    <q-icon name="check" color="primary" />
                  </q-item-section>
                </q-item>
              </template>
              <template #selected>
                <div class="row items-center no-wrap" v-if="selectedCountry">
                  <div class="country-flag q-mr-xs">{{ selectedCountry.flag }}</div>
                  <div>{{ selectedCountry.label }}</div>
                </div>
                <div v-else>{{ $t('addresses.form.placeholders.selectCountry') }}</div>
              </template>
            </q-select>
          </div>
          <div class="col-12 col-sm-6">
            <q-input
              v-model="form.postCode"
              :label="$t('addresses.form.fields.postCode')"
              :rules="[(val) => !!val || $t('addresses.form.validations.postCodeRequired'), (val) => isValidPostCode(val) || $t('addresses.form.validations.postCodeInvalid')]"
              outlined
              class="address-input"
              bg-color="white">
              <template #prepend>
                <q-icon name="markunread_mailbox" color="grey-7" />
              </template>
            </q-input>
          </div>
        </div>
      </div>

      <!-- 省/州和城市 -->
      <div class="form-section">
        <div class="section-title q-pb-xs">
          <q-icon name="location_city" size="xs" color="primary" class="q-mr-xs" />
          <span>{{ $t('addresses.form.sections.cityInfo') }}</span>
        </div>
        <div class="row q-col-gutter-sm">
          <div class="col-12 col-sm-6">
            <q-select
              v-if="stateOptions.length > 0"
              v-model="form.stateId"
              :options="stateOptions"
              :label="$t('addresses.form.fields.state')"
              :rules="[(val) => !!val || $t('addresses.form.validations.stateRequired')]"
              outlined
              map-options
              emit-value
              @update:model-value="onStateChange"
              class="address-input"
              bg-color="white">
              <template #prepend>
                <q-icon name="map" color="grey-7" />
              </template>
            </q-select>
            <q-input
              v-else
              v-model="form.stateName"
              :label="$t('addresses.form.fields.state')"
              :rules="[(val) => !!val || $t('addresses.form.validations.stateInputRequired')]"
              outlined
              class="address-input"
              bg-color="white">
              <template #prepend>
                <q-icon name="map" color="grey-7" />
              </template>
            </q-input>
          </div>
          <div class="col-12 col-sm-6">
            <q-select
              v-if="cityOptions.length > 0"
              v-model="form.cityId"
              :options="cityOptions"
              :label="$t('addresses.form.fields.city')"
              :rules="[(val) => !!val || $t('addresses.form.validations.cityRequired')]"
              outlined
              map-options
              emit-value
              class="address-input"
              bg-color="white">
              <template #prepend>
                <q-icon name="location_city" color="grey-7" />
              </template>
            </q-select>
            <q-input
              v-else
              v-model="form.cityName"
              :label="$t('addresses.form.fields.city')"
              :rules="[(val) => !!val || $t('addresses.form.validations.cityInputRequired')]"
              outlined
              class="address-input"
              bg-color="white">
              <template #prepend>
                <q-icon name="location_city" color="grey-7" />
              </template>
            </q-input>
          </div>
        </div>
      </div>

      <!-- 详细地址 -->
      <div class="form-section">
        <div class="section-title q-pb-xs">
          <q-icon name="home" size="xs" color="primary" class="q-mr-xs" />
          <span>{{ $t('addresses.form.sections.detailAddress') }}</span>
        </div>
        <div class="row q-col-gutter-sm">
          <div class="col-12">
            <q-input
              v-model="form.detailAddress"
              :label="$t('addresses.form.fields.detailAddress')"
              :rules="[(val) => !!val || $t('addresses.form.validations.detailAddressRequired')]"
              outlined
              type="textarea"
              rows="3"
              class="address-input"
              bg-color="white">
              <template #prepend>
                <q-icon name="home" color="grey-7" />
              </template>
              <template #hint> {{ $t('addresses.form.fields.detailAddressHint') }} </template>
            </q-input>
          </div>
        </div>
      </div>

      <!-- 设为默认地址 -->
      <div class="form-section q-mt-sm">
        <div class="row q-col-gutter-sm">
          <div class="col-12">
            <q-checkbox v-model="form.defaultStatus" :label="$t('addresses.form.fields.defaultAddress')" color="primary" class="text-subtitle2" />
          </div>
        </div>
      </div>

      <!-- 按钮区域 -->
      <div class="row justify-end q-mt-md">
        <q-btn :label="$t('addresses.buttons.save')" type="submit" color="primary" :loading="saving" icon-left="save" padding="xs md" />
      </div>
    </q-form>
  </div>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted } from 'vue';
import { useQuasar } from 'quasar';
import { useI18n } from 'vue-i18n';
import AddressApi from '~/composables/addressApi';

const $q = useQuasar();
const { t } = useI18n();
const emit = defineEmits(['submit', 'cancel']);

// 接收的属性
const props = defineProps({
  address: {
    type: Object,
    default: () => ({
      id: null,
      title: '',
      name: '',
      countryId: null,
      countryCode: '',
      phoneCode: '+86',
      mobile: '',
      stateId: null,
      stateName: '',
      cityId: null,
      cityName: '',
      postCode: '',
      detailAddress: '',
      defaultStatus: false,
    }),
  },
});

// 表单数据
const form = reactive({
  id: props.address.id,
  title: props.address.title || '',
  name: props.address.name || '',
  countryId: props.address.countryId || null,
  countryCode: props.address.countryCode || '',
  phoneCode: props.address.phoneCode || '+86',
  mobile: props.address.mobile || '',
  stateId: props.address.stateId || null,
  stateName: props.address.stateName || '',
  cityId: props.address.cityId || null,
  cityName: props.address.cityName || '',
  postCode: props.address.postCode || '',
  detailAddress: props.address.detailAddress || '',
  defaultStatus: props.address.defaultStatus || false,
});

// 加载状态
const saving = ref(false);
const loadingStates = ref(false);
const loadingCities = ref(false);

// 国家列表
const countries = ref([
  { id: 1, name: t('addresses.countries.china'), code: 'CN', phoneCode: '+86', flag: '🇨🇳' },
  { id: 2, name: t('addresses.countries.usa'), code: 'US', phoneCode: '+1', flag: '🇺🇸' },
  { id: 3, name: t('addresses.countries.japan'), code: 'JP', phoneCode: '+81', flag: '🇯🇵' },
  { id: 4, name: t('addresses.countries.korea'), code: 'KR', phoneCode: '+82', flag: '🇰🇷' },
  { id: 5, name: t('addresses.countries.uk'), code: 'GB', phoneCode: '+44', flag: '🇬🇧' },
  { id: 6, name: t('addresses.countries.germany'), code: 'DE', phoneCode: '+49', flag: '🇩🇪' },
  { id: 7, name: t('addresses.countries.france'), code: 'FR', phoneCode: '+33', flag: '🇫🇷' },
  { id: 8, name: t('addresses.countries.canada'), code: 'CA', phoneCode: '+1', flag: '🇨🇦' },
  { id: 9, name: t('addresses.countries.australia'), code: 'AU', phoneCode: '+61', flag: '🇦🇺' },
  { id: 10, name: t('addresses.countries.newZealand'), code: 'NZ', phoneCode: '+64', flag: '🇳🇿' },
  { id: 11, name: t('addresses.countries.singapore'), code: 'SG', phoneCode: '+65', flag: '🇸🇬' },
  { id: 12, name: t('addresses.countries.malaysia'), code: 'MY', phoneCode: '+60', flag: '🇲🇾' },
  { id: 13, name: t('addresses.countries.thailand'), code: 'TH', phoneCode: '+66', flag: '🇹🇭' },
  { id: 14, name: t('addresses.countries.indonesia'), code: 'ID', phoneCode: '+62', flag: '🇮🇩' },
  { id: 15, name: t('addresses.countries.philippines'), code: 'PH', phoneCode: '+63', flag: '🇵🇭' },
  { id: 16, name: t('addresses.countries.vietnam'), code: 'VN', phoneCode: '+84', flag: '🇻🇳' },
  { id: 17, name: t('addresses.countries.russia'), code: 'RU', phoneCode: '+7', flag: '🇷🇺' },
  { id: 18, name: t('addresses.countries.india'), code: 'IN', phoneCode: '+91', flag: '🇮🇳' },
  { id: 19, name: t('addresses.countries.brazil'), code: 'BR', phoneCode: '+55', flag: '🇧🇷' },
  { id: 20, name: t('addresses.countries.mexico'), code: 'MX', phoneCode: '+52', flag: '🇲🇽' },
]);

// 省份列表
const states = ref([]);
// 城市列表
const cities = ref([]);

// 国家选项
const countryOptions = computed(() => {
  return countries.value.map((country) => ({
    label: country.name,
    value: country.id,
    code: country.code,
    phoneCode: country.phoneCode,
    flag: country.flag,
  }));
});

// 电话区号选项
const phoneCodeOptions = computed(() => {
  return countries.value.map((country) => ({
    label: `${country.phoneCode} ${country.name}`,
    value: country.phoneCode,
  }));
});

// 省份选项
const stateOptions = computed(() => {
  return states.value.map((state) => ({
    label: state.name,
    value: state.id,
  }));
});

// 城市选项
const cityOptions = computed(() => {
  return cities.value.map((city) => ({
    label: city.name,
    value: city.id,
  }));
});

// 选中的国家
const selectedCountry = computed(() => {
  if (!form.countryId) return null;
  const country = countries.value.find((c) => c.id === form.countryId);
  if (country) {
    return {
      label: country.name,
      flag: country.flag,
    };
  }
  return null;
});

// 国家变更
async function onCountryChange(countryId) {
  if (!countryId) return;

  // 重置省份和城市
  form.stateId = null;
  form.stateName = '';
  form.cityId = null;
  form.cityName = '';

  // 设置国家代码和电话区号
  const country = countries.value.find((c) => c.id === countryId);
  if (country) {
    form.countryCode = country.code;
    form.phoneCode = country.phoneCode;
  }

  // 加载省份数据
  loadingStates.value = true;
  try {
    // 实际应用中，这里应该调用API获取省份列表
    // const { code, data } = await AddressApi.getState(countryId);
    // if (code === 0) {
    //   states.value = data;
    // }

    // 模拟数据
    if (countryId === 1) {
      // 中国
      states.value = [
        { id: 1, name: '北京市' },
        { id: 2, name: '上海市' },
        { id: 3, name: '广东省' },
        { id: 4, name: '江苏省' },
        { id: 5, name: '浙江省' },
      ];
    } else if (countryId === 2) {
      // 美国
      states.value = [
        { id: 101, name: 'California' },
        { id: 102, name: 'New York' },
        { id: 103, name: 'Texas' },
        { id: 104, name: 'Florida' },
        { id: 105, name: 'Illinois' },
      ];
    } else {
      states.value = [];
    }
  } catch (error) {
    console.error('获取省份列表失败', error);
    states.value = [];
  } finally {
    loadingStates.value = false;
  }
}

// 省份变更
async function onStateChange(stateId) {
  if (!stateId) return;

  // 重置城市
  form.cityId = null;
  form.cityName = '';

  // 设置省份名称
  const state = states.value.find((s) => s.id === stateId);
  if (state) {
    form.stateName = state.name;
  }

  // 加载城市数据
  loadingCities.value = true;
  try {
    // 实际应用中，这里应该调用API获取城市列表
    // const { code, data } = await AddressApi.getCity(stateId);
    // if (code === 0) {
    //   cities.value = data;
    // }

    // 模拟数据
    if (stateId === 3) {
      // 广东省
      cities.value = [
        { id: 1, name: '广州市' },
        { id: 2, name: '深圳市' },
        { id: 3, name: '东莞市' },
        { id: 4, name: '佛山市' },
        { id: 5, name: '珠海市' },
      ];
    } else if (stateId === 101) {
      // California
      cities.value = [
        { id: 201, name: 'Los Angeles' },
        { id: 202, name: 'San Francisco' },
        { id: 203, name: 'San Diego' },
        { id: 204, name: 'San Jose' },
        { id: 205, name: 'Sacramento' },
      ];
    } else {
      cities.value = [];
    }
  } catch (error) {
    console.error('获取城市列表失败', error);
    cities.value = [];
  } finally {
    loadingCities.value = false;
  }
}

// 城市变更
function onCityChange(cityId) {
  if (!cityId) return;

  // 设置城市名称
  const city = cities.value.find((c) => c.id === cityId);
  if (city) {
    form.cityName = city.name;
  }
}

// 验证邮政编码
function isValidPostCode(postCode) {
  if (!postCode) return false;

  // 根据不同国家验证邮政编码格式
  if (form.countryCode === 'CN') {
    // 中国邮编为6位数字
    return /^\d{6}$/.test(postCode);
  } else if (form.countryCode === 'US') {
    // 美国邮编为5位数字或5+4位数字
    return /^\d{5}(-\d{4})?$/.test(postCode);
  } else if (form.countryCode === 'JP') {
    // 日本邮编为3位数字-4位数字
    return /^\d{3}-\d{4}$/.test(postCode);
  } else if (form.countryCode === 'GB') {
    // 英国邮编格式较复杂
    return /^[A-Z]{1,2}[0-9][A-Z0-9]? ?[0-9][A-Z]{2}$/i.test(postCode);
  }

  // 其他国家默认通过
  return true;
}

// 提交表单
async function onSubmit() {
  saving.value = true;
  try {
    // 构建提交的数据
    const addressData = {
      id: form.id,
      title: form.title,
      name: form.name,
      countryId: form.countryId,
      countryCode: form.countryCode,
      phoneCode: form.phoneCode,
      mobile: form.mobile,
      stateId: form.stateId,
      stateName: form.stateName,
      cityId: form.cityId,
      cityName: form.cityName,
      postCode: form.postCode,
      detailAddress: form.detailAddress,
      defaultStatus: form.defaultStatus,
    };

    // 发送提交事件
    emit('submit', addressData);
  } catch (error) {
    console.error('保存地址失败', error);
    $q.notify({
      color: 'negative',
      message: '保存地址失败，请重试',
      icon: 'error',
    });
  } finally {
    saving.value = false;
  }
}

// 初始化
onMounted(() => {
  // 如果有国家ID，加载省份
  if (form.countryId) {
    onCountryChange(form.countryId);

    // 如果有省份ID，加载城市
    if (form.stateId) {
      onStateChange(form.stateId);
    }
  }
});
</script>

<style lang="scss" scoped>
.international-address-form {
  background-color: #f8f9fa;
  border-radius: 4px;
  padding: 12px;

  .form-section {
    margin-bottom: 10px;

    .section-title {
      display: flex;
      align-items: center;
      font-size: 0.9rem;
      font-weight: 500;
      color: #333;
      margin-bottom: 0;
    }
  }

  .address-input {
    .q-field__control {
      height: 36px;
    }

    &.q-textarea .q-field__control {
      height: auto;
    }

    .q-field__marginal {
      height: 36px;
    }
  }

  .country-flag {
    font-size: 1.2rem;
  }

  .q-checkbox {
    .q-checkbox__inner {
      font-size: 1rem;
    }
  }

  // 移动端优化
  @media (max-width: 599px) {
    padding: 10px 8px;

    .address-input {
      .q-field__control {
        height: 34px;
      }

      .q-field__marginal {
        height: 34px;
      }
    }

    .section-title {
      font-size: 0.85rem;
    }
  }
}
</style>
