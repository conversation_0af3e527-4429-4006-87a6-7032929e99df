const TicketApi = {
  /**
   * 创建工单
   * @param {Object} params - 工单数据
   * @param {string} params.title - 工单标题
   * @param {number} params.type - 工单类型 (1:订单问题 2:商品问题 3:包裹问题 4:物流问题 5:账户问题 6:其他问题)
   * @param {number} params.priority - 优先级 (1:高 2:中 3:低)
   * @param {string} params.description - 问题描述
   * @param {Array} params.attachmentUrls - 附件URL列表
   * @returns {Promise} API响应
   */
  create: (params) => {
    return useClientPost('/member/ticket/create', {
      params,
    });
  },

  /**
   * 获取工单分页列表
   * @param {Object} params - 查询参数
   * @param {number} params.pageNo - 页码
   * @param {number} params.pageSize - 每页数量
   * @param {number} params.type - 工单类型（可选）
   * @param {number} params.status - 工单状态（可选）
   * @param {string} params.keyword - 关键词搜索（可选）
   * @param {string} params.createTime - 创建时间范围（可选）
   * @returns {Promise} API响应
   */
  getPage: (params) => {
    return useClientGet('/member/ticket/page', {
      params,
    });
  },

  /**
   * 获取工单详情
   * @param {number} id - 工单ID
   * @returns {Promise} API响应
   */
  getDetail: (id) => {
    return useClientGet('/member/ticket/get', {
      params: { id },
    });
  },

  /**
   * 提交回复
   * @param {Object} params - 回复数据
   * @param {number} params.ticketId - 工单ID
   * @param {string} params.content - 回复内容
   * @param {Array} params.attachmentUrls - 附件URL列表（可选）
   * @returns {Promise} API响应
   */
  reply: (params) => {
    return useClientPost('/member/ticket/reply', {
      params,
    });
  },

  /**
   * 关闭工单
   * @param {number} id - 工单ID
   * @returns {Promise} API响应
   */
  close: (id) => {
    return useClientPut(`/member/ticket/close/${id}`);
  },

  /**
   * 评分工单
   * @param {Object} params - 评分数据
   * @param {number} params.ticketId - 工单ID
   * @param {number} params.rating - 评分 (1-5分)
   * @param {string} params.ratingComment - 评分备注（可选）
   * @returns {Promise} API响应
   */
  rate: (params) => {
    return useClientPut('/member/ticket/rate', {
      params,
    });
  },

  /**
   * 上传文件
   * @param {File} file - 文件对象
   * @param {string} path - 文件路径（可选）
   * @returns {Promise} 上传结果
   */
  uploadFile: (file, path = 'ticket') => {
    const formData = new FormData();
    formData.append('file', file);
    if (path) {
      formData.append('path', path);
    }

    return useClientPost('/infra/file/upload', {
      body: formData,
    });
  },

  /**
   * 批量上传文件
   * @param {Array<File>} files - 文件数组
   * @param {string} path - 文件路径（可选）
   * @returns {Promise<Array>} 上传结果数组
   */
  uploadFiles: async (files, path = 'ticket') => {
    const uploadPromises = files.map(file => TicketApi.uploadFile(file, path));
    return Promise.all(uploadPromises);
  },

  /**
   * 工单类型枚举
   */
  TICKET_TYPES: {
    ORDER: 1,      // 订单问题
    PRODUCT: 2,    // 商品问题
    PACKAGE: 3,    // 包裹问题
    LOGISTICS: 4,  // 物流问题
    ACCOUNT: 5,    // 账户问题
    OTHER: 6       // 其他问题
  },

  /**
   * 工单状态枚举
   */
  TICKET_STATUS: {
    PENDING: 10,        // 待处理
    PROCESSING: 20,     // 处理中
    WAITING_REPLY: 30,  // 待用户回复
    RESOLVED: 40,       // 已解决
    CLOSED: 50          // 已关闭
  },

  /**
   * 优先级枚举
   */
  PRIORITY: {
    HIGH: 1,    // 高
    MEDIUM: 2,  // 中
    LOW: 3      // 低
  },

  /**
   * 消息类型枚举
   */
  MESSAGE_TYPE: {
    USER_REPLY: 1,     // 用户回复
    SERVICE_REPLY: 2   // 客服回复
  },

  /**
   * 获取工单类型名称
   * @param {number} type - 工单类型
   * @returns {string} 类型名称
   */
  getTypeName: (type) => {
    const typeNames = {
      [TicketApi.TICKET_TYPES.ORDER]: '订单问题',
      [TicketApi.TICKET_TYPES.PRODUCT]: '商品问题',
      [TicketApi.TICKET_TYPES.PACKAGE]: '包裹问题',
      [TicketApi.TICKET_TYPES.LOGISTICS]: '物流问题',
      [TicketApi.TICKET_TYPES.ACCOUNT]: '账户问题',
      [TicketApi.TICKET_TYPES.OTHER]: '其他问题'
    };
    return typeNames[type] || '未知类型';
  },

  /**
   * 获取工单状态名称
   * @param {number} status - 工单状态
   * @returns {string} 状态名称
   */
  getStatusName: (status) => {
    const statusNames = {
      [TicketApi.TICKET_STATUS.PENDING]: '待处理',
      [TicketApi.TICKET_STATUS.PROCESSING]: '处理中',
      [TicketApi.TICKET_STATUS.WAITING_REPLY]: '待用户回复',
      [TicketApi.TICKET_STATUS.RESOLVED]: '已解决',
      [TicketApi.TICKET_STATUS.CLOSED]: '已关闭'
    };
    return statusNames[status] || '未知状态';
  },

  /**
   * 获取优先级名称
   * @param {number} priority - 优先级
   * @returns {string} 优先级名称
   */
  getPriorityName: (priority) => {
    const priorityNames = {
      [TicketApi.PRIORITY.HIGH]: '高',
      [TicketApi.PRIORITY.MEDIUM]: '中',
      [TicketApi.PRIORITY.LOW]: '低'
    };
    return priorityNames[priority] || '中';
  },

  /**
   * 获取状态颜色
   * @param {number} status - 工单状态
   * @returns {string} 颜色值
   */
  getStatusColor: (status) => {
    const statusColors = {
      [TicketApi.TICKET_STATUS.PENDING]: 'orange',
      [TicketApi.TICKET_STATUS.PROCESSING]: 'blue',
      [TicketApi.TICKET_STATUS.WAITING_REPLY]: 'purple',
      [TicketApi.TICKET_STATUS.RESOLVED]: 'green',
      [TicketApi.TICKET_STATUS.CLOSED]: 'grey'
    };
    return statusColors[status] || 'grey';
  },

  /**
   * 获取类型颜色
   * @param {number} type - 工单类型
   * @returns {string} 颜色值
   */
  getTypeColor: (type) => {
    const typeColors = {
      [TicketApi.TICKET_TYPES.ORDER]: 'primary',
      [TicketApi.TICKET_TYPES.PRODUCT]: 'teal',
      [TicketApi.TICKET_TYPES.PACKAGE]: 'purple',
      [TicketApi.TICKET_TYPES.LOGISTICS]: 'orange',
      [TicketApi.TICKET_TYPES.ACCOUNT]: 'red',
      [TicketApi.TICKET_TYPES.OTHER]: 'blue-grey'
    };
    return typeColors[type] || 'grey';
  }
};

export default TicketApi;
