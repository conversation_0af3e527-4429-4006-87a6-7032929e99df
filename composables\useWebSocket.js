import { reactive, onMounted, onUnmounted } from 'vue';
import { useCookie, useRuntimeConfig } from '#app';

/**
 * WebSocket 创建 hook
 * @param {Object} config - WebSocket配置
 * @returns {Object} - WebSocket实例和状态
 */
export function useWebSocket(config = {}) {
  // WebSocket配置选项
  const options = reactive({
    url: '/infra/ws', // WebSocket连接地址
    heartbeatInterval: 30000, // 心跳间隔，默认30秒
    reconnectInterval: 5000, // 重连间隔，默认5秒
    reconnectAttempts: 10, // 最大重连次数
    isConnected: false, // 连接状态
    isReconnecting: false, // 重连状态
    reconnectCount: 0, // 当前重连次数
    destroy: false, // 是否销毁
    isLoggedIn: false, // 登录状态
    loginRequired: true, // 是否需要登录才能连接
  });

  // 合并用户配置
  if (config) {
    Object.assign(options, config);
  }

  let ws = null; // WebSocket实例
  let heartbeatTimer = null; // 心跳定时器
  let reconnectTimer = null; // 重连定时器
  let pingTimeoutTimer = null; // 心跳超时定时器

  /**
   * 初始化WebSocket
   */
  function initWebSocket() {
    // 检查用户是否已登录
    const tokenCookie = useCookie('token');
    const token = tokenCookie.value;

    // 更新登录状态
    options.isLoggedIn = !!token;

    // 如果需要登录但未登录，则不建立连接
    if (options.loginRequired && !options.isLoggedIn) {
      console.error('未登录，无法建立WebSocket连接');

      // 触发未登录回调
      if (config.onNotLoggedIn) {
        config.onNotLoggedIn();
      }
      return;
    }

    // 关闭之前的连接
    if (ws) {
      ws.close();
    }

    // 创建新的WebSocket连接
    console.log('【WebSocket】开始构建WebSocket URL');
    let wsUrl = '';
    try {
      const runtimeConfig = useRuntimeConfig();
      let baseUrl = runtimeConfig.public.baseUrl || '';
      console.log('【WebSocket】从配置获取的baseUrl:', baseUrl);

      // 检查baseUrl是否已经包含ws或wss协议
      if (baseUrl.startsWith('ws://') || baseUrl.startsWith('wss://')) {
        console.log('【WebSocket】baseUrl已包含ws协议');
        wsUrl = `${baseUrl}${options.url}`;
      } else {
        // 将http转为ws，https转为wss
        const wsBaseUrl = baseUrl.replace(/^http:\/\//i, 'ws://').replace(/^https:\/\//i, 'wss://');
        console.log('【WebSocket】转换后的wsBaseUrl:', wsBaseUrl);
        wsUrl = `${wsBaseUrl}${options.url}`;
      }
    } catch (error) {
      console.error('【WebSocket】构建WebSocket URL失败', error);
      // 回退方案
      const origin = typeof window !== 'undefined' ? window.location.origin : '';
      wsUrl = `${origin.replace(/^http:\/\//i, 'ws://').replace(/^https:\/\//i, 'wss://')}${options.url}`;
      console.log('【WebSocket】使用回退WebSocket URL:', wsUrl);
    }

    // 添加token
    if (token) {
      wsUrl += `?token=${token}`;
      console.log('【WebSocket】添加token后的WebSocket URL:', wsUrl);
    } else {
      console.log('【WebSocket】未添加token的WebSocket URL:', wsUrl);
    }

    try {
      ws = new WebSocket(wsUrl);

      // 连接打开
      ws.onopen = () => {
        console.log('【WebSocket】连接成功');
        options.isConnected = true;
        options.isReconnecting = false;
        options.reconnectCount = 0;
        startHeartbeat();

        // 连接成功后的初始化操作
        console.log('【WebSocket】连接已就绪');

        if (config.onConnected) {
          config.onConnected();
        }
      };

      // 接收消息
      ws.onmessage = (event) => {
        // 添加详细日志，记录原始消息
        console.log('【WebSocket】收到原始消息:', event.data);

        try {
          // 处理心跳响应
          if (event.data === 'pong') {
            console.log('【WebSocket】收到心跳响应 pong');
            resetPingTimeout();
            return;
          }

          // 尝试解析消息
          let data;
          try {
            data = typeof event.data === 'string' ? JSON.parse(event.data) : event.data;
            console.log('【WebSocket】解析后的消息:', data);
            console.log('【WebSocket】消息类型:', typeof data);
            if (typeof data === 'object') {
              console.log('【WebSocket】消息结构:', Object.keys(data));
            }
          } catch (parseError) {
            console.warn('【WebSocket】消息不是有效的JSON格式:', event.data);
            console.warn('【WebSocket】解析错误:', parseError);
            data = event.data;
          }

          // 调用消息处理回调
          if (config.onMessage) {
            console.log('【WebSocket】调用onMessage回调');
            config.onMessage(data);
          } else {
            console.warn('【WebSocket】未设置onMessage回调函数');
          }
        } catch (error) {
          console.error('【WebSocket】处理消息失败', error, '原始消息:', event.data);
          // 即使解析失败，也尝试将原始消息传递给回调
          if (config.onMessage && event.data) {
            console.log('【WebSocket】尝试传递原始消息到回调');
            config.onMessage(event.data);
          }
        }
      };

      // 连接关闭
      ws.onclose = () => {
        console.log('WebSocket连接关闭');
        options.isConnected = false;
        clearHeartbeat();
        if (config.onClosed) {
          config.onClosed();
        }

        // 如果不是主动销毁，则尝试重连
        if (!options.destroy) {
          reconnect();
        }
      };

      // 连接错误
      ws.onerror = (error) => {
        console.error('WebSocket连接错误', error);
        options.isConnected = false;
        if (config.onError) {
          config.onError(error);
        }
        reconnect();
      };
    } catch (error) {
      console.error('创建WebSocket连接失败', error);
      reconnect();
    }
  }

  /**
   * 发送消息
   * @param {Object|string} message - 要发送的消息
   * @returns {boolean} - 是否发送成功
   */
  function sendMessage(message) {
    if (!ws || ws.readyState !== WebSocket.OPEN) {
      console.error('WebSocket未连接，无法发送消息');
      return false;
    }

    try {
      const data = typeof message === 'string' ? message : JSON.stringify(message);
      ws.send(data);
      return true;
    } catch (error) {
      console.error('发送WebSocket消息失败', error);
      return false;
    }
  }

  /**
   * 开始心跳
   */
  function startHeartbeat() {
    console.log('【WebSocket】开始心跳检测，间隔:', options.heartbeatInterval, 'ms');
    clearHeartbeat();
    heartbeatTimer = setInterval(() => {
      // 检查连接状态
      if (ws && ws.readyState === WebSocket.OPEN) {
        console.log('【WebSocket】发送心跳包 ping');
        const success = sendMessage('ping');

        if (success) {
          console.log('【WebSocket】心跳包发送成功，等待响应');
          // 设置心跳超时检测
          pingTimeoutTimer = setTimeout(() => {
            // 如果在超时时间内没有收到 pong，则认为连接断开
            console.log('【WebSocket】心跳超时(5秒内未收到pong)，尝试重连');
            if (ws) {
              console.log('【WebSocket】关闭现有连接');
              ws.close();
            }
            reconnect();
          }, 5000); // 5秒内没收到pong就认为断线了
        } else {
          console.error('【WebSocket】心跳包发送失败，尝试重连');
          reconnect();
        }
      } else {
        console.warn('【WebSocket】连接状态异常，无法发送心跳包');
        if (ws) {
          console.log('【WebSocket】当前连接状态:', ws.readyState);
          // 0: CONNECTING, 1: OPEN, 2: CLOSING, 3: CLOSED
          const states = ['CONNECTING', 'OPEN', 'CLOSING', 'CLOSED'];
          console.log('【WebSocket】连接状态说明:', states[ws.readyState]);
        } else {
          console.log('【WebSocket】连接对象不存在');
        }
        reconnect();
      }
    }, options.heartbeatInterval);
  }

  /**
   * 清除心跳
   */
  function clearHeartbeat() {
    if (heartbeatTimer) {
      clearInterval(heartbeatTimer);
      heartbeatTimer = null;
    }
    resetPingTimeout();
  }

  /**
   * 重置心跳超时
   */
  function resetPingTimeout() {
    if (pingTimeoutTimer) {
      clearTimeout(pingTimeoutTimer);
      pingTimeoutTimer = null;
    }
  }

  /**
   * 重连
   */
  function reconnect() {
    if (options.destroy || options.reconnectCount >= options.reconnectAttempts) {
      console.error('WebSocket重连次数已达上限，停止重连');
      return;
    }

    if (reconnectTimer) {
      clearTimeout(reconnectTimer);
    }

    options.isReconnecting = true;
    options.reconnectCount++;

    console.log(`WebSocket ${options.reconnectInterval / 1000}秒后重连...`);
    reconnectTimer = setTimeout(() => {
      console.log(`WebSocket第${options.reconnectCount}次重连`);
      initWebSocket();
    }, options.reconnectInterval);
  }

  /**
   * 关闭连接
   */
  function closeConnection() {
    options.destroy = true;
    clearHeartbeat();
    if (reconnectTimer) {
      clearTimeout(reconnectTimer);
      reconnectTimer = null;
    }
    if (ws) {
      ws.close();
      ws = null;
    }
  }

  // 组件挂载时连接
  onMounted(() => {
    initWebSocket();
  });

  // 组件卸载时断开连接
  onUnmounted(() => {
    closeConnection();
  });

  return {
    options,
    sendMessage,
    closeConnection,
    reconnect,
  };
}
