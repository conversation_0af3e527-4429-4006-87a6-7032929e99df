<template>
  <Header />
  <div class="news-page">
    <div class="news-container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1 class="page-title">通知公告</h1>
        <div class="page-subtitle">了解平台最新动态和重要通知</div>
      </div>

      <!-- 新闻分类 -->
      <div class="filter-section q-mb-lg">
        <div class="row items-center justify-between">
          <div class="filter-tabs">
            <q-tabs v-model="activeTab" class="text-primary" active-color="primary" indicator-color="primary" narrow-indicator dense @update:model-value="onTabChange">
              <q-tab name="0" label="全部" />
              <q-tab name="1" label="系统通知" />
              <q-tab name="2" label="平台公告" />
            </q-tabs>
          </div>
        </div>
      </div>

      <!-- 新闻列表 -->
      <div class="news-list">
        <div v-if="state.pagination.list.length > 0">
          <q-list bordered separator>
            <q-item v-for="notice in state.pagination.list" :key="notice.id" @click="goToDetail(notice)" clickable v-ripple class="news-item">
              <q-item-section avatar>
                <q-icon :name="getCategoryIcon(notice.type)" :color="getCategoryColor(notice.type)" size="md" />
              </q-item-section>

              <q-item-section>
                <q-item-label class="news-title">
                  {{ notice.title }}
                </q-item-label>
                <!-- <q-item-label caption lines="2" class="news-summary">
                  {{ notice.content }}
                </q-item-label> -->
                <div class="row items-center q-mt-xs">
                  <q-badge :color="getCategoryColor(notice.type)" class="q-mr-sm">
                    {{ getCategoryText(notice.type) }}
                  </q-badge>
                </div>
              </q-item-section>

              <q-item-section side>
                <div class="column items-end">
                  <q-item-label caption>{{ formatDate(notice.publishTime) }}</q-item-label>
                </div>
              </q-item-section>
            </q-item>
          </q-list>
        </div>

        <div v-else class="no-news">
          <q-icon name="article" size="4rem" color="grey-5" />
          <p class="text-grey-7 q-mt-md">暂无通知公告</p>
        </div>

        <!-- 分页 -->
        <div v-if="state.pagination.list.length > 0"  class="pagination-container q-mt-lg">
          <q-pagination
                v-model="state.pagination.pageNo"
                :max="Math.ceil(state.pagination.total / state.pagination.pageSize)"
                :max-pages="6"
                boundary-links
                direction-links
                @update:model-value="onPageChange" />
        </div>
      </div>
    </div>
  </div>
  <Footer />
</template>

<script setup>
import { ref, computed, onMounted, watch, reactive } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { date } from 'quasar';
import NoticeApi from '~/composables/noticeApi';

const router = useRouter();
const route = useRoute();

const state = reactive({
  pagination: {
    list: [],
    total: 0,
    pageNo: 1,
    pageSize: 2,
  },
  loadStatus: false,
});

// 当前激活的Tab
const activeTab = ref('0');


// 从URL获取页码和筛选条件
onMounted(() => {
  fetchNotice();
});


// 处理页码变化
const onPageChange = (page) => {
  state.pagination.pageNo = page;
  fetchNotice();
};




// Tab切换事件处理
const onTabChange = (newTab) => {
  activeTab.value = newTab;
  // 重置页码到第一页
  state.pagination.pageNo = 1;
  // 重新获取数据
  fetchNotice();
};

// 跳转到详情页面
const goToDetail = (notice) => {
  // 使用 useState 保存数据
  const detailState = useState('noticeDetail')
  detailState.value = {
    id: notice.id,
    title: notice.title,
    type: notice.type,
    content: notice.content,
    publishTime: notice.publishTime
  }

  navigateTo(`/notice/detail/${notice.id}`)
};

// 模拟API请求获取新闻列表
async function  fetchNotice() {
  state.loadStatus = true;

  // 根据Tab页确定类型
  let type = activeTab.value;

  // 动态构造参数对象
  const params = {
    pageNo: state.pagination.pageNo,
    pageSize: state.pagination.pageSize,
  };
  // 只有当 type 不为 '0' 时才添加
  if (type !== '0') {
    params.type = type;
  }

  const { code, data } = await NoticeApi.getPage(params);
  state.loadStatus = false;
  if (code === 0) {
    state.pagination.list = data.list || [];
    state.pagination.total = data.total || 0;
  }
};



// 格式化日期
const formatDate = (dateString) => {
  return date.formatDate(new Date(dateString), 'YYYY-MM-DD');
};



// 获取分类图标
const getCategoryIcon = (type) => {
  switch (type) {
    case 1:
      return 'announcement';
    case 2:
      return 'campaign';
    default:
      return 'article';
  }
};

// 获取分类颜色
const getCategoryColor = (type) => {
  switch (type) {
    case 1:
      return 'red';
    case 2:
      return 'orange';
    default:
      return 'grey';
  }
};

// 获取分类文本
const getCategoryText = (type) => {
  switch (type) {
    case 1:
      return '系统通知';
    case 2:
      return '平台公告';
    default:
      return '其他';
  }
};


</script>

<style lang="scss" scoped>
.news-page {
  padding: 20px 0 40px;
  background-color: #f8f8f8;
}

.news-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
  padding: 30px 0;
}

.page-title {
  font-size: 32px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

.page-subtitle {
  font-size: 18px;
  color: #666;
}

.filter-section {
  margin-bottom: 30px;
}

.pinned-news {
  margin-bottom: 40px;
}

.pinned-news-item {
  background-color: #f0f7ff;

  &:hover {
    background-color: #e3f2fd;
  }
}

.news-item {
  &:hover {
    background-color: #f5f5f5;
  }
}

.news-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  line-height: 1.3;
}

.news-summary {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  margin-top: 4px;
}

.no-news {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin: 40px 0 20px;
}

@media (max-width: 767px) {
  .page-header {
    margin-bottom: 30px;
    padding: 20px 0;
  }

  .page-title {
    font-size: 26px;
  }

  .page-subtitle {
    font-size: 16px;
  }

  .news-title {
    font-size: 15px;
  }

  .news-summary {
    font-size: 13px;
  }

  .pagination-container {
    margin: 30px 0 15px;
  }
}
</style>
