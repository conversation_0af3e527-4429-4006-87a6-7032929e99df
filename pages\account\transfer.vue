<template>
  <div class="my-transfer">
    <!-- 标题区域 -->
    <div class="bg-grey-2 q-px-md q-py-sm row justify-between items-center">
      <div class="row items-center">
        <q-icon name="local_shipping" size="xs" color="purple" class="q-mr-xs" />
        <span class="text-subtitle1 text-weight-medium">{{ $t('transfer.common.myTransfer') }}</span>
      </div>
      <div class="row items-center header-buttons">
        <WarehouseAddressButton class="q-mr-sm warehouse-btn" />
        <q-btn color="primary" icon="add" :label="$t('transfer.common.createTransfer')" dense to="/account/transfer-create" class="create-btn" />
      </div>
    </div>

    <div class="q-pa-md">
      <!-- 顶部操作区域 - 响应式布局 -->
      <div class="q-mb-md">
        <!-- 顶部 Tab -->
        <div class="row justify-between items-center wrap-on-mobile">
          <div class="col-12 col-md-auto">
            <q-tabs v-model="state.currentTab" class="text-primary compact-tabs" active-color="primary" indicator-color="primary" align="left" narrow-indicator @update:model-value="onTabChange">
              <q-tab name="all" :label="$t('transfer.status.all')" />
              <q-tab name="0" :label="$t('transfer.status.pending')" />
              <q-tab name="10" :label="$t('transfer.status.warehoused')" />
              <!-- <q-tab name="20" :label="$t('transfer.status.toBeShipped')" />
              <q-tab name="30" :label="$t('transfer.status.shipped')" /> -->
            </q-tabs>
          </div>

          <!-- 搜索框 -->
          <div class="col-12 col-md-auto row items-center justify-end-on-mobile q-mt-sm-md">
            <div class="row items-center search-container">
              <q-input v-model="state.searchNo" outlined dense clearable :placeholder="$t('transfer.list.searchPlaceholder')" @keyup.enter="getTransferList" class="search-input">
                <template #append>
                  <q-btn flat dense icon="search" @click="getTransferList" />
                </template>
              </q-input>
            </div>
          </div>
        </div>
      </div>

      <q-separator />

      <!-- 移除了仓库地址对话框，已移至组件中 -->

      <!-- 转运单列表 -->
      <div>
        <!-- 表格标题行 - 仅在桌面视图显示 -->
        <div class="row items-center q-py-sm bg-grey-2 text-grey-8 q-mt-md rounded-borders-top table-header desktop-only">
          <div class="col-2 text-center text-weight-medium">{{ $t('transfer.common.transferNumber') }}</div>
          <div class="col-2 text-center text-weight-medium">{{ $t('transfer.common.createTime') }}</div>
          <div class="col-2 text-center text-weight-medium">{{ $t('transfer.common.expressCompany') }}</div>
          <div class="col-2 text-center text-weight-medium">{{ $t('transfer.common.expressNumber') }}</div>
          <div class="col-1 text-center text-weight-medium">{{ $t('transfer.common.itemCount') }}</div>
          <div class="col-1 text-center text-weight-medium">{{ $t('transfer.common.status') }}</div>
          <div class="col-2 text-center text-weight-medium">{{ $t('transfer.common.actions') }}</div>
        </div>

        <!-- 加载中 -->
        <div v-if="state.loadStatus" class="column items-center q-py-lg">
          <q-spinner color="primary" size="3em" />
          <div class="q-mt-sm text-grey-7">{{ $t('transfer.common.loading') }}</div>
        </div>

        <!-- 无数据 -->
        <div v-else-if="state.pagination.list?.length === 0" class="column items-center q-py-lg">
          <q-icon name="local_shipping" size="3em" color="grey-5" />
          <div class="text-grey-7 q-mt-sm">{{ $t('transfer.list.noTransferRecords') }}</div>
          <q-btn color="primary" icon="add" :label="$t('transfer.common.createTransfer')" class="q-mt-md" to="/account/transfer-create" />
        </div>

        <!-- 转运单列表 -->
        <div v-else>
          <!-- 桌面视图 - 表格样式 -->
          <div v-for="transfer in state.pagination.list" :key="transfer.id" class="bg-white q-mb-md rounded-borders shadow-1 transfer-card desktop-view">
            <div class="q-pa-md">
              <div class="row items-center card-content">
                <div class="col-2 text-center">{{ transfer.no }}</div>
                <div class="col-2 text-center">{{ formatDateTime(transfer.createTime) }}</div>
                <div class="col-2 text-center">{{ transfer.logisticsId }}</div>
                <div class="col-2 text-center">{{ transfer.logisticsNo }}</div>
                <div class="col-1 text-center">{{ transfer.itemCount || 0 }}</div>
                <div class="col-1 text-center">
                  <q-badge :color="getStatusColor(transfer.status)" class="q-py-xs q-px-sm status-badge">
                    {{ getStatusText(transfer.status) }}
                  </q-badge>
                </div>
                <div class="col-2 text-center">
                  <q-btn flat color="primary" dense :label="$t('transfer.common.viewDetail')" class="action-btn" :to="`/account/transfer-detail?id=${transfer.id}`" />
                  <q-btn v-if="transfer.status === 0" flat color="teal" dense :label="$t('transfer.common.edit')" class="action-btn" :to="`/account/transfer-edit?id=${transfer.id}`" />
                  <q-btn v-if="transfer.status === 0" flat color="negative" dense :label="$t('transfer.common.cancel')" class="action-btn" @click="cancelTransfer(transfer.id)" />
                  <q-btn v-if="transfer.status === -1" flat color="deep-orange" dense :label="$t('transfer.common.delete')" class="action-btn" @click="deleteTransfer(transfer.id)" />
                </div>
              </div>

              <!-- 物品列表 -->
              <div v-if="transfer.items && transfer.items.length > 0" class="q-mt-md q-pa-sm bg-grey-1 rounded-borders items-list">
                <div class="text-caption text-weight-medium q-mb-xs">{{ $t('transfer.list.itemList') }}</div>
                <div v-for="(item, index) in transfer.items" :key="index" class="row q-py-xs" :class="{ 'border-top': index > 0 }">
                  <div class="col-4">{{ item.category }}</div>
                  <div class="col-4">{{ item.name }}</div>
                  <div class="col-2 text-center">{{ item.quantity }}{{ $t('transfer.common.piece') }}</div>
                  <div class="col-2 text-right">{{ item.weight ? item.weight + $t('transfer.common.kg') : $t('transfer.common.unknown') }}</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 移动视图 - 卡片样式 -->
          <div v-for="transfer in state.pagination.list" :key="`mobile-${transfer.id}`" class="bg-white q-mb-md rounded-borders shadow-1 transfer-card mobile-view">
            <q-card>
              <q-card-section>
                <!-- 卡片顶部 - 状态和操作按钮 -->
                <div class="row justify-between items-center q-mb-sm">
                  <q-badge :color="getStatusColor(transfer.status)" class="q-py-xs q-px-sm status-badge">
                    {{ getStatusText(transfer.status) }}
                  </q-badge>
                  <div class="row">
                    <q-btn flat round dense color="primary" icon="visibility" :to="`/account/transfer-detail?id=${transfer.id}`">
                      <q-tooltip>{{ $t('transfer.common.viewDetail') }}</q-tooltip>
                    </q-btn>
                    <q-btn v-if="transfer.status === 0" flat round dense color="teal" icon="edit" :to="`/account/transfer-edit?id=${transfer.id}`">
                      <q-tooltip>{{ $t('transfer.common.edit') }}</q-tooltip>
                    </q-btn>
                    <q-btn v-if="transfer.status === 0" flat round dense color="negative" icon="cancel" @click="cancelTransfer(transfer.id)">
                      <q-tooltip>{{ $t('transfer.common.cancel') }}</q-tooltip>
                    </q-btn>
                    <q-btn v-if="transfer.status === -1" flat round dense color="deep-orange" icon="delete" @click="deleteTransfer(transfer.id)">
                      <q-tooltip>{{ $t('transfer.common.delete') }}</q-tooltip>
                    </q-btn>
                  </div>
                </div>

                <!-- 转运单信息 -->
                <div class="q-mb-sm">
                  <div class="row q-mb-xs">
                    <div class="col-5 text-grey-8">{{ $t('transfer.common.transferNumber') }}:</div>
                    <div class="col-7 text-weight-medium">{{ transfer.no }}</div>
                  </div>
                  <div class="row q-mb-xs">
                    <div class="col-5 text-grey-8">{{ $t('transfer.common.createTime') }}:</div>
                    <div class="col-7">{{ formatDateTime(transfer.createTime) }}</div>
                  </div>
                  <div class="row q-mb-xs">
                    <div class="col-5 text-grey-8">{{ $t('transfer.common.expressCompany') }}:</div>
                    <div class="col-7">{{ transfer.logisticsId }}</div>
                  </div>
                  <div class="row q-mb-xs">
                    <div class="col-5 text-grey-8">{{ $t('transfer.common.expressNumber') }}:</div>
                    <div class="col-7">{{ transfer.logisticsNo }}</div>
                  </div>
                  <div class="row">
                    <div class="col-5 text-grey-8">{{ $t('transfer.common.itemCount') }}:</div>
                    <div class="col-7">{{ transfer.itemCount || 0 }}{{ $t('transfer.common.piece') }}</div>
                  </div>
                </div>

                <!-- 物品列表 -->
                <div v-if="transfer.items && transfer.items.length > 0" class="q-mt-md">
                  <q-expansion-item icon="inventory_2" :label="$t('transfer.list.itemList')" :caption="$t('transfer.detail.itemDetails')" header-class="text-primary">
                    <q-card>
                      <q-card-section>
                        <div v-for="(item, index) in transfer.items" :key="index" class="q-py-xs" :class="{ 'border-top': index > 0 }">
                          <div class="row q-mb-xs">
                            <div class="col-3 text-grey-8">{{ $t('transfer.list.category') }}:</div>
                            <div class="col-9">{{ item.category }}</div>
                          </div>
                          <div class="row q-mb-xs">
                            <div class="col-3 text-grey-8">{{ $t('transfer.list.name') }}:</div>
                            <div class="col-9">{{ item.name }}</div>
                          </div>
                          <div class="row">
                            <div class="col-3 text-grey-8">{{ $t('transfer.list.quantity') }}:</div>
                            <div class="col-4">{{ item.quantity }}{{ $t('transfer.common.piece') }}</div>
                            <div class="col-2 text-grey-8">{{ $t('transfer.list.weight') }}:</div>
                            <div class="col-3">{{ item.weight ? item.weight + $t('transfer.common.kg') : $t('transfer.common.unknown') }}</div>
                          </div>
                        </div>
                      </q-card-section>
                    </q-card>
                  </q-expansion-item>
                </div>

                <!-- 底部操作按钮 -->
                <div class="row justify-end q-mt-md">
                  <q-btn color="primary" :to="`/account/transfer-detail?id=${transfer.id}`" :label="$t('transfer.common.viewDetail')" />
                  <q-btn v-if="transfer.status === 0" color="teal" outline class="q-ml-sm" :label="$t('transfer.common.edit')" :to="`/account/transfer-edit?id=${transfer.id}`" />
                  <q-btn v-if="transfer.status === 0" color="negative" outline class="q-ml-sm" :label="$t('transfer.common.cancel')" @click="cancelTransfer(transfer.id)" />
                  <q-btn v-if="transfer.status === -1" color="deep-orange" outline class="q-ml-sm" :label="$t('transfer.common.delete')" @click="deleteTransfer(transfer.id)" />
                </div>
              </q-card-section>
            </q-card>
          </div>
        </div>

        <!-- 分页控件和记录信息 -->
        <div class="row justify-between items-center q-mt-md flex-column-mobile" v-if="state.pagination.total > 0">
          <div class="text-caption text-grey-8 pagination-info text-center-mobile q-mb-sm-md">
            {{ $t('transfer.list.totalRecords', { total: state.pagination.total, current: state.pagination.pageNo, totalPages: Math.ceil(state.pagination.total / state.pagination.pageSize) || 1 }) }}
          </div>
          <q-pagination
            v-model="state.pagination.pageNo"
            :max="Math.ceil(state.pagination.total / state.pagination.pageSize)"
            :max-pages="$q.screen.lt.sm ? 3 : 6"
            boundary-links
            direction-links
            dense
            class="pagination-control justify-center-mobile"
            @update:model-value="onPageChange" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, onMounted } from 'vue';
import { useQuasar } from 'quasar';
import TransferApi from '~/composables/transferApi';
import WarehouseAddressButton from '~/components/account/WarehouseAddressButton.vue';
import { useI18n } from 'vue-i18n';

definePageMeta({
  middleware: 'auth', // 引入身份验证中间件
});

const { t } = useI18n();
const $q = useQuasar();

// 数据状态
const state = reactive({
  currentTab: 'all', // 默认显示待入库
  searchNo: '',
  loadStatus: false,
  pagination: {
    list: [],
    total: 0,
    pageNo: 1,
    pageSize: 10,
  },
});

// 移除了仓库地址相关状态，已移至组件中

// 页面加载时获取数据
onMounted(() => {
  getTransferList();
});

// 获取转运单列表
async function getTransferList() {
  state.loadStatus = true;

  try {
    // 根据Tab页确定转运单状态
    const status = state.currentTab !== 'all' ? parseInt(state.currentTab, 10) : null;

    const { code, data } = await TransferApi.getTransferPage({
      pageNo: state.pagination.pageNo,
      pageSize: state.pagination.pageSize,
      status,
      no: state.searchNo || null,
    });

    if (code === 0) {
      state.pagination.list = data.list || [];
      state.pagination.total = data.total || 0;
    } else {
      $q.notify({
        type: 'negative',
        message: t('transfer.errors.getListFailed'),
      });
      state.pagination.list = [];
      state.pagination.total = 0;
    }
  } catch (error) {
    console.error(t('transfer.errors.getListError'), error);
    state.pagination.list = [];
    state.pagination.total = 0;
  } finally {
    state.loadStatus = false;
  }
}

// Tab切换事件
function onTabChange() {
  state.pagination.pageNo = 1;
  getTransferList();
}

// 分页变更事件
function onPageChange() {
  getTransferList();
}

// 取消转运单
async function cancelTransfer(id) {
  try {
    $q.dialog({
      title: $t('transfer.detail.cancelConfirmTitle'),
      message: $t('transfer.detail.cancelConfirmMessage'),
      cancel: true,
      persistent: true,
    }).onOk(async () => {
      const { code, msg } = await TransferApi.cancelTransfer(id);
      if (code === 0) {
        $q.notify({
          type: 'positive',
          message: $t('transfer.detail.cancelSuccess'),
        });
        getTransferList();
      } else {
        $q.notify({
          type: 'negative',
          message: msg || $t('transfer.detail.cancelFailed'),
        });
      }
    });
  } catch (error) {
    console.error($t('transfer.errors.cancelError'), error);
    $q.notify({
      type: 'negative',
      message: $t('transfer.errors.cancelError'),
    });
  }
}

// 删除转运单
async function deleteTransfer(id) {
  try {
    $q.dialog({
      title: $t('transfer.detail.deleteConfirmTitle'),
      message: $t('transfer.detail.deleteConfirmMessage'),
      color: 'negative',
      cancel: true,
      persistent: true,
    }).onOk(async () => {
      const { code, msg } = await TransferApi.deleteTransfer(id);
      if (code === 0) {
        $q.notify({
          type: 'positive',
          message: $t('transfer.detail.deleteSuccess'),
        });
        getTransferList();
      } else {
        $q.notify({
          type: 'negative',
          message: msg || $t('transfer.detail.deleteFailed'),
        });
      }
    });
  } catch (error) {
    console.error($t('transfer.errors.deleteError'), error);
    $q.notify({
      type: 'negative',
      message: $t('transfer.errors.deleteError'),
    });
  }
}

// 获取状态文本
function getStatusText(status) {
  switch (status) {
    case 0:
      return t('transfer.status.pending');
    case 10:
      return t('transfer.status.warehoused');
    case 20:
      return t('transfer.status.toBeShipped');
    case 30:
      return t('transfer.status.shipped');
    case 40:
      return t('transfer.status.completed');
    case -1:
      return t('transfer.status.cancelled');
    default:
      return t('transfer.status.unknown');
  }
}

// 获取状态颜色
function getStatusColor(status) {
  switch (status) {
    case 0:
      return 'orange';
    case 10:
      return 'blue';
    case 20:
      return 'teal';
    case 30:
      return 'purple';
    case 40:
      return 'positive';
    case -1:
      return 'grey';
    default:
      return 'grey';
  }
}

// 格式化日期时间
function formatDateTime(timestamp) {
  if (!timestamp) return '';
  const date = new Date(timestamp);
  return date
    .toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    })
    .replace(/\//g, '-');
}

// 移除了复制仓库地址函数，已移至组件中
</script>

<style lang="scss" scoped>
.my-transfer {
  .q-tab {
    font-weight: 500;
    padding: 0 12px;
    min-height: 32px;
  }

  .q-tabs {
    min-height: 32px;
  }

  .compact-tabs {
    .q-tab {
      padding: 0 10px;
      min-height: 32px;

      &__label {
        font-size: 0.9rem;
      }
    }
  }

  .search-input {
    width: 180px;

    :deep(.q-field__control) {
      height: 36px;
      min-height: 36px;
    }

    :deep(.q_field__native) {
      padding-top: 0;
      padding-bottom: 0;
    }
  }

  .warehouse-card {
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    }

    .text-caption {
      font-size: 0.8rem;
      margin-bottom: 2px;
    }
  }

  .table-header {
    font-size: 0.9rem;
    height: 40px;
  }

  .transfer-card {
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
    }

    .card-header {
      font-size: 0.85rem;
    }

    .card-content {
      font-size: 0.9rem;
    }

    .status-badge {
      font-size: 0.8rem;
      font-weight: 500;
    }

    .action-btn {
      min-height: 28px;
      padding: 0 8px;
      margin: 2px 0;
      width: 100%;
      max-width: 100px;
    }

    .items-list {
      font-size: 0.85rem;
    }

    // 移动视图卡片样式
    @media (max-width: 599px) {
      .text-grey-8 {
        white-space: nowrap;
        font-size: 0.85rem;
      }

      .col-7 {
        word-break: break-word;
        font-size: 0.85rem;
      }
    }
  }

  .pagination-control {
    .q-btn {
      font-weight: 500;
      padding: 0 8px;
      min-height: 32px;
    }
  }

  .pagination-info {
    font-size: 0.85rem;
  }

  .rounded-borders-top {
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
  }

  .border-top {
    border-top: 1px solid #e0e0e0;
  }

  // 响应式样式
  .desktop-only {
    @media (max-width: 767px) {
      display: none;
    }
  }

  .desktop-view {
    @media (max-width: 767px) {
      display: none;
    }
  }

  .mobile-view {
    @media (min-width: 768px) {
      display: none;
    }
  }

  .wrap-on-mobile {
    @media (max-width: 767px) {
      flex-wrap: wrap;
    }
  }

  .q-mb-sm-md {
    @media (max-width: 767px) {
      margin-bottom: 16px;
    }
  }

  .q-mt-sm-md {
    @media (max-width: 767px) {
      margin-top: 16px;
    }
  }

  .justify-between-on-mobile {
    @media (max-width: 767px) {
      justify-content: space-between;
    }
  }

  .justify-end-on-mobile {
    @media (max-width: 767px) {
      justify-content: flex-end;
    }
  }

  .text-center-mobile {
    @media (max-width: 767px) {
      text-align: center;
    }
  }

  .justify-center-mobile {
    @media (max-width: 767px) {
      justify-content: center;
    }
  }

  .flex-column-mobile {
    @media (max-width: 767px) {
      flex-direction: column;
      align-items: center;
    }
  }

  .search-container {
    @media (max-width: 767px) {
      width: 100%;
      margin-bottom: 12px;

      .search-input {
        width: 100%;
      }
    }
  }

  .header-buttons {
    @media (max-width: 599px) {
      .warehouse-btn {
        .q-btn__content {
          .q-icon {
            margin-right: 0;
          }
          span {
            display: none;
          }
        }
      }
    }
  }

  .create-btn {
    min-width: 140px;

    @media (max-width: 767px) {
      margin-left: 0 !important;
      width: auto;
      min-width: 120px;
      max-width: 200px;
      font-size: 0.8rem;
    }

    @media (max-width: 599px) {
      padding: 0 8px;
      min-height: 32px;
    }
  }
}
</style>
