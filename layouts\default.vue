<template>
  <q-layout view="hHh lpR fFf">
    <q-page-container>
      <slot />
    </q-page-container>

    <!-- 客服悬浮按钮 -->
    <FloatingButton v-if="showCustomerService" />

    <!-- 全站侧边栏 -->
    <FixedBar />
  </q-layout>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import { useRoute } from 'vue-router';
import FloatingButton from '~/components/CustomerService/FloatingButton.vue';

const route = useRoute();
const showCustomerService = ref(false);

// 在某些页面不显示客服按钮
onMounted(() => {
  // 监听路由变化，在聊天页面不显示客服按钮
  // showCustomerService.value = !route.path.includes('/chat');
  // 监听路由变化
  // watch(
  //   () => route.path,
  //   (newPath) => {
  //     showCustomerService.value = !newPath.includes('/chat');
  //   }
  // );
});
</script>
