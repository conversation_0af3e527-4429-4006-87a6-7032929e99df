const KeFuApi = {
  sendKefuMessage: (data) => {
    return useClientPost('/promotion/kefu-message/send', {
      body: data,
      custom: {
        auth: true,
        showLoading: true,
        loadingMsg: '发送中',
        showSuccess: true,
        successMsg: '发送成功',
      },
    });
  },
  getKefuMessagePage: (params) => {
    return useClientGet('/promotion/kefu-message/page', {
      params,
      custom: {
        auth: true,
        showLoading: false,
      },
    });
  },
};

export default KeFuApi;
