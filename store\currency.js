import { defineStore } from 'pinia';
import CurrencyApi from '~/composables/currencyApi';
import { formatCurrency } from '~/utils/currencyUtils';

export const useCurrencyStore = defineStore('currency', {
  state: () => {
    // 尝试从localStorage获取保存的货币设置
    let savedCurrency = null;
    if (import.meta.client) {
      const savedCurrencyStr = localStorage.getItem('currency');
      if (savedCurrencyStr) {
        try {
          savedCurrency = JSON.parse(savedCurrencyStr);
        } catch (e) {
          console.error('解析保存的货币设置失败:', e);
        }
      }
    }

    const defaultCurrencyValue = {
      currency: 'CNY',
      symbol: '￥',
      rate: 1,
    };

    return {
      list: [], // 存储所有的货币数据
      selectedCurrency: savedCurrency || defaultCurrencyValue,
      defaultCurrency: defaultCurrencyValue,
    };
  },
  getters: {
    getCurrencyList: (state) => state.list,
    getSelectedCurrency: (state) => state.selectedCurrency,
  },
  actions: {
    async fetchCurrencyList() {
      console.log('fetchCurrencyList');
      const { code, data } = await CurrencyApi.getCurrencyList();
      if (code === 0) {
        this.list = data;
      }
    },

    setCurrentCurrency(currency) {
      this.selectedCurrency = currency;
    },
    amountFormat(amount) {
      // 使用通用的货币格式化工具函数
      return formatCurrency(amount, this.selectedCurrency, this.defaultCurrency);
    },
  },
  persist: import.meta.client && {
    storage: localStorage,
    paths: ['selectedCurrency'],
  },
});
