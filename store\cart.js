import { defineStore } from 'pinia';
import CartApi from '@/composables/cartApi';
import { useAuthStore } from '~/store/auth';
import { useRoute, useRouter } from 'vue-router';

export const useCartStore = defineStore({
  id: 'cart',
  state: () => {
    return {
      list: [], // 购物车列表
      selectedIds: [], // 已选列表
      isAllSelected: false, // 是否全选
      totalPriceSelected: 0, // 选中项总金额
    };
  },
  actions: {
    // 获取购物车列表合并购物车
    async getList() {
      if (useAuthStore().isLogin) {
        const { data, code } = await CartApi.getCartList();
        console.log('data', data);
        console.log('code', code);
        if (code === 0) {
          //合并购物车
          // 使用 await 确保返回值被解析
          const mergedData = await this.mergeAndSyncCart(this.list, data.validList);

          this.list = mergedData; // 确保 this.list 是一个数组
        }
      }
    },

    //订单确认单获取已选择的商品
    getSelectList() {},

    // 添加购物车
    async add(item) {
      console.log('添加到购物车store');
      //判断是否登录  已登录则直接添加后端并从后端返回list,如果未登录则添加到本地缓存
      if (useAuthStore().isLogin) {
        console.log('已登录,直接添加到后端');
        // 添加购物项
        const { code } = await CartApi.addCart({
          skuId: item.sku.id,
          count: item.count,
          price: item.price,
          memo: item.memo,
          type: 0, //自营商品
        });
        // 获取 code
        console.log('code', code);
        // 刷新购物车列表
        if (code === 0) {
          await this.getList();
        }
      } else {
        console.log('未登录,跳转到登录页面');
        useRouter().push('/login');
      }
    },
    //代购商品添加到购物车
    async addAgentProduct(item) {
      console.log('添加到购物车store');
      //判断是否登录  已登录则直接添加后端并从后端返回list,如果未登录则添加到本地缓存
      if (useAuthStore().isLogin) {
        console.log('已登录,直接添加到后端');
        // 添加购物项
        const { code } = await CartApi.addCart({
          skuId: item.sku.id,
          count: item.count,
          price: item.price,
          type: item.type, //商品类型
          source: item.spu.source,
          sourceId: item.spu.id,
          sourceSkuId: item.sku.id,
          memo: item.memo,
        });
        // 获取 code
        console.log('code', code);
        // 刷新购物车列表
        // if (code === 0) {
        //   await this.getList();
        // }
      } else {
        console.log('未登录,跳转到登录页面');
        useRouter().push('/login');
      }
    },

    //合并购物车
    async mergeAndSyncCart(localList, serverList) {
      // 确保 `localList` 是一个数组
      localList = Array.isArray(localList) ? localList : [];
      const mergedList = [];
      const localOnlyList = [];

      // 创建 Map，快速查找服务端商品
      const serverMap = new Map(
        serverList.map((item) => [item.sku.id, item]) // 以 `sku.id` 为键
      );

      // 遍历本地购物车，合并与服务器的数据
      for (const localItem of localList) {
        const serverItem = serverMap.get(localItem.sku.id);

        if (serverItem) {
          // 如果两边都存在，取数量大的那一方
          const count = Math.max(localItem.count, serverItem.count);
          mergedList.push({
            ...serverItem, // 基于服务端的数据
            count, // 更新数量
          });
          // 从服务端 Map 中移除已经处理的商品
          serverMap.delete(localItem.sku.id);
        } else {
          // 如果仅本地存在，加入 `localOnlyList`
          localOnlyList.push(localItem);
        }
      }

      // 将服务端存在但本地不存在的商品加入合并列表
      for (const serverItem of serverMap.values()) {
        mergedList.push(serverItem);
      }

      // 循环调用 `addToServer` 方法，将本地存在但服务端不存在的商品同步到服务器
      for (const localOnlyItem of localOnlyList) {
        await CartApi.addCart({
          skuId: localOnlyItem.sku.id,
          count: localOnlyItem.count,
          price: localOnlyItem.price,
          memo: localOnlyItem.memo,
        });
      }
      // 返回合并后的列表
      return mergedList;
    },

    updateCartQuantity(payload) {
      function calculateStockCounts(product, quantity) {
        // const qty = product.count + quantity;
        const qty = quantity;
        const stock = product.sku.stock;
        if (stock < qty) {
          return false;
        }
        return true;
      }
      this.list.find((items, index) => {
        if (items.sku.id === payload.product.sku.id) {
          let qty = this.list[index].count + payload.qty;
          // const havaStock = calculateStockCounts(this.list[index], payload.qty);
          const stock = this.list[index].sku.stock;
          if (qty < 1) {
            //数量小于1则赋值为1
            qty = 1;
          }
          if (stock < qty) {
            //超过库存大小则赋值为库存大小
            qty = stock;
          }
          this.list[index].count = qty;
          if (useAuthStore().isLogin) {
            CartApi.updateCartCount({
              id: payload.product.id,
              count: qty,
            });
          }
          return true;
        }
      });

      // 更新总金额、选中状态等其他逻辑
      // this.calculateTotal();
    },

    // 更新购物车商品备注
    updateCartMemo(payload) {
      // 查找并更新本地数据
      const item = this.list.find((item) => item.id === payload.id);
      if (item) {
        item.memo = payload.memo;
      }

      // 如果已登录，则同步到服务器
      if (useAuthStore().isLogin) {
        CartApi.updateCartMemo({
          id: payload.id,
          memo: payload.memo,
        });
      }
    },

    toggleAllSelect(newSelected) {
      this.list.forEach((item) => {
        item.selected = newSelected; // 修改原始数据
      });
    },

    clearCart() {
      this.list = [];
      this.selectedIds = [];
      this.isAllSelected = true;
      this.totalPriceSelected = 0;
    },

    removeCartItem(payload) {
      this.list = this.list.filter((item) => item.sku.id != payload.sku.id);
      // const existingItems = this.list.find((cartItem) => cartItem.sku.id === payload.sku.id);
      if (useAuthStore().isLogin) {
        CartApi.deleteCart(payload.id);
      }
      // 更新总金额、选中状态等其他逻辑
      // this.calculateTotal();
    },
    //删除本地购物车项目，用户订单提交后
    removeCartItemLocal(cartItems) {
      // 创建一个 Set 用于快速查找需要删除的 sku.id
      const cartItemIdsToRemove = new Set(cartItems.map((item) => item.cartId));

      // 过滤掉需要删除的商品
      this.list = this.list.filter((item) => !cartItemIdsToRemove.has(item.id));
    },
  },
  getters: {
    cartTotalAmount: (state) => {
      return state.list.filter((item) => item.selected).reduce((total, item) => total + (item.price || item.sku.price) * item.count, 0);
    },
    cartTotalCount: (state) => {
      return state.list.filter((item) => item.selected).length;
    },
    cartTotalFreight: (state) => {
      // 创建一个 Map 来记录每个店铺的最大运费
      const shopFreightMap = new Map();

      // 遍历购物车中所有选中的商品
      state.list
        .filter((item) => item.selected) // 仅计算选中的商品
        .forEach((item) => {
          const shopName = item.spu.shopName || '未分组'; // 获取店铺名称
          const freight = item.spu.freight || 0; // 获取商品运费

          // 如果当前店铺还没有记录运费，或者当前商品运费更大，则更新
          if (!shopFreightMap.has(shopName) || shopFreightMap.get(shopName) < freight) {
            shopFreightMap.set(shopName, freight);
          }
        });

      // 将所有店铺的最大运费累加
      return Array.from(shopFreightMap.values()).reduce((total, freight) => total + freight, 0);
    },
    cartSelectIds: (state) => {
      return state.list
        .filter((item) => item.selected) // 筛选出已选择的项目
        .map((item) => item.id); // 提取它们的 ID
    },
  },
  //persist定义要做判断，因为localStorage是客户端参数，所以需要加process.client
  persist: process.client && {
    storage: localStorage,
    paths: ['carts'],
  },
});
