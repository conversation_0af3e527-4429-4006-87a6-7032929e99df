const AddressApi = {
  // 获得国家
  getCountry: () => {
    return useClientGet('/system/area/country');
  },
  // 获得省市
  getState: (countryId) => {
    return useClientGet('/system/area/state', {
      params: { countryId },
    });
  },
  // 获得城市
  getCity: (stateId) => {
    return useClientGet('/system/area/city', {
      params: { stateId },
    });
  },

  // 获得地区树
  getAreaTree: (id) => {
    return useClientGet('/system/area/treeById', {
      params: { id },
    });
  },
  // 获得地区树根据ID
  getAreaTreeById: (id) => {
    return useClientGet('/system/area/treeById', {
      params: { id },
    });
  },

  // 获得地区树根据ID
  getAreaTreeByChildId: (id, type) => {
    return useClientGet('/system/area/treeByChildId', {
      params: { id, type },
    });
  },
  // 获得用户收件地址分页
  getAddressPage: (params) => {
    return useClientGet('/member/address/page', {
      params,
      custom: {
        showLoading: false,
      },
    });
  },
  // 获得用户收件地址列表
  getAddressList: () => {
    return useClientGet('/member/address/list');
  },
  // 创建用户收件地址
  createAddress: (data) => {
    return useClientPost('/member/address/create', {
      body: data,
    });
  },
  // 更新用户收件地址
  updateAddress: (data) => {
    return useClientPut('/member/address/update', {
      body: data,
    });
  },
  // 设置默认收件地址
  setDefaultAddress: (id) => {
    return useClientPut('/member/address/setDefault', {
      params: { id },
    });
  },
  // 获得用户收件地址
  getAddress: (id) => {
    return useClientGet('/member/address/get', {
      params: { id },
    });
  },
  // 获得默认收件地址
  getDefaultAddress: () => {
    return useClientGet('/member/address/get-default');
  },
  // 删除用户收件地址
  deleteAddress: (id) => {
    return useClientDelete('/member/address/delete', {
      params: { id },
    });
  },
};

export default AddressApi;
