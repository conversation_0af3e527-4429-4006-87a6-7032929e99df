<template>
  <q-card 
    class="cursor-pointer" 
    :class="{ 'bg-blue-1': selected }" 
    @click="$emit('click')"
  >
    <q-card-section>
      <slot></slot>
    </q-card-section>
  </q-card>
</template>

<script setup>
// 定义组件接收的属性
defineProps({
  selected: {
    type: Boolean,
    default: false
  }
});

// 定义组件向外发出的事件
defineEmits(['click']);
</script>

<style lang="scss" scoped>
.q-card {
  transition: all 0.2s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
  }
}
</style>
